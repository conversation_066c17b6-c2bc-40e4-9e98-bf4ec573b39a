<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>物流利润计算器</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/select2-bootstrap-5-theme/1.3.0/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            background-color: #fff;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #eee;
            padding: 15px 20px;
        }
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        .form-control, .form-select {
            border-radius: 5px;
            border: 1px solid #ced4da;
            padding: 8px 12px;
            width: 100%;
        }
        .form-control:focus, .form-select:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 500;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
        }
        .table {
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 0;
        }
        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            color: #495057;
            font-weight: 600;
        }
        .highlight {
            background-color: #e3f2fd !important;
        }
        .select2-container--bootstrap-5 {
            width: 100% !important;
        }
        .select2-container--bootstrap-5 .select2-selection {
            border-radius: 5px;
            border: 1px solid #ced4da;
            padding-right: 30px;
            min-height: 38px;
        }
        .select2-container--bootstrap-5 .select2-selection--single:after,
        .select2-container--bootstrap-5 .select2-selection--multiple:after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #6c757d;
            pointer-events: none;
        }
        .select2-container--bootstrap-5 .select2-selection:hover {
            border-color: #80bdff;
        }
        .select2-container--bootstrap-5.select2-container--open .select2-selection {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .dimensions-group {
            display: flex;
            gap: 10px;
        }
        .dimensions-group .form-control {
            flex: 1;
        }
        .alert {
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .card {
                margin-bottom: 15px;
            }
            .dimensions-group {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">物流利润计算器</h1>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">输入参数</h5>
            </div>
            <div class="card-body">
                <form id="calculator-form">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">产品重量 (克)</label>
                            <input type="number" class="form-control" id="weight" min="1" step="1" value="500" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">产品体积</label>
                            <div class="dimensions-group">
                                <input type="number" class="form-control" id="length" placeholder="长(cm)" min="1" step="1" >
                                <input type="number" class="form-control" id="width" placeholder="宽(cm)" min="1" step="1" >
                                <input type="number" class="form-control" id="height" placeholder="高(cm)" min="1" step="1" >
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">产品售价 (¥)</label>
                            <input type="number" class="form-control" id="price" min="0" step="0.01" value="1000" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">采购成本 (¥)</label>
                            <input type="number" class="form-control" id="cost" min="0" step="0.01" value="500" required>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">佣金比例 (%)</label>
                            <input type="number" class="form-control" id="commission" min="0" max="100" step="0.1" value="15" required>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">贴单费用 (¥)</label>
                            <input type="number" class="form-control" id="label-fee" min="0" step="0.01" value="0" required>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">提现手续费 (%)</label>
                            <input type="number" class="form-control" id="withdrawal-fee" min="0" max="100" step="0.1" value="0" required>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">卢布兑换人民币汇率</label>
                            <input type="number" class="form-control" id="exchange-rate" min="0" step="0.0001" value="0.0795" required>
                            <small class="text-muted">1 卢布 = 0.0795 人民币 (示例汇率，请根据实际情况调整)</small>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">筛选物流商</label>
                                <select class="form-select" id="providerFilter" multiple>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">筛选物流方式</label>
                                <select class="form-select" id="methodFilter" multiple>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">筛选物流速度</label>
                                <select class="form-select" id="speedFilter" multiple>
                                    <option value="Express">特快</option>
                                    <option value="Standard">标准</option>
                                    <option value="Economy">经济</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-primary" id="calculate-btn">计算利润</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">计算结果</h5>
            </div>
            <div class="card-body">
                <div id="summary" class="mb-3"></div>
                <div class="table-responsive">
                    <table class="table table-hover" id="results-table">
                        <thead>
                            <tr>
                                <th>物流商</th>
                                <th>物流方式</th>
                                <th>物流速度</th>
                                <th>价格范围 (¥)</th>
                                <th>重量范围 (g)</th>
                                <th>运费 (¥)</th>
                                <th>佣金 (¥)</th>
                                <th>贴单费 (¥)</th>
                                <th>提现费 (¥)</th>
                                <th>总成本 (¥)</th>
                                <th>利润 (¥)</th>
                                <th>利润率 (%)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Results will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">目标利润率反推</h5>
            </div>
            <div class="card-body">
                <form id="reverse-form">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">目标利润率 (%)</label>
                            <input type="number" class="form-control" id="target-profit" min="0" max="100" step="0.1" value="20" required>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary" id="reverse-calculate-btn">计算所需售价</button>
                        </div>
                    </div>
                </form>
                <div id="reverse-result" class="mt-3"></div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            // 初始化Select2
            $('#providerFilter').select2({
                theme: 'bootstrap-5',
                placeholder: '选择物流商',
                allowClear: true
            });
            
            $('#methodFilter').select2({
                theme: 'bootstrap-5',
                placeholder: '选择物流方式',
                allowClear: true
            });
            
            $('#speedFilter').select2({
                theme: 'bootstrap-5',
                placeholder: '选择物流速度',
                allowClear: true
            });
            
            // 获取物流商列表
            $.ajax({
                url: 'calculate.php?action=get_providers',
                method: 'GET',
                dataType: 'json',
                success: function(providers) {
                    if (Array.isArray(providers)) {
                        providers.forEach(function(provider) {
                            $('#providerFilter').append(new Option(provider, provider));
                        });
                    } else {
                        console.error('Invalid providers data:', providers);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching providers:', error);
                }
            });

            // 获取物流方式列表
            $.ajax({
                url: 'calculate.php?action=get_methods',
                method: 'GET',
                dataType: 'json',
                success: function(methods) {
                    if (Array.isArray(methods)) {
                        methods.forEach(function(method) {
                            $('#methodFilter').append(new Option(method, method));
                        });
                    } else {
                        console.error('Invalid methods data:', methods);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching methods:', error);
                }
            });

            // 计算利润
            $('#calculator-form').on('submit', function(e) {
                e.preventDefault();
                
                const formData = {
                    weight: $('#weight').val(),
                    price: $('#price').val(),
                    cost: $('#cost').val(),
                    commission: $('#commission').val(),
                    label_fee: $('#label-fee').val(),
                    withdrawal_fee: $('#withdrawal-fee').val(),
                    exchange_rate: $('#exchange-rate').val(),
                    providers: $('#providerFilter').val(),
                    methods: $('#methodFilter').val(),
                    speeds: $('#speedFilter').val()
                };

                $.ajax({
                    url: 'calculate.php?action=calculate',
                    method: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(data) {
                        displayResults(data);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error calculating profit:', error);
                        alert('计算过程中发生错误，请查看控制台获取详细信息。');
                    }
                });
            });

            // 反推售价
            $('#reverse-form').on('submit', function(e) {
                e.preventDefault();
                
                const formData = {
                    weight: $('#weight').val(),
                    cost: $('#cost').val(),
                    commission: $('#commission').val(),
                    label_fee: $('#label-fee').val(),
                    withdrawal_fee: $('#withdrawal-fee').val(),
                    target_profit: $('#target-profit').val(),
                    exchange_rate: $('#exchange-rate').val(),
                    providers: $('#providerFilter').val(),
                    methods: $('#methodFilter').val(),
                    speeds: $('#speedFilter').val()
                };

                $.ajax({
                    url: 'calculate.php?action=reverse_calculate',
                    method: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(data) {
                        displayReverseResults(data);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error calculating target price:', error);
                        alert('计算过程中发生错误，请查看控制台获取详细信息。');
                    }
                });
            });

            // 显示计算结果
            function displayResults(data) {
                const tableBody = $('#results-table tbody');
                tableBody.empty();
                
                if (data.length === 0) {
                    tableBody.append('<tr><td colspan="11" class="text-center">没有找到符合条件的物流选项</td></tr>');
                    $('#summary').html('');
                    return;
                }

                // 获取表单中的实际输入值
                const weight = $('#weight').val();
                const price = $('#price').val();
                const cost = $('#cost').val();
                const commission = $('#commission').val();
                
                // 显示摘要信息
                $('#summary').html(`
                    <div class="alert alert-info">
                        <strong>输入信息:</strong> 重量: ${weight}g, 
                        售价: ¥${price}, 
                        采购成本: ¥${cost}, 
                        佣金比例: ${commission}%
                    </div>
                    <div class="alert alert-success">
                        <strong>找到 ${data.length} 个符合条件的物流选项</strong>
                    </div>
                `);
                
                // 填充表格
                data.forEach((result, index) => {
                    const row = $('<tr>');
                    if (index === 0) row.addClass('highlight');
                    
                    row.html(`
                        <td>${result.provider}</td>
                        <td>${result.method}</td>
                        <td>${result.speed}</td>
                        <td>¥${result.minPriceCNY}-¥${result.maxPriceCNY}</td>
                        <td>${result.minWeight}-${result.maxWeight}g</td>
                        <td>¥${result.shippingCost.toFixed(2)}</td>
                        <td>¥${result.commission.toFixed(2)}</td>
                        <td>¥${result.labelFee.toFixed(2)}</td>
                        <td>¥${result.withdrawalFee.toFixed(2)}</td>
                        <td>¥${result.totalCost.toFixed(2)}</td>
                        <td>¥${result.profit.toFixed(2)}</td>
                        <td>${result.profitMargin.toFixed(2)}%</td>
                    `);
                    
                    tableBody.append(row);
                });
            }

            // 显示反推结果
            function displayReverseResults(data) {
                const reverseResultDiv = $('#reverse-result');
                
                if (data.length === 0) {
                    reverseResultDiv.html('<div class="alert alert-warning">没有找到符合条件的物流选项</div>');
                    return;
                }
                
                const bestOption = data[0];
                
                reverseResultDiv.html(`
                    <div class="alert alert-info">
                        <h6 class="alert-heading">目标利润率: ${bestOption.targetProfit}%</h6>
                        <hr>
                        <p class="mb-1"><strong>最优物流方案:</strong> ${bestOption.provider} - ${bestOption.speed}</p>
                        <p class="mb-1"><strong>所需售价:</strong> ¥${bestOption.targetPrice.toFixed(2)}</p>
                        <p class="mb-1"><strong>运费成本:</strong> ¥${bestOption.shippingCost.toFixed(2)}</p>
                        <p class="mb-1"><strong>平台佣金:</strong> ¥${bestOption.commission.toFixed(2)} (${bestOption.commissionRate}%)</p>
                        <p class="mb-1"><strong>贴单费用:</strong> ¥${bestOption.labelFee.toFixed(2)}</p>
                        <p class="mb-1"><strong>提现费用:</strong> ¥${bestOption.withdrawalFee.toFixed(2)} (${bestOption.withdrawalFeeRate}%)</p>
                        <p class="mb-0"><strong>实际利润率:</strong> ${bestOption.profitMargin.toFixed(2)}% (¥${bestOption.profit.toFixed(2)})</p>
                    </div>
                `);
            }
        });
    </script>
</body>
</html> 