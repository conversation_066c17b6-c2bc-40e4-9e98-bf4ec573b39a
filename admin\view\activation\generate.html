<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>生成激活码</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-auz" style="margin-right: 8px;"></i>激活码生成器
        </div>
        <div class="layui-card-body">
            <div class="layui-form layui-form-pane">
                <div class="layui-form-item">
                    <label class="layui-form-label">会员等级</label>
                    <div class="layui-input-block">
                        <select id="user-level" lay-verify="required">
                            <option value=""></option>
                            <option value="1">个人用户</option>
                            <option value="2">SVIP用户</option>
                            <option value="3">企业用户</option>
                            <option value="4">定制会员</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">套餐类型</label>
                    <div class="layui-input-block">
                        <div class="layui-btn-group" id="days-options">
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" data-days="1">体验卡(1天)</button>
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" data-days="7">周卡(7天)</button>
                            <button type="button" class="layui-btn layui-btn-sm" data-days="30">月卡(30天)</button>
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" data-days="90">季卡(90天)</button>
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" data-days="365">年卡(365天)</button>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">自定义天数</label>
                    <div class="layui-input-block">
                        <input type="number" id="days" value="30" placeholder="可自定义有效天数" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">赠送倍数</label>
                    <div class="layui-input-block">
                        <input type="number" id="bonus-multiplier" value="1" step="0.1" placeholder="输入赠送倍数，例如1.2" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux">例如，月卡30天，倍数1.2，则实际天数为 30 * 1.2 = 36天。默认为1 (无赠送)。</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">生成数量</label>
                    <div class="layui-input-block">
                        <input type="number" id="num" value="10" placeholder="请输入生成数量" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux">单次最多生成100个</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">代理UID</label>
                    <div class="layui-input-block">
                        <input type="number" id="agent-uid" placeholder="可选，指定代理用户UID" autocomplete="off" class="layui-input">
                        <div class="layui-form-mid layui-word-aux">可选项，用于指定激活码归属的代理</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" id="generate-btn">
                            <i class="layui-icon layui-icon-add-1"></i> 立即生成
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="generated-codes" style="display: none; margin-top: 20px;">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="text-align: left; width: auto; padding-left: 10px;">已生成激活码:</label>
                    <div class="layui-input-block">
                        <textarea id="codes-textarea" readonly class="layui-textarea" style="min-height: 200px;"></textarea>
                        <div class="layui-form-mid layui-word-aux">
                            <button class="layui-btn layui-btn-xs" id="copy-all-btn">
                                <i class="layui-icon layui-icon-template-1"></i> 复制全部
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" id="download-btn">
                                <i class="layui-icon layui-icon-download-circle"></i> 下载文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="layui-row layui-col-space15" style="margin-top: 20px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            <i class="layui-icon layui-icon-chart" style="margin-right: 8px;"></i>生成统计
                        </div>
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md3">
                                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 4px;">
                                        <div style="font-size: 24px; font-weight: bold; color: #1890ff;" id="total-generated">0</div>
                                        <div style="color: #666; margin-top: 5px;">本次生成</div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 4px;">
                                        <div style="font-size: 24px; font-weight: bold; color: #52c41a;" id="success-count">0</div>
                                        <div style="color: #666; margin-top: 5px;">生成成功</div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 4px;">
                                        <div style="font-size: 24px; font-weight: bold; color: #fa8c16;" id="selected-level">-</div>
                                        <div style="color: #666; margin-top: 5px;">会员等级</div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 4px;">
                                        <div style="font-size: 24px; font-weight: bold; color: #722ed1;" id="selected-days">-</div>
                                        <div style="color: #666; margin-top: 5px;">有效天数</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    <script>
        layui.use(['layer', 'form', 'jquery'], function(){
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.jquery;

            var levelMap = {
                '1': '普通用户',
                '2': 'VIP用户', 
                '3': 'SVIP用户',
                '4': '企业用户'
            };

            $('#days-options .layui-btn').on('click', function() {
                var days = $(this).data('days');
                $('#days').val(days);
                $(this).removeClass('layui-btn-primary').siblings().addClass('layui-btn-primary');
            });

            // 生成按钮点击事件
            $('#generate-btn').on('click', function(){
                var user_level = $('#user-level').val();
                var days = $('#days').val();
                var bonus_multiplier = $('#bonus-multiplier').val() || 1;
                var final_days = Math.round(parseInt(days, 10) * parseFloat(bonus_multiplier));
                var num = $('#num').val();
                var agent_uid = $('#agent-uid').val();
                
                if(user_level === '' || days <= 0 || num <= 0){
                    layer.msg('请填写完整的生成参数', {icon: 7});
                    return;
                }

                if(num > 100){
                    layer.msg('单次最多生成100个激活码', {icon: 7});
                    return;
                }
                
                var loadingIndex = layer.load(1, {shade: [0.1,'#fff']});
                
                var requestData = { 
                    user_level: parseInt(user_level),
                    days: final_days,
                    num: parseInt(num)
                };

                if(agent_uid && agent_uid > 0){
                    requestData.agent_uid = parseInt(agent_uid);
                }
                
                $.ajax({
                    url: '/admin/ajax.php?act=generate_activation_codes',
                    type: 'POST',
                    contentType: 'application/json',
                    dataType: 'json',
                    data: JSON.stringify(requestData),
                    success: function(res){
                        layer.close(loadingIndex);
                        if(res.code === 1){
                            layer.msg(res.msg, {icon: 1, time: 2000});
                            $('#generated-codes').show();
                            $('#codes-textarea').val(res.data.join('\n'));
                            
                            // 更新统计信息
                            $('#total-generated').text(res.data.length);
                            $('#success-count').text(res.data.length);
                            $('#selected-level').text(levelMap[user_level]);
                            $('#selected-days').text(final_days + '天');
                        } else {
                            layer.msg(res.msg || '生成失败', {icon: 5});
                            $('#generated-codes').hide();
                            $('#codes-textarea').val('');
                        }
                    },
                    error: function(){
                        layer.close(loadingIndex);
                        layer.msg('网络请求失败，请稍后再试', {icon: 2});
                    }
                });
            });

            // 复制全部按钮
            $('#copy-all-btn').on('click', function(){
                var textarea = document.getElementById('codes-textarea');
                textarea.select();
                document.execCommand('copy');
                layer.msg('已复制到剪贴板', {icon: 1});
            });

            // 下载文件按钮
            $('#download-btn').on('click', function(){
                var codes = $('#codes-textarea').val();
                if(!codes){
                    layer.msg('没有可下载的激活码', {icon: 7});
                    return;
                }

                var blob = new Blob([codes], {type: 'text/plain'});
                var url = window.URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = url;
                a.download = '激活码_' + new Date().toISOString().substr(0,10) + '.txt';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                layer.msg('下载已开始', {icon: 1});
            });

        });
    </script>
</body>
</html> 