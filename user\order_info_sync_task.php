<?php
/**
 * Ozon订单信息同步守护进程
 * 自动获取所有店铺的买家咨询聊天，提取并缓存订单信息
 * 
 * 使用方法:
 * 1. 单次执行: php order_info_sync_task.php
 * 2. 守护进程: php order_info_sync_task.php --daemon
 * 3. 宝塔进程守护: 直接运行此脚本即可
 */

// 设置执行环境
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 0);
error_reporting(E_ALL);

// 检查是否在CLI模式下运行
if (php_sapi_name() !== 'cli') {
    header('Content-Type: text/plain');
    echo "此脚本只能在命令行模式下运行\n";
    exit(1);
}

// 包含必要文件
$baseDir = dirname(__FILE__);
require_once $baseDir . '/../includes/common.php';
require_once $baseDir . '/../includes/lib/OzonApiClient.php';

// 内置配置
$config = [
    'daemon_mode' => false,           // 是否以守护进程模式运行
    'sync_interval' => 3600,         // 同步间隔（秒），默认1小时
    'batch_size' => 300,              // 每批处理的聊天数量
    'api_delay' => 1,                // API调用间隔（秒）
    'max_execution_time' => 1800,    // 最大执行时间（秒），30分钟
    'log_level' => 'INFO',           // 日志级别: DEBUG, INFO, WARN, ERROR
    'skip_recent_hours' => 168,      // 跳过最近N小时内已缓存的聊天（7天）
    'chat_types' => ['Buyer_Seller'], // 要处理的聊天类型
    'max_chats_per_store' => 400,    // 每个店铺最多处理的聊天数量
    'only_chat_history' => true,     // 只使用聊天历史API提取订单信息
    'log_file' => $baseDir . '/logs/sync_task.log', // 日志文件路径
];

// 全局变量
$running = true;
$logDir = $baseDir . '/logs';

// 确保日志目录存在
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// 检查命令行参数
$isDaemonMode = in_array('--daemon', $argv ?? []);
$isDebugMode = in_array('--debug', $argv ?? []);

// 设置模式
if ($isDaemonMode) {
    $config['daemon_mode'] = true;
    echo "启动守护进程模式...\n";
}

if ($isDebugMode) {
    $config['log_level'] = 'DEBUG';
    echo "启用调试模式，将输出详细日志...\n";
}

// 注册信号处理器（如果支持的话）
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGTERM, 'signalHandler');
    pcntl_signal(SIGINT, 'signalHandler');
    pcntl_signal(SIGHUP, 'signalHandler');
}

// 日志函数
function writeLog($level, $message, $data = null) {
    global $config;
    
    // 检查日志级别
    $levels = ['DEBUG' => 0, 'INFO' => 1, 'WARN' => 2, 'ERROR' => 3];
    $currentLevelValue = $levels[$config['log_level']] ?? 1;
    $messageLevelValue = $levels[$level] ?? 1;
    
    if ($messageLevelValue < $currentLevelValue) {
        return; // 跳过低级别日志
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message";
    
    if ($data) {
        $logEntry .= ' | Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    // 写入日志文件
    file_put_contents($config['log_file'], $logEntry . "\n", FILE_APPEND | LOCK_EX);
    
    // 输出到控制台
    echo $logEntry . "\n";
    
    // 日志轮转 - 保留最近7天
    if (rand(1, 100) === 1) {
        cleanOldLogs();
    }
}

// 清理旧日志
function cleanOldLogs() {
    global $logDir;
    
    $files = glob($logDir . '/*.log');
    foreach ($files as $file) {
        if (filemtime($file) < time() - (7 * 24 * 3600)) {
            unlink($file);
        }
    }
}

// 信号处理器
function signalHandler($signal) {
    global $running;
    
    writeLog('INFO', "接收到信号: $signal");
    
    switch ($signal) {
        case SIGTERM:
        case SIGINT:
            writeLog('INFO', '准备停止守护进程...');
            $running = false;
            break;
        case SIGHUP:
            writeLog('INFO', '重新加载配置...');
            break;
    }
}

// 主执行函数
function executeSync() {
    global $DB, $config;
    
    $startTime = time();
    writeLog('INFO', '开始执行订单信息同步任务');
    
    try {
        // 检查数据库连接
        if (!$DB) {
            throw new Exception('数据库连接失败');
        }
        
        // 获取所有启用API的店铺
        $stores = $DB->getAll("SELECT id, storename, ClientId, `key`, apistatus FROM ozon_store WHERE apistatus = 1 AND `key` != ''");
        
        if (empty($stores)) {
            writeLog('WARN', '没有找到启用API的店铺');
            return;
        }
        
        writeLog('INFO', '找到启用API的店铺', ['count' => count($stores)]);
        
        $totalStats = [
            'stores_processed' => 0,
            'total_chats' => 0,
            'buyer_chats' => 0,
            'chats_with_history' => 0,
            'chats_with_orders' => 0,
            'chats_saved' => 0,
            'api_calls' => 0,
            'errors' => 0
        ];
        
        // 处理每个店铺
        foreach ($stores as $store) {
            if (time() - $startTime > $config['max_execution_time']) {
                writeLog('WARN', '达到最大执行时间，停止处理');
                break;
            }
            
            $storeStats = processSingleStore($store);
            
            // 累计统计
            foreach ($totalStats as $key => $value) {
                $totalStats[$key] += $storeStats[$key] ?? 0;
            }
            
            // API调用间隔
            sleep($config['api_delay']);
        }
        
        $duration = time() - $startTime;
        writeLog('INFO', '同步任务执行完成', array_merge($totalStats, ['duration_seconds' => $duration]));
        
    } catch (Exception $e) {
        writeLog('ERROR', '同步任务执行失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    }
}

// 处理单个店铺
function processSingleStore($store) {
    global $config;
    
    writeLog('INFO', "开始处理店铺: {$store['storename']}", ['store_id' => $store['id']]);
    
    $stats = [
        'stores_processed' => 1,
        'total_chats' => 0,
        'buyer_chats' => 0,
        'chats_with_history' => 0,
        'chats_with_orders' => 0,
        'chats_saved' => 0,
        'api_calls' => 0,
        'errors' => 0
    ];
    
    try {
        // 创建API客户端
        $ozonClient = new \lib\OzonApiClient($store['ClientId'], $store['key']);
        
        // 获取买家咨询聊天列表
        $offset = 0;
        $processedChats = 0;
        
        do {
            $response = $ozonClient->getChatList($config['batch_size'], $offset);
            $stats['api_calls']++;
            
            if (!$response || !isset($response['messages']) && !isset($response['chats'])) {
                // 尝试不同的响应格式
                $chats = [];
                if (isset($response['chats'])) {
                    $chats = $response['chats'];
                } elseif (isset($response['result']['chats'])) {
                    $chats = $response['result']['chats'];
                } elseif (is_array($response) && !empty($response)) {
                    $chats = $response;
                }
            } else {
                $chats = $response['messages'] ?? $response['chats'] ?? [];
            }
            
            $stats['total_chats'] += count($chats);
            
            if (empty($chats)) {
                writeLog('INFO', "店铺 {$store['storename']} 没有更多聊天数据");
                break;
            }
            
            // 筛选买家咨询类型的聊天
            $buyerChats = array_filter($chats, function($chat) use ($config) {
                return in_array($chat['chat_type'] ?? '', $config['chat_types']);
            });
            
            $stats['buyer_chats'] += count($buyerChats);
            writeLog('DEBUG', "找到买家咨询聊天", ['total' => count($chats), 'buyer_chats' => count($buyerChats)]);
            
            // 处理每个买家咨询
            foreach ($buyerChats as $chat) {
                if ($processedChats >= $config['max_chats_per_store']) {
                    writeLog('INFO', "达到单店铺最大处理数量限制: {$config['max_chats_per_store']}");
                    break 2;
                }
                
                // 检查是否需要跳过（已有较新缓存）
                if (shouldSkipChat($chat['chat_id'], $store['id'])) {
                    writeLog('DEBUG', "跳过最近已处理的聊天", ['chat_id' => $chat['chat_id']]);
                    continue;
                }
                
                // 对每个买家咨询获取聊天历史记录
                try {
                    writeLog('DEBUG', "获取聊天历史记录", ['chat_id' => $chat['chat_id']]);
                    
                    $historyResponse = $ozonClient->getChatHistory($chat['chat_id'], 1, 0);
                    $stats['api_calls']++;
                    $stats['chats_with_history']++;
                    
                    // 从聊天历史提取订单信息
                    $orderInfo = extractOrderInfoFromChatHistory($historyResponse, $chat['chat_id']);
                    
                    // 只有提取到订单号的聊天才保存到数据库
                    if ($orderInfo && !empty($orderInfo['order_number'])) {
                        $stats['chats_with_orders']++;
                        
                        // 缓存到数据库
                        if (cacheOrderInfo($chat['chat_id'], $store['id'], $orderInfo)) {
                            $stats['chats_saved']++;
                            writeLog('INFO', "保存有订单号的聊天", ['chat_id' => $chat['chat_id'], 'order_number' => $orderInfo['order_number']]);
                        }
                    } else {
                        writeLog('DEBUG', "聊天历史无订单号，跳过保存", ['chat_id' => $chat['chat_id']]);
                    }
                    
                    // API调用间隔
                    usleep(500000); // 0.5秒
                    
                } catch (Exception $e) {
                    writeLog('ERROR', "获取聊天历史失败", ['chat_id' => $chat['chat_id'], 'error' => $e->getMessage()]);
                    $stats['errors']++;
                }
                
                $processedChats++;
            }
            
            $offset += $config['batch_size'];
            
        } while (count($chats) >= $config['batch_size'] && $processedChats < $config['max_chats_per_store']);
        
        writeLog('INFO', "店铺处理完成: {$store['storename']}", $stats);
        
    } catch (Exception $e) {
        $stats['errors']++;
        writeLog('ERROR', "处理店铺失败: {$store['storename']}", ['error' => $e->getMessage()]);
    }
    
    return $stats;
}

// 检查是否应该跳过聊天
function shouldSkipChat($chatId, $storeId) {
    global $DB, $config;
    
    try {
        // 只跳过已经有订单号且最近更新过的聊天
        $recentCache = $DB->getRow(
            "SELECT updated_at, order_number FROM ozon_chats WHERE chat_id = ? AND store_id = ? AND order_number != '' AND order_number IS NOT NULL AND updated_at > DATE_SUB(NOW(), INTERVAL ? HOUR)",
            [$chatId, $storeId, $config['skip_recent_hours']]
        );
        
        if ($recentCache) {
            writeLog('DEBUG', "跳过已有订单号的聊天", ['chat_id' => $chatId, 'order_number' => $recentCache['order_number']]);
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        writeLog('ERROR', "检查跳过聊天失败", ['chat_id' => $chatId, 'error' => $e->getMessage()]);
        return false;
    }
}

// 从聊天列表响应中直接提取订单信息
function extractOrderInfoFromChatList($chat) {
    try {
        $chatId = $chat['chat_id'] ?? '';
        $chatType = $chat['chat_type'] ?? '';
        
        writeLog('DEBUG', "开始从聊天列表提取订单信息", [
            'chat_id' => $chatId, 
            'chat_type' => $chatType,
            'chat_keys' => array_keys($chat)
        ]);
        
        // 只处理买家咨询类型
        if ($chatType !== 'Buyer_Seller') {
            writeLog('DEBUG', "跳过非买家咨询聊天", ['chat_id' => $chatId, 'chat_type' => $chatType]);
            return null;
        }
        
        $orderInfo = [
            'order_number' => '',
            'sku' => '',
            'product_name' => '',
            'extraction_source' => 'chat_list_context'
        ];
        
        // 检查聊天对象是否包含context字段
        if (isset($chat['context']) && is_array($chat['context'])) {
            $context = $chat['context'];
            $orderInfo['order_number'] = $context['order_number'] ?? '';
            $orderInfo['sku'] = $context['sku'] ?? '';
            
            writeLog('DEBUG', "从聊天列表context提取订单信息", [
                'chat_id' => $chatId,
                'context' => $context,
                'extracted' => $orderInfo
            ]);
        } else {
            writeLog('DEBUG', "聊天列表中没有context字段", ['chat_id' => $chatId]);
        }
        
        // 如果没有context或context中没有订单号，尝试从data数组提取
        if (empty($orderInfo['order_number']) && isset($chat['data']) && is_array($chat['data']) && !empty($chat['data'])) {
            $firstDataItem = $chat['data'][0] ?? '';
            // 检查是否像订单号格式（包含数字和连字符）
            if (preg_match('/^\d{8,}-\d{4}-\d+$/', $firstDataItem)) {
                $orderInfo['order_number'] = $firstDataItem;
                writeLog('DEBUG', "从data数组提取订单号", ['chat_id' => $chatId, 'order_number' => $firstDataItem]);
            }
        }
        
        // 尝试从标题或最后消息中提取商品名称
        $title = $chat['title'] ?? '';
        $lastMessage = $chat['last_message'] ?? '';
        
        if (!empty($title) && preg_match('/"([^"]{3,100})"/u', $title, $matches)) {
            $orderInfo['product_name'] = $matches[1];
            writeLog('DEBUG', "从标题提取商品名称", ['chat_id' => $chatId, 'product_name' => $matches[1]]);
        } elseif (!empty($lastMessage) && preg_match('/"([^"]{3,100})"/u', $lastMessage, $matches)) {
            $orderInfo['product_name'] = $matches[1];
            writeLog('DEBUG', "从最后消息提取商品名称", ['chat_id' => $chatId, 'product_name' => $matches[1]]);
        }
        
        // 如果还是没有提取到商品名称，尝试从data数组的第二个元素提取
        if (empty($orderInfo['product_name']) && isset($chat['data']) && is_array($chat['data']) && count($chat['data']) > 1) {
            $secondDataItem = $chat['data'][1] ?? '';
            if (!empty($secondDataItem) && mb_strlen($secondDataItem) > 3) {
                $orderInfo['product_name'] = $secondDataItem;
                writeLog('DEBUG', "从data数组提取商品描述", ['chat_id' => $chatId, 'product_name' => $secondDataItem]);
            }
        }
        
        // 检查是否提取到有用信息
        $hasInfo = !empty($orderInfo['order_number']) || !empty($orderInfo['sku']) || !empty($orderInfo['product_name']);
        
        writeLog('DEBUG', "订单信息提取结果", [
            'chat_id' => $chatId,
            'order_info' => $orderInfo,
            'has_useful_info' => $hasInfo
        ]);
        
        if (!$hasInfo) {
            return null;
        }
        
        return $orderInfo;
        
    } catch (Exception $e) {
        writeLog('ERROR', "从聊天列表提取订单信息失败", ['chat_id' => $chat['chat_id'] ?? 'unknown', 'error' => $e->getMessage()]);
        return null;
    }
}

// 从聊天历史API响应中提取订单信息
function extractOrderInfoFromChatHistory($historyResponse, $chatId) {
    writeLog('DEBUG', "开始从聊天历史提取订单信息", ['chat_id' => $chatId]);
    
    $orderInfo = [
        'order_number' => '',
        'sku' => '',
        'product_name' => '',
        'extraction_source' => 'chat_history'
    ];
    
    // 检查响应格式
    $messages = null;
    if (isset($historyResponse['messages']) && is_array($historyResponse['messages'])) {
        $messages = $historyResponse['messages'];
    } elseif (isset($historyResponse['result']['messages']) && is_array($historyResponse['result']['messages'])) {
        $messages = $historyResponse['result']['messages'];
    } elseif (isset($historyResponse['result']) && is_array($historyResponse['result'])) {
        $messages = $historyResponse['result'];
    }
    
    if (empty($messages)) {
        writeLog('DEBUG', "聊天历史无消息", ['chat_id' => $chatId]);
        return null;
    }
    
    writeLog('DEBUG', "聊天历史消息数量", ['chat_id' => $chatId, 'message_count' => count($messages)]);
    
    // 检查第一条消息
    $firstMessage = $messages[0];
    writeLog('DEBUG', "第一条消息结构", ['chat_id' => $chatId, 'message_keys' => array_keys($firstMessage)]);
    
    // 优先从context字段提取
    if (isset($firstMessage['context']) && is_array($firstMessage['context'])) {
        $context = $firstMessage['context'];
        $orderInfo['order_number'] = $context['order_number'] ?? '';
        $orderInfo['sku'] = $context['sku'] ?? '';
        
        writeLog('DEBUG', "从聊天历史context提取", [
            'chat_id' => $chatId,
            'context' => $context,
            'extracted' => ['order_number' => $orderInfo['order_number'], 'sku' => $orderInfo['sku']]
        ]);
    }
    
    // 如果context中没有订单号，尝试从data数组提取
    if (empty($orderInfo['order_number']) && isset($firstMessage['data']) && is_array($firstMessage['data'])) {
        writeLog('DEBUG', "从聊天历史data数组提取", ['chat_id' => $chatId, 'data' => $firstMessage['data']]);
        
        if (!empty($firstMessage['data'][0])) {
            $firstDataItem = $firstMessage['data'][0];
            if (preg_match('/^\d{8,}-\d{4}-\d+$/', $firstDataItem)) {
                $orderInfo['order_number'] = $firstDataItem;
                $orderInfo['extraction_source'] = 'chat_history_data';
                writeLog('DEBUG', "从data[0]提取订单号", ['chat_id' => $chatId, 'order_number' => $firstDataItem]);
            }
        }
        
        // 提取商品描述
        if (empty($orderInfo['product_name']) && !empty($firstMessage['data'][1]) && mb_strlen($firstMessage['data'][1]) > 3) {
            $orderInfo['product_name'] = $firstMessage['data'][1];
            writeLog('DEBUG', "从data[1]提取商品描述", ['chat_id' => $chatId, 'product_name' => $firstMessage['data'][1]]);
        }
    }
    
    // 检查是否提取到订单号（这是必须的）
    if (empty($orderInfo['order_number'])) {
        writeLog('DEBUG', "聊天历史中未找到订单号", ['chat_id' => $chatId]);
        return null;
    }
    
    writeLog('DEBUG', "聊天历史提取结果", ['chat_id' => $chatId, 'order_info' => $orderInfo]);
    return $orderInfo;
}

// 缓存订单信息
function cacheOrderInfo($chatId, $storeId, $orderInfo) {
    global $DB;
    
    try {
        // 从店铺信息获取用户ID
        $storeInfo = $DB->getRow("SELECT uid FROM ozon_store WHERE id = ?", [$storeId]);
        if (!$storeInfo) {
            writeLog('ERROR', "找不到店铺信息", ['store_id' => $storeId]);
            return false;
        }
        $uid = $storeInfo['uid'];
        
        // 查找或创建聊天记录，直接在ozon_chats表中存储订单信息
        $existingChat = $DB->getRow(
            "SELECT id FROM ozon_chats WHERE chat_id = ? AND store_id = ? AND uid = ?",
            [$chatId, $storeId, $uid]
        );
        
        $orderInfoJson = json_encode($orderInfo, JSON_UNESCAPED_UNICODE);
        
        if ($existingChat) {
            // 更新现有聊天记录的订单信息
            $DB->exec(
                "UPDATE ozon_chats SET order_number = ?, sku = ?, product_name = ?, order_info_json = ?, extraction_source = ?, updated_at = NOW() WHERE id = ?",
                [
                    $orderInfo['order_number'] ?? '',
                    $orderInfo['sku'] ?? '',
                    $orderInfo['product_name'] ?? '',
                    $orderInfoJson,
                    $orderInfo['extraction_source'] ?? 'sync_task',
                    $existingChat['id']
                ]
            );
        } else {
            // 创建新的聊天记录
            $DB->exec(
                "INSERT INTO ozon_chats (uid, store_id, chat_id, title, chat_type, status, order_number, sku, product_name, order_info_json, extraction_source, created_at, updated_at) VALUES (?, ?, ?, '买家咨询', 'Buyer_Seller', 'active', ?, ?, ?, ?, ?, NOW(), NOW())",
                [
                    $uid,
                    $storeId,
                    $chatId,
                    $orderInfo['order_number'] ?? '',
                    $orderInfo['sku'] ?? '',
                    $orderInfo['product_name'] ?? '',
                    $orderInfoJson,
                    $orderInfo['extraction_source'] ?? 'sync_task'
                ]
            );
        }
        
        return true;
        
    } catch (Exception $e) {
        writeLog('ERROR', "缓存订单信息失败", ['chat_id' => $chatId, 'error' => $e->getMessage()]);
        return false;
    }
}



// 守护进程主循环
function daemonLoop() {
    global $config, $running;
    
    writeLog('INFO', '守护进程启动', ['sync_interval' => $config['sync_interval']]);
    
    while ($running) {
        try {
            executeSync();
            
            writeLog('INFO', "等待下次同步，间隔: {$config['sync_interval']} 秒");
            
            // 分段睡眠，以便能够响应信号
            $sleepTime = $config['sync_interval'];
            while ($sleepTime > 0 && $running) {
                sleep(min(30, $sleepTime)); // 每次最多睡眠30秒
                $sleepTime -= 30;
                
                // 处理信号
                if (function_exists('pcntl_signal_dispatch')) {
                    pcntl_signal_dispatch();
                }
            }
            
        } catch (Exception $e) {
            writeLog('ERROR', '守护进程异常', ['error' => $e->getMessage()]);
            sleep(60); // 出错后等待1分钟再重试
        }
    }
    
    writeLog('INFO', '守护进程已停止');
}

// 主入口
try {
    if ($config['daemon_mode']) {
        daemonLoop();
    } else {
        executeSync();
    }
} catch (Exception $e) {
    writeLog('ERROR', '脚本执行失败', ['error' => $e->getMessage()]);
    exit(1);
}
?> 