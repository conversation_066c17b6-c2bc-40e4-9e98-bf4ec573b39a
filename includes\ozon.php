<?php
function hasAttributeId($json, int $targetId): bool {
    // 处理非数组或空值情况
    if (!is_array($json)) {
        error_log("警告: hasAttributeId() 接收到非数组类型: " . gettype($json));
        return false;
    }
    
    // 安全提取属性ID
    $attributeIds = array_column($json['attributes'] ?? [], 'id');
    $complexAttributeIds = array_column($json['complex_attributes'] ?? [], 'id');
    
    // 检查是否存在目标ID
    return in_array($targetId, $attributeIds) || in_array($targetId, $complexAttributeIds);
}

function buildCategoryMaps($ru=false) {
    if($ru){
        $lm = "俄文类目";
    }else{
        $lm = "中文类目";
    }
    $data = json_decode(file_get_contents(ROOT.'user/config/'.$lm.'.json'),true)['result'];
    $maps = [
        'typeToCategory' => [],    // typeId -> categoryId
        'categoryParent' => [],    // categoryId -> parentId
        'categoryNames' => [],     // categoryId -> categoryName
        'typeNames' => [],         // typeId -> typeName
    ];

    $traverse = function ($nodes, $parentCategoryId = null) use (&$traverse, &$maps) {
        foreach ($nodes as $node) {
            $currentCategoryId = $node['descriptionCategoryId'];
            
            // 记录名称映射
            $maps['categoryNames'][$currentCategoryId] = $node['descriptionCategoryName'];
            
            // 记录父级关系
            if ($parentCategoryId !== null) {
                $maps['categoryParent'][$currentCategoryId] = $parentCategoryId;
            }

            // 处理子节点
            if (!empty($node['nodes'])) {
                foreach ($node['nodes'] as $childNode) {
                    if ($childNode['descriptionTypeId'] === '0') {
                        // 递归处理子分类
                        $traverse([$childNode], $currentCategoryId);
                    } else {
                        // 记录类型节点信息
                        $typeId = $childNode['descriptionTypeId'];
                        $maps['typeToCategory'][$typeId] = $currentCategoryId;
                        $maps['typeNames'][$typeId] = $childNode['descriptionTypeName'];
                    }
                }
            }
        }
    };

    foreach ($data as $topNode) {
        $traverse([$topNode]);
    }
    return $maps;
}

/**
 * 查找带名称的类目路径
 */
function findCategoryPathWithNames(string $targetTypeId, $ru=false) {
    $maps = buildCategoryMaps($ru);
    
    if (!isset($maps['typeToCategory'][$targetTypeId])) {
        return false;
    }

    $path = [$maps['typeNames'][$targetTypeId] ?? $targetTypeId];
    $categoryId = $maps['typeToCategory'][$targetTypeId];

    // 向上追溯父分类
    while (isset($maps['categoryParent'][$categoryId])) {
        $path[] = $maps['categoryNames'][$categoryId] ?? $categoryId;
        $categoryId = $maps['categoryParent'][$categoryId];
    }

    // 添加根分类名称
    $path[] = $maps['categoryNames'][$categoryId] ?? $categoryId;
    
    return implode(' > ', array_reverse($path));
}

/**
 * 查找纯ID的类目路径
 */
function findCategoryPath(string $targetTypeId) {
    $maps = buildCategoryMaps();
    
    if (!isset($maps['typeToCategory'][$targetTypeId])) {
        return false;
    }

    $path = [$targetTypeId];
    $categoryId = $maps['typeToCategory'][$targetTypeId];

    // 向上追溯父分类
    while (isset($maps['categoryParent'][$categoryId])) {
        $path[] = $categoryId;
        $categoryId = $maps['categoryParent'][$categoryId];
    }

    // 添加根分类ID
    $path[] = $categoryId;
    
    return implode(',', array_reverse($path));
}

function get_ozon_type_id($query){
    global $cookie;
    $url = "https://seller.ozon.ru/api/v1/seller-tree/search";
    $referer = "https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers";
    //{"company_id":"2648258","filters":{"search_in_aliases":true},"query":"棒球帽"}
    $sc_company_id = getSubstr($cookie, 'sc_company_id=', ';');
    $array = ['company_id'=>$sc_company_id,'filters'=>['search_in_aliases'=>true],'query'=>$query];
    $res = get_ozoncurl($url,$array, $referer, $cookie);
    $json = json_decode($res,true);
    if($json['items'][0]){
        return $json['items'][0]['description_type_id'];
    }
    return false; 
}
function jsondata($detail_url) {
    $blocks = []; // 初始化数组

    if (strpos($detail_url, ';') === false) {
        // 处理网页URL中的图片
        $res = get_curl($detail_url);
        if (empty($res)) {
            return ['error' => 'Failed to fetch URL content'];
        }

        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($res);
        
        // 记录DOM解析错误
        $errors = libxml_get_errors();
        if (!empty($errors)) {
            error_log("DOM解析错误: " . print_r($errors, true));
            libxml_clear_errors();
        }

        $images = $dom->getElementsByTagName('img');
        foreach ($images as $imgNode) {
            $src = $imgNode->getAttribute('src');
            if (!empty($src)) {
                processImage($src, $blocks);
            }
        }
    } else {
        $images = array_filter(explode(';', $detail_url), function($url) {
            return trim($url) !== '';
        });
        // 处理分号分隔的图片链接
        foreach ($images as $src) {
            processImage($src, $blocks);
        }
    }

    $array = [
        'content' => [
            [
                'widgetName' => 'raShowcase',
                'type' => 'roll',
                'blocks' => $blocks
            ]
        ],
        'version' => 0.3
    ];
    return $array;
}

/**
 * 处理单个图片链接，获取尺寸并填充到$blocks
 */
function processImage($src, &$blocks) {
    $src = convertImageUrl($src);
    $imageInfo = @getimagesize($src); // 抑制错误

    if ($imageInfo === false || empty($imageInfo[0]) || empty($imageInfo[1])) {
        error_log("无法获取图片尺寸: $src");
        return;
    }

    $width = $imageInfo[0];
    $height = $imageInfo[1];

    $blocks[] = [
        'imgLink' => "",
        'img' => [
            'src' => $src,
            'srcMobile' => $src,
            'alt' => '',
            'width' => $width,
            'height' => $height,
            'widthMobile' => $width,
            'eightMobile' => $height,
            'position' => 'width_full'
        ]
    ];
}

function commissionData($categories){
    $commissionData = [
        '内衣和袜类产品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '22.5%', 'FBP_1500plus' => '21.5%'],
        '季节性内衣和袜类产品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '22.5%', 'FBP_1500plus' => '21.5%'],
        '美容设备' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '16.0%', 'FBP_1500plus' => '15.0%'],
        '美容与健康' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '鞋类' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '季节性鞋类' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '服装和配饰' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.5%', 'FBP_1500plus' => '19.5%'],
        '服装' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.5%', 'FBP_1500plus' => '19.5%'],
        '季节性服装及配饰' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.5%', 'FBP_1500plus' => '19.5%'],
        '专业美容设备' => ['rFBS_1500' => '7.5%', 'FBP_1500' => '6.5%', 'rFBS_1500plus' => '7.5%', 'FBP_1500plus' => '6.5%'],
        '珠宝' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.5%', 'FBP_1500plus' => '13.5%'],
        '美容和卫生'=> ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '16.0%', 'FBP_1500plus' => '15.0%'],
        // 电子产品
        '戴森配件' => ['rFBS_1500' => '6.0%', 'FBP_1500' => '5.0%', 'rFBS_1500plus' => '6.0%', 'FBP_1500plus' => '5.0%'],
        '电子产品配饰' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.0%', 'FBP_1500plus' => '19.0%'],
        '音频和视频设备配件' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.5%', 'FBP_1500plus' => '13.5%'],
        '电子游戏' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.5%', 'FBP_1500plus' => '13.5%'],
        '索尼电子游戏' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '嵌入式大型家用电器' => ['rFBS_1500' => '9.0%', 'FBP_1500' => '8.0%', 'rFBS_1500plus' => '9.0%', 'FBP_1500plus' => '8.0%'],
        '电脑设备配件' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.5%', 'FBP_1500plus' => '12.5%'],
        '游戏主机及配件、摄影器材' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '12.5%', 'FBP_1500plus' => '11.5%'],
        '空调设备' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        '电脑及笔记本配件' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '12.5%', 'FBP_1500plus' => '11.5%'],
        '办公电脑设备、收银及仓储设备' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '16.0%', 'FBP_1500plus' => '15.0%'],
        '小型家用电器' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        '三星微波炉' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        '显示器' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '12.5%', 'FBP_1500plus' => '11.5%'],
        '索尼耳机' => ['rFBS_1500' => '8.0%', 'FBP_1500' => '7.0%', 'rFBS_1500plus' => '8.0%', 'FBP_1500plus' => '7.0%'],
        '三星 TWS 耳机' => ['rFBS_1500' => '8.0%', 'FBP_1500' => '7.0%', 'rFBS_1500plus' => '8.0%', 'FBP_1500plus' => '7.0%'],
        '不可调的大型家用电器' => ['rFBS_1500' => '9.0%', 'FBP_1500' => '8.0%', 'rFBS_1500plus' => '9.0%', 'FBP_1500plus' => '8.0%'],
        '笔记本电脑' => ['rFBS_1500' => '8.0%', 'FBP_1500' => '7.0%', 'rFBS_1500plus' => '8.0%', 'FBP_1500plus' => '7.0%'],
        '三星笔记本电脑' => ['rFBS_1500' => '7.0%', 'FBP_1500' => '6.0%', 'rFBS_1500plus' => '7.0%', 'FBP_1500plus' => '6.0%'],
        '电脑外设设备及耗材' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.5%', 'FBP_1500plus' => '13.5%'],
        '软件' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.5%', 'FBP_1500plus' => '17.5%'],
        '专业医疗设备' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.0%', 'FBP_1500plus' => '16.0%'],
        '三星吸尘器' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        '索尼智能手机' => ['rFBS_1500' => '9.0%', 'FBP_1500' => '8.0%', 'rFBS_1500plus' => '9.0%', 'FBP_1500plus' => '8.0%'],
        '智能手机和平板电脑' => ['rFBS_1500' => '11.5%', 'FBP_1500' => '10.5%', 'rFBS_1500plus' => '11.5%', 'FBP_1500plus' => '10.5%'],
        '三星智能手机和平板电脑' => ['rFBS_1500' => '8.0%', 'FBP_1500' => '7.0%', 'rFBS_1500plus' => '8.0%', 'FBP_1500plus' => '7.0%'],
        '智能手表与健身手环' => ['rFBS_1500' => '11.5%', 'FBP_1500' => '10.5%', 'rFBS_1500plus' => '11.5%', 'FBP_1500plus' => '10.5%'],
        '三星智能手表与健身手环' => ['rFBS_1500' => '8.0%', 'FBP_1500' => '7.0%', 'rFBS_1500plus' => '8.0%', 'FBP_1500plus' => '7.0%'],
        '台式电脑' => ['rFBS_1500' => '9.0%', 'FBP_1500' => '8.0%', 'rFBS_1500plus' => '9.0%', 'FBP_1500plus' => '8.0%'],
        '电视机' => ['rFBS_1500' => '9.0%', 'FBP_1500' => '8.0%', 'rFBS_1500plus' => '9.0%', 'FBP_1500plus' => '8.0%'],
        '苹果设备' => ['rFBS_1500' => '7.0%', 'FBP_1500' => '6.0%', 'rFBS_1500plus' => '7.0%', 'FBP_1500plus' => '6.0%'],
        '戴森设备' => ['rFBS_1500' => '8.0%', 'FBP_1500' => '7.0%', 'rFBS_1500plus' => '8.0%', 'FBP_1500plus' => '7.0%'],
        '数码商品' => ['rFBS_1500' => '11.0%', 'FBP_1500' => '10.0%', 'rFBS_1500plus' => '11.0%', 'FBP_1500plus' => '10.0%'],
        '电子锁' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.5%', 'FBP_1500plus' => '13.5%'],
        '游戏机套' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.0%', 'FBP_1500plus' => '19.0%'],
        '摄影和视频设备保护套' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.0%', 'FBP_1500plus' => '19.0%'],
        '音频设备配件' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.5%', 'FBP_1500plus' => '13.5%'],
        '游戏用具' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '12.5%', 'FBP_1500plus' => '11.5%'],
        '家庭和别墅安全系统'=>['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.5%', 'FBP_1500plus' => '13.5%'],
        // 家居与汽车用品
        '汽车和摩托车' => ['rFBS_1500' => '10.0%', 'FBP_1500' => '9.0%', 'rFBS_1500plus' => '10.0%', 'FBP_1500plus' => '9.0%'],
        '汽车用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.0%', 'FBP_1500plus' => '16.0%'],
        '游泳池' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '16.0%', 'FBP_1500plus' => '15.0%'],
        '自行车' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '行车记录仪和雷达探测器' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '12.0%', 'FBP_1500plus' => '11.0%'],
        '装饰、清洁与储物' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '住宅和花园' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.0%', 'FBP_1500plus' => '19.0%'],
        '书籍' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '22.0%', 'FBP_1500plus' => '21.0%'],
        '船只、马达和充气艇' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '家具' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '金属探测器' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '装饰材料' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.0%', 'FBP_1500plus' => '13.0%'],
        '新年装饰用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.0%', 'FBP_1500plus' => '19.0%'],
        '手动工具和测量仪器' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.0%', 'FBP_1500plus' => '16.0%'],
        '卫浴设备' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '14.0%', 'FBP_1500plus' => '13.0%'],
        '力量和心肺训练器材、蹦床' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '12.0%', 'FBP_1500plus' => '11.0%'],
        '运动手表' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '12.0%', 'FBP_1500plus' => '11.0%'],
        '建筑和装修' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '建筑、修缮和园艺设备' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '16.0%', 'FBP_1500plus' => '15.0%'],
        '运动和休闲用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '19.0%', 'FBP_1500plus' => '18.0%'],
        '重型建筑' => ['rFBS_1500' => '11.0%', 'FBP_1500' => '10.0%', 'rFBS_1500plus' => '11.0%', 'FBP_1500plus' => '10.0%'],
        '水过滤器' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.0%', 'FBP_1500plus' => '16.0%'],
        '数字图书' => ['rFBS_1500' => '6.0%', 'FBP_1500' => '5.0%', 'rFBS_1500plus' => '6.0%', 'FBP_1500plus' => '5.0%'],
        '轮胎' => ['rFBS_1500' => '10.0%', 'FBP_1500' => '9.0%', 'rFBS_1500plus' => '10.0%', 'FBP_1500plus' => '9.0%'],
        '电动滑板车' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.0%', 'FBP_1500plus' => '16.0%'],
        '运动与休闲' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '19.0%', 'FBP_1500plus' => '18.0%'],
        '小百货和配饰' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.5%', 'FBP_1500plus' => '19.5%'],
        '家电' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        // 药房商品
        '电子烟及加热系统配件' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '24.0%', 'FBP_1500plus' => '23.0%'],
        '药品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '药店' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '维生素和膳食补充剂' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '隐形眼镜' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '药用制剂' => ['rFBS_1500' => '3.0%', 'FBP_1500' => '2.0%', 'rFBS_1500plus' => '3.0%', 'FBP_1500plus' => '2.0%'],
        '矫形用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.0%', 'FBP_1500plus' => '16.0%'],
        '辅助药品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '专业口腔护理' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.0%', 'FBP_1500plus' => '16.0%'],
        '运动营养' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '康复设备' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.0%', 'FBP_1500plus' => '16.0%'],
        '成人用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '21.0%', 'FBP_1500plus' => '20.0%'],
        // 快速消费品（FMCG）
        '日化' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '个人卫生用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '食品' => ['rFBS_1500' => '11.0%', 'FBP_1500' => '10.0%', 'rFBS_1500plus' => '11.0%', 'FBP_1500plus' => '10.0%'],
        '新鲜食品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.5%', 'FBP_1500plus' => '12.5%'],
        // 儿童用品
        '儿童卫生用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '儿童餐具' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '18.0%', 'FBP_1500plus' => '17.0%'],
        '儿童电子产品、家具、配件' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.0%', 'FBP_1500plus' => '19.0%'],
        '玩具' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.5%', 'FBP_1500plus' => '16.5%'],
        '婴儿推车和汽车安全座椅' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.0%', 'FBP_1500plus' => '19.0%'],
        '儿童运动用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '20.0%', 'FBP_1500plus' => '19.0%'],
        '儿童纺织品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '19.0%', 'FBP_1500plus' => '18.0%'],
        '兴趣、创意与文具' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '16.0%', 'FBP_1500plus' => '15.0%'],
        '文具' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '16.0%', 'FBP_1500plus' => '15.0%'],
        '积木玩具套装' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '17.5%', 'FBP_1500plus' => '16.5%'],
        // 宠物用品
        '宠物卫生与护理' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        '宠物饲料与农场用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        '宠物用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        '农场' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '13.0%', 'FBP_1500plus' => '12.0%'],
        '宠物服饰、运输与遛狗用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        '宠物空间整理用品' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '15.0%', 'FBP_1500plus' => '14.0%'],
        // 其它
        '包装袋' => ['rFBS_1500' => '10.0%', 'FBP_1500' => '9.0%', 'rFBS_1500plus' => '10.0%', 'FBP_1500plus' => '9.0%'],
        '卖家礼品卡' => ['rFBS_1500' => '6.0%', 'FBP_1500' => '5.0%', 'rFBS_1500plus' => '6.0%', 'FBP_1500plus' => '5.0%'],
        '旅游' => ['rFBS_1500' => '1.6%', 'FBP_1500' => '0.6%', 'rFBS_1500plus' => '1.6%', 'FBP_1500plus' => '0.6%'],
        '爱好和创作' => ['rFBS_1500' => '12.0%', 'FBP_1500' => '11.0%', 'rFBS_1500plus' => '16.0%', 'FBP_1500plus' => '15.0%'],
        '媒体产品' => ['rFBS_1500' => '12%', 'FBP_1500' => '11%', 'rFBS_1500plus' => '13.5%', 'FBP_1500plus' => '12.5%'],
    ];
    
    if(empty($categories)){
        return false;
    }
    
    foreach (array_reverse($categories) as $item){
        if($commissionData[$item['name']]){
            return $commissionData[$item['name']];
        }
    }
}

function convertToOzonImage(string $imageUrl) {
    return $result['url']??$imageUrl;
}

function get_skuOrData($data,$type=1,$offset=0,$limit="50", $language='zh-Hans'){
    global $cookie;
    $url = "https://seller.ozon.ru/api/site/seller-analytics/what_to_sell/data/v3";
    $referer = "https://seller.ozon.ru/app/products/create";
    $array = [
        "limit"=>$limit,
        "offset"=>$offset,
        "filter"=>[
                "stock"=>"any_stock",
                "period"=>"monthly",
            ],
        "sort"=>["key"=>"sum_missed_gmv_desc"]
    ];
    
    if($type==1){
        $array['filter']['sku'] = $data;
    }elseif ($type==2) {
        $array['filter']['categories'] = $data;  #产品类目id 类型: array
    }else{
        $array['filter']['company_ids'] = $data; #店铺ID 类型: array
    }
    $res = gets_ozoncurl($url,$array, $referer, $cookie, $language);
    if(empty($res)){
        return false;
    }
    return json_decode($res,true);
}

function get_sku_query($sku, $language = 'zh-Hans') {
    global $cookie;
    $url = "https://seller.ozon.ru/api/v1/search-variant-model";
    $referer = "https://seller.ozon.ru/app/products/create";
    $array = [
        "name" => $sku ? strval($sku) : "1750362945",
        "limit" => "10"
    ];
    $res = gets_ozoncurl($url, $array, $referer, $cookie, $language);
    
    return $res ? json_decode($res, true) : false;
}

function gets_ozoncurl($url, $post, $referer, $cookie, $language) {
    
    $req = ozoncurls($url, $post, $referer, $cookie, $language); // 参数顺序调整
    
    if ($req['code'] == 307) {
        $reqs = ozoncurls($url, $post, $referer, $req['merged_cookies'], $language, $req['sc_company_id']);
        return ($reqs['code'] == 200) ? $reqs['body'] : false;
    }
    return $req['body'];
}

function parse_proxies($input) {
    $parts = explode(':', $input, 2);
    return (count($parts) < 2) 
        ? ['ip' => '', 'port' => ''] 
        : ['ip' => trim($parts[0]), 'port' => trim($parts[1])];
}


function ozoncurls($url, $post, $referer, $cookie, $language, $sc_company_id = false) {
    $localProxies = get_dailiip();
    
    if (!empty($localProxies['ip']) && !empty($localProxies['port'])) {
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_PROXY, $localProxies['ip']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $localProxies['port']);
        curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
    }
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 50);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    if ($referer) {
        curl_setopt($ch, CURLOPT_REFERER, $referer);
    }
    if ($post) {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
    }
    if(empty($sc_company_id)){
        $sc_company_id = getSubstr($cookie, 'sc_company_id=', ';');
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "accept: application/json, text/plain, */*",
        "accept-language: ".$language,
        "authority: seller.ozon.ru",
        "content-type: application/json",
        "origin: https://seller.ozon.ru",
        "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36",
        "x-o3-app-name: seller-ui",
        "x-o3-company-id: $sc_company_id",
        "x-o3-language: ".$language,
        "x-o3-page-type: products-other"
    ]);
    if ($cookie) {
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    }
    // 执行请求
    $response = curl_exec($ch);
    $err = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $originalCookies = [];
    if (!empty($cookie)) {
        $pairs = explode('; ', $cookie);
        foreach ($pairs as $pair) {
            if (strpos($pair, '=') !== false) {
                list($name, $value) = explode('=', $pair, 2);
                $originalCookies[trim($name)] = trim($value);
            }
        }
    }

    // 解析新Cookie（从响应头）
    $newCookies = [];
    $headerLines = explode("\r\n", substr($response, 0, $headerSize));
    foreach ($headerLines as $line) {
        if (stripos($line, 'Set-Cookie:') === 0) {
            $cookieStr = trim(substr($line, 11)); // 去掉"Set-Cookie:"
            // 分割键值对
            $parts = explode(';', $cookieStr);
            $cookiePair = trim($parts[0]);
            if (strpos($cookiePair, '=') !== false) {
                list($name, $value) = explode('=', $cookiePair, 2);
                $newCookies[trim($name)] = trim($value);
            }
        }
    }

    // 精准合并（重点处理 __Secure-ETC）
    $mergedCookies = $originalCookies;
    foreach ($newCookies as $key => $value) {
        // 强制更新目标Cookie
        if ($key === '__Secure-ETC') {
            $mergedCookies[$key] = $value;
            //error_log("已更新 __Secure-ETC: $value"); // 调试日志
        }
        // 其他Cookie正常合并
        else {
            $mergedCookies[$key] = $value;
        }
    }
    
    // 生成新Cookie字符串
    $mergedCookieStr = implode('; ', array_map(
        function ($k, $v) { return "$k=$v"; }, 
        array_keys($mergedCookies), 
        $mergedCookies
    ));
    curl_close($ch);//substr($response, $headerSize)
    return [
        'code' => $httpCode,
        'body' => substr($response, $headerSize),
        'error' => $err,
        'proxy' => $proxy, // 返回使用的代理信息
        'old_cookie' => $cookie,
        'new_cookies' => $newCookies,
        'merged_cookies' => $mergedCookieStr,
        'sc_company_id' => $sc_company_id
    ];
}

function getClient($row = null) {
    global $DB;
    try {
        // 开启事务
        $DB->beginTransaction();
        // 1. 尝试获取可用客户端（带行锁）
        $client = $DB->getRow("SELECT * FROM `ozon_client` 
                             WHERE `limit` >= 50 AND `status` = 0 AND `nums` = '0'
                             ORDER BY id ASC LIMIT 1 FOR UPDATE");
        if ($client) {
            // 2. 标记为使用中
            $DB->exec("UPDATE `ozon_client` SET `nums` = '1' 
                      WHERE `id` = ?", [$client['id']]);
            // 3. 如果有传入$row，更新cron表
            if ($row) {
                $DB->update('cron', ['clientids' => $client['id']], 
                          ['id' => $row['id']]);
            }
            $DB->commit();
            return $client['id'];
        }
        // 4. 如果没有可用客户端，尝试重置并获取
        $DB->exec("UPDATE `ozon_client` SET `nums` = '0' 
                  WHERE `status` = 0 AND `nums` = '1'");
        
        $client = $DB->getRow("SELECT * FROM `ozon_client` 
                             WHERE `limit` >= 50 AND `status` = 0
                             ORDER BY id ASC LIMIT 1 FOR UPDATE");
        if ($client) {
            $DB->exec("UPDATE `ozon_client` SET `nums` = '1' 
                      WHERE `id` = ?", [$client['id']]);
            if ($row) {
                $DB->update('cron', ['clientids' => $client['id']], 
                          ['id' => $row['id']]);
            }
            $DB->commit();
            return $client['id'];
        }
        $DB->commit();
        return false;
    } catch (Exception $e) {
        $DB->rollBack();
        // 可以记录日志 $e->getMessage()
        return false;
    }
}


function get_dailiip(){
    $redis = new Redis();$redis->connect('127.0.0.1', 6379);
    $redisdata = $redis->get('dailiip_000000001');
    $redis->close();
    return json_decode($redisdata,true);
}

function bdmozon($sku){
    $curl = curl_init();
    
    curl_setopt_array($curl, array(
       CURLOPT_URL => 'https://www.bdmozon.com/prod-api/plugin/data/get_data',
       CURLOPT_RETURNTRANSFER => true,
       CURLOPT_ENCODING => '',
       CURLOPT_MAXREDIRS => 10,
       CURLOPT_TIMEOUT => 0,
       CURLOPT_FOLLOWLOCATION => true,
       CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
       CURLOPT_CUSTOMREQUEST => 'POST',
       CURLOPT_POSTFIELDS =>'{"sku":"'.$sku.'"}',
       CURLOPT_HTTPHEADER => array(
          'Accept: */*',
          'Accept-Language: zh-CN,zh;q=0.9',
          'Authorization: 92LY6R7LPFM55S9BJH102MO4U5FUTT3Z',
          'Connection: keep-alive',
          'Origin: https://www.ozon.ru',
          'Referer: https://www.ozon.ru/product/zont-dlya-rybalki-zont-karpovyy-rybolovnyy-2-6m-rybolovnye-prinadlezhnosti-2019353117/?_bctx=CAQQ5PmgAQ&at=k2toygX5VhjngGRmu0jZg6ntv2A2czXKWoQtKGOBk9&hs=1',
          'Sec-Fetch-Dest: empty',
          'Sec-Fetch-Mode: cors',
          'Sec-Fetch-Site: cross-site',
          'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
          'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'sec-ch-ua-mobile: ?0',
          'sec-ch-ua-platform: "Windows"',
          'Content-Type: application/json'
       ),
    ));
    
    $response = curl_exec($curl);
    
    curl_close($curl);
    return $response;
}

function getAttributes($data){
    $targetIds = [9456 => true, 9454 => true, 9455 => true, 4497 => true];
    $results = [];
    foreach ($data['attributes'] as $attr) {
        $id = $attr['id'];
        if (isset($targetIds[$id])) { // O(1) 查找
            $results[$id] = $attr['values'][0]['value'] ?? null;
            // 如果已找到全部ID可提前终止循环
            if (count($results) === count($targetIds)) break;
        }
    }
    weightdata($data['id'],$results);
    return $results;
}

function weightdata($sku,$data){
    global $DB,$date;
    if(empty($data[9456]) or empty($data[9454]) or empty($data[9455]) or empty($data[4497])) return true;
    $baseData = [
        'sku'=>$sku,
        'height'=>$data[9456],
        'depth'=>$data[9454],
        'width'=>$data[9455],
        'weight'=>$data[4497],
        'date' =>date("Y-m-d")
    ];
    $exists = $DB->find('weight', 'sku', ['sku' => $sku]);
        
    if ($exists) {
        // 更新现有产品
        unset($baseData['sku']);
        return $DB->update('weight', $baseData, ['sku' => $sku]) !== false;
    } else {
        // 插入新产品
        return $DB->insert('weight', $baseData) !== false;
    }
}

function gmcron($id){
    global $DB;
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key,B.currency_code,B.apistatus FROM ozon_cron A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.id=:id limit 1", [':id'=>$id]);
    if($row['cron']==0 or empty($row['ClientId']) or $row['apistatus']!==1)return true;
    $date = date("Y-m-d H:i:s");
    $jsonDir = ROOT.'/assets/json/特征数据库';
    $jsonurl = $jsonDir . '/' . $row['sku'] . '.json';
    $jsonurl2 = $jsonDir . '/' . $row['sku'] . '_Dataattributes.json';
    $jsonurl3 = $jsonDir . '/' . $row['sku'] . '_2.json';
    if (!file_exists($jsonDir)) {
        mkdir($jsonDir, 0755, true);
    }
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    $redis->setOption(Redis::OPT_READ_TIMEOUT, 5); // 设置读取超时
    register_shutdown_function(function() use ($redis) {
        if ($redis->isConnected()) {
            $redis->close();
        }
    });
    switch ($row['status']) {
        case '准备中':
            error_log("准备中".$row['id'].'|||'.$date); // 调试日志
            $srow = file_get_contents($jsonurl);
            if($srow and isFileModifiedWithinDay($jsonurl)){            #判断该商品是否有缓存，缓存在一天内的直接上架。
                $save = ['status' => '数据处理中','msg'=>''];
                $jsondata = file_get_contents($jsonurl);
                $json = json_decode($jsondata,true);
                $save['primary_image'] = $json['primary_image'];
                $save['height'] = $json['height'];
                $save['depth'] = $json['depth'];
                $save['width'] = $json['width'];
                $save['weight'] = $json['weight'];
                $save['time'] = time()+1;
                if($json['name']){
                    $save['title'] = $json['name'];
                }
            }else if($redis->get($row['sku'].'_ozonsku') and $row['nums']==0){
                $jsondata = file_get_contents($jsonurl3);
                if($jsondata){
                    $save = ['status' => '数据处理中','msg'=>''];
                    $json = json_decode($jsondata,true);
                    $save['primary_image'] = $json['primary_image'];
                    $save['height'] = $json['height'];
                    $save['depth'] = $json['depth'];
                    $save['width'] = $json['width'];
                    $save['weight'] = $json['weight'];
                    $save['time'] = time()+1;
                    if($json['name']){
                        $save['title'] = $json['name'];
                    }
                }else{
                    $jsondata = json_decode($redis->get($row['sku'].'_ozonsku'),true);
                    if($jsondata['items'][0]['variantId']){
                        $data = apigetattributes($row['sku'],$jsondata['items'][0]['variantId']);
                        if($data){
                            $jsons['name'] = $data['name'];
                            $jsons['description_category_id'] = $data['description_category_id'];
                            $categoryId = $data['categories'][2]['id']; // 示例：蜂蜜制作分类ID
                            $pp = null;
                            foreach ($data['attributes'] as $itmes){
                                if(intval($itmes['key'])==4194){      #产品主图
                                    $jsons['primary_image'] = $itmes['value'];
                                    continue;
                                }elseif(intval($itmes['key'])==4195){ #图片数组
                                    $jsons['images'] = $itmes['collection'];
                                    continue;
                                }elseif(intval($itmes['key'])==9456){ #高 (毫米)
                                    $jsons['height'] = intval($itmes['value']);
                                    continue;
                                }elseif(intval($itmes['key'])==9455){ #宽 (毫米)
                                    $jsons['width'] = intval($itmes['value']);
                                    continue;
                                }elseif(intval($itmes['key'])==9454){ #高 (毫米)
                                    $jsons['depth'] = intval($itmes['value']);
                                    continue;
                                }elseif(intval($itmes['key'])==4497){ #商品重量 (克)
                                    $jsons['weight'] = intval($itmes['value']);
                                    continue;
                                }elseif(intval($itmes['key'])==100001){
                                    foreach ($itmes['complex'] as $complex){
                                        if($complex['id']==21837){
                                            $values = [];
                                            $values[]=['dictionary_value_id'=>0,'value'=>$complex['value']];
                                            $complex_attributes[]=['id'=>21837,'complex_id'=>100001,'values'=>$values];
                                        }elseif($complex['id']==21841){
                                            $values = [];
                                            $values[]=['dictionary_value_id'=>0,'value'=>$complex['value']];
                                            $complex_attributes[]=['id'=>21841,'complex_id'=>100001,'values'=>$values];
                                        }
                                    }
                                    $jsons['complex_attributes'] = $complex_attributes;
                                    continue;
                                }elseif(intval($itmes['key'])==8229){
                                    $typeName = $itmes['value'];
                                    $jsons['typeName'] = $typeName;
                                }elseif(intval($itmes['key'])==85){ #商品重量 (克)
                                    $pp = true;
                                }
                                $values = [];
                                if (count($itmes['collection'])>0) {
                                    foreach ($itmes['collection'] as $x) {
                                        $attribute_id_value = attribute_id_names($itmes['key'],$x);
                                        if($attribute_id_value){
                                            $values[] = $attribute_id_value;
                                        }else{
                                            $values[] = ['dictionary_value_id' => 0, 'value' => $x];
                                        }
                                    }
                                }else {
                                    if(empty($itmes['value']))continue;
                                    $attribute_id_value = attribute_id_names($itmes['key'],$itmes['value']);
                                    if($attribute_id_value){
                                        $values[] = $attribute_id_value;
                                    }else{
                                        $values[] = ['dictionary_value_id'=>0,'value'=>$itmes['value']];
                                    }
                                }
                                $attributes[] = ['id' => (int) $itmes['key'], 'complex_id' => 0, 'values' => $values];
                            }
                            $adata = $redis->get($row['sku'].'_ozonlhaw');
                            
                            $typeId = findTypeIdByCategoryAndName($categoryId, $typeName);
                            if($typeId){
                                $jsons['type_id'] = $typeId;
                            }else{
                                $data = bysku($sku);
                                if($data['description_type_id']){
                                    $jsons['type_id'] = $data['description_type_id'];
                                }
                            }
                            
                            if(empty($pp)){
                                $attributes[] = ['id' => 85, 'complex_id' => 0, 'values' => ['dictionary_value_id'=>126745801,'value'=>'Нет бренда']];
                            }
                            $jsons['attributes']=$attributes;
                            $jsons['pdf_list'] = [];
                            $jsons['color_image'] = "";
                            $jsons['sku']=$row['sku'];
                            file_put_contents($jsonurl3, json_encode($jsons, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                            $save = ['status' => '数据处理中','msg'=>''];
                            if($jsons['primary_image']){
                                $save['primary_image'] = $jsons['primary_image'];
                                $save['title'] = $jsons['name'];
                                $save['height'] = $jsons['height'];
                                $save['depth'] = $jsons['depth'];
                                $save['width'] = $jsons['width'];
                                $save['weight'] = $jsons['weight'];
                            }
                        }
                    }else{
                        $save = ['status' => 'no','msg'=>'采集特征数据失败000001'];
                    }
                }
            }else{
                if($row['task_id2']>0){
                    $save = ['status' => '采集数据中','msg'=>''];
                }else{
                    $clients = $DB->getRow("SELECT * FROM ozon_client WHERE id=:id limit 1", [':id'=>$row['clientids']]);
                    if($clients){
                        $client = new \lib\OzonApiClient($clients['ClientId'],$clients['key']);
                        $data = $client->OzonApiskufz($row);
                        
                        if($data['result']['task_id']>0){
                            $save = ['status' => '采集数据中','time' => time() + rand(60, 100),'msg'=>'','nums'=>0];
                            $save['task_id2'] = $data['result']['task_id'];
                            if(empty($row['old_price'])){
                                $current_price = $row['price'];
                                $discount_percentage = rand(30, 60) / 100; // 0.15 到 0.40
                                $original_price = $current_price * (1 + $discount_percentage);
                                $save['old_price'] = round($original_price,2);    #设置划线的原价
                            }
                            if($row['storeid']!=4){
                                /*
                                $res = get_curl('http://127.0.0.1:3000/api/sku-query',json_encode(['sku'=>strval($row['sku']),'limit'=>10]),0,0,0,0,0,['Content-Type: application/json']);
                                $json = json_decode($res, true);
                                if (empty($json['msg']) && !empty($json['items'])) {
                                    $variantIdData = $json['items'][0];
                                    file_put_contents($jsonurl2, json_encode($variantIdData['attributes'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                                }
                                
                                $res = apigetattributes(strval($row['sku']));
                                if (empty($res['msg']) && $res) {
                                    if($res['name']){
                                        $save['name'] = $res['name'];
                                    }
                                    file_put_contents($jsonurl2, json_encode($res, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                                }*/
                            }
                        }else if($data['code']==7){
                            $DB->update('client', ['status'=>1], ['id' => $row['clientids']]);
                            getclient($row);
                            $save = ['status' => '准备中','msg'=>'服务器错误，为你重新分配服务器...'];
                        }else{
                            $save = ['status' => 'no','msg'=>'数据获取失败，服务器ID:'.$row['clientids']];
                        }
                         
                        if(empty($row['title'])){
                            $res = apigetattributes(strval($row['sku']));
                            if (empty($res['msg']) && $res) {
                                if($res['name']){
                                    $save['title'] = $res['name'];
                                }
                                file_put_contents($jsonurl2, json_encode($res, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                            }
                            /*
                            $res = get_curl('http://43.139.204.216:5000/api/product?sku='.$row['sku']);
                            $json = json_decode($res,true);
                            $save['title'] = json_decode($json['widgetStates']['webProductHeading-3385933-default-1'],true)['title'];
                            */
                        }
                        
                    }else{
                        $save = ['status' => 'no','msg'=>'本次任务的服务器已下架，请联系客服处理。'];
                    }
                }
            }
        break;
        case '采集数据中':                          ###获取商品特征描述
            error_log("采集数据中".$row['id'].'|||'.$date); // 调试日志
            $clients = $DB->getRow("SELECT * FROM ozon_client WHERE id=:id limit 1", [':id'=>$row['clientids']]);
            $client = new \lib\OzonApiClient($clients['ClientId'],$clients['key']);
            $data = $client->characteristics($row);
            if ($data) {
                file_put_contents($jsonurl, json_encode($data['result'][0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                $save = ['status' => '数据采集成功','msg'=>''];
                $save['primary_image']=$data['result'][0]['primary_image'];
            } else {
                if($row['nums']==2){
                    $save = ['status' => 'no','msg' => '数据查询失败'];
                }else{
                    $jsondata = file_get_contents($jsonurl);
                    if($jsondata){
                        $json = json_decode($jsondata,true);
                        $save = ['status' => '数据采集成功','msg'=>''];
                        $save['primary_image']=$json['primary_image'];
                    }else{
                        $save = ['status' => '采集数据中','msg' => '数据查询失败','nums'=>$row['nums']+1];
                    }
                }
            }
            
        break;
        case '数据采集成功':                    
            error_log("数据采集成功".$row['id'].'|||'.$date); // 调试日志
            $save['status'] = "数据处理中";
            $jsonreturn = json_decode(file_get_contents($jsonurl), true, JSON_THROW_ON_ERROR);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $save = ['status' => 'no', 'msg' => '数据采集JSON解析错误1: ' . json_last_error_msg()];
            }
            $json = json_decode(file_get_contents($jsonurl2), true, JSON_THROW_ON_ERROR);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $save = ['status' => '数据处理中', 'msg' => '数据采集JSON解析错误2: ' . json_last_error_msg()];
                break;
            }
            if(isset($json['items'])){
                foreach ($json as $item){
                    switch ($item['key']) {
                        case 9456: // 高 (毫米)
                            $save['height'] = (int) $item['value'];
                            break;
                        case 9454: // 长 (毫米)
                            $save['depth'] = (int) $item['value'];
                            break;
                        case 9455: // 宽 (毫米)
                            $save['width'] = (int) $item['value'];
                            break;
                        case 4497: // 商品重量 (克)
                            $save['weight'] = (float) $item['value'];
                            break;
                        case '4194': //商品主图
                            if(empty($jsonreturn['primary_image'])){
                                $jsonreturn['primary_image'] = $item['value'];
                            }
                            break;
                        
                    }
                    $exists = hasAttributeId($jsonreturn, $item['key']);
                    if(empty($exists)){
                        $values = [];
                        if($item['key']!=4194 and $item['key']!=4195 and $item['key']!=22967 and $item['key']!=10098){ #屏蔽添加，避免重复例如：4194主图
                            if ($item['collection']) {
                                foreach ($item['collection'] as $x) {
                                    $values[] = ['dictionary_value_id' => 0, 'value' => $x];
                                }
                            } else {
                                $values[] = ['dictionary_value_id' => 0, 'value' => $item['value']];
                            }
                            $jsonreturn['attributes'][] = ['id' => (int) $item['key'], 'complex_id' => 0, 'values' => $values];
                        }
                    }
                }
                if(empty($row['primary_image'])){
                    $save['primary_image'] = $jsonreturn['primary_image'];
                }
                if(empty($row['title'])){
                    $save['title'] = $json['name'];
                }
                file_put_contents($jsonurl, json_encode($jsonreturn, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            }
            
        break;
        case '数据处理中':                     #创建商品
            error_log("数据处理中".$row['id'].'|||'.$date); // 调试日志
            $jsondata = file_get_contents($jsonurl3);
            if($jsondata){
                $row['return'] = file_get_contents($jsonurl3);
                $jsonreturn = json_decode($row['return'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $save = ['msg' => '3JSON解析错误: ' . json_last_error_msg(), 'status' => 'no'];
                    break;
                }
            }else{
                $jsondata = file_get_contents($jsonurl);
                if($jsondata){
                    $row['return'] = file_get_contents($jsonurl);
                    $jsonreturn = json_decode($row['return'], true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $save = ['msg' => '4JSON解析错误: ' . json_last_error_msg(), 'status' => 'no'];
                        break;
                    }
                }
            }
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $data = $client->productimport($row);
            if ($data){
                $save = [
                    'task_id' => $data[0],
                    'offer_id' => $data[1],
                    'time' => time() + rand(300, 600),
                    'status' => '数据上传中',
                    'msg'=>''
                ];
            }else{
                $save = ['msg'=>"不知名错误，上传不成功请联系客服反馈",'status' => 'no']; #腾讯交互翻译
            }
        break;
        case '数据上传中':                               
            error_log("数据上传中".$row['id'].'|||'.$date); // 调试日志
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            if($row['task_id']){
                $data = $client->productimportinfo($row);    #查询商品添加或更新状态
                if($data){
                    if($data['status']=='imported' and empty($data['errors'][0]['code'])){
                        if($row['warehouse_id']>0 and $row['stock']>0){
                            $save['status']="等待添加库存";
                        }else{
                            $save['status']="ok";
                        }
                    }else if($data['status']=='pending'){  //商品等待排队处理排队审核
                        $save['status']="数据上传中";
                    }else if($data['errors'][0]['code']=='double_without_merger_offer'){ #无法与其他商品合并
                        if($row['warehouse_id']>0 and $row['stock']>0){
                            $save['status']="等待添加库存";
                        }else{
                            $save['status']="ok";
                        }
                    }else if($data['errors'][0]['code']=='warning_attribute_values_out_of_range'){ #无法与其他商品合并
                        if($row['warehouse_id']>0 and $row['stock']>0){
                            $save['status']="等待添加库存";
                        }else{
                            $save['status']="ok";
                        }
                    }else if($data['errors'][0]['code']=='BR_hashtag_validation'){ #每一标签必须分开的一个空间，并开始与一个#标志。 检查有没有空间内的标签-允许他们之间只有标签
                        $save['status'] = 'no';
                        $save['msg'] = Transmart($data['errors'][0]['message']);
                    }else if($data['errors'][0]['code']=='DAILY_CREATE_LIMIT_EXCEEDED'){
                        $save['status'] = 'no';
                        $save['msg'] = '已达到官方每日限制';
                    }else if($data['errors'][0]['code']=='SPU_ALREADY_EXISTS_IN_ANOTHER_ACCOUNT'){
                        $save['status'] = 'no';
                        $save['msg'] = '同样的商品在以下卡片和账户中重复出现';
                    }else if($data['errors'][0]['code']=='INTERNAL'){#技术错误。请稍后尝试完成请求，或联系"内容/使用产品卡"→"创建和编辑产品"类别中的支持服务
                        $save['status'] = 'no';
                        $save['msg'] = Transmart($data['errors'][0]['message']);
                    }else if($data['errors'][0]['code']=='ML_INCORRECT_VOLUME_WEIGHT'){
                        $save['status'] = 'no';
                        $save['msg'] = Transmart($data['errors'][0]['message']);
                    }else if($data['errors'][0]['code']=='DESCRIPTION_DECLINE'){
                        $save['status'] = 'no';
                        $save['msg'] = Transmart($data['errors'][0]['message']);
                    }else if($data['errors'][0]['code']=='FB_KAPKAN'){
                        $save['status'] = 'no';
                        $save['msg'] = Transmart($data['errors'][0]['message']);
                    }else{
                        file_put_contents('log.txt', "数据上传中\n".json_encode($data) . "\n\n", FILE_APPEND);
                        $save = ['msg'=>Transmart($data['errors'][0]['message']),'status' => 'no']; #腾讯交互翻译
                    }
                    if($row['clientids']>0 and $row['task_id2']>0){
                        $clients = $DB->getRow("SELECT * FROM ozon_client WHERE id=:id limit 1", [':id'=>$row['clientids']]);
                        $client = new \lib\OzonApiClient($clients['ClientId'],$clients['key']);
                        $data = $client->productimportinfo(['task_id'=>$row['task_id2']]);
                        if($data['product_id']){
                            $batchArray[] = $data['product_id'];
                            $client->productarchive($batchArray);
                        }
                        
                    }
                }else{
                    $save = ['msg'=>'服务器内部出错','status' => 'no']; #腾讯交互翻译
                }
            }else{
                $save = ['msg'=>'不充分','status' => 'no']; #腾讯交互翻译
            }
            
        break;
        case '等待添加库存':                               
            error_log("等待添加库存".$row['id'].'|||'.$date); // 调试日志
            $data = productsstocksss($row);
            if($data==true){
                $save['status'] = 'ok';
            }else
            if($data['result'][0]['updated']==true){
                $save['status'] = 'ok';
            }else if($data['result'][0]['errors'][0]['code']=='NOT_PASS_MODERATION'){
                $save['status'] = 'no';
                $save['msg'] = '产品未创建成功';
            }else{
                file_put_contents('log.txt', "等待添加库存\n".json_encode($data) . "\n\n", FILE_APPEND);
                $save = ['msg'=>Transmart($data['result'][0]['errors'][0]['message']),'status' => 'ok']; #腾讯交互翻译
            }
            
        break;
        case '重新上架':
            error_log("重新上架".$row['id'].'|||'.$date); // 调试日志
            if($row['sku']){
                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                if($row['product_id']){
                    $client->productsstocks($row,'product_id',true);
                    $client->productarchive([strval($row['product_id'])]);
                }
                
                $data = $client->characteristics2($row);
                if ($data) {
                    file_put_contents($jsonurl, json_encode($data['result'][0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                    
                    $save = ['status' => '重新上传中','msg'=>''];
                    $save['primary_image']=$data['result'][0]['primary_image'];
                    $save['offer_id'] = $client->generate_offer_id();
                } else {
                    $save = ['status' => '重新上架','msg' => '数据查询失败2'];
                }
            }else{
                $save = ['msg' => 'SKU空' . json_last_error_msg(), 'status' => 'no'];
            }
        break;
        case '重新上传中':
            error_log("重新上传中".$row['id'].'|||'.$date); // 调试日志
            $jsondata = file_get_contents($jsonurl);
            if($jsondata){
                $row['return'] = file_get_contents($jsonurl);
                $jsonreturn = json_decode($row['return'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $save = ['msg' => '3JSON解析错误: ' . json_last_error_msg(), 'status' => 'no'];
                    break;
                }
            }
            
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $data = $client->productimport($row);
            if ($data){
                $save = [
                    'task_id' => $data[0],
                    'offer_id' => $data[1],
                    'time' => time() + rand(300, 600),
                    'status' => '等待添加库存',
                    'msg'=>''
                ];
            }else{
                $save = ['msg'=>"不知名错误，上传不成功请联系客服反馈",'status' => 'no']; #腾讯交互翻译
            }
        break;
        case '更新图片特征':
            error_log("更新图片特征".$row['id'].'|||'.$date); // 调试日志
            $jsondata = file_get_contents($jsonurl);
            if($jsondata){
                $row['return'] = file_get_contents($jsonurl);
                $jsonreturn = json_decode($row['return'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $save = ['msg' => '4JSON解析错误: ' . json_last_error_msg(), 'status' => 'no'];
                    break;
                }
            }
            
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $data = $client->attributesimagesupdate($row);
            if($data){
                $save = [
                    'status' => 'ok',
                    'msg'=>''
                ];
            }else{
                $save['status'] = 'no';
            }
        break;
    }
    if ($save) {
        if (!$save['time'])
            $save['time'] = time() + rand(5, 15);
            $save['cron'] = 0;
        $DB->update('cron', $save, ['id' => $row['id']]);
    }
    $redis->close();
    echo "\n".'任务执行完毕,ID: '.$id; 
}

#获取物流价格
function findShippingMethod(array $criteria): ?array {
    $shippingMethods = json_decode(file_get_contents(ROOT."assets/json/物流数据.json"),true);
    foreach ($shippingMethods as $method) {
        $match = true;
        foreach ($criteria as $key => $value) {
            if (!isset($method[$key]) || $method[$key] !== $value) {
                $match = false;
                break;
            }
        }
        if ($match) {
            return $method;
        }
    }
    return null;
}

#计算运费
function calculateShippingCost($option, $weight) {
    $weight*=1000;
    if (!isset($option['basePrice']) || !isset($option['perGramPrice'])) {
        return null;
    }
    return round(($option['basePrice'] + ($weight * $option['perGramPrice'])),2);
}

#匹配订单物流方式
function ozonmethodtype(string $value): ?string {
    // 按优先级从高到低排序（更具体的在前）
    $patterns = [
        'China Post ePacket EconomyTrack Kazakhstan',
        'China Post ePacket Kazakhstan',
        'China Post ePacket Economy Track',
        'ozon ePacket Super Express',
        'ozon ePacket EconomyTrack',
        'ePacket Economy BelarusOzon',
        'ePacket BelarusOzon',
        'Premium Big',
        'Premium Small',
        'Extra Small',
        'ARM Armenia',
        'ARM Azerbaijan',
        '格鲁吉亚',
        '乌兹别克斯坦',
        'Budget',
        'Small',
        'Big',
        'ozon ePacket'
    ];
    
    foreach ($patterns as $pattern) {
        if (strpos($value, $pattern) !== false) {
            return $pattern;
        }
    }
    return null; // 明确返回null表示未匹配
}

function productsync($id) {
    global $DB;
    $importer = new \lib\JsonImporter($DB);
    $row = $DB->find('store', '*', ['id' => $id]);
    $redis = new Redis();$redis->connect('127.0.0.1', 6379);
    $redis->set('productsync_uid'.$row['uid'],1,86400);
    if($row){
        //$DB->exec("DELETE FROM ozon_products WHERE storeid='{$row['id']}'");
        $lastId = null; // 每个店铺的分页标识独立
        $totalImported = 0;
        do {
            // 获取产品列表
            $url = 'https://api-seller.ozon.ru/v3/product/list';
            $array = [
                'filter'=>['visibility'=>'ALL'],
                'limit'=>1000
            ];
            if ($lastId) {
                $array['last_id'] = $lastId;
            }
            $res = ozonapicurl(['ClientId'=>$row['ClientId'],'key'=>$row['key']],$url,'POST',$array);
            $response = json_decode($res,true);

            if (empty($response['result']['items'])) {
                break; // 无数据时终止分页
            }
            
            // 提取当前页产品ID
            $productIds = [];
            foreach ($response['result']['items'] as $item) {
                $productIds[] = (string)$item['product_id'];
            }
            // 获取产品详情并导入
            $url = 'https://api-seller.ozon.ru/v3/product/info/list';
            $items = ['product_id'=>$productIds];
            $res = ozonapicurl(['ClientId'=>$row['ClientId'],'key'=>$row['key']],$url,'POST',$items);
            $productDetails = json_decode($res,true);
            
            $importResult = $importer->importProducts($productDetails, false, $row);
            if ($importResult) {
                $totalImported += count($response['result']['items']);
            }
            // 更新分页标识
            $lastId = $response['result']['last_id'] ?? null;
        } while ($lastId !== null); // 存在分页标识时继续循环
    }
    $redis->set('productsync_uid'.$row['uid'],0,60);
    $redis->close();
    return ['status' => 'success', 'message' => '同步完成'];
}

function productmodifystocks($id,$warehouse_id) {
    global $DB;
    $importer = new \lib\JsonImporter($DB);
    $row = $DB->find('store', '*', ['id' => $id]);
    if($row){
        $DB->exec("DELETE FROM ozon_products WHERE storeid='{$row['id']}'");
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $lastId = null; // 每个店铺的分页标识独立
        $totalImported = 0;
        do {
            $params = [
                'visibility' => 'ARCHIVED',
                'limit' => 100
            ];
            if ($lastId) {
                $params['last_id'] = $lastId;
            }
            // 获取产品列表
            $response = $client->productlist($params);

            if (empty($response['result']['items'])) {
                break; // 无数据时终止分页
            }
            
            // 提取当前页产品ID
            $productIds = [];
            foreach ($response['result']['items'] as $item) {
                $productIds[] = ['product_id'=>$item['product_id'],'stock'=>0,'warehouse_id'=>$warehouse_id];
            }
            // 获取产品详情并导入
            $productDetails = $client->productsstocks($productIds,'product_id',true,true);
            // 更新分页标识
            $lastId = $response['result']['last_id'] ?? null;
        } while ($lastId !== null); // 存在分页标识时继续循环
    }
    return ['status' => 'success', 'message' => '更新库存完成'];
}

function urlToBase64($imageUrl) {
    // 获取图片内容
    $imageData = file_get_contents($imageUrl);
    if ($imageData === false) {
        return false;
    }

    // 获取图片MIME类型（通过URL扩展名或HTTP头）
    $mimeType = null;
    
    // 方法1：通过URL扩展名猜测（可能不准确）
    $extension = strtolower(pathinfo($imageUrl, PATHINFO_EXTENSION));
    $knownExtensions = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'svg' => 'image/svg+xml',
    ];
    if (isset($knownExtensions[$extension])) {
        $mimeType = $knownExtensions[$extension];
    }

    // 方法2：尝试获取HTTP头（更准确，但需要allow_url_fopen=On）
    if (function_exists('get_headers')) {
        $headers = get_headers($imageUrl, true);
        if (isset($headers['Content-Type'])) {
            $mimeType = is_array($headers['Content-Type']) ? end($headers['Content-Type']) : $headers['Content-Type'];
        }
    }

    // 如果没有检测到MIME类型，默认使用JPEG
    if (!$mimeType) {
        $mimeType = 'image/jpeg';
    }

    // 编码为Base64并返回数据URI
    return 'data:' . $mimeType . ';base64,' . base64_encode($imageData);
}

function ozonapicurl($row, $url, $method, $items=false){
    $proxys = get_dailisk5ip();

    $ch = curl_init();
    if($proxys){
        curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5);
        curl_setopt($ch, CURLOPT_PROXY, $proxys['proxy_server']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $proxys['proxy_port']);
        curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxys['proxy_user'].":".$proxys['proxy_pwd']);
    }
    $headers = [
        'Client-Id: ' . $row['ClientId'],
        'Api-Key: ' . $row['key'],
        'Content-Type: application/json'
    ];
    curl_setopt_array($ch, [
        CURLOPT_CUSTOMREQUEST  => $method,
        CURLOPT_URL            => $url,
        CURLOPT_HTTPHEADER     => $headers,
        CURLOPT_RETURNTRANSFER => true,    // 返回响应内容
        CURLOPT_HEADER         => false,   // 不包含响应头
        CURLOPT_TIMEOUT        => 120,       // 超时时间(秒)
        CURLOPT_CONNECTTIMEOUT => 30
    ]);
    if($items){
        curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($items,JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
    // 执行请求
    $response = curl_exec($ch);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    if ($error) {
        file_put_contents('ozonapicurl.txt', "\n\n".json_encode(['error' => "cURL Error: $error"]). "\n\n". $response. "\n\n", FILE_APPEND);
        return json_encode(['error' => "cURL Error: $error"]);
    }
    return $response;
}

/**
 * 插入单个商品到数据库
 */
function insertProduct($product, $store, $config, $DB, $uid) {
    try {
        $sku = intval($product['sku']);
        $price = floatval($product['price']);
        $storeId = intval($store['storeId']);
        $warehouse = intval($store['warehouse']);
        
        // 应用价格策略
        $finalPrice = $price;
        $pricing = $config['pricing'] ?? ['mode' => 'none'];
        
        if ($pricing['mode'] !== 'none' && !empty($pricing['value']) && $pricing['value'] > 0) {
            switch ($pricing['mode']) {
                case '加模式':
                    $finalPrice += $pricing['value'];
                    break;
                case '减模式':
                    $finalPrice -= $pricing['value'];
                    break;
                case '乘模式':
                    $finalPrice *= $pricing['value'];
                    break;
                case '除模式':
                    $finalPrice /= $pricing['value'];
                    break;
            }
        }
            // 应用随机价格区间（每个店铺独立计算）
        $priceRange = $config['priceRange'] ?? [];
        if (!empty($priceRange) && (isset($priceRange['min']) || isset($priceRange['max']))) {
            $randomAdjustment = calculateRandomPriceAdjustment($priceRange, $finalPrice, $sku, $storeId);
            $finalPrice += $randomAdjustment;
            
            // 确保价格不会低于0.01
            $finalPrice = max($finalPrice, 0.01);
            
            // 记录调试日志
            error_log("SKU: {$sku}, 店铺: {$storeId}, 原价: {$price}, 策略后: " . ($finalPrice - $randomAdjustment) . ", 随机调整: {$randomAdjustment}, 最终价格: {$finalPrice}");
        }
        
        // 保留两位小数
        $finalPrice = round($finalPrice, 2);
        $stock = intval($config['stock'] ?? 0);
        $client = new \lib\OzonApiClient();
            $offerIdSettings = $config['offerIdSettings'] ?? [];
        $prefix = $offerIdSettings['prefix'] ?? '';
        $method = $offerIdSettings['method'] ?? 'random';

        if ($method === 'sku_based') {
            $randomPart = $client->generate_offer_id(3, false);
            $offer_id = $prefix . $sku . $randomPart;
        } else { // 'random' or default
            $offer_id = $prefix . $client->generate_offer_id(10 - strlen($prefix), false);
        }
        $offer_id2 = $client->generate_offer_id(15);
        $clientids = getclient();
        $date = date("Y-m-d");
        $time = time() + rand(1, 10);
        
        // 执行插入
        $sql = "INSERT INTO `ozon_cron` (`uid`, `sku`, `price`, `offer_id`, `offer_id2`, `storeid`, `clientids`, `type`, `warehouse_id`, `status`, `stock`, `time`, `addtime`, `date`)
            VALUES 
            ('{$uid}', '{$sku}', '{$finalPrice}', '{$offer_id}', '{$offer_id2}',
            '{$storeId}','{$clientids}', 'Sellwith', '{$warehouse}',
            '准备中', '{$stock}','{$time}', NOW(), '{$date}')";
        
        return $DB->exec($sql);
        
    } catch (Exception $e) {
        error_log("insertProduct错误: " . $e->getMessage());
        return false;
    }
}
/**
 * 计算随机价格调整值
 * @param array $priceRange 价格区间配置
 * @param float $currentPrice 当前价格
 * @param int $sku SKU编号
 * @param int $storeId 店铺ID
 * @return float 随机调整值
 */
function calculateRandomPriceAdjustment($priceRange, $currentPrice, $sku, $storeId) {
    $min = isset($priceRange['min']) ? floatval($priceRange['min']) : null;
    $max = isset($priceRange['max']) ? floatval($priceRange['max']) : null;
    
    // 使用SKU和店铺ID作为随机种子的一部分，确保同一SKU在不同店铺有不同的价格
    // 但在同一次导入中，同一SKU在同一店铺的价格是确定的
    $seed = crc32($sku . '_' . $storeId . '_' . date('Y-m-d-H'));
    mt_srand($seed);
    
    $randomAdjustment = 0;
    
    if ($min !== null && $max !== null) {
        // 两个值都设置了，在区间内随机
        $randomAdjustment = mt_rand() / mt_getrandmax() * ($max - $min) + $min;
    } elseif ($min !== null) {
        // 只设置了最小值，在最小值到最小值+原价20%之间随机（最少+5）
        $defaultRange = max(abs($currentPrice * 0.2), 5);
        $randomAdjustment = mt_rand() / mt_getrandmax() * $defaultRange + $min;
    } elseif ($max !== null) {
        // 只设置了最大值，在负最大值到最大值之间随机
        $randomAdjustment = mt_rand() / mt_getrandmax() * (abs($max) * 2) - abs($max);
    }
    
    // 重置随机种子以避免影响其他随机数生成
    mt_srand();
    
    return round($randomAdjustment, 2);
}