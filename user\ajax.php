<?php
include("../includes/common.php");
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
@header('Content-Type: application/json; charset=UTF-8');
if($act!='login' and $act!='reg'){
    if($islogin2==1){}else exit('{"code":-3,"msg":"No Login"}');
}

use setasign\Fpdi\Tcpdf\Fpdi; // Assuming composer autoload or manual include pdf引入
switch($act){
case 'config':
    echo file_get_contents('./config/pear.config.json');exit;
break;
case 'menu':
    // 检查用户状态，根据status字段过滤菜单
   // 检查是否启用菜单权限控制
    if ($permission_config['enable_menu_control'] && $userrow['status'] != 1) {
        // 用户被禁用，只显示基础菜单
        $basicMenu = [
            [
                "id" => 1,
                "title" => "首页(开发中)",
                "icon" => "layui-icon layui-icon-console",
                "type" => 1,
                "href" => "./view/analysis/index.html"
            ],
            [
                "id" => 2,
                "title" => "工作空间",
                "icon" => "layui-icon layui-icon-console",
                "type" => 1,
                "href" => "main.php"
            ]
        ];
        echo json_encode($basicMenu);
    } else {
         // 正常用户或菜单控制被禁用，显示完整菜单
        echo file_get_contents('./config/menu.json');
    }
    exit;
break;
case 'category':
    echo file_get_contents('./config/中文类目.json');exit;
break;

// 获取用户权限信息
case 'get_user_permissions':
    $uid = $userrow['uid'] ?? 0;
    if ($uid === 0) {
        exit(json_encode(['code' => -3, 'msg' => '未登录']));
    }
    
    // 使用通用权限函数
    $permissions = getUserPermissions($uid);
    if (!$permissions) {
        exit(json_encode(['code' => -1, 'msg' => '用户信息不存在']));
    }
    
    exit(json_encode([
        'code' => 0,
        'data' => $permissions
    ]));
break;
case 'login':
	$username=trim($_POST['username']);
	$password=trim($_POST['password']);
	if(empty($username) || empty($password))exit('{"code":-1,"msg":"请确保各项不能为空"}');
	if(!$_REQUEST['csrf_token'] || $_REQUEST['csrf_token']!=$_SESSION['csrf_token'])exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
	$userrow=$DB->getRow("SELECT * FROM ozon_user WHERE username=:username limit 1", [':username'=>$username]);
	if($userrow && password_verify($password, $userrow['password'])) {
	    $auth = new \lib\AuthSystem();
	    $token = $auth->generateToken($userrow['uid'], $username);
		ob_clean();
		$host = $_SERVER['HTTP_HOST'];
        $domainParts = explode('.', $host);
        
        // 处理本地开发环境（如localhost）
        if (count($domainParts) === 1 || in_array(end($domainParts), ['localhost', 'test', 'local'])) {
            $mainDomain = $host;
        } 
        // 处理正式域名（支持二级国家域名如.co.uk）
        else {
            $mainDomain = implode('.', array_slice($domainParts, -2, 2));
        }
        
        // 设置全局Cookie参数（PHP 7.3+版本推荐写法）
        $cookieOptions = [
            'expires' => time() + 691200,  // 8天后过期
            'path' => '/',                 // 全路径有效
            'domain' => '.100b.cn',        // 支持所有子域名
            'secure' => true,              // 仅HTTPS传输
            'httponly' => true,            // 禁止JavaScript访问
            'samesite' => 'None'           // 允许跨站/跨子域请求
        ];
        
        setcookie('Authorization', $token, $cookieOptions);
		// 检查用户是否绑定手机号
        if(empty($userrow['phone'])) {
            $result=array("code"=>2,"msg"=>"请绑定手机号");
        } else {
            $result=array("code"=>1,"msg"=>"登录成功！正在跳转到用户中心");
        }
	
		// 只有登录成功后才清除csrf_token
		if($result['code'] == 1) {
		    unset($_SESSION['csrf_token']);
		}
	}else {
		$result=array("code"=>-1,"msg"=>"用户名或密码不正确！");
	}
	exit(json_encode($result));
break;
case 'reg':
    $username=htmlspecialchars(strip_tags(trim($_POST['username'])));
    $password=trim($_POST['password']);
    $email=htmlspecialchars(strip_tags(trim($_POST['email'])));
	$phone=htmlspecialchars(strip_tags(trim($_POST['phone'])));
	$code=trim($_POST['sms_code']);
	if(empty($phone) && empty($email) || empty($code) || empty($password)){
		exit('{"code":-1,"msg":"请确保各项不能为空"}');
	}
    if(!$_POST['csrf_token'] || $_POST['csrf_token']!=$_SESSION['csrf_token'])exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
    $row=$DB->getRow("select * from ozon_user where phone=:phone limit 1", [':phone'=>$phone]);
	if($row){
		exit('{"code":-1,"msg":"该手机号已经注册过商户，如需找回信息，请返回登录页面点击找回商户"}');
	}
	$row=$DB->getRow("select * from ozon_user where email=:email limit 1", [':email'=>$email]);
	if($row){
		exit('{"code":-1,"msg":"该邮箱已经注册过商户，如需找回商户信息，请返回登录页面点击找回商户"}');
	}
	$result = \lib\VerifyCode::verify_code('reg', 1, $phone, $code);
	if($result !== true){
		exit(json_encode(['code'=>-1, 'msg'=>$result]));
	}
    if (strlen($password) < 6) {
		exit('{"code":-1,"msg":"密码不能低于6位"}');
	}elseif (is_numeric($password)) {
		exit('{"code":-1,"msg":"密码不能为纯数字"}');
	}
    $row=$DB->getRow("select * from ozon_user where username=:username limit 1", [':username'=>$username]);
	if($row){
		exit('{"code":-1,"msg":"该用户名已经注册过，如需找回信息，请返回登录页面找回"}');
	}else{
	    $password = password_hash($password, PASSWORD_DEFAULT);
	    $sds=$DB->exec("INSERT INTO `ozon_user` (`uid`, `username`, `password`, `email`, `phone`, `addtime`, `status`) VALUES (NULL, '{$username}', '{$password}', '{$email}', '{$phone}',NOW(),1)");
	    if($sds){
			$result=array("code"=>1,"msg"=>"注册成功！");
		}else{
			$result=array("code"=>-1,"msg"=>"注册失败！".$DB->error());
		}
	}
    exit(json_encode($result));
break;
case 'captcha':
    if(!$_POST['csrf_token'] || $_POST['csrf_token']!=$_SESSION['csrf_token'])exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
    $conf['captcha_version'] = 1;
    if($conf['captcha_version'] == '1'){
		$captcha_id = !empty($conf['captcha_id'])?$conf['captcha_id']:'54088bb07d2df3c46b79f80300b0abbe';
		$result = ['success'=>1, 'gt'=>$captcha_id, 'version'=>1];
	}else{
		$GtSdk = new \lib\GeetestLib($conf['captcha_id'], $conf['captcha_key']);
		$data = array(
			'user_id' => isset($uid)?$uid:'public',
			'client_type' => "web",
			'ip_address' => $clientip
		);
		$result = $GtSdk->pre_process($data);
		$result['version'] = 0;
	}
	$_SESSION['gtserver'] = $result['success'];
	exit(json_encode($result));
break;
case 'sendcode':
    if(!$_POST['csrf_token'] || $_POST['csrf_token']!=$_SESSION['csrf_token'])exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
	$phone=htmlspecialchars(strip_tags(trim($_POST['phone'])));
	$conf['reg_open'] = 1;
	if($conf['reg_open']==0)exit('{"code":-1,"msg":"未开放注册"}');
	if(isset($_SESSION['send_code_time']) && $_SESSION['send_code_time']>time()-10){
		exit('{"code":-1,"msg":"请勿频繁发送验证码"}');
	}

	if(!isset($_SESSION['gtserver']))exit('{"code":-1,"msg":"验证加载失败"}');
	//if(!verify_captcha())exit('{"code":-1,"msg":"验证失败，请重新验证"}');

	$row=$DB->getRow("select * from ozon_user where phone=:phone limit 1", [':phone'=>$phone]);
	if($row){
		exit('{"code":-1,"msg":"该手机号已经注册过商户，如需找回信息，请返回登录页面点击找回商户"}');
	}
	$row=$DB->getRow("select * from ozon_user where email=:email limit 1", [':email'=>$email]);
	if($row){
		exit('{"code":-1,"msg":"该邮箱已经注册过商户，如需找回商户信息，请返回登录页面点击找回商户"}');
	}
	$result = \lib\VerifyCode::send_code('reg', 1, $phone);
	if($result === true){
		$_SESSION['send_code_time']=time();
		exit('{"code":0,"msg":"succ"}');
	}else{
		exit(json_encode(['code'=>-1, 'msg'=>$result]));
	}
break;
case 'send_reset_code':
    if(!$_POST['csrf_token'] || $_POST['csrf_token']!=$_SESSION['csrf_token'])exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
	$phone=htmlspecialchars(strip_tags(trim($_POST['phone'])));
	if(isset($_SESSION['send_code_time']) && $_SESSION['send_code_time']>time()-10){
		exit('{"code":-1,"msg":"请勿频繁发送验证码"}');
	}

	if(!isset($_SESSION['gtserver']))exit('{"code":-1,"msg":"验证加载失败"}');

	$row=$DB->getRow("select * from ozon_user where phone=:phone limit 1", [':phone'=>$phone]);
	if(!$row){
		exit('{"code":-1,"msg":"该手机号未注册"}');
	}
	$result = \lib\VerifyCode::send_code('reset', 1, $phone);
	if($result === true){
		$_SESSION['send_code_time']=time();
		exit('{"code":0,"msg":"succ"}');
	}else{
		exit(json_encode(['code'=>-1, 'msg'=>$result]));
	}
break;
case 'send_bind_code':
    if(!$_POST['csrf_token'] || $_POST['csrf_token']!=$_SESSION['csrf_token'])exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
	$phone=htmlspecialchars(strip_tags(trim($_POST['phone'])));
	if(isset($_SESSION['send_code_time']) && $_SESSION['send_code_time']>time()-10){
		exit('{"code":-1,"msg":"请勿频繁发送验证码"}');
	}

	if(!isset($_SESSION['gtserver']))exit('{"code":-1,"msg":"验证加载失败"}');

	$row=$DB->getRow("select * from ozon_user where phone=:phone limit 1", [':phone'=>$phone]);
	if($row){
		exit('{"code":-1,"msg":"该手机号已被其他用户绑定"}');
	}
	$result = \lib\VerifyCode::send_code('edit', 1, $phone);
	if($result === true){
		$_SESSION['send_code_time']=time();
		exit('{"code":0,"msg":"succ"}');
	}else{
		exit(json_encode(['code'=>-1, 'msg'=>$result]));
	}
break;
case 'forgot_password':
    if(!$_POST['csrf_token'] || $_POST['csrf_token']!=$_SESSION['csrf_token'])exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
    $phone=htmlspecialchars(strip_tags(trim($_POST['phone'])));
    $password=trim($_POST['password']);
    $code=trim($_POST['sms_code']);
    
    if(empty($phone) || empty($password) || empty($code)){
        exit('{"code":-1,"msg":"请确保各项不能为空"}');
    }
    
    if (strlen($password) < 6) {
        exit('{"code":-1,"msg":"密码不能低于6位"}');
    }elseif (is_numeric($password)) {
        exit('{"code":-1,"msg":"密码不能为纯数字"}');
    }
    
    $row=$DB->getRow("select * from ozon_user where phone=:phone limit 1", [':phone'=>$phone]);
    if(!$row){
        exit('{"code":-1,"msg":"该手机号未注册"}');
    }
    
    $result = \lib\VerifyCode::verify_code('reset', 1, $phone, $code);
    if($result !== true){
        exit(json_encode(['code'=>-1, 'msg'=>$result]));
    }
    
    $password = password_hash($password, PASSWORD_DEFAULT);
    $sds=$DB->exec("UPDATE ozon_user SET password=:password WHERE phone=:phone", [':password'=>$password, ':phone'=>$phone]);
    if($sds){
        $result=array("code"=>1,"msg"=>"密码重置成功！");
    }else{
        $result=array("code"=>-1,"msg"=>"密码重置失败！".$DB->error());
    }
    exit(json_encode($result));
break;
case 'bind_phone':
    if(!$_POST['csrf_token'] || $_POST['csrf_token']!=$_SESSION['csrf_token'])exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
    $phone=htmlspecialchars(strip_tags(trim($_POST['phone'])));
    $code=trim($_POST['sms_code']);
    
    if(empty($phone) || empty($code)){
        exit('{"code":-1,"msg":"请确保各项不能为空"}');
    }
    
    $row=$DB->getRow("select * from ozon_user where phone=:phone limit 1", [':phone'=>$phone]);
    if($row){
        exit('{"code":-1,"msg":"该手机号已被其他用户绑定"}');
    }
    
    $result = \lib\VerifyCode::verify_code('edit', 1, $phone, $code);
    if($result !== true){
        exit(json_encode(['code'=>-1, 'msg'=>$result]));
    }
    
    $sds=$DB->exec("UPDATE ozon_user SET phone=:phone WHERE uid=:uid", [':phone'=>$phone, ':uid'=>$uid]);
    if($sds){
        $result=array("code"=>1,"msg"=>"手机号绑定成功！");
    }else{
        $result=array("code"=>-1,"msg"=>"手机号绑定失败！".$DB->error());
    }
    exit(json_encode($result));
break;
case 'getUserInfo':
    $userId = $GLOBALS['uid'] ?? 0;
    
    // 验证用户登录状态
    if ($userId === 0) {
        exit(json_encode([
            'code' => 0,
            'msg' => '未登录',
            'errorCode' => 401
        ]));
    }
    
    // 修正变量名并使用正确的查询方法，增加expiry_date字段
    $userInfo = $DB->getRow("SELECT uid, username, user_level, expiry_date FROM ozon_user WHERE uid = ?", [$userId]);
    
    // 检查用户是否存在
    if (!$userInfo) {
        exit(json_encode([
            'code' => 0,
            'msg' => '用户不存在',
            'errorCode' => 404
        ]));
    }
    
    // 返回用户信息
    exit(json_encode([
        'code' => 1,
        'data' => $userInfo,
        'msg' => '获取成功'
    ]));
    
    break;


case 'logout':
    setcookie("Authorization", "", time() - 604800);
	@header('Content-Type: text/html; charset=UTF-8');
	$result = ['code'=>1,'您已成功注销本次登录！'];
	exit(json_encode($result));
break;
case 'upload_image':
    // 图片上传处理
    if (!isset($_FILES['file'])) {
        exit(json_encode(['code' => -1, 'msg' => '没有上传文件']));
    }

    $file = $_FILES['file'];
    if ($file['error'] !== UPLOAD_ERR_OK) {
        exit(json_encode(['code' => -1, 'msg' => '文件上传失败，错误代码: ' . $file['error']]));
    }

    // 检查文件类型
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = mime_content_type($file['tmp_name']);
    if (!in_array($fileType, $allowedTypes)) {
        exit(json_encode(['code' => -1, 'msg' => '只允许上传图片文件 (JPEG, PNG, GIF, WebP)']));
    }

    // 检查文件大小 (10MB)
    $maxSize = 10 * 1024 * 1024;
    if ($file['size'] > $maxSize) {
        exit(json_encode(['code' => -1, 'msg' => '文件大小不能超过10MB']));
    }

    // 生成唯一文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    
    // 确保uploads目录存在
    $uploadDir = '../uploads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // 移动文件到目标位置
    $filepath = $uploadDir . $filename;
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // 生成访问URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $domain = $_SERVER['HTTP_HOST'];
        $imageUrl = $protocol . '://' . $domain . '/uploads/' . $filename;
        
        exit(json_encode([
            'code' => 0,
            'msg' => '上传成功',
            'data' => ['src' => $imageUrl]
        ]));
    } else {
        exit(json_encode(['code' => -1, 'msg' => '文件保存失败']));
    }
break;

//商品编辑页面
case 'edit_product':
    // 根据您的反馈，已移除打开时对Ozon API的调用，直接从本地数据库加载数据
    $offer_id = $_GET['offer_id'] ?? '';
    $storeid = $_GET['storeid'] ?? '';
    
    if (empty($offer_id) || empty($storeid)) {
        exit('{"code":-1,"msg":"参数错误"}');
    }
    
    // 从本地数据库获取数据 - 修正表名为ozon_store
    $store = $DB->getRow("SELECT * FROM ozon_store WHERE id = ?", [$storeid]);
    if (!$store) {
        exit('{"code":-1,"msg":"店铺不存在"}');
    }
    
    $product = $DB->getRow("SELECT * FROM ozon_products WHERE offer_id = ? AND storeid = ?", [$offer_id, $storeid]);
    if (!$product) {
        exit('{"code":-1,"msg":"商品不存在"}');
    }
    
    // 加载关联数据 - 先简化不查询仓库，减少可能的错误
    $warehouses = [];
    
    $categories_json = file_get_contents('./config/中文类目.json');
    $categories_data = json_decode($categories_json, true);
    $categories = $categories_data['data'] ?? [];
    
    // 属性暂时留空，后续可以从本地属性表加载
    $attributes = [];

    // 返回JSON数据而不是HTML页面
    $result = [
        'code' => 0,
        'msg' => '获取成功',
        'data' => [
            'product' => $product,
            'store' => $store,
            'warehouses' => $warehouses,
            'categories' => $categories,
            'attributes' => $attributes
        ]
    ];
    exit(json_encode($result));
    break;

case 'save_product':
    // 保存编辑的商品信息
    $offer_id = $_POST['offer_id'] ?? '';
    $storeid = $_POST['storeid'] ?? '';
    $name = $_POST['name'] ?? '';
    $description = $_POST['description'] ?? '';
    $price = $_POST['price'] ?? '';
    $old_price = $_POST['old_price'] ?? '';
    $category_id = $_POST['category_id'] ?? '';
    $stock = $_POST['stock'] ?? '';
    
    if (empty($offer_id) || empty($storeid)) {
        exit('{"code":-1,"msg":"参数错误"}');
    }
    
    // 1. 更新本地数据库
    $updateData = [];
    $updateParams = [];
    if (!empty($name)) { $updateData[] = "name = ?"; $updateParams[] = $name; }
    if (!empty($description)) { $updateData[] = "description = ?"; $updateParams[] = $description; }
    if (!empty($price)) { $updateData[] = "price = ?"; $updateParams[] = floatval($price); }
    if (!empty($old_price)) { $updateData[] = "old_price = ?"; $updateParams[] = floatval($old_price); }
    if (!empty($category_id)) { $updateData[] = "category_id = ?"; $updateParams[] = $category_id; }
    if (!empty($stock)) { $updateData[] = "stock = ?"; $updateParams[] = intval($stock); }

    if (empty($updateData)) {
        exit('{"code":-1,"msg":"没有要更新的数据"}');
    }
    $updateData[] = "updated_at = NOW()";
    $updateParams[] = $offer_id;
    $updateParams[] = $storeid;
    
    $sql = "UPDATE ozon_products SET " . implode(', ', $updateData) . " WHERE offer_id = ? AND storeid = ?";
    $DB->exec($sql, $updateParams);

    // 2. 调用Ozon API更新商品
    // 获取店铺的API凭证 - 修正表名为ozon_store
    $store = $DB->getRow("SELECT ClientId, key FROM ozon_store WHERE id = ?", [$storeid]);
    if (!$store) {
        exit('{"code":-1,"msg":"店铺不存在"}');
    }
    
    $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);

    // 更新价格
    if (!empty($price)) {
        $priceData = [
            'offer_id' => $offer_id,
            'price' => (string)$price,
            'currency_code' => 'RUB', // 或者从商品信息中获取
        ];
        if(!empty($old_price)) $priceData['old_price'] = (string)$old_price;
        $client->importprices($priceData);
    }

    // 更新库存 (需要仓库ID，这里暂时无法获取，需要前端传递或补充逻辑)
    if (!empty($stock)) {
         // 注意：这里的 warehouse_id 需要从前端传递，或者有一个默认值
         // $warehouse_id = $_POST['warehouse_id'] ?? $default_warehouse_id;
         // $client->productsstocks(['offer_id' => $offer_id, 'stock' => $stock, 'warehouse_id' => $warehouse_id]);
    }
    
    // 注意：更新名称、描述、分类等需要使用更复杂的 `productimport` 方法，
    // 这需要获取所有属性。当前实现只更新价格和库存。
    // 一个完整的实现需要从前端收集所有属性，或者在后端重新获取它们。
    
    exit('{"code":0,"msg":"商品信息已提交更新请求"}');
    break;

case 'save_product_management':
    // 保存商品管理页面编辑的商品信息
    $offer_id = $_POST['offer_id'] ?? '';
    $storeid = intval($_POST['storeid']);
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    $price = floatval($_POST['price']);
    $old_price = floatval($_POST['old_price']);
    $images = $_POST['images'] ?? '';
    $depth = intval($_POST['depth']);
    $width = intval($_POST['width']);
    $height = intval($_POST['height']);
    $weight = intval($_POST['weight']);
    
    // 获取类目信息
    $category1 = $_POST['category1'] ?? '';
    $category2 = $_POST['category2'] ?? '';
    $category3 = $_POST['category3'] ?? '';
    
    // 获取商品属性
    $attributes = $_POST['attributes'] ?? [];
    $rich_content_json = $_POST['rich_content_json'] ?? '';
    $rich_content_html = $_POST['rich_content_html'] ?? ''; // 从富文本编辑器获取HTML
    
    if (!$offer_id || !$storeid) {
        exit('{"code":-1,"msg":"参数错误"}');
    }
    
    try {
        // 1. 更新本地数据库
        $updateData = [
            'name' => $title,
            'description' => $description,
            'price' => $price,
            'old_price' => $old_price,
            'images' => json_encode(array_filter(explode(';', $images))),
            'depth' => $depth,
            'width' => $width,
            'height' => $height,
            'weight' => $weight,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 如果有类目信息，更新类目字段
        if ($category2) {
            $updateData['description_category_id'] = $category2;
        }
        if ($category3) {
            $updateData['type_id'] = $category3;
        }
        
        // 如果有属性信息，序列化存储
        if (!empty($attributes)) {
            $updateData['attributes'] = json_encode($attributes);
        }
        
                 // 检查表中存在的字段，动态构建SQL
         $existingColumns = $DB->getAll("DESCRIBE ozon_products");
         $columnNames = array_column($existingColumns, 'Field');
         
         $updateFields = [];
         $updateValues = [];
         
         // 基础字段（必定存在）
         $updateFields[] = "name=?";
         $updateValues[] = $updateData['name'];
         
         $updateFields[] = "description=?";
         $updateValues[] = $updateData['description'];
         
         $updateFields[] = "price=?";
         $updateValues[] = $updateData['price'];
         
         $updateFields[] = "old_price=?";
         $updateValues[] = $updateData['old_price'];
         
         $updateFields[] = "images=?";
         $updateValues[] = $updateData['images'];
         
         $updateFields[] = "updated_at=?";
         $updateValues[] = $updateData['updated_at'];
         
         // 可选字段（检查是否存在）
         if (in_array('depth', $columnNames)) {
             $updateFields[] = "depth=?";
             $updateValues[] = $updateData['depth'];
         }
         
         if (in_array('width', $columnNames)) {
             $updateFields[] = "width=?";
             $updateValues[] = $updateData['width'];
         }
         
         if (in_array('height', $columnNames)) {
             $updateFields[] = "height=?";
             $updateValues[] = $updateData['height'];
         }
         
         if (in_array('weight', $columnNames)) {
             $updateFields[] = "weight=?";
             $updateValues[] = $updateData['weight'];
         }
         
         if (in_array('description_category_id', $columnNames) && isset($updateData['description_category_id'])) {
             $updateFields[] = "description_category_id=?";
             $updateValues[] = $updateData['description_category_id'];
         }
         
         if (in_array('type_id', $columnNames) && isset($updateData['type_id'])) {
             $updateFields[] = "type_id=?";
             $updateValues[] = $updateData['type_id'];
         }
         
         if (in_array('attributes', $columnNames) && isset($updateData['attributes'])) {
             $updateFields[] = "attributes=?";
             $updateValues[] = $updateData['attributes'];
         }
         
         // 添加WHERE条件的参数
         $updateValues[] = $offer_id;
         $updateValues[] = $storeid;
         
         $sql = "UPDATE ozon_products SET " . implode(", ", $updateFields) . " WHERE offer_id=? AND storeid=?";
         $result = $DB->query($sql, $updateValues);
        
        if (!$result) {
            exit('{"code":-1,"msg":"数据库更新失败"}');
        }
        
        // 2. 调用Ozon API更新商品
        $store = $DB->getRow("SELECT * FROM ozon_store WHERE id=?", [$storeid]);
        if (!$store) {
            error_log("店铺信息查询失败 - storeid: $storeid");
            exit('{"code":-1,"msg":"店铺信息不存在"}');
        }
        
        error_log("店铺信息获取成功 - ClientId: " . $store['ClientId'] . ", storename: " . $store['storename']);
        
        $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
        
        // 构建商品数据用于API更新
        $currency_code = $store['currency_code'] ?: 'RUB'; // 从店铺设置获取货币，默认RUB
        $productData = [
            'offer_id' => $offer_id,
            'name' => $title,
            'price' => (string)$price,
            'currency_code' => $currency_code,
            'vat' => '0'
        ];
        
        // 添加可选字段
        if (!empty($description)) {
            $productData['description'] = $description;
        }
        if ($old_price > 0) {
            $productData['old_price'] = (string)$old_price;
        }
        if ($depth > 0) {
            $productData['depth'] = $depth;
            $productData['dimension_unit'] = 'mm';
        }
        if ($width > 0) {
            $productData['width'] = $width;
            $productData['dimension_unit'] = 'mm';
        }
        if ($height > 0) {
            $productData['height'] = $height;
            $productData['dimension_unit'] = 'mm';
        }
        if ($weight > 0) {
            $productData['weight'] = $weight;
            $productData['weight_unit'] = 'g';
        }
        
        // 添加类目信息（从数据库获取现有值如果前端没有提供）
        $currentProduct = $DB->getRow("SELECT description_category_id, type_id FROM ozon_products WHERE offer_id=? AND storeid=?", [$offer_id, $storeid]);
        
        $finalCategory2 = $category2 ?: ($currentProduct['description_category_id'] ?? null);
        $finalCategory3 = $category3 ?: ($currentProduct['type_id'] ?? null);
        
        if ($finalCategory2) {
            $productData['description_category_id'] = intval($finalCategory2);
        }
        if ($finalCategory3) {
            $productData['type_id'] = intval($finalCategory3);
        }
        
        // 处理图片
        if ($images) {
            $imageArray = array_filter(explode(';', $images));
            if (!empty($imageArray)) {
                $productData['primary_image'] = $imageArray[0];
                if (count($imageArray) > 1) {
                    $productData['images'] = $imageArray;
                }
            }
        }
        
        // 处理商品属性
        if (!empty($attributes)) {
            $formattedAttributes = [];
            foreach ($attributes as $attrId => $attrValue) {
                if (!empty($attrValue)) {
                    $attribute = [
                        'id' => intval($attrId),
                        'complex_id' => 0
                    ];
                    
                    // 如果是数组值（多选属性）
                    if (is_array($attrValue)) {
                        $values = [];
                        foreach ($attrValue as $value) {
                            if (is_numeric($value)) {
                                $values[] = ['dictionary_value_id' => intval($value)];
                            } else {
                                $values[] = ['value' => (string)$value];
                            }
                        }
                        $attribute['values'] = $values;
                    } else {
                        // 单值属性
                        if (is_numeric($attrValue)) {
                            $attribute['values'] = [['dictionary_value_id' => intval($attrValue)]];
                        } else {
                            $attribute['values'] = [['value' => (string)$attrValue]];
                        }
                    }
                    
                    $formattedAttributes[] = $attribute;
                }
            }
            
            // 添加必填属性：如果9048属性缺失，使用商品名称作为默认值
            $hasModelName = false;
            foreach ($formattedAttributes as $attr) {
                if ($attr['id'] == 9048) {
                    $hasModelName = true;
                    break;
                }
            }
            
            if (!$hasModelName) {
                $formattedAttributes[] = [
                    'id' => 9048,
                    'complex_id' => 0,
                    'values' => [['value' => $title]] // 使用商品名称作为模型名称
                ];
            }
            
            $productData['attributes'] = $formattedAttributes;
        } else {
            // 如果没有属性，至少添加必填的模型名称属性
            $productData['attributes'] = [
                [
                    'id' => 9048,
                    'complex_id' => 0,
                    'values' => [['value' => $title]]
                ]
            ];
        }

        // Add rich content back to attributes if it exists
        if (!empty($rich_content_json)) {
            if (!isset($productData['attributes'])) {
                $productData['attributes'] = [];
            }
            $productData['attributes'][] = [
                'id' => 11254,
                'complex_id' => 0,
                'values' => [['value' => $rich_content_json]]
            ];
        }

        // 处理Rich Content - 如果用户输入了内容且是有效的JSON
        if (!empty($rich_content_html)) {
            // 检查是否是有效的JSON
            $decoded = json_decode($rich_content_html, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                // 如果是有效的JSON，直接使用
                if (!isset($productData['attributes'])) {
                    $productData['attributes'] = [];
                }
                $productData['attributes'][] = [
                    'id' => 11254,
                    'complex_id' => 0,
                    'values' => [['value' => $rich_content_html]]
                ];
            }
            // 如果不是有效的JSON，暂时跳过Rich Content，避免API错误
        }

        // 调用Ozon API更新商品
        try {
            // 添加调试日志
            $log_file = __DIR__ . '/product_sync.log';
            file_put_contents($log_file, "API Request Data: " . json_encode($productData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
            
            $apiResult = $client->productimportV2([$productData]);
            
            // 记录API响应
            $responseLog = is_string($apiResult) ? $apiResult : json_encode($apiResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            file_put_contents($log_file, "API Response: " . $responseLog . "\n", FILE_APPEND);
            
            if (isset($apiResult['result']['task_id'])) {
                // API调用成功，返回成功信息
                exit('{"code":0,"msg":"商品信息保存成功，正在同步到Ozon平台，任务ID：' . $apiResult['result']['task_id'] . '"}');
            } else {
                // API调用失败，但本地数据已保存
                $errorMsg = isset($apiResult['message']) ? $apiResult['message'] : (isset($apiResult['error']) ? $apiResult['error'] : 'API调用失败');
                if (isset($apiResult['code']) && $apiResult['code'] != 0) {
                    $errorMsg .= " (错误代码: " . $apiResult['code'] . ")";
                }
                exit('{"code":1,"msg":"本地数据已保存，但同步到Ozon平台时出现问题：' . $errorMsg . '"}');
            }
        } catch (Exception $e) {
            // API异常，但本地数据已保存
            file_put_contents($log_file, "API Exception: " . $e->getMessage() . "\n", FILE_APPEND);
            exit('{"code":1,"msg":"本地数据已保存，但同步到Ozon平台时发生异常：' . $e->getMessage() . '"}');
        }
        
    } catch (Exception $e) {
        exit('{"code":-1,"msg":"保存失败：' . $e->getMessage() . '"}');
    }
    break;

case 'check_task_status':
    // 检查Ozon API任务状态
    $task_id = $_POST['task_id'] ?? '';
    $storeid = intval($_POST['storeid']);
    
    if (!$task_id || !$storeid) {
        exit('{"code":-1,"msg":"参数错误"}');
    }
    
    try {
        $store = $DB->getRow("SELECT * FROM ozon_store WHERE id=?", [$storeid]);
        if (!$store) {
            exit('{"code":-1,"msg":"店铺信息不存在"}');
        }
        
        $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
        $result = $client->productimportinfo(['task_id' => $task_id]);
        
        if (isset($result['status'])) {
            $status_map = [
                'pending' => '等待处理',
                'importing' => '正在处理',
                'finished' => '处理完成',
                'failed' => '处理失败'
            ];
            
            $status_text = $status_map[$result['status']] ?? $result['status'];
            
            if ($result['status'] === 'finished') {
                exit('{"code":0,"msg":"任务处理完成","data":{"status":"' . $status_text . '","task_id":"' . $task_id . '"}}');
            } elseif ($result['status'] === 'failed') {
                $error_msg = isset($result['error']) ? $result['error'] : '未知错误';
                exit('{"code":-1,"msg":"任务处理失败：' . $error_msg . '","data":{"status":"' . $status_text . '","task_id":"' . $task_id . '"}}');
            } else {
                exit('{"code":1,"msg":"任务正在处理中：' . $status_text . '","data":{"status":"' . $status_text . '","task_id":"' . $task_id . '"}}');
            }
        } else {
            exit('{"code":-1,"msg":"无法获取任务状态"}');
        }
    } catch (Exception $e) {
        exit('{"code":-1,"msg":"查询任务状态失败：' . $e->getMessage() . '"}');
    }
    break;

    //商品编辑结束







// ##################################################################
// # 定时上架功能模块 START
// ##################################################################



// 获取定时上架商品列表
case 'get_scheduled_products':
    $uid = $userrow['uid'] ?? 0;
    if (!$uid) {
        exit(json_encode(['code' => -1, 'msg' => '未登录']));
    }
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = $limit * ($page - 1);
    
    // 支持按状态筛选
    $status_filter = '';
    if (!empty($_GET['status'])) {
        $status = daddslashes($_GET['status']);
        $status_filter = " AND status = '$status'";
    }
    
    $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_scheduled_products WHERE uid = $uid $status_filter");
    $list = $DB->getAll("SELECT sp.*, os.storename, os.warehouses FROM ozon_scheduled_products sp 
                         LEFT JOIN ozon_store os ON sp.storeid = os.id 
                         WHERE sp.uid = $uid $status_filter ORDER BY sp.addtime DESC LIMIT $offset, $limit");
    
    // 处理数据格式
    foreach ($list as &$item) {
        // 解析仓库名称
        $warehouse_name = '未知仓库';
        if (!empty($item['warehouses'])) {
            $warehouses = json_decode($item['warehouses'], true);
            if (is_array($warehouses)) {
                foreach ($warehouses as $warehouse) {
                    if (isset($warehouse['warehouse_id']) && $warehouse['warehouse_id'] == $item['warehouse_id']) {
                        $warehouse_name = $warehouse['name'] ?: '仓库-' . $item['warehouse_id'];
                        break;
                    }
                }
            }
        }
        
        // 构建店铺和仓库信息
        $item['store_info'] = [
            'store_id' => $item['storeid'],
            'store_name' => $item['storename'] ?: '未知店铺',
            'warehouse_id' => $item['warehouse_id'],
            'warehouse_name' => $warehouse_name
        ];
        
        // 检查是否为均分任务（通过task_id查询同组记录数判断）
        $same_group_count = $DB->getColumn("SELECT COUNT(DISTINCT storeid) FROM ozon_scheduled_products WHERE task_id = ? AND sku = ?", 
            [$item['task_id'], $item['sku']]);
        $item['is_distributed'] = $same_group_count == 1 ? 1 : 0;
        
        // 格式化时间
        if ($item['scheduled_at']) {
            $item['scheduled_at'] = date('Y-m-d H:i:s', strtotime($item['scheduled_at']));
        }
        
        // 清理不需要的字段
        unset($item['storeid'], $item['storename'], $item['warehouses']);
    }
    
    exit(json_encode([
        'code' => 0,
        'msg' => 'success',
        'count' => $total,
        'data' => $list
    ]));
break;

// 创建定时上架任务
case 'create_scheduled_task':
    $uid = $userrow['uid'] ?? 0;
    if (!$uid) {
        exit(json_encode(['code' => -1, 'msg' => '未登录']));
    }
    
    // 获取POST的JSON数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        exit(json_encode(['code' => -1, 'msg' => '数据格式错误']));
    }
    
    $products = $data['products'] ?? [];
    $store_config = $data['store_config'] ?? [];
    $price_preset = intval($data['price_preset'] ?? 0);
    $stock = intval($data['stock'] ?? 0);
    $scheduled_at = $data['scheduled_at'] ?? null;
    $distribute_skus = isset($data['distributeSkus']) ? (bool)$data['distributeSkus'] : false;
    $price_range = $data['price_range'] ?? null;
    
    // 增强数据验证
    if (empty($products)) {
        exit(json_encode(['code' => -1, 'msg' => '商品列表不能为空']));
    }
    
    if (empty($store_config)) {
        exit(json_encode(['code' => -1, 'msg' => '店铺配置不能为空']));
    }
    
    if ($stock <= 0) {
        exit(json_encode(['code' => -1, 'msg' => '库存必须大于0']));
    }
    
    // 验证SKU格式和重复性
    $skus = [];
    foreach ($products as $product) {
        if (empty($product['sku']) || !is_string($product['sku'])) {
            exit(json_encode(['code' => -1, 'msg' => 'SKU格式错误: ' . ($product['sku'] ?? 'empty')]));
        }
        if (in_array($product['sku'], $skus)) {
            exit(json_encode(['code' => -1, 'msg' => 'SKU重复: ' . $product['sku']]));
        }
        $skus[] = $product['sku'];
    }
    
    // 验证店铺权限
    $valid_store_ids = [];
    foreach ($store_config as $store) {
        if (empty($store['store_id']) || empty($store['warehouse_id'])) {
            exit(json_encode(['code' => -1, 'msg' => '店铺或仓库配置不完整']));
        }
        
        // 验证店铺是否属于当前用户
        if (!in_array($store['store_id'], $valid_store_ids)) {
            $store_check = $DB->getRow("SELECT id FROM ozon_store WHERE id = ? AND uid = ?", 
                [$store['store_id'], $uid]);
            if (!$store_check) {
                exit(json_encode(['code' => -1, 'msg' => '店铺ID ' . $store['store_id'] . ' 不存在或无权限']));
            }
            $valid_store_ids[] = $store['store_id'];
        }
    }
    
    // 检查是否有待处理的重复SKU
    $existing_skus = [];
    $sku_placeholders = implode(',', array_fill(0, count($skus), '?'));
    $existing_records = $DB->getAll("SELECT sku FROM ozon_scheduled_products 
        WHERE uid = ? AND sku IN ($sku_placeholders) AND status IN ('pending', 'processing')", 
        array_merge([$uid], $skus));
    
    if (!empty($existing_records)) {
        $existing_skus = array_column($existing_records, 'sku');
        exit(json_encode(['code' => -1, 'msg' => '以下SKU已存在待处理任务: ' . implode(', ', $existing_skus)]));
    }
    
    // 生成任务组ID (优化格式)
    $task_group_id = 'task_' . date('YmdHis') . '_' . uniqid();
    
    $DB->beginTransaction();
    try {
        $inserted_count = 0;
        $failed_inserts = [];
        
        // 预编译插入语句以提高性能
        $insert_sql = "INSERT INTO ozon_scheduled_products (uid, sku, price, purchase_price, purchase_link, average_price, predicted_price, task_id, storeid, warehouse_id, price_preset, stock, scheduled_at, status, addtime, date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        if ($distribute_skus) {
            // 均分模式：每个SKU分配给一个特定店铺
            $product_index = 0;
            $store_count = count($store_config);
            
            foreach ($products as $product) {
                try {
                    // 轮询分配店铺
                    $assigned_store = $store_config[$product_index % $store_count];
                    
                    // 计算最终价格（包含随机区间）
                    $final_price = $product['price'] ? floatval($product['price']) : null;
                    if ($final_price && $price_range) {
                        $random_offset = mt_rand($price_range['min'] * 100, $price_range['max'] * 100) / 100;
                        $final_price = round($final_price + $random_offset, 2);
                        // 确保价格不为负数
                        $final_price = max(0.01, $final_price);
                    }
                    
                    $insert_data = [
                        $uid,
                        $product['sku'],
                        $final_price,
                        $product['purchase_price'] ? floatval($product['purchase_price']) : null,
                        $product['purchase_link'] ?? null,
                        null, // average_price - 用户有其他方式获取
                        null, // predicted_price - 用户有其他方式获取
                        $task_group_id,
                        $assigned_store['store_id'],
                        $assigned_store['warehouse_id'],
                        $price_preset,
                        $stock,
                        $scheduled_at,
                        'pending',
                        date('Y-m-d H:i:s'),
                        date('Y-m-d')
                    ];
                    
                    $result = $DB->exec($insert_sql, $insert_data);
                    
                    if ($result) {
                        $inserted_count++;
                    } else {
                        $failed_inserts[] = $product['sku'] . '@店铺' . $assigned_store['store_id'];
                    }
                } catch (Exception $e) {
                    $failed_inserts[] = $product['sku'] . ': ' . $e->getMessage();
                }
                $product_index++;
            }
        } else {
            // 普通模式：每个SKU对应所有选中的店铺
            foreach ($products as $product) {
                try {
                    foreach ($store_config as $store) {
                        try {
                            // 计算最终价格（包含随机区间）- 每个店铺都有不同的随机价格
                            $final_price = $product['price'] ? floatval($product['price']) : null;
                            if ($final_price && $price_range) {
                                $random_offset = mt_rand($price_range['min'] * 100, $price_range['max'] * 100) / 100;
                                $final_price = round($final_price + $random_offset, 2);
                                // 确保价格不为负数
                                $final_price = max(0.01, $final_price);
                            }
                            
                            $insert_data = [
                                $uid,
                                $product['sku'],
                                $final_price,
                                $product['purchase_price'] ? floatval($product['purchase_price']) : null,
                                $product['purchase_link'] ?? null,
                                null, // average_price - 用户有其他方式获取
                                null, // predicted_price - 用户有其他方式获取
                                $task_group_id,
                                $store['store_id'],
                                $store['warehouse_id'],
                                $price_preset,
                                $stock,
                                $scheduled_at,
                                'pending',
                                date('Y-m-d H:i:s'),
                                date('Y-m-d')
                            ];
                            
                            $result = $DB->exec($insert_sql, $insert_data);
                            
                            if ($result) {
                                $inserted_count++;
                            } else {
                                $failed_inserts[] = $product['sku'] . '@店铺' . $store['store_id'];
                            }
                        } catch (Exception $e) {
                            $failed_inserts[] = $product['sku'] . '@店铺' . $store['store_id'] . ': ' . $e->getMessage();
                                                 }
                     }
                 } catch (Exception $e) {
                     $failed_inserts[] = $product['sku'] . ': ' . $e->getMessage();
                 }
            }
        }
        
        $DB->commit();
        
        // 构建返回信息
        $response_data = [
            'task_group_id' => $task_group_id,
            'inserted_count' => $inserted_count,
            'total_products' => count($products),
            'store_count' => count($store_config),
            'distribute_skus' => $distribute_skus
        ];
        
        if (!empty($failed_inserts)) {
            $response_data['failed_inserts'] = $failed_inserts;
            $response_data['failed_count'] = count($failed_inserts);
        }
        
        // 构建消息
        if ($distribute_skus) {
            $message = '均分任务创建完成，共' . count($products) . '个SKU已分配到' . count($store_config) . '个店铺';
            $message .= '，成功插入' . $inserted_count . '条记录';
        } else {
            $expected_total = count($products) * count($store_config);
            $message = '定时上架任务创建完成，预期' . $expected_total . '条记录，实际成功插入' . $inserted_count . '条记录';
        }
        
        // 如果启用了价格区间，添加相关信息
        if ($price_range) {
            $message .= '，已应用价格区间[' . $price_range['min'] . '~' . $price_range['max'] . '￥]';
        }
        
        if (!empty($failed_inserts)) {
            $message .= '，失败' . count($failed_inserts) . '条';
        }
        
        // 确定响应代码
        $code = 0;
        if ($inserted_count == 0) {
            $code = -1;
            $message = '任务创建失败，没有任何记录被成功插入';
        } elseif (!empty($failed_inserts)) {
            $code = 1; // 部分成功
        }
            
        exit(json_encode([
            'code' => $code,
            'msg' => $message,
            'data' => $response_data
        ]));
        
    } catch (Exception $e) {
        $DB->rollBack();
        exit(json_encode(['code' => -1, 'msg' => '任务创建失败: ' . $e->getMessage()]));
    }
break;

// 删除定时上架商品
case 'delete_scheduled_products':
    $uid = $userrow['uid'] ?? 0;
    if (!$uid) {
        exit(json_encode(['code' => -1, 'msg' => '未登录']));
    }
    
    $ids = $_POST['ids'] ?? [];
    if (empty($ids) || !is_array($ids)) {
        exit(json_encode(['code' => -1, 'msg' => '请选择要删除的商品']));
    }
    
    $ids = array_map('intval', $ids);
    $placeholders = implode(',', array_fill(0, count($ids), '?'));
    
    $deleted = $DB->exec("DELETE FROM ozon_scheduled_products WHERE id IN ($placeholders) AND uid = ?", 
        array_merge($ids, [$uid]));
    
    if ($deleted) {
        exit(json_encode(['code' => 0, 'msg' => '删除成功', 'deleted_count' => $deleted]));
    } else {
        exit(json_encode(['code' => -1, 'msg' => '删除失败']));
    }
break;

// 清空待上架商品
case 'clear_scheduled_products':
    $uid = $userrow['uid'] ?? 0;
    if (!$uid) {
        exit(json_encode(['code' => -1, 'msg' => '未登录']));
    }
    
    $deleted = $DB->exec("DELETE FROM ozon_scheduled_products WHERE uid = ? AND status = 'pending'", [$uid]);
    
    exit(json_encode([
        'code' => 0,
        'msg' => '清空成功',
        'deleted_count' => $deleted
    ]));
break;

// ##################################################################
// # 定时上架功能模块 END
// ##################################################################
// --- 会员中心 ---

// 获取用户详细资料
case 'get_user_profile':
    $uid = $userrow['uid'] ?? 0;
    if ($uid === 0) {
        exit(json_encode(['code' => -3, 'msg' => '未登录']));
    }
    
    $userInfo = $DB->getRow("SELECT uid, username, email, phone, user_level, expiry_date, max_shops FROM ozon_user WHERE uid = ?", [$uid]);
    
    if (!$userInfo) {
        exit(json_encode(['code' => -1, 'msg' => '用户不存在']));
    }
    
    exit(json_encode(['code' => 1, 'data' => $userInfo, 'msg' => '获取成功']));
break;


// 兑换码激活
case 'redeem_code':
    $uid = $userrow['uid'] ?? 0;
    if ($uid === 0) {
        exit(json_encode(['code' => -3, 'msg' => '未登录']));
    }

    $code = isset($_POST['code']) ? trim($_POST['code']) : '';
    if (empty($code)) {
        exit(json_encode(['code' => -1, 'msg' => '请输入兑换码']));
    }

    $DB->beginTransaction();
    try {
        // 1. 查找并锁定兑换码
        $redemptionCode = $DB->getRow("SELECT * FROM ozon_redemption_codes WHERE code = :code AND status = 0 FOR UPDATE", [':code' => $code]);

        // 2. 验证兑换码
        if (!$redemptionCode) {
            throw new Exception('兑换码无效或已被使用');
        }

        // 3. 定义会员等级 -> 最大店铺数的映射关系
        $level_to_shops_map = [
            1 => 5,   // 普通用户
            2 => 10,  // VIP用户
            3 => 50,  // SVIP用户
            4 => 100   // 企业用户
        ];
        $new_level = (int)$redemptionCode['user_level'];
        $new_max_shops = $level_to_shops_map[$new_level] ?? 5; // 默认值

        // 4. 根据会员状态和兑换码等级，执行激活逻辑
        $now = new DateTime();
        $is_expired = true; // 默认已过期
        if ($userrow['expiry_date']) {
            try {
                $expiry = new DateTime($userrow['expiry_date']);
                if ($expiry >= $now) {
                    $is_expired = false;
                }
            } catch(Exception $e) {
                // date格式错误，也视为过期
            }
        }
        
        $is_same_level = (int)$userrow['user_level'] === $new_level;
        $days_to_add = (int)$redemptionCode['days'];
        
        $data_to_update = [];

        if ($is_same_level) {
            // 场景：同等级续费
            // 如果已过期，从今天算；如果未过期，从到期日算
            $start_date = $is_expired ? $now : $expiry;
            $new_expiry_date_obj = $start_date->add(new DateInterval("P{$days_to_add}D"));
            $data_to_update['expiry_date'] = $new_expiry_date_obj->format('Y-m-d H:i:s');
        
        } else {
            // 场景：跨等级兑换（升级或降级）
            if (!$is_expired) {
                throw new Exception('您的当前会员未过期，无法更换会员类型，请到期后再试。');
            }
            // 已过期用户，更换会员类型并重置天数
            $new_expiry_date_obj = $now->add(new DateInterval("P{$days_to_add}D"));
            $data_to_update['expiry_date'] = $new_expiry_date_obj->format('Y-m-d H:i:s');
            $data_to_update['user_level'] = $new_level;
            $data_to_update['max_shops'] = $new_max_shops;
        }
        
        $new_expiry_date_str = $data_to_update['expiry_date'];

        // 5. 更新用户表
        $DB->update('user', $data_to_update, ['uid' => $uid]);

        // 6. 更新兑换码表
        $DB->update('ozon_redemption_codes', [
            'status' => 1, 
            'used_at' => date('Y-m-d H:i:s'), 
            'used_by_uid' => $uid
        ], ['id' => $redemptionCode['id']]);

        // 7. 记录激活日志
        $DB->insert('ozon_activation_log', [
            'uid' => $uid,
            'code' => $code,
            'days_added' => $days_to_add,
            'new_expiry_date' => $new_expiry_date_str,
            'activated_at' => date('Y-m-d H:i:s')
        ]);

        $DB->commit();
        exit(json_encode([
            'code' => 1, 
            'msg' => '激活成功！会员权益已更新。',
            'data' => ['new_expiry_date' => $new_expiry_date_str]
        ]));

    } catch (Exception $e) {
        $DB->rollBack();
        exit(json_encode(['code' => -1, 'msg' => $e->getMessage()]));
    }
break;


// 获取激活日志
case 'get_activation_log':
    $uid = $userrow['uid'] ?? 0;
    if ($uid === 0) {
        exit(json_encode(['code' => -3, 'msg' => '未登录']));
    }

    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    $offset = ($page - 1) * $limit;

    $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_activation_log WHERE uid = :uid", [':uid' => $uid]);
    $logs = $DB->getAll("SELECT * FROM ozon_activation_log WHERE uid = :uid ORDER BY activated_at DESC LIMIT $offset, $limit", [':uid' => $uid]);

    if(!$logs) $logs = [];

    exit(json_encode([
        'code' => 0,
        'msg' => 'success',
        'count' => $total,
        'data' => $logs
    ]));
break;

// 生成兑换码（管理员功能）
/*case 'generate_codes':
    $uid = $islogin2 ? $userrow['uid'] : 0;
    
    // 检查用户登录状态
    if (!$islogin2 || !$userrow) {
        exit(json_encode(['code' => -3, 'msg' => '用户未登录']));
    }
    
    // 检查用户权限 - uid为10和10000的用户是管理员
    $adminUids = [10, 10000];
    if (!in_array(intval($userrow['uid']), $adminUids)) {
        exit(json_encode(['code' => -10, 'msg' => '无权操作，需要管理员权限']));
    }

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $user_level = isset($input['user_level']) ? intval($input['user_level']) : 0;
        $days = isset($input['days']) ? intval($input['days']) : 0;
        $num = isset($input['num']) ? intval($input['num']) : 0;
        $agent_uid = isset($input['agent_uid']) ? intval($input['agent_uid']) : null; // 新增代理uid参数
        
        if ($user_level <= 0 || $days <= 0 || $num <= 0) {
            throw new Exception("参数无效");
        }
        if ($num > 100) {
             throw new Exception("单次最多生成100个");
        }

        // 如果指定了代理uid，验证代理是否存在且有权限
        if ($agent_uid) {
            $agent = $DB->getRow("SELECT uid, username, agent_level FROM ozon_user WHERE uid = ?", [$agent_uid]);
            if (!$agent) {
                throw new Exception("指定的代理用户不存在");
            }
            // 可以在这里添加更多代理权限验证逻辑
        }

        $codes = [];
        $db_errors = [];
        for ($i = 0; $i < $num; $i++) {
            // 生成更可靠的唯一码
            $code = 'CDK' . strtoupper(bin2hex(random_bytes(8)));
            $insertData = [
                'code' => $code,
                'user_level' => $user_level,
                'days' => $days,
                'status' => 0
            ];
            
            // 如果指定了代理，添加代理字段
            if ($agent_uid) {
                $insertData['agent_uid'] = $agent_uid;
            }
            
            $res = $DB->insert('ozon_redemption_codes', $insertData);
            if ($res) {
                $codes[] = $code;
            } else {
                $db_errors[] = $DB->error();
            }
        }

        if (!empty($db_errors) && empty($codes)) {
            exit(json_encode([
                'code' => -1,
                'msg' => '数据库插入失败，请检查表结构和字段是否正确。',
                'errors' => $db_errors
            ]));
        }

        exit(json_encode([
            'code' => 1,
            'msg' => "成功生成 " . count($codes) . " 个兑换码",
            'data' => $codes
        ]));

    } catch(Exception $e) {
        exit(json_encode(['code' => -1, 'msg' => $e->getMessage()]));
    }
break;*/
//批量填写采购金额
case 'update_ozon_orders':
    if(empty($uid)) {
        exit(json_encode(['code' => -1, 'msg' => '未登录']));
    }
    $text_data = isset($_POST['text_data']) ? trim($_POST['text_data']) : '';
    if(empty($text_data)) {
        exit(json_encode(['code' => -2, 'msg' => '请提供订单号和金额文本']));
    }
    $lines = explode("\n", $text_data);
    $updated = 0;
    $not_found = [];
    $failed = [];
    foreach($lines as $line) {
        $line = trim($line);
        if(empty($line)) continue;
        // 过滤多余空格，确保只有一个空格分隔，且订单号可能包含'-'，只分割最后一个空格
        $lastSpacePos = strrpos($line, ' ');
        if ($lastSpacePos === false) {
            continue; // 跳过格式不正确的行
        }
        $posting_number = substr($line, 0, $lastSpacePos);
        $cost_str = substr($line, $lastSpacePos + 1);
        $posting_number = trim($posting_number);
        $cost = floatval(trim($cost_str));
        if($posting_number === '' || $cost <= 0) {
            continue; // 跳过无效数据
        }
        // 检查订单是否存在，先尝试精确匹配
        $exists = $DB->getRow('SELECT * FROM ozon_order WHERE posting_number = :posting_number AND uid = :uid', [':posting_number' => $posting_number, ':uid' => $uid]);
        if(!$exists) {
            // 尝试用'-'前缀部分模糊查询
            $prefix = explode('-', $posting_number)[0];
            if($prefix !== $posting_number){
                // 使用原生SQL进行模糊查询
                $sql = "SELECT * FROM ozon_order WHERE posting_number LIKE :posting_number AND uid = :uid";
                $rows = $DB->getAll($sql, [':posting_number' => $prefix . '%', ':uid' => $uid]);
                if(empty($rows)){
                    $not_found[] = $posting_number;
                    continue;
                } else {
                    $posting_numbers_to_use = array_column($rows, 'posting_number');
                }
            } else {
                $not_found[] = $posting_number;
                continue;
            }
        } else {
            $posting_numbers_to_use = [$posting_number];
        }
        // 执行更新（只更新当前用户的记录）
        $success_update = false;
        foreach($posting_numbers_to_use as $pn){
            error_log("update_ozon_orders: updating posting_number={$pn}, uid={$uid}, cost={$cost}");
            $update_result = $DB->update('order', ['cost' => $cost], ['posting_number' => $pn, 'uid' => $uid]);
            error_log("update_ozon_orders: update_result={$update_result}");
            if($update_result !== false){
                $success_update = true;
                $updated++;
            } else {
                error_log("update_ozon_orders: update failed for posting_number={$pn}, uid={$uid}, cost={$cost}, error=" . $DB->error());
            }
        }
        if($success_update){
            
        } else {
            $failed[] = $posting_number;
        }
        $DB->beginTransaction();
        try {
            foreach($posting_numbers_to_use as $pn){
                $update_result = $DB->update('order', ['cost' => $cost], ['posting_number' => $pn, 'uid' => $uid]);
                if($update_result === false) {
                    throw new Exception("更新失败: ".$DB->error());
                }
            }
            $DB->commit();
            $updated++;
        } catch (Exception $e) {
            $DB->rollBack();
            $failed[] = $posting_number;
            error_log("更新失败: ".$e->getMessage());
        }
    }
    $msg = "更新完成，成功: $updated 条";
    if(!empty($not_found)) {
        $msg .= "，未找到订单号: " . implode(', ', $not_found);
    }
    if(!empty($failed)) {
        $msg .= "，更新失败订单号: " . implode(', ', $updatedarray);
    }
    $result = [
        'code' => 0,
        'msg' => $msg,
        'updated' => $updated,
        'not_found' => $not_found,
        'failed' => $failed
    ];
    exit(json_encode($result));
break;
case 'list':// 新增产品列表接口，分页查询产品信息
    // 获取当前用户的产品列表
    $uid = $islogin2 ? $userrow['uid'] : 0;
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
    $offset = ($page - 1) * $limit;

    $total = $DB->getColumn("SELECT COUNT(*) FROM product_inventory WHERE uid = :uid", [':uid' => $uid]);
    $products = $DB->getAll("SELECT * FROM product_inventory WHERE uid = :uid ORDER BY updated_at DESC LIMIT $offset, $limit", [
        ':uid' => $uid
    ]);

    if(!$products) $products = [];

    foreach ($products as &$product) {
        $product['created_at'] = date('Y-m-d H:i:s', strtotime($product['created_at']));
        $product['updated_at'] = date('Y-m-d H:i:s', strtotime($product['updated_at']));
    }

    exit(json_encode([
        'code' => 0,
        'msg' => 'success',
        'count' => $total,
        'data' => $products
    ]));
break;
case 'save'://仓库库存项目
    $uid = $islogin2 ? $userrow['uid'] : 0;
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $product_type = isset($_POST['product_type']) ? daddslashes($_POST['product_type']) : 'single';
    $title = isset($_POST['title']) ? daddslashes($_POST['title']) : '';
    $status = isset($_POST['status']) ? daddslashes($_POST['status']) : '';
    $sku = isset($_POST['sku']) ? daddslashes($_POST['sku']) : '';
    
    // 平台SKU处理 - 支持JSON格式和旧格式
    $platform_sku = isset($_POST['platform_sku']) ? trim($_POST['platform_sku']) : '';
    $platform_sku_array = [];
    
    if (!empty($platform_sku)) {
        // 尝试解析JSON格式
        $decoded = json_decode($platform_sku, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            // 是有效的JSON数组
            $platform_sku_array = $decoded;
        } else {
            // 不是JSON格式，尝试按逗号分隔处理
            $platform_sku_array = explode(',', $platform_sku);
        }
        
        // 清理和验证SKU
        $clean_skus = [];
        foreach ($platform_sku_array as $sku_value) {
            $clean_sku = trim(daddslashes($sku_value));
            if (!empty($clean_sku)) {
                $clean_skus[] = $clean_sku;
            }
        }
        
        // 去重并转换为JSON
        $platform_sku = json_encode(array_unique($clean_skus));
    } else {
        $platform_sku = json_encode([]);
    }
    
    $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
    $remark = isset($_POST['remark']) ? daddslashes($_POST['remark']) : '';
    $warehouse = isset($_POST['warehouse']) ? daddslashes($_POST['warehouse']) : '';
    $weight = isset($_POST['weight']) ? floatval($_POST['weight']) : 0;
    $size_length = isset($_POST['size_length']) ? floatval($_POST['size_length']) : 0;
    $size_width = isset($_POST['size_width']) ? floatval($_POST['size_width']) : 0;
    $size_height = isset($_POST['size_height']) ? floatval($_POST['size_height']) : 0;
    $warehouse_stock = isset($_POST['warehouse_stock']) ? intval($_POST['warehouse_stock']) : 0;

    // 处理图片上传，改为接收图片链接
    $image_url = isset($_POST['image_url']) ? daddslashes($_POST['image_url']) : '';

    if($id > 0){
        // 更新产品
        $sql = "UPDATE product_inventory SET 
                    product_type = :product_type, 
                    title = :title, 
                    status = :status, 
                    sku = :sku, 
                    platform_sku = :platform_sku, 
                    price = :price, 
                    remark = :remark, 
                    warehouse = :warehouse, 
                    weight = :weight, 
                    size_length = :size_length, 
                    size_width = :size_width, 
                    size_height = :size_height, 
                    warehouse_stock = :warehouse_stock";
        
        if($image_url){
            $sql .= ", image_url = :image_url";
        }
        
        $sql .= " WHERE id = :id AND uid = :uid";
        
        $params = [
            ':product_type' => $product_type,
            ':title' => $title,
            ':status' => $status,
            ':sku' => $sku,
            ':platform_sku' => $platform_sku,
            ':price' => $price,
            ':remark' => $remark,
            ':warehouse' => $warehouse,
            ':weight' => $weight,
            ':size_length' => $size_length,
            ':size_width' => $size_width,
            ':size_height' => $size_height,
            ':warehouse_stock' => $warehouse_stock,
            ':id' => $id,
            ':uid' => $uid
        ];
        
        if($image_url){
            $params[':image_url'] = $image_url;
        }
        
        $res = $DB->exec($sql, $params);
        
        if($res){
            exit(json_encode(['code'=>0, 'msg'=>'更新成功']));
        } else {
            // 检查是否数据未变更
            $current = $DB->getRow("SELECT * FROM product_inventory WHERE id = :id", [':id' => $id]);
            $changed = false;
            foreach ($params as $key => $value) {
                if ($key !== ':id' && $key !== ':uid' && $current[ltrim($key, ':')] != $value) {
                    $changed = true;
                    break;
                }
            }
            
            if ($changed) {
                exit(json_encode(['code'=>-1, 'msg'=>'更新失败: ' . $DB->error()]));
            } else {
                exit(json_encode(['code'=>0, 'msg'=>'数据未变更']));
            }
        }
    } else {
        // 新增产品
        $sql = "INSERT INTO product_inventory 
                (uid, product_type, title, status, sku, platform_sku, price, remark, warehouse, weight, 
                size_length, size_width, size_height, warehouse_stock, image_url, created_at, updated_at) 
                VALUES 
                (:uid, :product_type, :title, :status, :sku, :platform_sku, :price, :remark, :warehouse, :weight, 
                :size_length, :size_width, :size_height, :warehouse_stock, :image_url, NOW(), NOW())";
        
        $params = [
            ':uid' => $uid,
            ':product_type' => $product_type,
            ':title' => $title,
            ':status' => $status,
            ':sku' => $sku,
            ':platform_sku' => $platform_sku,
            ':price' => $price,
            ':remark' => $remark,
            ':warehouse' => $warehouse,
            ':weight' => $weight,
            ':size_length' => $size_length,
            ':size_width' => $size_width,
            ':size_height' => $size_height,
            ':warehouse_stock' => $warehouse_stock,
            ':image_url' => $image_url
        ];
        
        $res = $DB->exec($sql, $params);
        
        if($res){
            exit(json_encode(['code'=>0, 'msg'=>'添加成功']));
        } else {
            $error = $DB->error();
            error_log("添加产品失败: " . $error);
            exit(json_encode(['code'=>-1, 'msg'=>'添加失败: ' . $error]));
        }
    }
break;
case 'delete'://仓库产品删除
    $uid = $islogin2 ? $userrow['uid'] : 0;
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if($id <= 0){
        exit(json_encode(['code'=>-1, 'msg'=>'无效的产品ID']));
    }
    $res = $DB->exec("DELETE FROM product_inventory WHERE id=:id AND uid=:uid", [':id'=>$id, ':uid'=>$uid]);
    if($res){
        exit(json_encode(['code'=>0, 'msg'=>'删除成功']));
    } else {
        exit(json_encode(['code'=>-1, 'msg'=>'删除失败']));
    }
break;
case 'warehouse_stock_log_list'://仓库出入库日志
    $product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
    $sku = isset($_GET['sku']) ? daddslashes($_GET['sku']) : '';
    $uid = $userrow['uid'] ?? 0;

    if ($product_id > 0) {
        $logs = $DB->getAll("SELECT * FROM warehouse_stock_log WHERE product_id = :product_id ORDER BY created_at DESC", [':product_id' => $product_id]);
    } elseif ($sku !== '') {
        // Find product IDs by SKU for current user
        $products = $DB->getAll("SELECT id FROM product_inventory WHERE sku = :sku AND uid = :uid", [':sku' => $sku, ':uid' => $uid]);
        if (!$products) {
            exit(json_encode(['code' => 0, 'data' => []]));
        }
        $product_ids = array_column($products, 'id');
        $placeholders = implode(',', array_fill(0, count($product_ids), '?'));
        $logs = $DB->getAll("SELECT * FROM warehouse_stock_log WHERE product_id IN ($placeholders) ORDER BY created_at DESC", $product_ids);
    } else {
        // Return all logs for current user
        $logs = $DB->getAll("SELECT * FROM warehouse_stock_log WHERE uid = :uid ORDER BY created_at DESC", [':uid' => $uid]);
    }
    exit(json_encode(['code' => 0, 'data' => $logs]));
break;
case 'warehouse_stock_log_add'://出入库日志添加
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $change_quantity = isset($_POST['change_quantity']) ? intval($_POST['change_quantity']) : 0;
    $change_type = isset($_POST['change_type']) ? $_POST['change_type'] : '';
    $remark = isset($_POST['remark']) ? daddslashes($_POST['remark']) : '';
    $uid = $userrow['uid'] ?? 0;

    if ($product_id <= 0 || $change_quantity == 0 || !in_array($change_type, ['in', 'out'])) {
        exit(json_encode(['code' => -1, 'msg' => '参数错误']));
    }

    $current_stock = $DB->getColumn("SELECT warehouse_stock FROM product_inventory WHERE id = :id", [':id' => $product_id]);
    if ($current_stock === false) {
        exit(json_encode(['code' => -1, 'msg' => '产品不存在']));
    }
    if ($change_type === 'out' && $change_quantity > $current_stock) {
        exit(json_encode(['code' => -1, 'msg' => '出库数量不能大于当前库存']));
    }

    $DB->beginTransaction();
    try {
        $res = $DB->exec("INSERT INTO warehouse_stock_log (product_id, change_quantity, change_type, remark, uid) VALUES (:product_id, :change_quantity, :change_type, :remark, :uid)", [
            ':product_id' => $product_id,
            ':change_quantity' => $change_quantity,
            ':change_type' => $change_type,
            ':remark' => $remark,
            ':uid' => $uid
        ]);
        if (!$res) {
            throw new Exception('添加日志失败');
        }

        // 更新产品仓库库存数量
        if ($change_type === 'in') {
            $new_stock = $current_stock + $change_quantity;
        } else {
            $new_stock = $current_stock - $change_quantity;
        }
        $update_res = $DB->exec("UPDATE product_inventory SET warehouse_stock = :warehouse_stock WHERE id = :id", [
            ':warehouse_stock' => $new_stock,
            ':id' => $product_id
        ]);
        if (!$update_res) {
            throw new Exception('更新库存失败');
        }

        $DB->commit();
        exit(json_encode(['code' => 0, 'msg' => '添加成功']));
    } catch (Exception $e) {
        $DB->rollBack();
        exit(json_encode(['code' => -1, 'msg' => $e->getMessage()]));
    }
break;
//出入库日志删除
case 'warehouse_stock_log_delete':
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $uid = $userrow['uid'] ?? 0;
    if ($id <= 0) {
        exit(json_encode(['code' => -1, 'msg' => '无效的日志ID']));
    }
    $res = $DB->exec("DELETE FROM warehouse_stock_log WHERE id = :id", [':id' => $id]);
    if ($res) {
        exit(json_encode(['code' => 0, 'msg' => '删除成功']));
    } else {
        exit(json_encode(['code' => -1, 'msg' => '删除失败']));
    }
break;

//订单列表查询库存关联SKU
//订单列表查询库存关联SKU,也得加个匹配货号，要么换成货号
case 'query_platform_sku':
    $uid = $islogin2 ? $userrow['uid'] : 0;
    if (!$uid) {
        exit(json_encode(['code' => -1, 'msg' => '未登录']));
    }
    $sku_list = isset($_GET['sku_list']) ? trim($_GET['sku_list']) : '';
    if (empty($sku_list)) {
        exit(json_encode(['code' => -1, 'msg' => '缺少sku_list参数']));
    }
    $skus = explode(',', $sku_list);
    $skus = array_map('trim', $skus);
    $skus = array_filter($skus);
    if (empty($skus)) {
        exit(json_encode(['code' => -1, 'msg' => 'sku_list参数无效']));
    }
    // 构造模糊匹配SQL，platform_sku字段包含任一sku
    $conditions = [];
    $params = [':uid' => $uid];
    foreach ($skus as $index => $sku) {
        $key = ":sku$index";
        $conditions[] = "platform_sku LIKE CONCAT('%', $key, '%')";
        $params[$key] = $sku;
    }
    $where = implode(' OR ', $conditions);
    $sql = "SELECT * FROM product_inventory WHERE uid = :uid AND ($where)";
    $result = $DB->getAll($sql, $params);
    if (!$result) {
        exit(json_encode(['code' => 0, 'msg' => '无匹配记录', 'data' => []]));
    }
    exit(json_encode(['code' => 0, 'msg' => '查询成功', 'data' => $result]));
break;

case 'get_platform_sku':
    $sku = isset($_GET['sku']) ? trim($_GET['sku']) : '';
    $uid = $userrow['uid'] ?? 0;
    if (empty($sku)) {
        exit(json_encode(['code' => -1, 'msg' => '缺少SKU参数']));
    }
    if (empty($uid)) {
        exit(json_encode(['code' => -1, 'msg' => '未登录']));
    }
    $row = $DB->getRow("SELECT platform_sku FROM product_inventory WHERE sku = :sku AND uid = :uid LIMIT 1", [
        ':sku' => $sku,
        ':uid' => $uid
    ]);
    if ($row) {
        exit(json_encode(['code' => 0, 'platform_sku' => $row['platform_sku']]));
    } else {
        exit(json_encode(['code' => 0, 'platform_sku' => '[]']));
    }
    break;

//调用库存填写采购

//调用库存填写采购
case 'update_purchase_info_inventory':
    $posting_number = isset($_POST['posting_number']) ? trim($_POST['posting_number']) : (isset($_GET['posting_number']) ? trim($_GET['posting_number']) : '');
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : (isset($_GET['product_id']) ? intval($_GET['product_id']) : 0);
    $change_quantity = isset($_POST['change_quantity']) ? intval($_POST['change_quantity']) : (isset($_GET['change_quantity']) ? intval($_GET['change_quantity']) : 0);
    $change_type = isset($_POST['change_type']) ? $_POST['change_type'] : (isset($_GET['change_type']) ? $_GET['change_type'] : '');
    $remark = isset($_POST['remark']) ? daddslashes($_POST['remark']) : (isset($_GET['remark']) ? daddslashes($_GET['remark']) : '');
    $uid = $userrow['uid'] ?? 0;
    // 获取特定的SKU（如果提供）
    $specific_sku = isset($_POST['sku']) ? trim($_POST['sku']) : (isset($_GET['sku']) ? trim($_GET['sku']) : '');

    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '缺少订单号']));
    }
    if ($product_id <= 0 || $change_quantity == 0 || !in_array($change_type, ['in', 'out'])) {
        exit(json_encode(['code' => -1, 'msg' => '参数错误']));
    }

    // 查询该订单信息
    $order = $DB->getRow("SELECT sku, products FROM ozon_order WHERE posting_number = :posting_number LIMIT 1", [
        ':posting_number' => $posting_number
    ]);
    if (!$order) {
        exit(json_encode(['code' => -1, 'msg' => '订单不存在']));
    }
    
    // 确定要使用的SKU
    $sku = $specific_sku ?: $order['sku'];
    
    // 检查是否有多个商品
    $has_multiple_products = !empty($order['products']);
    $products_info = $has_multiple_products ? json_decode($order['products'], true) : null;

    // 查询产品库存
    $product = $DB->getRow("SELECT price, warehouse_stock FROM product_inventory WHERE id = :id LIMIT 1", [
        ':id' => $product_id
    ]);
    if (!$product) {
        exit(json_encode(['code' => -1, 'msg' => '产品库存不存在']));
    }
    $current_stock = $product['warehouse_stock'];
    $price = $product['price'];

    if ($change_type === 'out' && $change_quantity > $current_stock) {
        exit(json_encode(['code' => -1, 'msg' => '出库数量不能大于当前库存']));
    }

    $DB->beginTransaction();
    try {
        // 添加库存变动日志
        $res = $DB->exec("INSERT INTO warehouse_stock_log (product_id, change_quantity, change_type, remark, uid) VALUES (:product_id, :change_quantity, :change_type, :remark, :uid)", [
            ':product_id' => $product_id,
            ':change_quantity' => $change_quantity,
            ':change_type' => $change_type,
            ':remark' => $remark,
            ':uid' => $uid
        ]);
        if (!$res) {
            throw new Exception('添加日志失败');
        }

        // 更新产品库存
        if ($change_type === 'in') {
            $new_stock = $current_stock + $change_quantity;
        } else {
            $new_stock = $current_stock - $change_quantity;
        }
        $update_res = $DB->exec("UPDATE product_inventory SET warehouse_stock = :warehouse_stock WHERE id = :id", [
            ':warehouse_stock' => $new_stock,
            ':id' => $product_id
        ]);
        if (!$update_res) {
            throw new Exception('更新库存失败');
        }
 
        // 计算采购金额
        $cost = $change_quantity * $price;
        $purchase_date = date('Y-m-d');
        $courierNumber = $warehouse;
        
        // 更新订单采购金额和采购日期和仓库位置=国内仓库
        $update_data = [
            ':cost' => $cost,
            ':purchase_date' => $purchase_date,
            ':Outbound_data' => $purchase_date,
            ':posting_number' => $posting_number
        ];
        
        if (!empty($courierNumber)) {
            $update_data[':warehouse'] = $courierNumber;
            $sql = "UPDATE ozon_order SET cost = :cost, purchase_date = :purchase_date, courierNumber = :warehouse WHERE posting_number = :posting_number";
        } else {
            $sql = "UPDATE ozon_order SET cost = :cost, purchase_date = :purchase_date,Outbound_data = :Outbound_data WHERE posting_number = :posting_number";
        }
        
        $res2 = $DB->exec($sql, $update_data);
        if (!$res2) {
            throw new Exception('更新采购信息失败');
        }

        $DB->commit();
        exit(json_encode(['code' => 0, 'msg' => '调用库存成功，采购信息已更新']));
    } catch (Exception $e) {
        $DB->rollBack();
        exit(json_encode(['code' => -1, 'msg' => $e->getMessage()]));
    }
    break;



//调用库存填写采购 - 多商品版本
case 'update_purchase_info_inventory_multi':
    $posting_number = isset($_POST['posting_number']) ? trim($_POST['posting_number']) : (isset($_GET['posting_number']) ? trim($_GET['posting_number']) : '');
    $uid = $userrow['uid'] ?? 0;

    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '缺少订单号']));
    }

    // 查询该订单信息
    $order = $DB->getRow("SELECT sku, products FROM ozon_order WHERE posting_number = :posting_number LIMIT 1", [
        ':posting_number' => $posting_number
    ]);
    if (!$order) {
        exit(json_encode(['code' => -1, 'msg' => '订单不存在']));
    }

    // 解析订单中的多个商品
    $products_info = [];
    if (!empty($order['products'])) {
        $products_info = json_decode($order['products'], true);
        if (!is_array($products_info) || empty($products_info)) {
            exit(json_encode(['code' => -1, 'msg' => '订单商品信息格式错误']));
        }
    } else {
        exit(json_encode(['code' => -1, 'msg' => '订单中没有商品信息']));
    }

    $DB->beginTransaction();
    try {
        $total_cost = 0;
        $processed_products = [];
        $warehouse = ''; // 仓库信息

        // 遍历每个商品进行库存处理
        foreach ($products_info as $product) {
            $sku = $product['sku'];
            $quantity = $product['quantity'];
            $product_name = $product['name'] ?? '';

            // 查找对应的库存产品
            $inventory_product = $DB->getRow("SELECT id, price, warehouse_stock, title FROM product_inventory WHERE sku = :sku AND uid = :uid LIMIT 1", [
                ':sku' => $sku,
                ':uid' => $uid
            ]);

            if (!$inventory_product) {
                throw new Exception("商品 SKU: {$sku} 在库存中不存在");
            }

            $product_id = $inventory_product['id'];
            $current_stock = $inventory_product['warehouse_stock'];
            $price = $inventory_product['price'];

            // 检查库存是否足够
            if ($quantity > $current_stock) {
                throw new Exception("商品 {$inventory_product['title']} (SKU: {$sku}) 库存不足，当前库存: {$current_stock}，需要: {$quantity}");
            }

            // 计算新库存
            $new_stock = $current_stock - $quantity;

            // 更新产品库存
            $update_res = $DB->exec("UPDATE product_inventory SET warehouse_stock = :warehouse_stock WHERE id = :id", [
                ':warehouse_stock' => $new_stock,
                ':id' => $product_id
            ]);
            if (!$update_res) {
                throw new Exception("更新商品 {$inventory_product['title']} 库存失败");
            }

            // 添加库存变动日志
            $remark = "订单 {$posting_number} 多商品出库 - {$product_name}";
            $log_res = $DB->exec("INSERT INTO warehouse_stock_log (product_id, change_quantity, change_type, remark, uid) VALUES (:product_id, :change_quantity, :change_type, :remark, :uid)", [
                ':product_id' => $product_id,
                ':change_quantity' => $quantity,
                ':change_type' => 'out',
                ':remark' => $remark,
                ':uid' => $uid
            ]);
            if (!$log_res) {
                throw new Exception("添加商品 {$inventory_product['title']} 库存日志失败");
            }

            // 计算该商品的采购成本
            $product_cost = $quantity * $price;
            $total_cost += $product_cost;

            // 记录处理的商品信息
            $processed_products[] = [
                'sku' => $sku,
                'name' => $product_name,
                'quantity' => $quantity,
                'price' => $price,
                'cost' => $product_cost,
                'stock_before' => $current_stock,
                'stock_after' => $new_stock
            ];
        }

        // 更新订单采购金额和采购日期
        $purchase_date = date('Y-m-d');
        $update_data = [
            ':cost' => $total_cost,
            ':purchase_date' => $purchase_date,
            ':posting_number' => $posting_number
        ];

        if (!empty($warehouse)) {
            $update_data[':warehouse'] = $warehouse;
            $sql = "UPDATE ozon_order SET cost = :cost, purchase_date = :purchase_date, courierNumber = :warehouse WHERE posting_number = :posting_number";
        } else {
            $sql = "UPDATE ozon_order SET cost = :cost, purchase_date = :purchase_date WHERE posting_number = :posting_number";
        }

        $res2 = $DB->exec($sql, $update_data);
        if (!$res2) {
            throw new Exception('更新订单采购信息失败');
        }

        $DB->commit();

        // 返回成功信息
        $success_msg = "多商品库存调用成功！\n";
        $success_msg .= "处理商品数量: " . count($processed_products) . "\n";
        $success_msg .= "总采购成本: ¥" . number_format($total_cost, 2) . "\n";
        $success_msg .= "详细信息:\n";
        foreach ($processed_products as $item) {
            $success_msg .= "- {$item['name']} (SKU: {$item['sku']}) 数量: {$item['quantity']} 成本: ¥" . number_format($item['cost'], 2) . "\n";
        }

        exit(json_encode([
            'code' => 0,
            'msg' => $success_msg,
            'data' => [
                'total_cost' => $total_cost,
                'processed_products' => $processed_products,
                'purchase_date' => $purchase_date
            ]
        ]));

    } catch (Exception $e) {
        $DB->rollBack();
        exit(json_encode(['code' => -1, 'msg' => $e->getMessage()]));
    }
    break;


//统计工作空间数据
case 'dashboard_summary':
    // 返回总订单量、销售额、净利润、采购金额等汇总数据及趋势数据，支持时间范围
    $uid = $GLOBALS['uid'];
    $result = [];

    // 总订单量
    $totalOrders = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE  uid = ? AND status!='awaiting_verification' AND status!='cancelled_from_split_pending'", [$uid]);
    $result['totalOrders'] = $totalOrders ?: 0;

    // 销售额
    $totalSales = $DB->getColumn("SELECT SUM(price) FROM ozon_order WHERE uid = ? AND status!='awaiting_verification' AND status!='cancelled_from_split_pending'", [$uid]);
    $result['totalSales'] = $totalSales ?: 0;

    // 净利润
    $totalProfit = $DB->getColumn("SELECT SUM(profit) FROM ozon_order WHERE uid = ? AND status!='awaiting_verification' AND status!='cancelled_from_split_pending'", [$uid]);
    $result['totalProfit'] = $totalProfit ?: 0;

    // 采购金额
    $totalPurchase = $DB->getColumn("SELECT SUM(cost) FROM ozon_order WHERE uid = ? AND status!='awaiting_verification' AND status!='cancelled_from_split_pending'", [$uid]);
    $result['totalPurchase'] = $totalPurchase ?: 0;

    // 获取时间范围参数，默认7天
    $range = isset($_GET['range']) ? $_GET['range'] : '7d';

    // 计算起始日期和结束日期
    $endDate = date('Y-m-d');
    switch ($range) {
        case 'today':
            $startDate = $endDate;
            $daysCount = 1;
            break;
        case '7d':
            $startDate = date('Y-m-d', strtotime('-6 days'));
            $daysCount = 7;
            break;
        case '14d':
            $startDate = date('Y-m-d', strtotime('-13 days'));
            $daysCount = 14;
            break;
        case 'month':
            $startDate = date('Y-m-01');
            $daysCount = (int)date('d');
            break;
        default:
            $startDate = date('Y-m-d', strtotime('-6 days'));
            $daysCount = 7;
    }

    $dates = [];
    $sales = [];
    $purchase = [];
    $orders = [];

    $sql = "SELECT DATE(in_process_at) as date,
                   COUNT(*) as order_count,
                   SUM(price) as sales_sum,
                   SUM(cost) as purchase_sum,
                   SUM(profit) as profit_sum
            FROM ozon_order
            WHERE uid = ?
              AND status!='cancelled'
              AND status!='awaiting_verification'
              AND status!='cancelled_from_split_pending'
              AND DATE(in_process_at) BETWEEN ? AND ?
            GROUP BY DATE(in_process_at)
            ORDER BY DATE(in_process_at) ASC";

    $rows = $DB->getAll($sql, [$uid, $startDate, $endDate]);

    $dateMap = [];
    foreach ($rows as $row) {
        $dateMap[$row['date']] = $row;
    }

    for ($i = 0; $i < $daysCount; $i++) {
        $date = date('Y-m-d', strtotime("-" . ($daysCount - 1 - $i) . " days"));
        $dates[] = $date;
        if (isset($dateMap[$date])) {
            $sales[] = (float)$dateMap[$date]['sales_sum'];
            $purchase[] = (float)$dateMap[$date]['purchase_sum'];
            $orders[] = (int)$dateMap[$date]['order_count'];
        } else {
            $sales[] = 0;
            $purchase[] = 0;
            $orders[] = 0;
        }
    }

    // Calculate trends for yesterday, week-over-week, and month-over-month
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    $weekAgo = date('Y-m-d', strtotime('-7 days'));
    $monthAgo = date('Y-m-d', strtotime('-1 month'));

    $getValue = function($date, $field) use ($dateMap) {
        return isset($dateMap[$date]) ? (float)$dateMap[$date][$field] : 0;
    };

    // Yesterday vs day before yesterday
    $yesterdayOrders = $getValue($yesterday, 'order_count');
    $dayBeforeOrders = $getValue(date('Y-m-d', strtotime('-2 days')), 'order_count');
    $ordersTrendYesterday = $dayBeforeOrders != 0 ? (($yesterdayOrders - $dayBeforeOrders) / $dayBeforeOrders) * 100 : 0;

    $yesterdaySales = $getValue($yesterday, 'sales_sum');
    $dayBeforeSales = $getValue(date('Y-m-d', strtotime('-2 days')), 'sales_sum');
    $salesTrendYesterday = $dayBeforeSales != 0 ? (($yesterdaySales - $dayBeforeSales) / $dayBeforeSales) * 100 : 0;

    $yesterdayPurchase = $getValue($yesterday, 'purchase_sum');
    $dayBeforePurchase = $getValue(date('Y-m-d', strtotime('-2 days')), 'purchase_sum');
    $purchaseTrendYesterday = $dayBeforePurchase != 0 ? (($yesterdayPurchase - $dayBeforePurchase) / $dayBeforePurchase) * 100 : 0;

    // Week-over-week (compare last 7 days to previous 7 days)
    $lastWeekStart = date('Y-m-d', strtotime('-7 days'));
    $lastWeekEnd = $yesterday;
    $prevWeekStart = date('Y-m-d', strtotime('-14 days'));
    $prevWeekEnd = date('Y-m-d', strtotime('-8 days'));

    $sqlSum = function($field, $start, $end) use ($DB, $uid) {
        return (float)$DB->getColumn("SELECT SUM($field) FROM ozon_order WHERE uid = ? AND status!='cancelled' AND status!='awaiting_verification' AND status!='cancelled_from_split_pending' AND DATE(in_process_at) BETWEEN ? AND ?", [$uid, $start, $end]) ?: 0;
    };

    $ordersLastWeek = $sqlSum('1', $lastWeekStart, $lastWeekEnd);
    $ordersPrevWeek = $sqlSum('1', $prevWeekStart, $prevWeekEnd);
    $ordersTrendWeek = $ordersPrevWeek != 0 ? (($ordersLastWeek - $ordersPrevWeek) / $ordersPrevWeek) * 100 : 0;

    $salesLastWeek = $sqlSum('price', $lastWeekStart, $lastWeekEnd);
    $salesPrevWeek = $sqlSum('price', $prevWeekStart, $prevWeekEnd);
    $salesTrendWeek = $salesPrevWeek != 0 ? (($salesLastWeek - $salesPrevWeek) / $salesPrevWeek) * 100 : 0;

    $purchaseLastWeek = $sqlSum('cost', $lastWeekStart, $lastWeekEnd);
    $purchasePrevWeek = $sqlSum('cost', $prevWeekStart, $prevWeekEnd);
    $purchaseTrendWeek = $purchasePrevWeek != 0 ? (($purchaseLastWeek - $purchasePrevWeek) / $purchasePrevWeek) * 100 : 0;

    // Month-over-month (compare current month to previous month)
    $currentMonthStart = date('Y-m-01');
    $previousMonthStart = date('Y-m-01', strtotime('-1 month'));
    $previousMonthEnd = date('Y-m-t', strtotime('-1 month'));

    $ordersCurrentMonth = $sqlSum('1', $currentMonthStart, $endDate);
    $ordersPreviousMonth = $sqlSum('1', $previousMonthStart, $previousMonthEnd);
    $ordersTrendMonth = $ordersPreviousMonth != 0 ? (($ordersCurrentMonth - $ordersPreviousMonth) / $ordersPreviousMonth) * 100 : 0;

    $salesCurrentMonth = $sqlSum('price', $currentMonthStart, $endDate);
    $salesPreviousMonth = $sqlSum('price', $previousMonthStart, $previousMonthEnd);
    $salesTrendMonth = $salesPreviousMonth != 0 ? (($salesCurrentMonth - $salesPreviousMonth) / $salesPreviousMonth) * 100 : 0;

    $purchaseCurrentMonth = $sqlSum('cost', $currentMonthStart, $endDate);
    $purchasePreviousMonth = $sqlSum('cost', $previousMonthStart, $previousMonthEnd);
    $purchaseTrendMonth = $purchasePreviousMonth != 0 ? (($purchaseCurrentMonth - $purchasePreviousMonth) / $purchasePreviousMonth) * 100 : 0;

    $result['dates'] = $dates;
    $result['sales'] = $sales;
    $result['purchase'] = $purchase;
    $result['orders'] = $orders;

    $result['trends'] = [
        'orders' => [
            'yesterday' => round($ordersTrendYesterday, 2),
            'week' => round($ordersTrendWeek, 2),
            'month' => round($ordersTrendMonth, 2)
        ],
        'sales' => [
            'yesterday' => round($salesTrendYesterday, 2),
            'week' => round($salesTrendWeek, 2),
            'month' => round($salesTrendMonth, 2)
        ],
        'purchase' => [
            'yesterday' => round($purchaseTrendYesterday, 2),
            'week' => round($purchaseTrendWeek, 2),
            'month' => round($purchaseTrendMonth, 2)
        ]
    ];

    exit(json_encode(['code' => 0, 'data' => $result]));
break;
case 'dashboard_rankings':
    // 返回销售额、订单量、利润的排行数据
   
    $metric = isset($_GET['metric']) ? $_GET['metric'] : 'sales';
    $range = isset($_GET['range']) ? $_GET['range'] : 'today';

    // 计算时间范围
    $startDate = null;
    $endDate = date('Y-m-d 23:59:59');
    switch ($range) {
        case 'today':
            $startDate = date('Y-m-d 00:00:00');
            break;
        case '7d':
            $startDate = date('Y-m-d 00:00:00', strtotime('-6 days'));
            break;
        case '14d':
            $startDate = date('Y-m-d 00:00:00', strtotime('-13 days'));
            break;
        case 'month':
            $startDate = date('Y-m-01 00:00:00');
            break;
        default:
            $startDate = date('Y-m-d 00:00:00');
    }

    $rankings = [];

    // Get store IDs for the user
    $stores = $DB->getAll("SELECT id FROM ozon_store WHERE uid = ?", [$uid]);
    if (!$stores) {
        exit(json_encode(['code' => 0, 'data' => []]));
    }
    $storeIds = array_column($stores, 'id');
    $placeholders = implode(',', array_fill(0, count($storeIds), '?'));

    if ($metric === 'sales') {
        $sql = "SELECT s.storename AS name, SUM(o.price) AS value 
                FROM ozon_order o 
                JOIN ozon_store s ON o.storeid = s.id 
                WHERE o.uid = ? 
                  AND o.storeid IN ($placeholders) 
                  AND o.status!='cancelled'
                  AND o.status!='awaiting_verification'
                  AND o.status!='cancelled_from_split_pending'
                  AND o.in_process_at BETWEEN ? AND ? 
                GROUP BY s.storename 
                ORDER BY value DESC 
                LIMIT 10";
    } elseif ($metric === 'orders') {
        $sql = "SELECT s.storename AS name, COUNT(*) AS value 
                FROM ozon_order o 
                JOIN ozon_store s ON o.storeid = s.id 
                WHERE o.uid = ? 
                  AND o.storeid IN ($placeholders)
                  AND o.status!='cancelled'
                  AND o.status!='awaiting_verification'
                  AND o.status!='cancelled_from_split_pending'
                  AND o.in_process_at BETWEEN ? AND ? 
                GROUP BY s.storename 
                ORDER BY value DESC 
                LIMIT 10";
    } elseif ($metric === 'profit') {
        $sql = "SELECT s.storename AS name, SUM(o.profit) AS value 
                FROM ozon_order o 
                JOIN ozon_store s ON o.storeid = s.id 
                WHERE o.uid = ? 
                  AND o.storeid IN ($placeholders)
                  AND o.status!='cancelled'
                  AND o.status!='awaiting_verification'
                  AND o.status!='cancelled_from_split_pending'
                  AND o.in_process_at BETWEEN ? AND ? 
                GROUP BY s.storename 
                ORDER BY value DESC 
                LIMIT 10";
    } elseif ($metric === 'purchase'){
        $sql = "SELECT s.storename AS name, SUM(o.cost) AS value 
                FROM ozon_order o 
                JOIN ozon_store s ON o.storeid = s.id 
                WHERE o.uid = ? 
                  AND o.storeid IN ($placeholders)
                  AND o.status!='cancelled'
                  AND o.status!='awaiting_verification'
                  AND o.status!='cancelled_from_split_pending'
                  AND o.in_process_at BETWEEN ? AND ? 
                GROUP BY s.storename 
                ORDER BY value DESC 
                LIMIT 10";
    } else {
        exit(json_encode(['code' => -1, 'msg' => '无效的指标']));
    }
    $params = array_merge([$uid], $storeIds, [$startDate, $endDate]);
    error_log("dashboard_rankings called with uid=$uid, metric=$metric, range=$range, startDate=$startDate, endDate=$endDate");
    error_log("SQL: $sql");
    error_log("Params: " . json_encode($params));
    $rankings = $DB->getAll($sql, $params);
    error_log("dashboard_rankings query result count: " . count($rankings));
    error_log("dashboard_rankings query result sample: " . json_encode(array_slice($rankings, 0, 3)));
    if (!$rankings) {
        $rankings = [];
    }

    exit(json_encode(['code' => 0, 'data' => $rankings]));
break;
case 'dashboard_table_data':
    // 返回数据表格内容，支持时间范围过滤
    $range = isset($_GET['range']) ? $_GET['range'] : null;
    $startDate = isset($_GET['startDate']) ? $_GET['startDate'] : null;
    $endDate = isset($_GET['endDate']) ? $_GET['endDate'] : null;

    $uid = $GLOBALS['uid'];

    // 计算时间范围
    if ($range && !$startDate && !$endDate) {
        $endDate = date('Y-m-d 23:59:59');
        switch ($range) {
            case 'today':
                $startDate = date('Y-m-d 00:00:00');
                break;
            case '7d':
                $startDate = date('Y-m-d 00:00:00', strtotime('-6 days'));
                break;
            case '14d':
                $startDate = date('Y-m-d 00:00:00', strtotime('-13 days'));
                break;
            case 'month':
                $startDate = date('Y-m-01 00:00:00');
                break;
            default:
                $startDate = date('Y-m-d 00:00:00', strtotime('-6 days'));
        }
    } elseif ($startDate && $endDate) {
        // Append time to dates if not present
        if (strlen($startDate) === 10) {
            $startDate .= ' 00:00:00';
        }
        if (strlen($endDate) === 10) {
            $endDate .= ' 23:59:59';
        }
    } else {
        // Default to last 7 days
        $endDate = date('Y-m-d 23:59:59');
        $startDate = date('Y-m-d 00:00:00', strtotime('-6 days'));
    }

    // Query data grouped by date
    $sql = "SELECT DATE(in_process_at) as date,
                   COUNT(*) as orders,
                   SUM(price) as sales,
                   SUM(cost) as purchase,
                   ROUND(SUM(profit) / NULLIF(SUM(price), 0) * 100, 2) as profitRate
            FROM ozon_order
            WHERE uid = ?
              AND status!='cancelled'
              AND status!='awaiting_verification'
              AND status!='cancelled_from_split_pending'
              AND in_process_at BETWEEN ? AND ?
            GROUP BY DATE(in_process_at)
            ORDER BY DATE(in_process_at) ASC";

    $rows = $DB->getAll($sql, [$uid, $startDate, $endDate]);

    // Calculate trend (percentage change of sales compared to previous day)
    $result = [];
    $prevSales = null;
    foreach ($rows as $row) {
        $trend = 0;
        if ($prevSales !== null && $prevSales != 0) {
            $trend = (($row['sales'] - $prevSales) / $prevSales) * 100;
        }
        $result[] = [
            'date' => $row['date'],
            'orders' => (int)$row['orders'],
            'sales' => (float)$row['sales'],
            'purchase' => (float)$row['purchase'],
            'profitRate' => round($row['profitRate'], 2),
            'trend' => round($trend, 2)
        ];
        $prevSales = $row['sales'];
    }

    exit(json_encode(['code' => 0, 'data' => $result]));
break;
//首页数据源统计
case 'todo_metrics':
    try {
        $today = date('Y-m-d');
        // 查询今日采购数量和金额
        $todayPurchaseSql = "SELECT 
                                COUNT(*) as quantity,
                                COALESCE(SUM(cost), 0) as amount
                            FROM ozon_order 
                            WHERE DATE(Purchase_date) = :today
                              AND uid = :uid
                              AND status != 'cancelled'
                              AND cost > 0";
        $todayPurchase = $DB->getRow($todayPurchaseSql, [':today' => $today, ':uid' => $uid]);

        // 查询延迟发货
        $delayedShipmentSql = "SELECT COUNT(*) FROM ozon_order WHERE uid = :uid AND shipment_date < NOW() AND status= 'awaiting_deliver'";
        
        $delayedShipment = $DB->getColumn($delayedShipmentSql, [':uid' => $uid]);


        // 查询剩余发货小于1天
        $remainingDeliverySql = "SELECT COUNT(*) FROM ozon_order WHERE uid = :uid AND status = 'awaiting_packaging' AND shipment_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 1 DAY)";
        $remainingDelivery = $DB->getColumn($remainingDeliverySql, [':uid' => $uid]);
    
    
        // 查询待采购，改为判断采购cost是否为空
        $pendingPurchaseSql = "SELECT COUNT(*) FROM ozon_order WHERE uid = :uid AND (cost IS NULL OR cost = '') AND status != 'cancelled' AND status != 'awaiting_verification' AND status != 'cancelled_from_split_pending'";
        $pendingPurchase = $DB->getColumn($pendingPurchaseSql, [':uid' => $uid]);

        // 查询等待发货
        $waitingShipmentSql = "SELECT COUNT(*) FROM ozon_order WHERE uid = :uid AND status='awaiting_deliver'";
        $waitingShipment = $DB->getColumn($waitingShipmentSql, [':uid' => $uid]);

        // 查询运输中
        $inTransitSql = "SELECT COUNT(*) FROM ozon_order WHERE uid = :uid AND status = 'delivering'";
        $inTransit = $DB->getColumn($inTransitSql, [':uid' => $uid]);

        // 查询采集箱（改成ozon_production表的数量）
        $collectionBoxSql = "SELECT COUNT(*) FROM ozon_production WHERE uid = :uid";
        $collectionBox = $DB->getColumn($collectionBoxSql, [':uid' => $uid]);

        $data = [
        'todayPurchaseQuantity' => $todayPurchase['quantity'] ?: 0,
        'todayPurchaseAmount' => $todayPurchase['amount'] ?: 0,
        'delayedShipment' => $delayedShipment ?: 0,
        'remainingDelivery' => $remainingDelivery ?: 0,
        'pendingPurchase' => $pendingPurchase ?: 0,
        'waitingShipment' => $waitingShipment ?: 0,
        'inTransit' => $inTransit ?: 0,
        'collectionBox' => $collectionBox ?: 0
        ];

        echo json_encode(['code' => 0, 'data' => $data]);
    } catch(Exception $e) {
        error_log("Error in todo_metrics: " . $e->getMessage());
        echo json_encode(['code' => 500, 'msg' => '系统错误']);
    }
break;

case 'import':
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        error_log('[Import] Input: ' . var_export($input, true));
        error_log('[Import] UID before check: ' . var_export($uid, true));
        if (!$input || !isset($input['products']) || !is_array($input['products'])) {
            throw new Exception('无效的请求参数');
        }
        $skus = array_map(function($item) {
            return $item['sku'];
        }, $input['products']);
        error_log('[Import] SKUs: ' . var_export($skus, true));
        if (empty($skus)) {
            throw new Exception('未选择商品');
        }
        if (empty($uid)) {
            throw new Exception('用户未登录或身份验证失败');
        }
        $mark = isset($input['mark']) ? intval($input['mark']) : 1; // 默认标记为已跟卖
        if ($mark !== 0 && $mark !== 1) {
            throw new Exception('无效的标记参数');
        }
        error_log('[Import] UID: ' . var_export($uid, true));
        // 更新ozon_seller表中对应sku的gm字段为mark值
        $count = 0;
        foreach ($skus as $sku) {
            $result = $DB->update('ozon_seller', ['gm' => $mark], ['sku' => $sku, 'uid' => $uid]);
            if ($result) {
                $count++;
            } else {
                error_log("[Import] Failed to update sku: $sku for uid: $uid");
            }
        }
        $msg = $mark === 1 ? '成功标记为已跟卖' : '成功取消标记';
        exit(json_encode(['code' => 0, 'msg' => $msg, 'count' => $count]));
    } catch (Exception $e) {
        error_log('[Import Error] ' . $e->getMessage());
        exit(json_encode(['code' => -1, 'msg' => '系统错误: ' . $e->getMessage()]));
    }
break;

//店铺保存
case 'store_add':
    
      // 检查用户状态
    if ($userrow['status'] != 1) {
        exit(json_encode(['code' => -1, 'msg' => '账户已禁用或已到期，无法添加店铺']));
    }
    
    // 检查店铺数量限制
    $user = $DB->getRow("SELECT max_shops FROM ozon_user WHERE uid = ?", [$uid]);
    if (!$user) {
        exit(json_encode(['code' => -1, 'msg' => '用户信息不存在']));
    }
    
    $max_shops = intval($user['max_shops']);
    $current_shops = $DB->getColumn("SELECT COUNT(*) FROM ozon_store WHERE uid = ?", [$uid]);
    
    if ($current_shops >= $max_shops) {
        exit(json_encode(['code' => -1, 'msg' => "店铺数量已达上限({$max_shops})，无法继续添加"]));
    }
    
    $name=htmlspecialchars(strip_tags(trim($_POST['name'])));
    $ClientId = intval(trim($_POST['ClientId']));
    $key=htmlspecialchars(strip_tags(trim($_POST['key'])));
    $cookie=htmlspecialchars(strip_tags(trim($_POST['cookie'])));
    $money=intval(trim($_POST['money']));
    $group_id = isset($_POST['group_id']) ? intval($_POST['group_id']) : 0;
    if($money==2){
        $money = 'RUB';
    }elseif($money==3){
        $money = 'USD';
    }else{
        $money = 'CNY';
    }
    
   $sds=$DB->exec("INSERT INTO `ozon_store` (`ClientId`, `uid`, `storename`, `key`, `cookie`, `currency_code`, `group_id`, `addtime`, `apistatus`) VALUES ('{$ClientId}', '{$uid}', '{$name}', '{$key}', '{$cookie}', '{$money}', '{$group_id}', NOW(),  '1')");
    if($sds){
        $newStoreId = $DB->lastInsertId();
        if ($group_id > 0 && $newStoreId > 0) {
            $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
            $shop_groups = $user && $user['shop_groups'] 
                ? json_decode($user['shop_groups'], true) 
                : ['groups' => [], 'defaultGroupId' => null];

            foreach ($shop_groups['groups'] as &$group) {
                if ($group['id'] == $group_id) {
                    if (!in_array($newStoreId, $group['shopIds'])) {
                        $group['shopIds'][] = $newStoreId;
                    }
                    break;
                }
            }
            
            $DB->exec("UPDATE ozon_user SET shop_groups = :shop_groups WHERE uid = :uid", [
                ':shop_groups' => json_encode($shop_groups),
                ':uid' => $uid
            ]);
        }
        $result = ['code'=>1,'msg'=>'新增店铺成功！'];
    }else{
        $result = ['code'=>0,'msg'=>'新增店铺失败！'];
    }
    exit(json_encode($result));
break;
//店铺删除
case 'store_delete':
    $id = intval(trim($_POST['id']));
    if($id <= 0) exit(json_encode(['code'=>0, 'msg'=>'参数错误']));

    // 带存在性检查的版本
    if(!$DB->query("SELECT id FROM ozon_store WHERE id = $id")->fetch()){
        exit(json_encode(['code'=>0, 'msg'=>'店铺不存在']));
    }

    $DB->exec("DELETE FROM ozon_store WHERE id = $id");
    exit(json_encode(['code'=>1, 'msg'=>'删除成功']));
    break;
//店铺编辑
//店铺编辑
case 'store_edit':
    // 验证必要参数
    $id = intval($_POST['id'] ?? 0);
    $name = trim($_POST['name'] ?? '');
    $key = trim($_POST['key'] ?? '');
    $money = $_POST['money'] ?? '1';
    
    // 参数验证
    if($id <= 0) {
        exit(json_encode(['code' => 0, 'msg' => '无效的店铺ID']));
    }
    if(empty($name)) {
        exit(json_encode(['code' => 0, 'msg' => '店铺名称不能为空！']));
    }
    if(empty($key)) {
        exit(json_encode(['code' => 0, 'msg' => 'API密钥不能为空！']));
    }
    
    // 检查店铺是否存在且属于当前用户
    $existingStore = $DB->getRow("SELECT id, storename, group_id FROM ozon_store WHERE id = ? AND uid = ?", [$id, $uid]);
    if(!$existingStore) {
        exit(json_encode(['code' => 0, 'msg' => '店铺不存在或您没有权限修改']));
    }
    
    // 检查店铺名称是否已被其他店铺使用
  /*  $nameConflict = $DB->query("SELECT id FROM ozon_store WHERE storename = ? AND uid = ? AND id != ?", [$name, $uid, $id])->fetch();
    if($nameConflict) {
        exit(json_encode(['code' => 0, 'msg' => '店铺名称已被其他店铺使用！']));
    }*/
    
    try {
        $new_group_id = isset($_POST['group_id']) ? intval($_POST['group_id']) : 0;
        $old_group_id = $existingStore['group_id'];

        $updateData = [
            'storename' => daddslashes($name),
            'key' => daddslashes($key),
            'currency_code' => getCurrencyCode($money),
            'group_id' => $new_group_id
        ];
        
        // 处理Cookie字段
        if(array_key_exists('cookie', $_POST)){
            $updateData['cookie'] = daddslashes($_POST['cookie']);
        }
        
        // 执行更新操作
        $result = $DB->update('store', $updateData, 
            ['id' => $id, 'uid' => $uid]
        );
        
        // Update shop_groups in ozon_user if group has changed
        if ($result !== false && $old_group_id != $new_group_id) {
            $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
            $shop_groups = $user && $user['shop_groups'] 
                ? json_decode($user['shop_groups'], true) 
                : ['groups' => [], 'defaultGroupId' => null];

            // Remove from old group
            if ($old_group_id > 0) {
                foreach ($shop_groups['groups'] as &$group) {
                    if ($group['id'] == $old_group_id) {
                        $group['shopIds'] = array_values(array_diff($group['shopIds'] ?? [], [$id]));
                        break;
                    }
                }
            }
            
            // Add to new group
            if ($new_group_id > 0) {
                 foreach ($shop_groups['groups'] as &$group) {
                    if ($group['id'] == $new_group_id) {
                        if (!in_array($id, $group['shopIds'])) {
                            $group['shopIds'][] = $id;
                        }
                        break;
                    }
                }
            }
            unset($group);
            
            $DB->exec("UPDATE ozon_user SET shop_groups = :shop_groups WHERE uid = :uid", [
                ':shop_groups' => json_encode($shop_groups),
                ':uid' => $uid
            ]);
        }
        
        // 修复：判断更新结果，区分无变化和失败
        if($result === false) {
            exit(json_encode(['code' => 0, 'msg' => '更新失败：数据库操作异常']));
        } elseif($result === 0 && $old_group_id == $new_group_id) {
            exit(json_encode(['code' => 1, 'msg' => '没有数据变化']));
        } else {
            exit(json_encode(['code' => 1, 'msg' => '更新成功']));
        }
    } catch(Exception $e) {
        exit(json_encode(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]));
    }
break;
case 'manage_shop_groups':
    // 验证用户登录状态
    if(empty($uid)) {
        exit(json_encode(['code' => 0, 'msg' => '请先登录']));
    }

    // 获取当前用户分组数据
    $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
    $shop_groups = $user && $user['shop_groups'] 
        ? json_decode($user['shop_groups'], true) 
        : ['groups' => [], 'defaultGroupId' => null];

    // 获取操作类型
    $operation = $_POST['op'] ?? 'get';
    $response = ['code' => 0, 'msg' => '操作失败'];

    try {
        switch ($operation) {
            case 'create':
                $name = trim($_POST['name'] ?? '');
                if (empty($name)) throw new Exception('分组名称不能为空');

                // 生成新分组ID
                $newId = empty($shop_groups['groups']) 
                    ? 1 
                    : max(array_column($shop_groups['groups'], 'id')) + 1;
                
                $shopIds = isset($_POST['shopIds']) 
                    ? array_map('intval', (array)$_POST['shopIds']) 
                    : [];
                
                $newGroup = [
                    'id' => $newId,
                    'name' => $name,
                    'shopIds' => $shopIds,
                    'createdAt' => date('Y-m-d H:i:s')
                ];

                $shop_groups['groups'][] = $newGroup;
                
                // 更新店铺分组关联
                if (!empty($shopIds)) {
                    foreach ($shopIds as $shopId) {
                        $DB->exec("UPDATE ozon_store SET group_id = :groupId WHERE id = :shopId AND uid = :uid", [
                            ':groupId' => $newId,
                            ':shopId' => $shopId,
                            ':uid' => $uid
                        ]);
                    }
                }
                
                $response = ['code' => 1, 'msg' => '创建成功', 'groupId' => $newId];
                $DB->exec("UPDATE ozon_user SET shop_groups = :shop_groups WHERE uid = :uid", [
                    ':shop_groups' => json_encode($shop_groups),
                    ':uid' => $uid
                ]);
                break;

         case 'update':
                $groupId = intval($_POST['groupId'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $shopIds = isset($_POST['shopIds']) 
                    ? array_map('intval', (array)$_POST['shopIds']) 
                    : [];
                $exclusiveMode = isset($_POST['exclusiveMode']) ? (bool)$_POST['exclusiveMode'] : false;

                if (empty($name)) throw new Exception('分组名称不能为空');
                if ($groupId <= 0) throw new Exception('无效的分组ID');

                $groupFound = false;
                foreach ($shop_groups['groups'] as &$group) {
                    if ($group['id'] == $groupId) {
                        $group['name'] = $name;
                        
                        // 获取当前分组原有的店铺ID
                        $oldShopIds = $group['shopIds'] ?? [];
                        
                        // 找出需要移除的店铺
                        $removedShopIds = array_diff($oldShopIds, $shopIds);
                        // 找出新增的店铺
                        $addedShopIds = array_diff($shopIds, $oldShopIds);
                        
                        // 排他模式：先从所有分组中移除新增的店铺
                        if ($exclusiveMode && !empty($addedShopIds)) {
                            // 从其他分组中移除这些店铺
                            foreach ($shop_groups['groups'] as &$otherGroup) {
                                if ($otherGroup['id'] != $groupId && !empty($otherGroup['shopIds'])) {
                                    $otherGroup['shopIds'] = array_diff($otherGroup['shopIds'], $addedShopIds);
                                }
                            }
                        }
                        
                        // 更新分组关联
                        // 1. 移除不再属于该分组的店铺
                        if (!empty($removedShopIds)) {
                            $placeholders = implode(',', array_fill(0, count($removedShopIds), '?'));
                            $DB->exec("UPDATE ozon_store SET group_id = 0 WHERE id IN ($placeholders) AND uid = ?", 
                                array_merge($removedShopIds, [$uid]));
                        }
                        
                        // 2. 添加新分配到该分组的店铺
                        if (!empty($addedShopIds)) {
                            foreach ($addedShopIds as $shopId) {
                                $DB->exec("UPDATE ozon_store SET group_id = :groupId WHERE id = :shopId AND uid = :uid", [
                                    ':groupId' => $groupId,
                                    ':shopId' => $shopId,
                                    ':uid' => $uid
                                ]);
                            }
                        }
                        
                        // 更新分组数据
                        $group['shopIds'] = $shopIds;
                        $groupFound = true;
                        break;
                    }
                }

                if (!$groupFound) throw new Exception('分组不存在');
                $response = ['code' => 1, 'msg' => '更新成功'];
                $DB->exec("UPDATE ozon_user SET shop_groups = :shop_groups WHERE uid = :uid", [
                    ':shop_groups' => json_encode($shop_groups),
                    ':uid' => $uid
                ]);
                break;

            case 'delete':
                $groupId = intval($_POST['groupId'] ?? 0);
                if ($groupId <= 0) throw new Exception('无效的分组ID');

                $originalCount = count($shop_groups['groups']);
                $shop_groups['groups'] = array_values(array_filter(
                    $shop_groups['groups'],
                    function($group) use ($groupId) {
                        return $group['id'] != $groupId;
                    }
                ));

                if (count($shop_groups['groups']) === $originalCount) {
                    throw new Exception('分组不存在');
                }

                // 重置关联店铺
                $DB->exec("UPDATE ozon_store SET group_id = 0 WHERE group_id = :groupId AND uid = :uid", [
                    ':groupId' => $groupId,
                    ':uid' => $uid
                ]);

                // 清除默认分组
                if ($shop_groups['defaultGroupId'] == $groupId) {
                    $shop_groups['defaultGroupId'] = null;
                }

                $response = ['code' => 1, 'msg' => '删除成功'];
                $DB->exec("UPDATE ozon_user SET shop_groups = :shop_groups WHERE uid = :uid", [
                    ':shop_groups' => json_encode($shop_groups),
                    ':uid' => $uid
                ]);
                break;

            case 'set_default':
                $groupId = intval($_POST['groupId'] ?? 0);
                if ($groupId <= 0) throw new Exception('无效的分组ID');

                // 验证分组是否存在
                $groupExists = false;
                foreach ($shop_groups['groups'] as $group) {
                    if ($group['id'] == $groupId) {
                        $groupExists = true;
                        break;
                    }
                }

                if (!$groupExists) throw new Exception('分组不存在');

                $shop_groups['defaultGroupId'] = $groupId;
                $response = ['code' => 1, 'msg' => '设置默认分组成功'];
                $DB->exec("UPDATE ozon_user SET shop_groups = :shop_groups WHERE uid = :uid", [
                    ':shop_groups' => json_encode($shop_groups),
                    ':uid' => $uid
                ]);
                break;

            case 'batch_assign':
                $groupId = intval($_POST['groupId'] ?? 0);
                $shopIds = isset($_POST['shopIds']) ? $_POST['shopIds'] : '';
                $exclusiveMode = isset($_POST['exclusiveMode']) ? (bool)$_POST['exclusiveMode'] : false;

                if ($groupId <= 0) {
                    throw new Exception('请选择分组');
                }
                if (empty($shopIds)) {
                    throw new Exception('请选择店铺');
                }

                // 修复：处理逗号分隔的字符串
                $shopIds = explode(',', $shopIds);
                $shopIds = array_map('intval', $shopIds);
                $shopIds = array_filter($shopIds);

                // 排他模式：先从所有分组中移除这些店铺
                if ($exclusiveMode) {
                    // 1. 更新数据库中的group_id
                    foreach ($shopIds as $shopId) {
                        $DB->exec("UPDATE ozon_store SET group_id = :groupId WHERE id = :shopId AND uid = :uid", [
                            ':groupId' => $groupId,
                            ':shopId' => $shopId,
                            ':uid' => $uid
                        ]);
                    }
                    
                    // 2. 从所有分组的shopIds中移除这些店铺
                    foreach ($shop_groups['groups'] as &$group) {
                        if ($group['id'] != $groupId && !empty($group['shopIds'])) {
                            $group['shopIds'] = array_diff($group['shopIds'], $shopIds);
                        }
                    }
                }

                // 更新分组数据中的店铺ID列表
                $groupUpdated = false;
                foreach ($shop_groups['groups'] as &$group) {
                    if ($group['id'] == $groupId) {
                        // 合并现有店铺ID和新分配的店铺ID，去重
                        $existingIds = $group['shopIds'] ?? [];
                        $newIds = array_unique(array_merge($existingIds, $shopIds));
                        $group['shopIds'] = $newIds;
                        $groupUpdated = true;
                        break;
                    }
                }
                
                // 如果分组不存在，创建一个新分组
                if (!$groupUpdated) {
                    $newGroup = [
                        'id' => $groupId,
                        'name' => '未命名分组',
                        'shopIds' => $shopIds,
                        'createdAt' => date('Y-m-d H:i:s')
                    ];
                    $shop_groups['groups'][] = $newGroup;
                }

                $response = ['code' => 1, 'msg' => '批量分组成功'];
                $DB->exec("UPDATE ozon_user SET shop_groups = :shop_groups WHERE uid = :uid", [
                    ':shop_groups' => json_encode($shop_groups),
                    ':uid' => $uid
                ]);
                break;

            case 'get':
            default:
                // 获取店铺列表
                $stores = $DB->getAll("SELECT id, storename FROM ozon_store WHERE uid = ?", [$uid]);
                
                // 确保每个分组都有shopIds属性
                $groups = $shop_groups['groups'] ?? [];
                foreach ($groups as &$group) {
                    if (!isset($group['shopIds'])) {
                        $group['shopIds'] = [];
                    }
                }
                
                $response = [
                    'code' => 1,
                    'data' => [
                        'groups' => $groups,
                        'stores' => $stores,
                        'defaultGroupId' => $shop_groups['defaultGroupId'] ?? null
                    ]
                ];
                break;
        }



    } catch (Exception $e) {
        $response = ['code' => 0, 'msg' => $e->getMessage()];
    }

    exit(json_encode($response));
break;
case 'store_List':
    // 构造查询条件，限制为当前用户的店铺
    $sql = "uid = :uid";
    $params = [':uid' => $uid];
    
    // 处理店铺ID筛选
    if(isset($_GET['store_ids']) && !empty($_GET['store_ids'])) {
        $storeIds = explode(',', $_GET['store_ids']);
        $storeIds = array_map('intval', $storeIds);
        $storeIds = array_filter($storeIds);
        
        if(!empty($storeIds)) {
            $placeholders = [];
            foreach($storeIds as $index => $id) {
                $paramName = ":store_id_" . $index;
                $placeholders[] = $paramName;
                $params[$paramName] = $id;
            }
            $placeholdersStr = implode(',', $placeholders);
            $sql .= " AND id IN ($placeholdersStr)";
        }
    }
    
    // 处理分组ID筛选
    if(isset($_GET['group_ids']) && !empty($_GET['group_ids'])) {
        $groupIds = explode(',', $_GET['group_ids']);
        $groupIds = array_map('intval', $groupIds);
        $groupIds = array_filter($groupIds);
        
        if(!empty($groupIds)) {
            $placeholders = [];
            foreach($groupIds as $index => $id) {
                $paramName = ":group_id_" . $index;
                $placeholders[] = $paramName;
                $params[$paramName] = $id;
            }
            $placeholdersStr = implode(',', $placeholders);
            $sql .= " AND group_id IN ($placeholdersStr)";
        }
    }
    
    // 添加调试输出
    error_log("Store_List SQL: SELECT count(*) from ozon_store WHERE {$sql}");
    error_log("Store_List Params: " . json_encode($params));
    
    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
    if ($page < 1) $page = 1;
    if ($limit < 1) $limit = 20;
    $offset = ($page - 1) * $limit;
    
    // 查询总店铺数
    $total = $DB->getColumn("SELECT count(*) from ozon_store WHERE {$sql}", $params);
    
    // 查询当前页店铺列表
    $list = $DB->getAll("SELECT * FROM ozon_store WHERE {$sql} ORDER BY addtime ASC LIMIT {$offset}, {$limit}", $params);

    // 获取当前用户的分组数据
    $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
    
    // 解析分组数据
    $groups = $user && $user['shop_groups'] ? json_decode($user['shop_groups'], true) : [];
    $groupMap = [];
    
    // 构建分组ID到分组名称的映射
    if (isset($groups['groups']) && is_array($groups['groups'])) {
        foreach ($groups['groups'] as $group) {
            $groupMap[$group['id']] = $group['name'];
        }
    }

    $list2 = [];
    foreach ($list as $row) {
        // 查询该店铺相关订单，计算总采购成本和总销售金额
        //$rs = $DB->query("SELECT price, quantity, cost, status FROM ozon_order WHERE storeid = :storeid", 
            $rs = $DB->query("SELECT price, quantity, cost, status, products FROM ozon_order WHERE storeid = :storeid", 
            [':storeid' => $row['id']]);
        
        $cost = $price = 0;
        if($rs) {
            while ($rowa = $rs->fetch()) {
                if ($rowa && isset($rowa['status']) && $rowa['status'] != 'cancelled') {
                     // 检查是否为多商品订单 - 如果有products字段且不为空，说明是多商品订单
                    $hasProducts = !empty($rowa['products']);
                    
                    if ($hasProducts) {
                        // 多商品订单：price字段已经是总价，直接累加
                        $price += (float)($rowa['price'] ?? 0);
                    } else {
                        // 单商品订单：price字段是单价，需要乘以数量
                        $price += (float)($rowa['price'] ?? 0) * (int)($rowa['quantity'] ?? 1);
                    }
                    
                    $cost  += (float)($rowa['cost'] ?? 0);
                }
            }
        }
        
        $row['cost'] = round($cost, 2);
        $row['price'] = round($price, 2);
        
        // 格式化添加时间
        $row['time'] = isset($row['time']) && is_numeric($row['time']) 
            ? date("Y-m-d H:i:s", $row['time']) 
            : '';
        
        // 根据店铺的group_id字段映射分组名称
        $groupId = $row['group_id'] ?? 0;
        $row['Groupname'] = isset($groupMap[$groupId]) ? $groupMap[$groupId] : '未分组';
        
        $list2[] = $row;
    }
    
    exit(json_encode(['code' => 0, 'count' => $total, 'data' => $list2]));
break;
case 'store_switch':
    $id = intval($_POST['id']);
    $status = intval($_POST['status']);
    if($DB->update('store', ['actions'=>$status], ['id'=>$id,'uid'=>$uid])){
        $result = ['code'=>0];
    }else{
        $result = ['code'=>-1];
    }
    exit(json_encode($result));
break;
case 'store_upwarehouseedit':
    $id = intval($_POST['id']);
    $row = $DB->getRow("SELECT * FROM ozon_store WHERE id=:id AND uid=:uid limit 1", [':id'=>$id,':uid'=>$uid]);
    if($row){
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
	    $row['warehouses']=$client->Ozonwarehouses();
	    if($row['warehouses']){
	        if($DB->update('store', ['warehouses'=>json_encode($row['warehouses']),'apistatus'=>1], ['id'=>$row['id']])){
    	        $result = ['code'=>0,'msg'=>'更新数据成功'];
    	    }else{
    	        $result = ['code'=>-2,'msg'=>'没有变化或更新失败'];
    	    }
	    }else{
	        $result = ['code'=>-2,'msg'=>'没有变化或更新失败'];
	    }
    }else{
        $result = ['code'=>-1,'msg'=>'没有此数据'];
    }
    exit(json_encode($result));
break;
//通用测试的分组信息，可删除
case 'get_grouped_shops':
    // 获取当前登录用户的分组店铺信息
    // 1. 验证用户是否登录
    if ($islogin3 != 1) {
        exit(json_encode(['code' => -3, 'msg' => '未登录']));
    }

    // 2. 获取当前用户ID
    $packerId = $packerId ?? 0;
    if ($packerId <= 0) {
        exit(json_encode(['code' => -1, 'msg' => '无效的用户ID']));
    }

    // 3. 查询用户的分组信息，存储在ozon_user表的shop_groups字段中，格式为JSON
    $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);

    // 4. 解析分组信息，如果为空则初始化为空数组
    $shop_groups = $user && $user['shop_groups'] ? json_decode($user['shop_groups'], true) : ['groups' => [], 'defaultGroupId' => null];

    // 5. 查询当前用户的所有店铺信息
    $stores = $DB->getAll("SELECT id, storename, group_id FROM ozon_store WHERE uid = ?", [$uid]);

    // 6. 返回分组和店铺数据
    exit(json_encode([
        'code' => 0,
        'msg' => '成功获取分组店铺信息',
        'data' => [
            'groups' => $shop_groups['groups'] ?? [],
            'defaultGroupId' => $shop_groups['defaultGroupId'] ?? null,
            'stores' => $stores
        ]
    ]));
break;
//商品列表筛选器
case 'productlist':
    $shopIds = [];
    if (isset($_GET['shop_id'])) {
        if (is_array($_GET['shop_id'])) {
            $shopIds = array_map('intval', $_GET['shop_id']);
        } else {
            $shopIds = explode(',', $_GET['shop_id']);
            $shopIds = array_map('intval', $shopIds);
        }
        $shopIds = array_filter($shopIds); // 过滤空值
    }
    
    //过滤分组
    
    $groupId = isset($_GET['group_id']) ? intval($_GET['group_id']) : 0;
    if ($groupId > 0) {
        // 查询该分组下的所有店铺ID
        $groupShopIds = $DB->getAll("SELECT id FROM ozon_store WHERE uid = ? AND group_id = ?", [$uid, $groupId]);
        $groupShopIds = array_column($groupShopIds, 'id');
        if (!empty($groupShopIds)) {
            if (!empty($shopIds)) {
                // 取交集
                $shopIds = array_intersect($shopIds, $groupShopIds);
            } else {
                $shopIds = $groupShopIds;
            }
        } else {
            // 如果该分组没有店铺，则返回空结果
            exit(json_encode(['code' => 0, 'data' => [], 'count' => 0]));
        }
    }

    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $name = isset($_GET['name']) ? trim($_GET['name']) : '';
    $sku = intval(trim($_GET['sku']));
    $offer_id = daddslashes(trim($_GET['offer_id']));
    $sql = "A.uid = {$uid}";

    // Handle multiple shop IDs
    if (!empty($shopIds)) {
        $shopIdList = implode(',', $shopIds);
        $sql .= " AND A.storeid IN ({$shopIdList})";
    }
    if ($sku) {
        $sql .= " AND A.sku = '{$sku}'";
    }
    if($offer_id){
        $sql .= " AND A.offer_id = '{$offer_id}'";
    }
    if ($name !== '') {
        $sql .= " AND A.name LIKE '%{$name}%'";
    }





    // Handle SKU status filter
    $sku_status = isset($_GET['sku_status']) ? trim($_GET['sku_status']) : '';
    if ($sku_status !== '') {
        if ($sku_status === 'null') {
            $sql .= " AND (A.sku IS NULL OR A.sku = '')";
        } else if ($sku_status === 'not_null') {
            $sql .= " AND A.sku IS NOT NULL AND A.sku != ''";
        }
    }

    // Handle multiple status values
    if (!empty($status)) {
        if (is_array($status)) {
            $statusConditions = [];
            foreach ($status as $s) {
                switch ($s) {
                    case '1': // 正常销售
                        $statusConditions[] = "A.status_info LIKE '%Продается%'";
                        break;
                    case '2': // 已归档
                        $statusConditions[] = "A.is_archived=1 OR A.is_autoarchived=1";
                        break;
                   case '3': // 审核中 (no mapping found)
    $statusConditions[] = "A.status_description LIKE '%На модерации%'";
    break;
                    case '4': // 已下架
                        $statusConditions[] = "A.status_description LIKE '%Убран из продажи%'";
                        break;
                    case '5': // 库存不足
                        $statusConditions[] = "A.status_info LIKE '%Готов к продаже%'";
                        break;
                    case '6': // 重复出现
                        $statusConditions[] = "A.status = 6";
                        break;
                         case '7': // 不可出售
                        $statusConditions[] = "A.status = 7";
                        break;
                }
            }
            if (!empty($statusConditions)) {
                $sql .= " AND (" . implode(' OR ', $statusConditions) . ")";
            }
        } else {
            // Single status value handling
            switch ($status) {
                case '1': // 正常销售
                    $sql .= " AND A.status_info LIKE '%Продается%'";
                    break;
               case '2': // 已归档
    $sql .= " AND (A.is_archived=1 OR A.is_autoarchived=1)";
    break;
                case '3': // 审核中 (no mapping found)
                    $sql .= " AND A.status_description LIKE '%На модерации%'";
                    break;
                case '4': // 已下架
                    $sql .= " AND A.status_description LIKE '%Убран из продажи%'";
                    break;
                case '5': // 库存不足
                    $sql .= " AND A.status_info LIKE '%Готов к продаже%'";
                    break;
                case '6': // 重复出现
                    $sql .= " AND A.status = 6";
                    break;
                    case '7': // 不可出售
                    $sql .= " AND A.status = 7";
                    break;
            }
        }
    }
    
    
    
     // Handle weight and sales volume filters
    $weight_min = isset($_GET['weight_min']) && $_GET['weight_min'] !== '' ? floatval($_GET['weight_min']) : null;
    $weight_max = isset($_GET['weight_max']) && $_GET['weight_max'] !== '' ? floatval($_GET['weight_max']) : null;
    $salesvolume_min = isset($_GET['salesvolume_min']) && $_GET['salesvolume_min'] !== '' ? intval($_GET['salesvolume_min']) : null;
    $salesvolume_max = isset($_GET['salesvolume_max']) && $_GET['salesvolume_max'] !== '' ? intval($_GET['salesvolume_max']) : null;

    // Add dimension filters to SQL
    if ($depth_min !== null) {
        $sql .= " AND C.depth >= {$depth_min}";
    }
    if ($depth_max !== null) {
        $sql .= " AND C.depth <= {$depth_max}";
    }
    if ($width_min !== null) {
        $sql .= " AND C.width >= {$width_min}";
    }
    if ($width_max !== null) {
        $sql .= " AND C.width <= {$width_max}";
    }
    if ($height_min !== null) {
        $sql .= " AND C.height >= {$height_min}";
    }
    if ($height_max !== null) {
        $sql .= " AND C.height <= {$height_max}";
    }
    if ($weight_min !== null) {
        $sql .= " AND C.weight >= {$weight_min}";
    }
    if ($weight_max !== null) {
        $sql .= " AND C.weight <= {$weight_max}";
    }
    
    // Add sales volume filters to SQL
    if ($salesvolume_min !== null) {
        $sql .= " AND A.salesvolume >= {$salesvolume_min}";
    }
    if ($salesvolume_max !== null) {
        $sql .= " AND A.salesvolume <= {$salesvolume_max}";
    }
    
    
    // Handle sorting parameters
    $sort_field = isset($_GET['field']) ? trim($_GET['field']) : 'created_at';
    $sort_order = isset($_GET['order']) ? strtoupper(trim($_GET['order'])) : 'DESC';
    
    // Validate sort order
    if (!in_array($sort_order, ['ASC', 'DESC'])) {
        $sort_order = 'DESC';
    }
    
    // Map frontend field names to database fields
       // Map frontend field names to database fields
    $field_mapping = [
        'price' => 'A.price',
        'salesvolume' => 'A.salesvolume',
        'created_at' => 'A.created_at',
        'weight' => 'C.weight'
    ];
    // Build ORDER BY clause
    $order_by = "A.created_at DESC"; // Default sort
    
    if ($sort_field === 'stock') {
        // For stock, we need to extract the present value from JSON
        $order_by = "CAST(JSON_UNQUOTE(JSON_EXTRACT(A.stocks, '$.stocks[0].present')) AS UNSIGNED) {$sort_order}";
    } else {
        $db_field = isset($field_mapping[$sort_field]) ? $field_mapping[$sort_field] : 'A.created_at';
        $order_by = "{$db_field} {$sort_order}";
    }
    
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = $limit * ($page - 1);
 $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_products A LEFT JOIN ozon_weight C ON A.sku = C.sku WHERE {$sql}");
    $list = $DB->getAll("SELECT A.*, B.storename, C.width, C.depth, C.height ,C.weight
                        FROM ozon_products A 
                        LEFT JOIN ozon_store B ON A.storeid = B.id 
                        LEFT JOIN ozon_weight C ON A.sku = C.sku 
                        WHERE {$sql} 
                          ORDER BY {$order_by}
                        LIMIT $offset, $limit");
    $list2 = [];
    foreach($list as $row){
        $stocks = json_decode($row['stocks'],true);
        $row['stock'] = $stocks['stocks'][0]['present'];
        $row['source'] = $stocks['stocks'][0]['source'];
        $commissions = json_decode($row['commissions'],true);
        switch($stocks['stocks'][0]['source']){
            case 'fbo':
                $i=0;
                break;
            case 'fbs':
                $i=1;
                break;
            case 'rfbs':
                $i=2;
                break;
            case 'fbp':
                $i=3;
                break;
        }
        $row['commissions'] = "<P>{$stocks['stocks'][0]['source']}: {$commissions[$i]['percent']}%</P><P>佣金: {$commissions[$i]['value']}</P>";
        $row['return_amount'] = "<p>物流: {$commissions[$i]['return_amount']} {$row['currency_code']}</p>
                                 <p>最后一公里: {$commissions[$i]['delivery_amount']} {$row['currency_code']}</p>
                                 <p>退货或取消: {$commissions[$i]['value']} {$row['currency_code']}</p>";
        $status_info = json_decode($row['status_info'],true);
        switch($row['status_name']){
            case 'Не продается':
                $errors = json_decode($row['errors'],true);
                if($row['status_description']=='Убран из продажи'){
                    $row['status'] = 4;//已停止销售
                }else if($errors[0]['code']=='SPU_ALREADY_EXISTS_IN_ANOTHER_ACCOUNT'){
                    $row['status'] = 6;
                    $row['errors']  ="同样商品在以下卡片和个人中心重复出现：{$errors[0]['texts']['params'][0]['value']}。请在https://docs.ozon.ru/global/products/general-information/errors-with-pdps/?country=CN了解更多信息。";
                }else{
                    $row['status'] = 7;//不可出售
                }
                break;
            case 'Продается':
                $row['status'] = 1;//正常销售
                break;
            case 'Готов к продаже':
                $row['status'] = 5;
                break;
        }
        if($row['is_archived']==1 or $row['is_autoarchived']){
            $row['status'] = 2; //已归档
        }
        
        $list2[] = $row;
    }
    exit(json_encode(['code' => 0, 'data' => $list2, 'count' => $total]));
break;
case 'update_stock':
    $offer_id = daddslashes($_POST['offer_id']);
    $stock = intval($_POST['stock']);
    $warehouse_id = intval($_POST['storeid']); // 仓库ID参数
    
    // 调试日志
    error_log("update_stock called with offer_id=$offer_id, stock=$stock, warehouse_id=$warehouse_id, uid=$uid");
    
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key,B.warehouses FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.offer_id=:offer_id LIMIT 1", [':uid'=>$uid,':offer_id' => $offer_id]);
    if($row){
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $warehouses = json_decode($row['warehouses'],true);
        
        // 确保使用前端传递的warehouse_id参数
        if($warehouse_id <= 0 && !empty($warehouses)) {
            // 如果没有传递有效的warehouse_id，则使用第一个仓库作为默认值
            $warehouse_id = $warehouses[0]['id'];
            error_log("Using default warehouse_id: $warehouse_id");
        }
        
        // 调用API更新库存，确保传递warehouse_id参数
        $data = $client->productsstocks([
            'offer_id' => $row['offer_id'],
            'stock' => $stock,
            'warehouse_id' => $warehouse_id
        ]);
        
        if($data){
            $result = ['code'=>0, 'msg'=>'库存更新成功'];
            $importer = new \lib\JsonImporter($DB);
            $items[] = $offer_id;
            $data2 = $client->productinfolist($items,'offer_id');
            $importer->importProducts($data2, false, $row);
        } else{
            error_log("Failed to update stock for offer_id=$offer_id, stock=$stock, warehouse_id=$warehouse_id");
            $result = ['code'=>-2,'msg'=>'添加库存失败'];
        }
    }else{
        error_log("Product not found for offer_id=$offer_id, uid=$uid");
        $result = ['code'=>-1,'msg'=>'获取数据失败'];
    }
    exit(json_encode($result));
break;
// 批量更新库存接口
case 'batch_update_stock':
    $offer_ids_str = isset($_POST['offer_ids']) ? $_POST['offer_ids'] : '';
    $stock = intval($_POST['stock']);
    $warehouse_id = intval($_POST['warehouse_id']);
    
    if (empty($offer_ids_str)) {
        exit(json_encode(['code' => -1, 'msg' => '缺少商品ID参数']));
    }
    
    $offer_ids = explode(',', $offer_ids_str);
    $success_count = 0;
    $fail_count = 0;
    
    // 按店铺分组商品，减少API调用次数
    $products_by_store = [];
    
    // 第一步：获取所有商品信息并按店铺分组
    foreach ($offer_ids as $offer_id) {
        $offer_id = daddslashes($offer_id);
        $row = $DB->getRow("SELECT A.*,B.id as store_id,B.ClientId,B.key FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.offer_id=:offer_id LIMIT 1", 
                           [':uid'=>$uid, ':offer_id'=>$offer_id]);
        
        if ($row) {
            $store_id = $row['store_id'];
            if (!isset($products_by_store[$store_id])) {
                $products_by_store[$store_id] = [
                    'client_id' => $row['ClientId'],
                    'key' => $row['key'],
                    'products' => []
                ];
            }
            $products_by_store[$store_id]['products'][] = $row;
        } else {
            $fail_count++;
        }
    }
    
    // 第二步：按店铺批量更新库存
    foreach ($products_by_store as $store_id => $store_data) {
        $client = new \lib\OzonApiClient($store_data['client_id'], $store_data['key']);
        $importer = new \lib\JsonImporter($DB);
        
        // 获取该店铺的所有商品ID，用于后续批量更新商品信息
        $store_offer_ids = [];
        
        // 逐个更新库存
        $all = [];
        foreach ($store_data['products'] as $product) {
            $offer_id = $product['offer_id'];
            $store_offer_ids[] = $offer_id;
            if($offer_id and $warehouse_id){
                $all[] = [
                    'offer_id'=>$offer_id,
                    'stock'=>$stock?$stock:0,
                    'warehouse_id' => $warehouse_id
                ];
            }
            
            /*
            $data = $client->productsstocks([
                'offer_id' => $offer_id,
                'stock' => $stock,
                'warehouse_id' => $warehouse_id
            ]);
            
            if ($data) {
                $success_count++;
            } else {
                $fail_count++;
                error_log("Failed to update stock for offer_id=$offer_id in batch operation");
            }
            */
            
        }
        
        $data = $client->productsstocksarray($all);
        if($data){
            //exit(json_encode($data));
            // 批量更新商品信息
            if (!empty($store_offer_ids)) {
                $data2 = $client->productinfolist($store_offer_ids, 'offer_id');
                $importer->importProducts($data2, false, $store_data['products'][0]);
            }
            $success_count+=count($all);
        }else{
            $fail_count+=count($all);
        }
    }
    
    $result = [
        'code' => 0,
        'msg' => "批量更新库存完成，成功：$success_count，失败：$fail_count",
        'success_count' => $success_count,
        'fail_count' => $fail_count
    ];
    
    exit(json_encode($result));
break;
case 'update_price':
    $offer_id = daddslashes($_POST['offer_id']);
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key,B.currency_code FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.offer_id=:offer_id LIMIT 1", [':uid'=>$uid,':offer_id' => $offer_id]);
    
    if($row){
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $row['price'] = $_POST['price'];
        $data = $client->importprices($row);
        if($data){
            $result = ['code'=>0,'msg'=>'修改成功'];
            $importer = new \lib\JsonImporter($DB);
            $items[] = $offer_id;
            $data2 = $client->productinfolist($items,'offer_id');
            $importer->importProducts($data2, false, $row);
        }else{
            $result = ['code'=>-2,'msg'=>'修改价格失败'];
        }
    }else{
        $result = ['code'=>-1,'msg'=>'获取数据失败'];
    }
    exit(json_encode($result));
break;
case 'batch_update_price':
    $skus = isset($_POST['skus']) ? explode(',', $_POST['skus']) : [];
    $multiplier = isset($_POST['multiplier']) ? floatval($_POST['multiplier']) : 1.0;
    $add_price = isset($_POST['add_price']) ? floatval($_POST['add_price']) : 0.0;
    $subtract_price = isset($_POST['subtract_price']) ? floatval($_POST['subtract_price']) : 0.0;
    if (empty($skus)) {
        exit(json_encode(['code' => -1, 'msg' => '缺少SKU参数']));
    }
    if ($multiplier < 0) {
        exit(json_encode(['code' => -1, 'msg' => '倍数不能小于0']));
    }
    $successCount = 0;
    $failCount = 0;
    foreach ($skus as $sku) {
        $sku = trim($sku);
        if (empty($sku)) {
            continue;
        }
        $row = $DB->getRow("SELECT A.*,B.ClientId,B.key,B.price FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.sku=:sku LIMIT 1", [':uid'=>$uid, ':sku'=>$sku]);
        if ($row) {
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $newPrice = $row['price'] * $multiplier + $add_price - $subtract_price;
            if ($newPrice < 0) {
                $newPrice = 0;
            }
            $row['price'] = round($newPrice, 2);
            $data = $client->importprices($row);
            if ($data) {
                $successCount++;
                $importer = new \lib\JsonImporter($DB);
                $items[] = $row['offer_id'];
                $data2 = $client->productinfolist($items, 'offer_id');
                $importer->importProducts($data2, false, $row);
            } else {
                $failCount++;
            }
        } else {
            $failCount++;
        }
    }

    if ($failCount === 0) {
        $result = ['code' => 0, 'msg' => '价格批量更新成功'];
    } else {
        $result = ['code' => -1, 'msg' => "价格批量更新完成，成功：$successCount，失败：$failCount"];
    }
    exit(json_encode($result));
    break;


case 'productsync':
    if (function_exists("set_time_limit")) {
        @set_time_limit(0);
    }
    $importer = new \lib\JsonImporter($DB);
    
    if($_POST['offer_id']){
        $storeid = intval($_POST['storeid']);
        $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid={$uid} AND id='{$storeid}'");
        $client = new \lib\OzonApiClient($list[0]['ClientId'], $list[0]['key']);
        $items[] = $_POST['offer_id'];
        $data2 = $client->productinfolist($items,'offer_id');
        $result = $importer->importProducts($data2, false, $list);
        if($result['message']=='没有可导入的产品数据'){
            $DB->exec("DELETE FROM ozon_products WHERE offer_id='{$_POST['offer_id']}'");
        }
    }else{
        $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid = ? ORDER BY addtime ASC", [$uid]);
        $productsync = null;
        foreach ($list as $row) {
            if($row['updata_products']==1){
                $productsync = true;
            }
        }
        $redis = new Redis();$redis->connect('127.0.0.1', 6379);
        $redisdata = $redis->get('productsync_uid'.$uid);
        if(empty($productsync) or (empty($redisdata) or $redisdata!=1)){
            $DB->exec("DELETE FROM `ozon_products` WHERE `uid`='$uid'");
            $row=[];
            foreach ($list as $row) {
                $DB->update('store',['updata_products'=>1],['id'=>$row['id']]);
            }
        }else{
            exit(json_encode(['code'=>-1,'msg'=>'已经提交过任务了，不能重复提交！！！']));
        }
        $redis->close();
    }
exit(json_encode(['code'=>0,'msg'=>'success']));
break;
case 'batch_archive_products': #批量归档商品
    //$product_ids = isset($_POST['product_ids']) ? $_POST['product_ids'] : [];
    $product_ids = explode(',',$_POST['product_ids']);
    if (empty($product_ids) || !is_array($product_ids)) {
        exit(json_encode(['code' => -1, 'msg' => '请选择要归档的商品']));
    }
    
    // 调试日志
    error_log("批量归档接收到的产品IDs: " . json_encode($product_ids));
    
    $success_count = 0;
    $fail_count = 0;
    $fail_products = [];
    
    // 按店铺分组处理
    $products_by_store = [];
    foreach ($product_ids as $identifier) {
        // 根据identifier的类型来判断是SKU还是product_id
        if (is_numeric($identifier)) {
            // 数字类型，可能是product_id或SKU
            $product = $DB->getRow("SELECT A.*, B.ClientId, B.key FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid = B.id WHERE A.uid = :uid AND (A.product_id = :id OR A.sku = :id) LIMIT 1", [':uid' => $uid, ':id' => $identifier]);
        } else {
            // 非数字类型，按SKU查找
            $product = $DB->getRow("SELECT A.*, B.ClientId, B.key FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid = B.id WHERE A.uid = :uid AND A.sku = :sku LIMIT 1", [':uid' => $uid, ':sku' => $identifier]);
        }
        
        if (!$product || empty($product['ClientId']) || empty($product['key'])) {
            $fail_count++;
            $fail_products[] = $identifier;
            error_log("未找到商品信息: " . $identifier);
            continue;
        }
        
        error_log("找到商品: SKU={$product['sku']}, product_id={$product['product_id']}");
        
        $store_key = $product['ClientId'] . '_' . $product['key'];
        if (!isset($products_by_store[$store_key])) {
            $products_by_store[$store_key] = [
                'client_id' => $product['ClientId'],
                'api_key' => $product['key'],
                'storeid' => $product['storeid'],
                'product_ids' => []
            ];
        }
        $products_by_store[$store_key]['product_ids'][] = ['product_id'=>$product['product_id'],'sku'=>$product['sku']];
    }
    
    error_log("按店铺分组的产品: " . json_encode($products_by_store));
    
    // 分店铺处理归档
    foreach ($products_by_store as $store_info) {
        try {
            $client = new \lib\OzonApiClient($store_info['client_id'], $store_info['api_key']);
            error_log("调用归档API，product_ids: " . json_encode($store_info['product_ids']));
            
            // 逐个处理产品归档，因为批量可能会失败
            foreach ($store_info['product_ids'] as $products) {
                // 关键修复：将product_id转换为字符串，学习ozon.php的做法
                $string_product_id = strval($products['product_id']);
                $json = $client->stockswarehouse($products['sku']);
                $productData = ['stock'=>0,'warehouse_id'=>0];
                foreach ($json as $item){
                    if($item['present']>$productData['stock']){
                        $row = ['sku'=>$item['sku'],'stock'=>$item['present'],'warehouse_id'=>$item['warehouse_id'],'product_id'=>$item['product_id']];
                    }
                }
                if($row){
                    $client->productsstocks($row,'product_id',true);
                }
                $single_result = $client->productarchive([$string_product_id]);
                error_log("单个归档API响应 product_id=$string_product_id: " . json_encode($single_result));
                
                if ($single_result) {
                    $update_result = $DB->exec("DELETE FROM `ozon_products` WHERE `uid`='$uid' AND `product_id`='$string_product_id'");
                    if ($update_result) {
                        $success_count++;
                        error_log("数据库更新成功: product_id=$string_product_id");
                    } else {
                        $fail_count++;
                        $fail_products[] = $string_product_id;
                        error_log("数据库更新失败: product_id=$string_product_id");
                    }
                } else {
                    // 归档失败
                    $fail_count++;
                    $fail_products[] = $string_product_id;
                    error_log("API归档失败: product_id=$string_product_id");
                }
            }
        } catch (Exception $e) {
            error_log("批量归档商品异常: " . $e->getMessage());
            $fail_count += count($store_info['product_ids']);
            $fail_products = array_merge($fail_products, $store_info['product_ids']);
        }
    }
    
    $msg = "批量归档完成";
    if ($success_count > 0) {
        $msg .= "，成功归档 {$success_count} 个商品";
    }
    if ($fail_count > 0) {
        $msg .= "，失败 {$fail_count} 个商品";
    }
    
    exit(json_encode([
        'code' => 0,
        'msg' => $msg,
        'success_count' => $success_count,
        'fail_count' => $fail_count,
        'fail_products' => $fail_products
    ]));
break;



case 'Management_reupload': #批量重传
    if ($_POST && isset($_POST['skus'])) {
        $skus = $_POST['skus']; // 获取前端传来的 SKU 数组
        if (!empty($skus)) {
            $i=0;$x=0;
            $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
            $channel = $connection->channel();
            $channel->queue_declare('task_queue', false, true, false, false);
            foreach ($skus as $sku) {
                // 处理每个 SKU
                $sku = intval($sku);
                if(empty($sku)) continue;
                
                $data = ['type' => 'Management_reupload','sku'=>$sku]; // 你的任务数据
                // 创建消息
                $msg = new AMQPMessage(
                    json_encode($data),
                    ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT] // 使消息持久化
                );
                // 发送消息
                if($channel->basic_publish($msg, '', 'task_queue')){
                    $x++;
                }else{
                    $DB->exec("DELETE FROM `ozon_products` WHERE `sku`='{$sku}'");
                    $i++;
                }
                
                /*
                $data = $DB->find('products', '*', ['sku' => $sku,'uid'=>$uid]);
                if($data){
                    $store = $DB->find('store', '*', ['id'=>$data['storeid']]);
                    $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
                    $json = $client->stockswarehouse($sku);
                    $productData = ['stock'=>0,'warehouse_id'=>0];
                    foreach ($json as $item){
                        if($item['present']>$productData['stock']){
                            $productData = ['sku'=>$item['sku'],'stock'=>$item['present'],'warehouse_id'=>$item['warehouse_id'],'product_id'=>$item['product_id']];
                        }
                    }
                    $multiplier = 2; 
                    $productData['uid'] = $uid;
                    $productData['storeid'] = $data['storeid'];
                    $productData['status'] = '重新上架';
                    $productData['title'] = $data['name'];
                    $productData['price'] = $data['price'];
                    $productData['old_price'] = $data['old_price']*$multiplier;
                    $productData['primary_image'] = $data['primary_image'];
                    $productData['date'] = date("Y-m-d");
                    $productData['addtime'] = $date;
                    $productData['type'] = 'reupload';
                    if($DB->insert('cron', $productData)){
                        $DB->exec("DELETE FROM `ozon_products` WHERE `sku`='{$sku}'");
                        $i++;
                    }else{
                        $x++;
                    }
                }
                */
            }
            $channel->close();
            $connection->close();
            $result = ['code'=>0,'msg'=>"批量重传成功{$i}条，失败{$x}条"];
        } else {
            $result = ['code'=>-1,'msg'=>"SKU 数组为空！"];
        }
    } else {
        $sku = intval($_POST['sku']);
        $data = $DB->find('products', '*', ['sku' => $sku,'uid'=>$uid]);
        if($data){
            $store = $DB->find('store', '*', ['id'=>$data['storeid']]);
            $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
            $json = $client->stockswarehouse($sku);
            $productData = ['stock'=>0,'warehouse_id'=>0];
            foreach ($json as $item){
                if($item['present']>$productData['stock']){
                    $productData = ['sku'=>$item['sku'],'stock'=>$item['present'],'warehouse_id'=>$item['warehouse_id'],'product_id'=>$item['product_id']];
                }
            }
             //$multiplier = 1.5; 
            $productData['uid'] = $uid;
            $productData['storeid'] = $data['storeid'];
            $productData['status'] = '重新上架';
            $productData['title'] = $data['name'];
            $productData['price'] = $data['price'];
           // $productData['old_price'] = $data['old_price']*$multiplier;
            $productData['old_price'] = $data['old_price'];
            $productData['primary_image'] = $data['primary_image'];
            $productData['date'] = date("Y-m-d");
            $productData['addtime'] = $date;
            $productData['type'] = 'reupload';
            if($DB->insert('cron', $productData)){
                $result = ['code'=>0,'msg'=>'重传任务成功'];
            }else{
                $result = ['code'=>-1,'msg'=>'重传失败或数据不存在'];
            }
        }else{
            $result = ['code'=>-1,'msg'=>'数据不存在'];
        }
    }
    exit(json_encode($result));
break;

case 'get_warehouses':
    $id = intval($_POST['id']);
    error_log("get_warehouses called for store id=$id, uid=$uid");
    
    $row = $DB->getRow("SELECT * FROM ozon_store WHERE id=:id AND uid=:uid", [':id'=>$id, ':uid'=>$uid]);
    if (!$row) {
        error_log("Store not found: id=$id, uid=$uid");
        exit(json_encode(['code'=>-1, 'msg'=>'店铺不存在']));
    }
    
	$list2 = [];
	$client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
    
    if(!$row['warehouses'] || $row['warehouses'] == 'null'){
        // 获取仓库信息
        $list2 = $client->Ozonwarehouses();
        
        // 确保返回的是数组
        if (is_array($list2) && !empty($list2)) {
            // 正确更新数据库中的warehouses字段
            $warehouses_json = json_encode($list2);
            $DB->update('ozon_store', ['warehouses' => $warehouses_json], ['id' => $id]);
            error_log("Updated warehouses for store id=$id: " . substr($warehouses_json, 0, 100) . "...");
        } else {
            error_log("Failed to get warehouses for store id=$id: " . print_r($list2, true));
            $list2 = [];
        }
    } else {
        $list2 = json_decode($row['warehouses'], true);
        if (!is_array($list2)) {
            error_log("Invalid warehouses data in DB for store id=$id: " . $row['warehouses']);
            $list2 = [];
        }
    }
    
	exit(json_encode(['code'=>0, 'data'=>$list2]));
break;


// 添加异步更新仓库信息的函数
function async_update_warehouse($store_id, $client_id, $key) {
    // 这里只是简单地记录日志，实际应该异步调用API更新仓库信息
    error_log("Async warehouse update triggered for store_id=$store_id");
    
    try {
        $client = new \lib\OzonApiClient($client_id, $key);
        $warehouses = $client->Ozonwarehouses();
        
        if (is_array($warehouses) && !empty($warehouses)) {
            global $DB;
            $warehouses_json = json_encode($warehouses);
            $DB->update('ozon_store', ['warehouses' => $warehouses_json], ['id' => $store_id]);
            error_log("Successfully updated warehouses for store_id=$store_id");
            return true;
        }
    } catch (Exception $e) {
        error_log("Error updating warehouses for store_id=$store_id: " . $e->getMessage());
    }
    
    return false;
}

case 'getShops':
    // 安全查询
    $list = $DB->getAll("SELECT id, storename, ClientId, `key`, warehouses FROM ozon_store WHERE uid = ?", [$uid]);
    $list2 = [];
    
    foreach ($list as $row) {
        // 触发异步更新仓库（如果缺失）
        if (empty($row['warehouses']) || $row['warehouses'] === 'null') {
            async_update_warehouse($row['id'], $row['ClientId'], $row['key']);
            $row['warehouses'] = []; // 前端展示空数组
        } else {
            $row['warehouses'] = json_decode($row['warehouses'], true);
        }
        
        // 仅返回必要字段
        $list2[] = [
            'id' => $row['id'],
            'storename' => $row['storename'],
            'warehouses' => $row['warehouses']
        ];
    }
    
    exit(json_encode(['code' => 0, 'data' => $list2]));
case 'cron_list':
    $status = trim($_GET['status']);
    $offer_id = trim($_GET['offer_id']);
    $sku = intval($_GET['sku']);
    $shop_id = isset($_GET['shop_id']) ? $_GET['shop_id'] : '';
    
    $sql=" A.uid={$uid}";
    if($sku){
        $sql.=" AND A.sku='{$sku}'";
    }
    if($offer_id){
        $sql.=" AND A.offer_id='{$offer_id}'";
    }
    if($shop_id){
        // 处理多选店铺的情况
        if(is_array($shop_id)){
            $shop_ids = array_map('intval', $shop_id);
            $shop_ids = array_filter($shop_ids); // 过滤掉0和空值
            if(!empty($shop_ids)){
                $shop_ids_str = implode(',', $shop_ids);
                $sql.=" AND A.storeid IN ($shop_ids_str)";
            }
        } else if(strpos($shop_id, ',') !== false) {
            // 处理逗号分隔的字符串
            $shop_ids = explode(',', $shop_id);
            $shop_ids = array_map('intval', $shop_ids);
            $shop_ids = array_filter($shop_ids); // 过滤掉0和空值
            if(!empty($shop_ids)){
                $shop_ids_str = implode(',', $shop_ids);
                $sql.=" AND A.storeid IN ($shop_ids_str)";
            }
        } else {
            $shop_id = intval($shop_id);
            if($shop_id > 0){
                $sql.=" AND A.storeid='{$shop_id}'";
            }
        }
    }
    if($status){
        $sql.=" AND (A.status='{$status}' OR A.msg LIKE '%{$status}%')";
    }
    
    $page = intval($_GET['page']);
	$limit = intval($_GET['limit']);
	$page=isset($_GET['page'])?intval($_GET['page']):1;
    $offset=$limit*($page - 1);
    $total = $DB->getColumn("SELECT count(*) from ozon_cron A WHERE{$sql}");
	$list = $DB->getAll("SELECT A.*,B.storename FROM ozon_cron A LEFT JOIN ozon_store B ON A.storeid=B.id LEFT JOIN ozon_weight C ON A.sku=C.sku WHERE{$sql} ORDER BY A.addtime DESC limit $offset,$limit");
	$list2 = [];
	foreach($list as $row){
	    unset($row['offer_id2']);
	    unset($row['return']);
	    unset($row['task_id2']);
	    $list2[] = $row;
	}
	exit(json_encode(['code'=>0,'msg'=>'success','count'=>$total,'data'=>$list2]));
break;
case 'cron_reupload':
    $id = intval($_POST['id']);
    $sku = intval($_POST['sku']);
    if($DB->update('cron', ['status'=>'准备中','msg'=>'','time'=>0,'cron'=>0,'nums'=>0], ['id'=>$id,'uid'=>$uid])){ #$uid 隐藏在全局文件
        $result = ['code'=>0,'msg'=>'重传任务成功'.unlink(ROOT."assets/json/特征数据库/{$sku}_2.json")];
    }else{
        $result = ['code'=>-1,'msg'=>'重传失败或数据不存在'];
    }
    exit(json_encode($result));
break;
case 'batch_cron_reupload':
    if ($_POST && isset($_POST['ids'])) {
        $ids = $_POST['ids']; // 获取前端传来的ID数组
        if (!empty($ids)) {
            $success = 0;
            $failed = 0;
            foreach ($ids as $id) {
                $id = intval($id);
                if(empty($id)) continue;
                if($DB->update('cron', ['status'=>'准备中','msg'=>'','time'=>0], ['id'=>$id,'uid'=>$uid])){
                    $success++;
                }else{
                    $failed++;
                }
            }
            $result = ['code'=>0, 'msg'=>"批量重传成功{$success}条，失败{$failed}条"];
        } else {
            $result = ['code'=>-1, 'msg'=>"ID数组为空！"];
        }
    } else {
        $result = ['code'=>-1, 'msg'=>"请求参数错误"];
    }
    exit(json_encode($result));
break;

case 'bestsellers':
    $offset = $_GET['page']-1;
    $filter['stock'] = "any_stock";
    $filter['period'] = "monthly";
    
    // 原有参数
    if($_GET['sku']){
        $filter['sku'] = $_GET['sku'];
    }
    if($_GET['categorie']){
        $filter['categories'] = [$_GET['categorie']];
    }
    if($_GET['company_id']){
        $filter['company_ids'] = [$_GET['company_id']];
    }
    
    // 排序设置 - 简化版本，只使用已知有效的排序
    $sort_key = 'sum_gmv_desc'; // 默认按GMV降序
    if($_GET['sort_field'] && $_GET['sort_order']){
        $sort_field = $_GET['sort_field'];
        $sort_order = $_GET['sort_order'] === 'asc' ? 'asc' : 'desc';
        
        // 简化的字段映射，只使用确认有效的
        $sort_mapping = [
            'soldCount' => 'sold_count',
            'gmvSum' => 'sum_gmv',
            'sessionCount' => 'session_count'
        ];
        
        if(isset($sort_mapping[$sort_field])){
            $sort_key = $sort_mapping[$sort_field] . '_' . $sort_order;
        }
    }
    
    $array  = [
        'limit'=>$_GET['limit']?$_GET['limit']:50,
        'offset'=>$offset,
        'filter'=>$filter,
        'sort'=>['key'=>$sort_key]
    ];
    
    // 添加调试信息
    error_log('Bestsellers API Request: ' . json_encode($array));
    
    $url = "https://seller.ozon.ru/api/site/seller-analytics/what_to_sell/data/v3";
    $referer = "https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers";
    $res = doRequests($url,$array, $referer);
    
    // 记录响应调试信息
    error_log('Bestsellers API Response Status: ' . $res['status']);
    
    // 如果有筛选参数，尝试在前端进行筛选
    $response_data = json_decode($res['body'], true);
    if($response_data && isset($response_data['items'])) {
        $items = $response_data['items'];
        
        // 前端筛选 - 标题关键词
        if($_GET['title_keyword']) {
            $keyword = trim($_GET['title_keyword']);
            $items = array_filter($items, function($item) use ($keyword) {
                return stripos($item['name'], $keyword) !== false;
            });
        }
        
        // 前端筛选 - 价格区间
        if($_GET['min_price'] || $_GET['max_price']) {
            $min_price = $_GET['min_price'] ? floatval($_GET['min_price']) : 0;
            $max_price = $_GET['max_price'] ? floatval($_GET['max_price']) : PHP_FLOAT_MAX;
            $items = array_filter($items, function($item) use ($min_price, $max_price) {
                $price = floatval($item['avgPrice']);
                return $price >= $min_price && $price <= $max_price;
            });
        }
        
        // 前端筛选 - 销量区间
        if($_GET['min_sold'] || $_GET['max_sold']) {
            $min_sold = $_GET['min_sold'] ? intval($_GET['min_sold']) : 0;
            $max_sold = $_GET['max_sold'] ? intval($_GET['max_sold']) : PHP_INT_MAX;
            $items = array_filter($items, function($item) use ($min_sold, $max_sold) {
                $sold = intval($item['soldCount']);
                return $sold >= $min_sold && $sold <= $max_sold;
            });
        }
        
        // 前端筛选 - 加购率区间
        if($_GET['min_cart_rate'] || $_GET['max_cart_rate']) {
            $min_rate = $_GET['min_cart_rate'] ? floatval($_GET['min_cart_rate']) : 0;
            $max_rate = $_GET['max_cart_rate'] ? floatval($_GET['max_cart_rate']) : 100;
            $items = array_filter($items, function($item) use ($min_rate, $max_rate) {
                $rate = floatval($item['convToCartPdp']);
                return $rate >= $min_rate && $rate <= $max_rate;
            });
        }
        
        // 重新排序（如果需要）
        if($_GET['sort_field'] && $_GET['sort_order']) {
            $sort_field = $_GET['sort_field'];
            $sort_order = $_GET['sort_order'];
            
            usort($items, function($a, $b) use ($sort_field, $sort_order) {
                $val_a = isset($a[$sort_field]) ? $a[$sort_field] : 0;
                $val_b = isset($b[$sort_field]) ? $b[$sort_field] : 0;
                
                if($sort_order === 'asc') {
                    return $val_a <=> $val_b;
                } else {
                    return $val_b <=> $val_a;
                }
            });
        }
        
        // 重新计算总数和分页
        $total = count($items);
        $items = array_values($items); // 重新索引
        
        // 应用分页
        $limit = $_GET['limit'] ? intval($_GET['limit']) : 20;
        $page = $_GET['page'] ? intval($_GET['page']) : 1;
        $start = ($page - 1) * $limit;
        $items = array_slice($items, $start, $limit);
        
        $response_data['items'] = $items;
        $response_data['totals'] = $total;
        
        exit(json_encode($response_data));
    }
    
    exit($res['body']);
break;
case 'sellerbestsellers':
    $sku = isset($_GET['sku']) ? trim($_GET['sku']) : '';
    $title = isset($_GET['title']) ? trim($_GET['title']) : '';
    $seller_id = isset($_GET['seller_id']) ? trim($_GET['seller_id']) : '';
    $money = isset($_GET['money']) ? trim($_GET['money']) : '';
    $moneys = isset($_GET['moneys']) ? trim($_GET['moneys']) : '';
    $g = isset($_GET['g']) ? trim($_GET['g']) : '';
    $gs = isset($_GET['gs']) ? trim($_GET['gs']) : '';
    $sl = isset($_GET['sl']) ? trim($_GET['sl']) : '';
    $sls = isset($_GET['sls']) ? trim($_GET['sls']) : '';
    $gm = isset($_GET['gm']) ? trim($_GET['gm']) : '';
    $sql=" uid={$uid}";
    if($sku !== ''){
        $sql.=" AND A.sku='{$sku}'";
    }
    if($title !== ''){
        $sql.=" AND A.name LIKE '%{$title}%'";
    }
    if($seller_id !== ''){
        $sql.=" AND A.seller_id='{$seller_id}'";
    }
    if($money !== '' || $moneys !== ''){
        if($money === '') $money = 0;
        if($moneys === '') $moneys = 999999999;
       $sql.=" AND A.zprice >= '{$money}' AND A.zprice <= '{$moneys}'";
    }
    if($sl !== '' || $sls !== ''){
        if($sl === '') $sl = 0;
        if($sls === '') $sls = 999999999;
        $sql.=" AND A.sold_count >= '{$sl}' AND A.sold_count <= '{$sls}'";
    }
    if($g !== '' || $gs !== ''){
        if($g === '') $g = 0;
        if($gs === '') $gs = 999999999;
        $sql.=" AND B.weight >= '{$g}' AND B.weight <= '{$gs}'";
    }
    if($gm !== ''){
        $sql.=" AND A.gm='{$gm}'";
    }
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
    $offset = $limit * ($page - 1);
    $total = $DB->getColumn("SELECT count(*) from ozon_seller A WHERE{$sql}");
    $list = $DB->getAll("SELECT A.*,B.weight,B.depth,B.height,B.width FROM ozon_seller A LEFT JOIN ozon_weight B ON A.sku=B.sku WHERE{$sql} ORDER BY A.create_date DESC limit $offset,$limit");
    $list2 = [];
    foreach($list as $row){
        $row['rmb'] = round($row['price']/$fx['ru']['num'],2);
        $row['us'] = round($row['rmb']*$fx['us']['num'],2);
        $list2[] = $row;
    }
    exit(json_encode(['code'=>0,'msg'=>'success','count'=>$total,'data'=>$list2]));
break;
case 'getselle':
    $list = $DB->getAll("SELECT * FROM ozon_sellerjk WHERE uid = ?", [$uid]);
    $list2 = [];
    
    foreach ($list as $row) {
        $list2[] = $row;
    }
    exit(json_encode(['code' => 0, 'data' => $list2]));
break;
case 'getstore':
    $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid={$uid}");
	$list2 = [];
	foreach($list as $row){
	    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
	    $client->productinfolimit($row);
	    if(!$row['warehouses'] or $row['warehouses']=='null'){
	        $row['warehouses']=$client->Ozonwarehouses();
	        $DB->update('store', ['warehouses'=>json_encode($row['warehouses'])], ['id'=>$row['id']]);
	    }else{
	        $row['warehouses']=json_decode($row['warehouses'],true);
	    }
	    unset($row['apistatus']);
	    unset($row['key']);
	    unset($row['cookie']);
	    unset($row['addtime']);
	    unset($row['status']);
	    unset($row['uid']);
		$list2[] = $row;
		$i++;
	}
	exit(json_encode(['code'=>0,'stores'=>$list2,'fx'=>$fx['ru']]));
break;
case 'getShopGroups':
        $uid = $islogin2 ? $userrow['uid'] : 0;
        if (empty($uid)) {
            exit(json_encode(['code' => -1, 'msg' => '未登录']));
        }
        $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
        $shop_groups = $user && $user['shop_groups'] 
            ? json_decode($user['shop_groups'], true) 
            : ['groups' => [], 'defaultGroupId' => null];
        
        // 确保每个分组都有shopIds属性
        $groups = $shop_groups['groups'] ?? [];
        foreach ($groups as &$group) {
            if (!isset($group['shopIds'])) {
                $group['shopIds'] = [];
            }
        }
        
        exit(json_encode([
            'code' => 0,
            'msg' => 'success',
            'data' => $groups,
            'defaultGroupId' => $shop_groups['defaultGroupId'] ?? null
        ]));
    break;
case 'production_list':
    $status = trim($_GET['status']);
    $posting_number = trim($_GET['posting_number']);
    $sku = intval($_GET['sku']);
    $sql=" uid={$uid}";
    if($sku){
        $sql.=" AND sku='{$sku}'";
    }
    
    $page = intval($_GET['page']);
	$limit = intval($_GET['limit']);
	$page=isset($_GET['page'])?intval($_GET['page']):1;
    $offset=$limit*($page - 1);
    $total = $DB->getColumn("SELECT count(*) from ozon_production WHERE{$sql}");
	$list = $DB->getAll("SELECT * FROM ozon_production WHERE{$sql} ORDER BY addtime DESC limit $offset,$limit");
	$list2 = [];
	foreach($list as $row){
	    $row['category'] = json_decode($row['category_chain'],true)['texts'];
	    $list2[] = $row;
	}
	exit(json_encode(['code'=>0,'msg'=>'success','count'=>$total,'data'=>$list2]));
break;
case 'get_category_attributes':
    $client = new \lib\OzonApiClient(2763302, '0a1f6874-fe81-4b30-97ce-4a3339eb077e');
    $data = $client->attribute(['description_category_id'=>$_POST['category2'],'type_id'=>$_POST['category3']]);
    exit(json_encode($data?['code'=>0,'data'=>$data]:['code'=>-1]));
break;
case 'get_sku':
    // 初始化代理轮换器
    $sku = (string) get_sku($_POST['skuurl']);
    $referer = 'https://seller.ozon.ru/app/products/add/general-info';
    $result = doRequests('https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku',['skus'=>[$sku]], $referer);
    $json = json_decode($result['body'],true);
    $data = $json['resolved_categories_by_sku'][$sku];
    $data['sku'] = $sku;
    $data['code']=0;
    //exit(json_encode($result2));
    exit(json_encode($data));
break;
case 'get_sku_attributes':
    // 初始化代理轮换器
    $sku = (string) get_sku($_POST['skuurl']);
    $referer = 'https://seller.ozon.ru/app/products/create';
    $result = doRequests('https://seller.ozon.ru/api/v1/search-variant-model',['name'=>$sku,'limit'=>'10'],$referer);
    $json = json_decode($result['body'],true);
    $data = $json['items'][0];
    $data['sku'] = $sku;
    $data['code']=0;
    exit(json_encode($data));
break;
case 'get_sku_keywords':
    // 初始化代理轮换器
    $sku = (string) get_sku($_POST['skuurl']);
    $referer = 'https://seller.ozon.ru/app/products/create';
    $result = doRequests('https://seller.ozon.ru/api/v1/search-variant-model',['name'=>$sku,'limit'=>'10'],$referer);
    $json = json_decode($result['body'],true);
    $data = $json['items'][0]['attributes'];
    $i = 0;
    foreach ($data as $itmt){
        if($itmt['key']==22336){
            $keywords = $itmt['value'];
            break;
        }
    }
    //usort($data);
    $data = [];
    $data['sku'] = $sku;
    if($keywords){
       $data['keywords'] = $keywords;
    $data['code']=0;  
    }else{
        $data['code']=-1;
        $data['msg'] = '查询不到';
    }
   
    
    exit(json_encode($data));
break;
case 'get_sku_attributevalues':
    $url = SYSTEM_ROOT.'../assets/ozon/attributes/attribute_'.$_POST['attribute_id'].'.json';
    $redisdata = file_get_contents($url);
    if($redisdata){
        exit(json_encode(['code'=>0,'data'=>json_decode($redisdata,true)]));
    }
    $item = ['attribute_id'=>$_POST['attribute_id'],'description_category_id'=>$_POST['category2'],'type_id'=>$_POST['category3']];
    $client = new \lib\OzonApiClient(2763302, '0a1f6874-fe81-4b30-97ce-4a3339eb077e');
    $data = $client->attributevalues($item);
    foreach ($data as $imte){
        $json3[$imte['id']] = $imte['value'];
    }
    $item = ['attribute_id'=>$_POST['attribute_id'],'description_category_id'=>$_POST['category2'],'type_id'=>$_POST['category3'],'language'=>'DEFAULT'];
    $client = new \lib\OzonApiClient(2763302, '0a1f6874-fe81-4b30-97ce-4a3339eb077e');
    $data = $client->attributevalues($item);
    foreach ($data as $imte){
        $json4[] = ['id'=>$imte['id'],'name'=>$json3[$imte['id']],'value'=>$imte['value'],'info'=>$imte['info'],'picture'=>$imte['picture']];
    }
    if($data){
        file_put_contents($url,json_encode($json4, JSON_UNESCAPED_UNICODE));
    }
    exit(json_encode($data?['code'=>0,'data'=>$json4]:['code'=>-1]));
break;
case 'save_production':
    $json_data = file_get_contents('php://input');
    $data = json_decode(trim($json_data,':'), true);
    $id = intval($data['id']);
    $seller_lisr = $data['selectedStores'];
    
    $attributes = [];
    if($data['attributes[85]']=="" or empty($data['attributes[85]'])){
        $attributes[]=['id'=>85,
            'complex_id'=>0,
            'values'=>[['dictionary_value_id'=>126745801,'value'=>'Нет бренда']]
        ];
    }
    if($data['height']){
        $datas['height'] = $data['height'];
        $attributesdata[] = ['attributes[9456]'=>$data['height']];
    }
    if($data['depth']){
        $datas['depth'] = $data['depth'];
        $attributesdata[] = ['attributes[9454]'=>$data['depth']];
    }
    if($data['width']){
        $datas['width'] = $data['width'];
        $attributesdata[] = ['attributes[9455]'=>$data['width']];
    }
    if($data['weight']){
        $datas['weight'] = $data['weight'];
        $attributesdata[] = ['attributes[4497]'=>$data['weight']];
    }
    foreach ($data as $key => $value) {
        if(empty($value))continue;
        $values = [];
        if (strpos($key, 'attributes[') === 0) {
            preg_match('/attributes\[(\d+)\]/', $key, $matches);
            $attrId = $matches[1];
            if(is_array($value)){
                foreach ($value as $item){
                    $attribute_id_value = attribute_id_value($attrId,$item);
                    if($attribute_id_value){
                        $values[] = $attribute_id_value;
                    }else{
                        $values[] = ['dictionary_value_id'=>0,'value'=>$item];
                    }
                }
            }else{
                $attribute_id_value = attribute_id_value($attrId,$value);
                if($attribute_id_value){
                    $values[] = $attribute_id_value;
                }else{
                    $values[] = ['dictionary_value_id'=>0,'value'=>$value];
                }
            }
            if($attrId){
                $attributes[] = ['id'=>$attrId,
                    'complex_id'=>0,
                    'values'=>$values
                ];
                $attributesdata[] = [$key=>$value];
            }
        }else{
            $array[$key] = $value;
        }
    }
    if($data['title']){
        $datas['title'] = htmlspecialchars($data['title']);
    }
    if($data['images']){
        $images = array_filter(explode(';', $data['images']));
        $images = array_map('htmlspecialchars', $images);
        $images2 = $images;
        $images_str = implode(';', $images);
        $datas['images'] = $images_str;
        $attributes[] = ['id'=>4194,'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$images2[0]]]];
        $datas['mainImage'] = $images2[0];
        unset($images2[0]);
        $values = [];
        foreach ($images2 as $img){
            $values[]=['dictionary_value_id'=>0,'value'=>$img];
        }
        $attributes[] = ['id'=>4195,'complex_id'=>0,'values'=>$values];
    }
    $array['attributes'] = $attributes;
    $datas['attributes'] = json_encode($attributes);
    $datas['attributesdata'] = json_encode($attributesdata);
    $datas['category_chain'] = json_encode($data['category_chain']);
    if($data['price']){
        $datas['price'] = trim(daddslashes($data['price']));
    }
    if($data['old_price']){
        $datas['old_price'] = trim(daddslashes($data['old_price']));
    }
    if($data['offer_id']){
        $datas['offer_id'] = trim(daddslashes($data['offer_id']));
    }
    
    if($data['is_publish']==1){
        $rows = $DB->getRow("SELECT * FROM ozon_production WHERE id={$id} AND uid={$uid}");
        $attributes[] = ['id'=>11254,'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$rows['json']]]];
        foreach ($seller_lisr as $item){
            $store_id = intval($item['storeId']);
            $row = $DB->getRow("SELECT * FROM ozon_store WHERE id={$store_id} AND uid={$uid}");
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $gmdata = $client->productimports($array,$attributes);
            if($gmdata['result']['task_id']){
                $datas['task_id'] = $gmdata['result']['task_id'];
            }
        }
        
    }
     
    // 判断是新增还是更新
    if($id > 0) {
        // 更新现有商品
        if($DB->update('production', $datas, ['id' => $id, 'uid' => $uid])){
            $result = ['code'=>0, 'msg'=>'更新成功'];
        }else{
            $result = ['code'=>-1, 'msg'=>'更新失败'];
        }
    } else {
        // 新增商品
        $datas['uid'] = $uid;
        $datas['addtime'] = date('Y-m-d H:i:s');
        
        // 如果没有提供offer_id，自动生成一个
        if(empty($datas['offer_id'])) {
            $client = new \lib\OzonApiClient();
            $datas['offer_id'] = $client->generate_offer_id();
        }
        
        if($DB->insert('production', $datas)){
            $result = ['code'=>0, 'msg'=>'保存成功'];
        }else{
            $result = ['code'=>-1, 'msg'=>'保存失败: ' . $DB->error()];
        }
    }
    exit(json_encode($result));
break;
case 'translate':
    $text = htmlspecialchars($_POST['text']);
    $source = $_POST['source'];
    $target = $_POST['target'];
    $translated = [
        'code' => 0,
        'data' => [
            'translated_text' => Transmart($text,$source,$target) // 示例返回
        ]
    ];
    exit(json_encode($translated));
break;
case 'submit_ImgTranslate':
    $json_data = file_get_contents('php://input');
    //exit(urldecode($json_data));
    $data = json_decode(trim($json_data,':'), true);
    switch($data['service']){
    case 1:
        $code = '9897275551';#阿里标识码
    break;
    case 2:
        $code = '9407294493';#谷歌标识码
    break;
    case 3:
        $code = '2985122323';#Papago标识码
    break;
    case 4:
        $code = '9440763900';#DeepL标识码
    break;
    case 5:
        $code = '9897275551';#ChatGPT标识码
    break;
    case 5:
        $code = '8107816915';#百度标识码
    break;
    default:
        $code = '9897275551';#阿里标识码
    break;
    }
    $xjai = new \lib\XiangJiAI('1569555821'); // 用户密钥
    $xjai->setImgTransKey($code); // 图片翻译服务阿里云标识码
    foreach ($data['images'] as $originUrl){
        $response = $xjai->getImageTranslate($originUrl, 'CHS', 'RUS');
        $images[] = [$originUrl,$response['Data']['Url']];
    }
    exit(json_encode(['code'=>0,'images'=>$images,'result'=>$response]));
break;
case 'json_translate':
    $json_data = file_get_contents('php://input');
    //exit(urldecode($json_data));
    $data = json_decode(trim($json_data,':'), true);
    $url = $data['url'];
    $code = '9897275551';#阿里标识码
    $xjai = new \lib\XiangJiAI('1569555821'); // 用户密钥
    $xjai->setImgTransKey($code); // 图片翻译服务阿里云标识码
    $response = $xjai->getImageTranslate($url, 'CHS', 'RUS');
    exit(json_encode(['code'=>0,'translatedUrl'=>$response['Data']['Url'],'result'=>$response]));
break;
case 'save_json':
    $json_data = file_get_contents('php://input');
    $data = json_decode(trim($json_data,':'), true);
    $id = intval($data['id']);
    $i=0;
    foreach ($data['data']['content'][0]['blocks'] as $item){
        try {
            $url = convertToOzonImage($item['img']['src']);
            if($url){
                $data['data']['content'][0]['blocks'][$i]['img']['src'] = $url;
                $data['data']['content'][0]['blocks'][$i]['img']['srcMobile'] = $url;
                unset($data['data']['content'][0]['blocks'][$i]['img']['translated']);
                unset($data['data']['content'][0]['blocks'][$i]['img']['translatedMobile']);
            }
            // 如果转换失败，保留原始图片URL，不删除整个图片块
        } catch (Exception $e) {
            // 转换过程中出现异常，记录错误但保留原始数据
            error_log("图片转换失败: " . $e->getMessage() . " - URL: " . $item['img']['src']);
        }
        $i++;
    }
    if($DB->update('production', ['json'=>json_encode($data['data'])], ['id' => $id])){
        $result = ['code'=>0];
    }else{
        $result = ['code'=>-1];
    }
    exit(json_encode($result));
break;
case 'delete_production':
    $id = intval(trim($_POST['id']));
    if($DB->exec("DELETE FROM ozon_production WHERE id='$id' AND uid='$uid'")){
        $result = ['code'=>0];
    }else{
        $result = ['code'=>-1];
    }
    exit(json_encode($result));
break;
case 'get_shops':
    $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid={$uid}");
	$list2 = [];
	foreach($list as $row){
	    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);

	    unset($row['apistatus']);
	    unset($row['key']);
	    unset($row['cookie']);
	    unset($row['addtime']);
	    unset($row['status']);
	    unset($row['uid']);
		$list2[] = $row;
		$i++;
	}
	exit(json_encode(['code'=>0,'data'=>$list2]));
break;

case 'queries':
    $limit= $_GET['limit'];
    $page=$_GET['page']-1;
    $filter['stock'] = "any_stock";
    $filter['period'] = "monthly"; //days_28
    if($_GET['sku']){
        $filter['sku'] = $_GET['sku'];
    }
    if($page>=1){
       $page = intval($limit*$page);
    }
    $array  = [
        'text'=>$_GET['text']?$_GET['text']:'',
        'limit'=>$_GET['limit']?$_GET['limit']:50,
        'offset'=>$page,
        'sort_by'=>$_GET['sort_by']?$_GET['sort_by']:'count',
        'sort_dir'=>$_GET['sort_dir']?$_GET['sort_dir']:'desc',
        'period'=>$period?$period:'monthly'
    ];
    $url = "https://seller.ozon.ru/api/site/searchteam/Stats/queries/search/v2";
    $referer = "https://seller.ozon.ru/app/analytics/what-to-sell/all-queries";
    $res = doRequests($url,$array, $referer);
    //$res = doRequests($url,$array, $referer, $DB->find('dailiip', '*', ['id' => 1]));
    //exit(json_encode($res));
    
    $json = json_decode($res['body'],true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        exit("哈哈哈");
    }
    if($fx['ru']['num']){
        foreach($json['data'] as $row){
    	    $row['gmvrmb'] = round($row['gmv']/$fx['ru']['num'],2);
    	    $row['gmvus'] = round($row['gmvrmb']*$fx['us']['num'],2);
    	    $row['avgCaRubrmb']  = round($row['avgCaRub']/$fx['ru']['num'],2);
    	    $row['avgCaRubus'] = round($row['avgCaRubrmb']*$fx['us']['num'],2);
    	    $list2[] = $row;
    	}
        exit(json_encode(['code'=>0,'msg'=>'success','count'=>$json['total'],'data'=>$list2]));
    }
    exit($res['body']);
break;
case 'save_edited_image':
    $image_data = isset($_POST['image_data']) ? $_POST['image_data'] : '';
    $image_type = isset($_POST['image_type']) ? $_POST['image_type'] : 'jpg';
    $record_id = isset($_POST['record_id']) ? $_POST['record_id'] : '';
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $original_index = isset($_POST['original_index']) ? intval($_POST['original_index']) : 0;
    
    if (empty($image_data)) {
        exit(json_encode(['code' => -1, 'msg' => '图片数据不能为空']));
    }
    
    try {
        // 从base64数据中提取图片内容
        if (preg_match('/^data:image\/(\w+);base64,/', $image_data, $matches)) {
            $image_type = $matches[1];
            $image_data = substr($image_data, strpos($image_data, ',') + 1);
        }
        
        // 解码base64数据
        $image_content = base64_decode($image_data);
        if ($image_content === false) {
            throw new Exception('图片数据解码失败');
        }
        
        // 生成文件名
        $filename = 'mt_edited_' . time() . '_' . uniqid() . '.' . $image_type;
        $filepath = '../uploads/' . $filename;
        
        // 保存文件
        if (file_put_contents($filepath, $image_content) === false) {
            throw new Exception("无法保存编辑后的图片到 {$filepath}");
        }
        
        // 生成访问URL
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $domain = $_SERVER['HTTP_HOST'];
        $new_image_url = $protocol . $domain . '/uploads/' . $filename;
        
        // 如果有产品ID，可以在这里更新数据库中的图片记录
        if ($product_id > 0) {
            // 获取当前产品的图片列表
            $product_row = $DB->getRow("SELECT images FROM production WHERE id=:id AND uid=:uid", [':id'=>$product_id, ':uid'=>$uid]);
            if ($product_row && $product_row['images']) {
                $images = explode(';', $product_row['images']);
                if (isset($images[$original_index])) {
                    // 替换指定索引的图片
                    $images[$original_index] = $new_image_url;
                    $new_images_string = implode(';', $images);
                    
                    // 更新数据库
                    $DB->update('production', ['images' => $new_images_string], ['id' => $product_id, 'uid' => $uid]);
                }
            }
        }
        
        exit(json_encode([
            'code' => 0, 
            'data' => [
                'new_image_url' => $new_image_url,
                'record_id' => $record_id,
                'filename' => $filename
            ],
            'msg' => '图片编辑保存成功！'
        ]));
        
    } catch (Exception $e) {
        exit(json_encode(['code' => -1, 'msg' => $e->getMessage()]));
    }
    break;
case 'cnproducts':
    // 获取筛选参数
    $categorie1 = daddslashes($_GET['categorie1'] ?? '');
    $categorie2 = daddslashes($_GET['categorie2'] ?? '');
    $categorie3 = daddslashes($_GET['categorie3'] ?? '');
    $title = daddslashes($_GET['title'] ?? '');
    
    $sql = " 1=1";
    
    // 类目筛选
    if(!empty($categorie1)){
        $sql.=" AND categories1='$categorie1'";
    }
    if(!empty($categorie2)){
        $sql.=" AND categories2='$categorie2'";
    }
    if(!empty($categorie3)){
        $sql.=" AND categories3='$categorie3'";
    }
    
    // 标题搜索
    if(!empty($title)){
        $sql.=" AND name LIKE '%$title%'";
    }
    
    // 均价区间
    if(isset($_GET['price_min']) && is_numeric($_GET['price_min'])){
        $sql.=" AND avgGmv >= ".floatval($_GET['price_min']);
    }
    if(isset($_GET['price_max']) && is_numeric($_GET['price_max']) && $_GET['price_max'] !== ''){
        $sql.=" AND avgGmv <= ".floatval($_GET['price_max']);
    }
    
    // 销量区间
    if(isset($_GET['sold_min']) && is_numeric($_GET['sold_min'])){
        $sql.=" AND soldCount >= ".intval($_GET['sold_min']);
    }
    if(isset($_GET['sold_max']) && is_numeric($_GET['sold_max']) && $_GET['sold_max'] !== ''){
        $sql.=" AND soldCount <= ".intval($_GET['sold_max']);
    }
    
    // 跟卖数量区间
    if(isset($_GET['seller_min']) && is_numeric($_GET['seller_min'])){
        $sql.=" AND sellerlength >= ".intval($_GET['seller_min']);
    }
    if(isset($_GET['seller_max']) && is_numeric($_GET['seller_max']) && $_GET['seller_max'] !== ''){
        $sql.=" AND sellerlength <= ".intval($_GET['seller_max']);
    }
    
    // 详情页访问区间
    if(isset($_GET['session_min']) && is_numeric($_GET['session_min'])){
        $sql.=" AND sessionCount >= ".intval($_GET['session_min']);
    }
    if(isset($_GET['session_max']) && is_numeric($_GET['session_max']) && $_GET['session_max'] !== ''){
        $sql.=" AND sessionCount <= ".intval($_GET['session_max']);
    }
    
    // 创建时间筛选
    if(!empty($_GET['create_start'])){
        $create_start = daddslashes($_GET['create_start']);
        $sql.=" AND DATE(nullableCreateDate) >= '{$create_start}'";
    }
    if(!empty($_GET['create_end'])){
        $create_end = daddslashes($_GET['create_end']);
        $sql.=" AND DATE(nullableCreateDate) <= '{$create_end}'";
    }
    
    // 分页参数
    $page = intval($_GET['page'] ?? 1);
	$limit = intval($_GET['limit'] ?? 20);
    $offset = $limit * ($page - 1);
    
    // 排序参数
    $field = daddslashes($_GET['field'] ?? 'nullableCreateDate');
    $order = strtoupper($_GET['order'] ?? 'DESC');
    if(!in_array($order, ['ASC', 'DESC'])) $order = 'DESC';
    
    // 允许排序的字段白名单，防止SQL注入
    $allowedFields = [
        'soldCount', 'sellerlength', 'gmvSum', 'avgGmv', 'salesDynamics', 
        'sessionCount', 'avgDeliveryDays', 'accessibility', 'daysInPromo', 
        'promoRevenueShare', 'drr', 'discount', 'blockedBySeller', 'nullableCreateDate'
    ];
    if(!in_array($field, $allowedFields)) $field = 'nullableCreateDate';
    
    $total = $DB->getColumn("SELECT count(*) from ozon_cnproducts WHERE{$sql}");
	$list = $DB->getAll("SELECT * FROM ozon_cnproducts WHERE{$sql} ORDER BY {$field} {$order} limit $offset,$limit");
	
	$list2 = [];
	foreach($list as $row){
	    unset($row['offer_id2']);
	    unset($row['return']);
	    unset($row['task_id2']);
	    $list2[] = $row;
	}
	exit(json_encode(['code'=>0,'msg'=>'success','count'=>$total,'data'=>$list2]));
break;

case 'ai_image_edit':
        $image_url = isset($_POST['image_url']) ? $_POST['image_url'] : '';
        $prompt = isset($_POST['prompt']) ? $_POST['prompt'] : '';
    
        if (empty($image_url) || empty($prompt)) {
            exit(json_encode(['code' => -1, 'msg' => '缺少图片地址或修改指令']));
        }
    
        $apiKey = 'AIzaSyCF2HtqcRCXGC7p5JSx8-MQikMdifkPvZ8';
        $gatewayBaseUrl = 'https://gateway.ai.cloudflare.com/v1/0797abe7c3014751e75977f24210dcfa/gemini/google-ai-studio';
        $visionModel = 'gemini-2.0-flash-exp'; // Use a reliable vision model
    
        try {
            // Step 1: Use Gemini to generate a detailed prompt for the new image
            $image_data = @file_get_contents($image_url);
            if ($image_data === false) {
                throw new Exception('无法从URL获取图片内容，请检查图片链接是否可公开访问。');
            }
            $base64_image = base64_encode($image_data);
            $mime_type = 'image/jpeg';
            
            $visionUrl = "{$gatewayBaseUrl}/v1beta/models/{$visionModel}:generateContent?key=" . $apiKey;
    
            $generation_prompt = "The user wants to change the background of their product image. Their request is: '{$prompt}'.
            Analyze the main subject of the provided image. Create a detailed, photorealistic prompt in English for an AI image generation model (like Stable Diffusion) to recreate the exact same product with a new background.
            The prompt must include:
            1. A detailed description of the product itself (object type, material, color, texture, specific details).
            2. The desired new background as described by the user.
            3. Instructions for photorealistic lighting and high-quality rendering.
            The final output should be ONLY the English prompt, ready to be used for image generation.
            Example output for a blue vase with a request for 'white background': 'A photorealistic studio product shot of a glossy blue ceramic vase with intricate patterns, standing on a reflective surface. The background is a clean, seamless, pure white backdrop. The lighting is soft and diffused, highlighting the vase's texture. 8k, ultra-realistic.'";
    
            $vision_payload = [
                'contents' => [
                    ['parts' => [['text' => $generation_prompt], ['inline_data' => ['mime_type' => $mime_type, 'data' => $base64_image]]]]
                ]
            ];
    
            $ch_vision = curl_init($visionUrl);
            curl_setopt($ch_vision, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch_vision, CURLOPT_POSTFIELDS, json_encode($vision_payload));
            curl_setopt($ch_vision, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch_vision, CURLOPT_SSL_VERIFYPEER, false);
            $vision_response_str = curl_exec($ch_vision);
            $vision_http_code = curl_getinfo($ch_vision, CURLINFO_HTTP_CODE);
            curl_close($ch_vision);
    
            $vision_result = json_decode($vision_response_str, true);
    
            if ($vision_http_code !== 200 || !isset($vision_result['candidates'][0]['content']['parts'][0]['text'])) {
                 throw new Exception('Gemini Prompt生成失败: ' . ($vision_result['error']['message'] ?? '未知API错误') . ' (HTTP ' . $vision_http_code . ')');
            }
            
            $detailed_prompt = $vision_result['candidates'][0]['content']['parts'][0]['text'];
            // Clean up the prompt from potential markdown, and remove leading/trailing quotes
            $detailed_prompt = trim(str_replace(['```json', '```', 'json'], '', $detailed_prompt));
            $detailed_prompt = trim($detailed_prompt, '"\''); // Remove surrounding quotes
    
            // Log the prompt for debugging
            error_log("HF Prompt: " . $detailed_prompt);
    
    
            // Step 2: Use Hugging Face API for Stable Diffusion with your token, via Cloudflare Gateway
            $hf_token = '*************************************';
            // Correct endpoint format for Hugging Face models via Cloudflare Gateway
            $hf_endpoint = 'https://gateway.ai.cloudflare.com/v1/0797abe7c3014751e75977f24210dcfa/huggingface/models/runwayml/stable-diffusion-v1-5';
            
            $image_gen_payload = [
                'inputs' => $detailed_prompt,
                'parameters' => [
                    'num_inference_steps' => 20,
                    'guidance_scale' => 7.5
                ],
                'options' => ['wait_for_model' => true] // Wait if the model is loading
            ];
            
            $ch_image = curl_init($hf_endpoint);
            curl_setopt($ch_image, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $hf_token
            ]);
            curl_setopt($ch_image, CURLOPT_POSTFIELDS, json_encode($image_gen_payload));
            curl_setopt($ch_image, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch_image, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch_image, CURLOPT_CONNECTTIMEOUT, 30); 
            curl_setopt($ch_image, CURLOPT_TIMEOUT, 120); 
    
            $image_response_data = curl_exec($ch_image);
            $http_code = curl_getinfo($ch_image, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch_image);
            curl_close($ch_image);
    
            if ($http_code !== 200) {
                $error_details = json_decode($image_response_data, true);
                error_log("HF Error: " . $image_response_data); // Log the raw error
                $hf_error_msg = '未知错误';
                if (isset($error_details['error'])) {
                    // Display the full error, whether it's a string or an array
                    $hf_error_msg = is_array($error_details['error']) ? json_encode($error_details['error']) : $error_details['error'];
                }
                
                // Try alternative approach - use a different model or fallback
                if ($http_code === 404) {
                    // Try a different Stable Diffusion model
                    $alt_endpoint = 'https://gateway.ai.cloudflare.com/v1/0797abe7c3014751e75977f24210dcfa/huggingface/models/stabilityai/stable-diffusion-2-1';
                    
                    $ch_alt = curl_init($alt_endpoint);
                    curl_setopt($ch_alt, CURLOPT_HTTPHEADER, [
                        'Content-Type: application/json',
                        'Authorization: Bearer ' . $hf_token
                    ]);
                    curl_setopt($ch_alt, CURLOPT_POSTFIELDS, json_encode($image_gen_payload));
                    curl_setopt($ch_alt, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch_alt, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch_alt, CURLOPT_CONNECTTIMEOUT, 30); 
                    curl_setopt($ch_alt, CURLOPT_TIMEOUT, 120); 

                    $alt_response = curl_exec($ch_alt);
                    $alt_http_code = curl_getinfo($ch_alt, CURLINFO_HTTP_CODE);
                    curl_close($ch_alt);
                    
                    if ($alt_http_code === 200) {
                        $image_response_data = $alt_response;
                        $http_code = $alt_http_code;
                    } else {
                        throw new Exception("主要和备用图片生成API都调用失败。主要错误: (HTTP: {$http_code}): " . $hf_error_msg . ". 备用错误: (HTTP: {$alt_http_code})");
                    }
                } else {
                    throw new Exception("图片生成API调用失败 (HTTP: {$http_code}): " . $hf_error_msg . ". Curl Error: " . $curl_error);
                }
            }
    
            // Save the generated image
            $filename = 'ai_generated_' . uniqid() . '.jpeg';
            $filepath = '../uploads/' . $filename;
            if (file_put_contents($filepath, $image_response_data) === false) {
                throw new Exception("无法保存生成的图片到 {$filepath}");
            }
    
            $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
            $domain = $_SERVER['HTTP_HOST'];
            $new_image_url = $protocol . $domain . '/uploads/' . $filename;
    
            exit(json_encode([
                'code' => 0, 
                'data' => [
                    'new_image_url' => $new_image_url,
                    'generated_prompt' => $detailed_prompt
                ],
                'msg' => '图片生成成功！'
            ]));
    
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => $e->getMessage()]));
        }
        break;
        

default:
	exit('{"code":-4,"msg":"No Act"}');
break;

}

// 辅助函数
function getCurrencyCode($value) {
    $map = [
        '1' => 'CNY',
        '2' => 'RUB',
        '3' => 'USD'
    ];
    return $map[$value] ?? '';
}