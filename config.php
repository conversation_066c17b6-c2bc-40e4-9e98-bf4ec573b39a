<?php
    /*数据库配置*/
    $dbconfig=array(
        'host' => 'localhost', //数据库服务器
        'port' => 3306, //数据库端口
        'user' => 'erp', //数据库用户名
        'pwd' => 'prn5EhLpGCwyCEeX', //数据库密码
        'dbname' => 'erp', //数据库名
        'dbqz' => 'ozon' //数据表前缀
    );
    
    $Raconfig=array(
        'host' => 'localhost', //rabbitmq服务器
        'port' => 5672, //rabbitmq端口
        'user' => 'rabbitmq', //rabbitmq用户名
        'pwd' => 'mKPNhdcB3dyXTWBN', //rabbitmq密码
    );
    
    

    /*AI改图配置*/
    $aiconfig=array(
        'api_key' => 'AIzaSyCF2HtqcRCXGC7p5JSx8-MQikMdifkPvZ8', // OpenAI API密钥，请在此填写您的API密钥
        'base_url' => 'https://gemini.ainb.cc/v1/', // API基础地址，支持OpenAI兼容API
              'default_model' => 'gemini-2.0-flash-exp', // 默认AI模型
        'image_quality' => 'hd', // 默认图片质量: standard, hd, ultra
        'image_size' => '1024x1024', // 默认图片尺寸
        'timeout' => 60, // API请求超时时间（秒）
        'max_file_size' => 10485760, // 最大文件大小（10MB）
        'allowed_formats' => ['jpg', 'jpeg', 'png', 'webp'], // 支持的图片格式
        'save_original' => true, // 是否保存原图备份
        'auto_compress' => true, // 是否自动压缩大图片
    );
     /*权限系统配置*/
    $permission_config=array(
        'enable_store_limit' => false, // 是否启用店铺数量限制 (true=启用, false=禁用)
        'enable_menu_control' => false, // 是否启用菜单权限控制 (true=启用, false=禁用)
        'enable_status_check' => false, // 是否启用用户状态检查 (true=启用, false=禁用)
    );