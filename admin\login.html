<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>海豚ERP系统 - 管理员登录</title>
    <link rel="stylesheet" href="../assets/component/pear/css/pear.css" />
    <style>
        :root {
            --primary-color: #1890ff;
            --bg-gradient: linear-gradient(145deg, #f0f9ff 0%, #e6f4ff 100%);
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: var(--bg-gradient);
            background-image: url(../assets/admin/images/background.svg);
        }

        .login-card {
            width: 1000px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            display: grid;
            grid-template-columns: 1fr 1fr;
        }

        .brand-section {
            padding: 50px;
            background: var(--primary-color);
            color: white;
            position: relative;
            border-radius: 12px 0 0 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .brand-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 30px;
        }

        .brand-title {
            font-size: 28px;
            font-weight: 600;
        }

        .login-section {
            padding: 50px 60px;
            position: relative;
        }

        .form-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .form-title-main {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }

        .form-title-sub {
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- 左侧品牌信息 -->
            <div class="brand-section">
                <div class="brand-header">
                    <i class="layui-icon layui-icon-set" style="font-size:36px"></i>
                    <div>
                        <div class="brand-title">海豚ERP - 管理后台</div>
                        <div style="font-size:16px">高效、可靠、安全</div>
                    </div>
                </div>
                <p>请使用您的管理员账户登录。如果您忘记了密码，请联系系统管理员。</p>
            </div>

            <!-- 右侧表单区域 -->
            <div class="login-section">
                <!-- 登录表单 -->
                <div class="layui-form login-form">
                    <div class="form-title">
                        <div class="form-title-main">管理员登录</div>
                        <div class="form-title-sub">请使用您的管理员账号登录</div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-username"></i>
                            </div>
                            <input name="username" type="text" 
                                   placeholder="管理员用户名" 
                                   class="layui-input" 
                                   lay-verify="required">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-password"></i>
                            </div>
                            <input name="password" type="password" 
                                   placeholder="请输入密码" 
                                   class="layui-input"
                                   lay-affix="eye">
                        </div>
                    </div>

                    <!-- 滑块验证码 -->
                    <div class="layui-form-item">
                        <div id="captcha-container" style="margin: 15px 0;">
                            <!-- 极验验证码将在这里显示 -->
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <button class="layui-btn layui-btn-fluid" 
                                id="loginBtn"
                                style="background:var(--primary-color)"
                                lay-submit lay-filter="login">立即登录</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../assets/component/layui/layui.js"></script>
    <script src="../assets/component/pear/pear.js"></script>
    <!-- 极验验证码SDK -->
    <script src="//static.geetest.com/static/tools/gt.js"></script>
    <script src="//static.geetest.com/v4/gt4.js"></script>
    <script>
    // 防止某些环境下的问题
    window.appendChildOrg = Element.prototype.appendChild;
    Element.prototype.appendChild = function() {
        if(arguments[0].tagName == 'SCRIPT'){
            arguments[0].setAttribute('referrerpolicy', 'no-referrer');
        }
        return window.appendChildOrg.apply(this, arguments);
    };
    
    layui.use(['form', 'jquery', 'popup'], function() {
        var form = layui.form;
        var $ = layui.jquery;
        var popup = layui.popup;
        
        var captchaObj = null; // 验证码对象
        var captchaValidated = false; // 验证码是否验证通过
        var isTestMode = false; // 是否为测试模式

        // 初始化极验验证码处理函数
        var handlerEmbed = function(captchaObjInstance) {
            captchaObj = captchaObjInstance;
            
            // 将验证码插入到指定容器
            captchaObjInstance.appendTo('#captcha-container');
            
            // 监听验证码就绪事件
            captchaObjInstance.onReady(function() {
                console.log('验证码已就绪');
            });
            
            // 监听验证成功事件
            captchaObjInstance.onSuccess(function() {
                var result = captchaObjInstance.getValidate();
                if (!result) {
                    popup.failure('请完成验证');
                    return;
                }
                captchaValidated = true;
                console.log('验证码验证成功');
            });
            
            // 监听验证失败或关闭事件
            captchaObjInstance.onClose(function() {
                captchaValidated = false;
                console.log('验证码验证关闭');
            });
            
            // 监听验证错误事件
            captchaObjInstance.onError(function() {
                captchaValidated = false;
                console.log('验证码验证出错');
                showFallbackCaptcha();
            });
        };

        // 初始化极验验证码
        function initCaptcha() {
            $.ajax({
                url: "./ajax.php?act=captcha",
                type: "POST",
                cache: false,
                dataType: 'json',
                success: function(data) {
                    console.log('验证码配置:', data);
                    
                    // 检查是否为开发模式
                    if (data.gt === '647f5ed2ed8acb4be36784e01556bb71') {
                        isTestMode = true;
                        showTestModeCaptcha();
                        return;
                    }
                    
                    // 根据版本号初始化对应的验证码
                    if (data.version == 1) {
                        // 极验4.0
                        if (typeof initGeetest4 === 'function') {
                            initGeetest4({
                                captchaId: data.gt,
                                product: 'bind',
                                protocol: 'https://',
                                riskType: 'slide',
                                hideSuccess: true,
                                width: '100%'
                            }, handlerEmbed);
                        } else {
                            console.error('极验4.0 SDK未加载');
                            showFallbackCaptcha();
                        }
                    } else {
                        // 极验3.0
                        if (typeof initGeetest === 'function') {
                            initGeetest({
                                width: '100%',
                                gt: data.gt,
                                challenge: data.challenge,
                                new_captcha: data.new_captcha,
                                product: "bind",
                                offline: !data.success
                            }, handlerEmbed);
                        } else {
                            console.error('极验3.0 SDK未加载');
                            showFallbackCaptcha();
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('获取验证码配置失败:', status, error);
                    showFallbackCaptcha();
                }
            });
        }
        
        // 显示测试模式验证码
        function showTestModeCaptcha() {
            $('#captcha-container').html(
                '<div style="border: 2px dashed #1890ff; padding: 20px; text-align: center; background: #f0f9ff; border-radius: 4px;">' +
                '<i class="layui-icon layui-icon-ok-circle" style="font-size: 24px; color: #1890ff;"></i><br>' +
                '<span style="color: #666; margin-top: 10px; display: inline-block;">开发模式 - 验证码已自动通过</span>' +
                '</div>'
            );
            captchaValidated = true;
            
            // 创建模拟的验证对象
            captchaObj = {
                getValidate: function() {
                    return {
                        lot_number: 'dev_mode_' + Date.now(),
                        captcha_output: 'dev_mode_output',
                        pass_token: 'dev_mode_token',
                        gen_time: Date.now()
                    };
                },
                reset: function() {
                    captchaValidated = true;
                }
            };
        }
        
        // 显示降级验证码
        function showFallbackCaptcha() {
            $('#captcha-container').html(
                '<div style="border: 1px solid #ff4d4f; padding: 15px; text-align: center; background: #fff2f0; border-radius: 4px;">' +
                '<i class="layui-icon layui-icon-close-fill" style="font-size: 18px; color: #ff4d4f;"></i><br>' +
                '<span style="color: #ff4d4f; margin-top: 8px; display: inline-block;">验证码服务暂时不可用</span><br>' +
                '<span style="color: #999; font-size: 12px; margin-top: 5px; display: inline-block;">请稍后重试或联系管理员</span>' +
                '</div>'
            );
            
            // 提供重试按钮
            setTimeout(function() {
                if (!captchaValidated) {
                    $('#captcha-container').append(
                        '<div style="text-align: center; margin-top: 10px;">' +
                        '<button type="button" class="layui-btn layui-btn-sm" id="retry-captcha">重新加载验证码</button>' +
                        '</div>'
                    );
                    
                    $('#retry-captcha').on('click', function() {
                        $('#captcha-container').html('<div style="text-align: center; padding: 20px;">加载中...</div>');
                        setTimeout(initCaptcha, 1000);
                    });
                }
            }, 2000);
        }

        // 页面加载完成后初始化验证码
        $(document).ready(function() {
            initCaptcha();
        });

        form.on('submit(login)', function(data) {
            // 如果验证码服务不可用，允许登录但记录日志
            if (!captchaObj) {
                console.warn('验证码服务不可用，允许登录');
                // 继续登录流程，但不包含验证码数据
                submitLogin(data.field, null);
                return false;
            }
            
            // 检查验证码是否验证通过
            if (!captchaValidated) {
                popup.failure('请先完成滑块验证');
                return false;
            }

            // 获取验证码验证结果
            var captchaResult = null;
            if (captchaObj && typeof captchaObj.getValidate === 'function') {
                captchaResult = captchaObj.getValidate();
                if (!captchaResult && !isTestMode) {
                    popup.failure('验证码验证失败，请重试');
                    captchaValidated = false;
                    return false;
                }
            }

            submitLogin(data.field, captchaResult);
            return false;
        });
        
        // 提交登录请求
        function submitLogin(formData, captchaResult) {
            if (captchaResult) {
                formData.captcha_result = JSON.stringify(captchaResult);
            }

            $.ajax({
                url: './ajax.php?act=login',
                type: 'POST',
                dataType: 'json',
                data: formData,
                success: function(res) {
                    if (res.code == 1) {
                        popup.success("登录成功", function() {
                            location.href = 'index.php';
                        });
                    } else {
                        popup.failure(res.msg);
                        // 登录失败时重置验证码
                        resetCaptcha();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('登录请求失败:', status, error);
                    popup.failure('网络异常，请重试');
                    resetCaptcha();
                }
            });
        }
        
        // 重置验证码
        function resetCaptcha() {
            captchaValidated = false;
            if (captchaObj && typeof captchaObj.reset === 'function') {
                captchaObj.reset();
            } else if (!isTestMode) {
                // 如果重置失败，重新初始化
                setTimeout(function() {
                    initCaptcha();
                }, 1000);
            }
        }
    });
    </script>
</body>
</html> 