<?php
session_start();
error_reporting(E_ERROR | E_PARSE | E_COMPILE_ERROR);
if(defined('IN_CRONLITE'))return;
define('VERSION', '3061');
define('DB_VERSION', '2034');
define('IN_CRONLITE', true);
define('SYSTEM_ROOT', dirname(__FILE__).'/');
define('ROOT', dirname(SYSTEM_ROOT).'/');
date_default_timezone_set('Asia/Shanghai');
$date = date("Y-m-d H:i:s");
require_once SYSTEM_ROOT."vendor/autoload.php";
include_once(SYSTEM_ROOT."autoloader.php");
Autoloader::register();

if($is_defend){
	include_once(SYSTEM_ROOT."txprotect.php");
}

require ROOT.'config.php';
define('SQ', $dbconfig['user'].'_');
define('DBQZ', $dbconfig['dbqz']);
define('SYS_KEY', 'fgks#$6OO*');

if(is_file(SYSTEM_ROOT.'360safe/360webscan.php')){//360网站卫士
    require_once(SYSTEM_ROOT.'360safe/360webscan.php');
}

if(!$dbconfig['user']||!$dbconfig['pwd']||!$dbconfig['dbname'])//检测安装1
{
header('Content-type:text/html;charset=utf-8');
echo '你还没安装！<a href="/install/">点此安装</a>';
exit();
}

$DB = new \lib\PdoHelper($dbconfig);

include_once(SYSTEM_ROOT."functions.php");
include_once(SYSTEM_ROOT."member.php");
include_once(SYSTEM_ROOT."ozon.php");
include_once(SYSTEM_ROOT."pdd1688.php");

$redis = new Redis();$redis->connect('127.0.0.1', 6379);
$redisdata = $redis->get('fx');
if($redisdata){
    $fx = json_decode($redisdata,true);
    if(empty($fx['ru']['num']) or empty($fx['us']['num'])){
        $fx = ['ru'=>ForeignExchange(),'us'=>ForeignExchange(100,"美元"),'rmb'=>ForeignExchange(100,"人民币")];
        $redis->set('fx',json_encode($fx),3600);
    }
}else {
    $fx = ['ru'=>ForeignExchange(),'us'=>ForeignExchange(100,"美元"),'rmb'=>ForeignExchange(100,"人民币")];
    $redis->set('fx',json_encode($fx),3600);
}
$redis->close();
?>