This project aims at increasing the number of available Arabic free and
open source fonts. The goals of this project are,

 * Create and/or salvage high quality free and open TTF Arabic fonts.

 * Create a central repository of all free and open source Arabic fonts
(TTF and otherwise) in order to get them included into the various
distributions.

 * To better couple and artistically match Arabic fonts to their latin
counterparts.

You can visit our web page
http://www.arabeyes.org/project.php?proj=Khotot

This software package/product and attached documentations are provided
"as is", with no warranty.

                     -* www.arabeyes.org *-

If you'd like to help the Arabeyes Project, then consider:

  http://www.arabeyes.org/donate.php

Typeface and data © 2003-2007, Arabeyes.org.

Latin glyphs (U+0021-U+007E, U+00A1-U+0237, U+1E00-U+1EF9 and
U+FB00-U+FB06 Unicode ranges) are based on "Free UCS Outline Fonts",
www.nongnu.org/freefont (Copyleft 2002, 2003, 2005 Free Software
Foundation).

These fonts are subject to the GNU GENERAL PUBLIC (GPL) LICENSE (Version 2).
See COPYING for details or http://www.gnu.org/copyleft/gpl.html

As a special exception, if you create a document which uses this
font, and embed this font or unaltered portions of this font into the
document, this font does not by itself cause the resulting document to
be covered by the GNU General Public License. This exception does not
however invalidate any other reasons why the document might be covered
by the GNU General Public License. If you modify this font, you may
extend this exception to your version of the font, but you are not
obligated to do so. If you do not wish to do so, delete this exception
statement from your version.

Release Notes
-------------
2.0
This the second major release of Arabeyes fonts, this release features:
  * Completely new Latin Glyphs, based on font distributed by FreeFonts project
    (See the legal notes above)
  * Proper support for Arabic diacritics; all fonts now have anchor points
    adjusted manually to fit nicely with each glyph.
  * Diacritics for AlMothnna, AlArabiya and Tholoth fonts have been redisigned
    too look better and fit with the style of the font.
  * We dropped ae_ prefix from all fonts, so please fix your configuration
    files.
  * Various other small fixes, see ChangeLog file for details.

