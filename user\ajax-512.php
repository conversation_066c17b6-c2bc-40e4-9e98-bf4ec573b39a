<?php
include("../includes/common.php");
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;

if(!checkRefererHost())exit('{"code":403}');
if($act!='login' and $act!='reg'){
    if($islogin2==1){}else exit('{"code":-3,"msg":"No Login"}');
}
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
@header('Content-Type: application/json; charset=UTF-8');

switch($act){
case 'config':
    echo file_get_contents('./config/pear.config.json');exit;
break;
case 'menu':
    echo file_get_contents('./config/menu.json');exit;
break;
case 'category':
    echo file_get_contents('./config/中文类目.json');exit;
break;
case 'login':
	$username=trim($_POST['username']);
	$password=trim($_POST['password']);
	if(empty($username) || empty($password))exit('{"code":-1,"msg":"请确保各项不能为空"}');
	$userrow=$DB->getRow("SELECT * FROM ozon_user WHERE username=:username limit 1", [':username'=>$username]);
	if($userrow && password_verify($password, $userrow['password'])) {
	    $auth = new \lib\AuthSystem();
	    $token = $auth->generateToken($userrow['uid'], $username);
		ob_clean();
		$host = $_SERVER['HTTP_HOST'];
        $domainParts = explode('.', $host);
        
        // 处理本地开发环境（如localhost）
        if (count($domainParts) === 1 || in_array(end($domainParts), ['localhost', 'test', 'local'])) {
            $mainDomain = $host;
        } 
        // 处理正式域名（支持二级国家域名如.co.uk）
        else {
            $mainDomain = implode('.', array_slice($domainParts, -2, 2));
        }
        
        // 设置全局Cookie参数
        setcookie(
            "Authorization",          // Cookie名称
            $token,                   // Token值
            time() + 691200,          // 过期时间（8天）
            "/",                      // 全路径有效
            ".{$mainDomain}",         // 支持所有子域名
            isset($_SERVER['HTTPS']), // 自动启用HTTPS安全传输
            true                      // 禁止JavaScript访问
        );
		$result=array("code"=>1,"msg"=>"登录成功！正在跳转到用户中心");
		
		unset($_SESSION['csrf_token']);
	}else {
		$result=array("code"=>-1,"msg"=>"用户名或密码不正确！");
	}
	exit(json_encode($result));
break;
case 'reg':
    $username=htmlspecialchars(strip_tags(trim($_POST['username'])));
    $password=trim($_POST['password']);
    if (strlen($password) < 6) {
		exit('{"code":-1,"msg":"密码不能低于6位"}');
	}elseif (is_numeric($password)) {
		exit('{"code":-1,"msg":"密码不能为纯数字"}');
	}
    $row=$DB->getRow("select * from ozon_user where username=:username limit 1", [':username'=>$username]);
	if($row){
		exit('{"code":-1,"msg":"该用户名已经注册过，如需找回信息，请返回登录页面找回"}');
	}else{
	    $password = password_hash($password, PASSWORD_DEFAULT);
	    $sds=$DB->exec("INSERT INTO `ozon_user` (`uid`, `username`, `password`, `addtime`, `status`) VALUES (NULL, '{$username}', '{$password}',NOW(),1)");
	    if($sds){
			$result=array("code"=>1,"msg"=>"注册成功！");
		}else{
			$result=array("code"=>-1,"msg"=>"注册失败！".$DB->error());
		}
	}
    exit(json_encode($result));
break;
case 'getUserInfo':
    // 用户信息查询
    $_SESSION['user_id'] = 1; // 模拟登录用户
    $stmt = $db->prepare("SELECT * FROM ozon_user WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    echo json_encode(['code'=>1, 'data'=>$user]);
break;
case 'redeem':
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (empty($data['code'])) {
        throw new Exception('请输入兑换码');
    }
    
    $db->beginTransaction();
    try {
        // 查询有效兑换码
        $stmt = $db->prepare("SELECT * FROM ozon_ck 
            WHERE ck_code = ? AND status = 0 
            AND create_time > NOW() - INTERVAL 30 DAY 
            FOR UPDATE");
        $stmt->execute([$data['code']]);
        
        if (!$code = $stmt->fetch()) {
            throw new Exception('兑换码无效或已过期');
        }
    
        // 更新状态
        $db->prepare("UPDATE ozon_ck SET 
            status = 1, 
            use_time = NOW() 
            WHERE ck_id = ?")
           ->execute([$code['ck_id']]);
    
        // 用户权益处理逻辑
        $level_map = [
            1 => '普通会员',
            2 => '白银会员',
            3 => '黄金会员',
            4 => '钻石会员'
        ];
    
        $db->commit();
        
        $response = [
            'code' => 200,
            'msg' => '兑换成功',
            'data' => [
                'level_name' => $level_map[$code['user_level'] ?? 1],
                'expire_date' => date('Y-m-d H:i:s', 
                    strtotime($code['create_time']) + $code['days'] * 86400)
            ]
        ];
    } catch (Exception $e) {
        $db->rollBack();
        throw $e;
    }
break;

case 'generate':
    try {
        // 获取并解析输入
        $raw_input = file_get_contents('php://input');
        if(empty($raw_input)) throw new Exception("请求体为空");
        $data = json_decode($raw_input, true);
        if(json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON解析失败: " . json_last_error_msg());
        }

        // 参数验证
        $required = ['user_level', 'days', 'num'];
        foreach ($required as $field) {
            if(!isset($data[$field])) {
                throw new Exception("缺少参数: {$field}");
            }
        }

        // 参数处理
        $user_level = (int)$data['user_level'];
        $days = min(max((int)$data['days'], 1), 3650);
        $num = min(max((int)$data['num'], 1), 100);

        // 生成逻辑
        $codes = [];
        $stmt = $db->prepare("INSERT INTO ozon_ck 
            (ck_code, days, user_level) 
            VALUES (?, ?, ?)");

        for ($i = 0; $i < $num; $i++) {
            do {
                $code = strtoupper(bin2hex(random_bytes(8)));
                $check = $db->prepare("SELECT ck_id FROM ozon_ck WHERE ck_code = ?");
                $check->execute([$code]);
            } while ($check->rowCount() > 0);

            $stmt->execute([$code, $days, $user_level]);
            $codes[] = $code;
        }

        $response = [
            'code' => 200,
            'msg' => "成功生成{$num}个兑换码",
            'data' => $codes
        ];

    } catch(PDOException $e) {
        $response = [
            'code' => 500,
            'msg' => '数据库错误: ' . $e->getMessage()
        ];
        error_log("[Generate] PDO Error: " . $e->getMessage());
    } catch(Exception $e) {
        $response = [
            'code' => 400,
            'msg' => $e->getMessage()
        ];
    }
break;
case 'logout':
    setcookie("Authorization", "", time() - 604800);
	@header('Content-Type: text/html; charset=UTF-8');
	$result = ['code'=>1,'您已成功注销本次登录！'];
	exit(json_encode($result));
break;
//用户资料会员信息
case 'getUserInfo':
    try {
        // 验证用户登录状态
        if(empty($_SESSION['user_id'])) {
            throw new Exception('请先登录系统', 401);
        }

        $pdo = new PDO("mysql:host=localhost;dbname=your_db;charset=utf8", 'db_user', 'db_pass');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ATTR_ERRMODE_EXCEPTION);

        // 查询基础信息
        $stmt = $pdo->prepare("
            SELECT 
                user_id AS username,
                email,
                DATE_FORMAT(register_time, '%Y年%m月%d日%H:%i:%s') AS register_time,
                membership_type AS current_identity,
                DATE_FORMAT(expire_time, '%Y年%m月%d日%H:%i:%s') AS identity_expire
            FROM ozon_user 
            WHERE user_id = :user_id
        ");
        $stmt->bindParam(':user_id', $_SESSION['user_id'], PDO::PARAM_INT);
        $stmt->execute();
        $userData = $stmt->fetch(PDO::PARAM_STR);

        if(empty($userData)) {
            throw new Exception('用户不存在', 404);
        }

        // 权限数据（根据业务需求可改为从数据库读取）
        $permissions = [
            'plugin_access'      => '不限',
            'daily_upload'       => '不限',
            'max_shops'          => 150,
            'daily_collect'      => '不限',
            'daily_filter'       => '不限',
            'max_devices'        => 10,
            'token_count'        => 0
        ];

        // 构建响应数据结构
        $response = [
            'code' => 200,
            'data' => [
                'basic_info' => [
                    '用户名'        => $userData['username'],
                    '绑定邮箱'       => $userData['email'],
                    '注册时间'       => $userData['register_time'],
                    '当前身份'       => $userData['current_identity'],
                    '身份到期时间'    => $userData['identity_expire']
                ],
                'permissions' => [
                    '插件访问次数'    => $permissions['plugin_access'],
                    '每日跟卖商品数'   => $permissions['daily_upload'],
                    '最大店铺数'     => $permissions['max_shops'],
                    '每日采集量'     => $permissions['daily_collect'],
                    '每日筛选次数'    => $permissions['daily_filter'],
                    '最大登录设备'    => $permissions['max_devices'],
                    'Token数量'    => $permissions['token_count']
                ]
            ]
        ];

        echo json_encode($response, JSON_UNESCAPED_UNICODE);

    } catch(PDOException $e) {
        error_log("DB Error: ".$e->getMessage());
        echo json_encode([
            'code' => 500,
            'msg'  => '数据库服务异常'
        ]);
    } catch(Exception $e) {
        echo json_encode([
            'code' => $e->getCode(),
            'msg'  => $e->getMessage()
        ]);
    }
break;
case 'store_add':
    $name=htmlspecialchars(strip_tags(trim($_POST['name'])));
    $ClientId = intval(trim($_POST['ClientId']));
    $key=htmlspecialchars(strip_tags(trim($_POST['key'])));
    $cookie=htmlspecialchars(strip_tags(trim($_POST['cookie'])));
    $money=intval(trim($_POST['money']));
    if($money==2){
        $money = 'RUB';
    }elseif($money==3){
        $money = 'USD';
    }else{
        $money = 'CNY';
    }
    $sds=$DB->exec("INSERT INTO `ozon_store` (`ClientId`, `uid`, `storename`, `key`, `cookie`, `currency_code`, `addtime`, `status`, `apistatus`) VALUES ('{$ClientId}', '{$uid}', '{$name}', '{$key}', '{$cookie}', '{$money}', NOW(), '1', '1')");
    if($sds){
        $result = ['code'=>1,'msg'=>'新增店铺成功！'];
    }else{
        $result = ['code'=>1,'msg'=>'新增店铺失败！'];
    }
    exit(json_encode($result));
break;
case 'store_delete':
    $id = intval(trim($_POST['id']));
    if($id <= 0) exit(json_encode(['code'=>0, 'msg'=>'参数错误']));

    // 带存在性检查的版本
    if(!$DB->query("SELECT id FROM ozon_store WHERE id = $id")->fetch()){
        exit(json_encode(['code'=>0, 'msg'=>'店铺不存在']));
    }

    $DB->exec("DELETE FROM ozon_store WHERE id = $id");
    exit(json_encode(['code'=>1, 'msg'=>'删除成功']));
    break;
    case 'addUserGroup':
    $group_name = trim($_POST['group_name']);
    if(empty($group_name)) {
        exit(json_encode(['code'=>0, 'msg'=>'分组名称不能为空']));
    }
    
    $user_id = $_SESSION['uid'];
    
    // 获取现有分组数据
    $stmt = $DB->prepare("SELECT user_json FROM ozon_user WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $userData = $user ? json_decode($user['user_json'], true) : [];
    $groups = $userData['groups'] ?? [];
    
    // 生成新分组ID
    $new_id = 1;
    if(!empty($groups)) {
        $ids = array_column($groups, 'id');
        $new_id = max($ids) + 1;
    }
    
    // 添加新分组
    $groups[] = [
        'id' => $new_id,
        'name' => $group_name,
        'create_time' => date('Y-m-d H:i:s')
    ];
    
    $userData['groups'] = $groups;
    $new_json = json_encode($userData);
    
    // 更新数据库
    $stmt = $DB->prepare("UPDATE ozon_user SET user_json = ? WHERE id = ?");
    $result = $stmt->execute([$new_json, $user_id]);
    
    if($result) {
        exit(json_encode(['code'=>1, 'msg'=>'分组添加成功']));
    } else {
        exit(json_encode(['code'=>0, 'msg'=>'分组添加失败']));
    }
    break;
case 'store_List':
    $sql=" uid={$uid}";
    $page = intval($_POST['page']);
	$limit = intval($_POST['limit']);
    $total = $DB->getColumn("SELECT count(*) from ozon_store A WHERE{$sql}");
    if(!$limit)$limit=$total;
	$list = $DB->getAll("SELECT * FROM ozon_store WHERE{$sql} ORDER BY addtime ASC limit $page,$limit");
	$list2 = [];
	foreach($list as $row){
	    $rs=$DB->query("SELECT price,quantity,cost,status from ozon_order where storeid='{$row['id']}'");
	    $cost = $price=0 ;
	    while($rowa = $rs->fetch())
		{
		    if($rowa['status']!='cancelled'){
		        $price += $rowa['price']*$rowa['quantity'];
		        $cost  += $rowa['cost'];
		    }
		}
		$row['cost'] = round($cost,2);
		$row['price'] =round($price,2);
		$row['time'] = date("Y-m-d H:i:s",$row['time']);
		$list2[] = $row;
		$i++;
	}
exit(json_encode(['code'=>0,'count'=>$total,'data'=>$list2]));
break;
case 'store_switch':
    $id = intval($_POST['id']);
    $status = intval($_POST['status']);
    if($DB->update('store', ['actions'=>$status], ['id'=>$id,'uid'=>$uid])){
        $result = ['code'=>0];
    }else{
        $result = ['code'=>-1];
    }
    exit(json_encode($result));
break;
case 'store_edit':
    $id = intval($_POST['id']);
    $updateData = [
        'storename' => daddslashes($_POST['name']),
        'key' => daddslashes($_POST['key']),
        'currency_code' => getCurrencyCode($_POST['money'])
    ];
    if($_POST['cookie']){
        $updateData['cookie'] = daddslashes($_POST['cookie']);
    }
    
    // 执行更新操作
    $result = $DB->update('store', $updateData, ['id' => $id,'uid'=>$uid]);
    
    if($result) {
        exit(json_encode(['code' => 1, 'msg' => '更新成功']));
    } else {
        exit(json_encode(['code' => 0, 'msg' => '没有变化或更新失败']));
    }
break;
case 'orders_all':
    $status = trim($_GET['status']);
    $posting_number = trim($_GET['posting_number']);
    $sku = intval($_GET['sku']);
    $sql=" A.uid={$uid}";
    if($posting_number!=''){
        $sql.=" AND A.posting_number='{$posting_number}'";
    }
    if($status and !$posting_number){
        if($status=='PPW'){
            $sql.=" AND A.cost IS NULL AND A.status!='cancelled'";
        }else if($status=='all'){
            $sql.=" AND A.status!='cancelled'";
        }else{
            $sql.=" AND A.status='{$status}'";
        }
    }
    if($sku){
        $sql.=" AND A.sku='{$sku}'";
    }
    $page = intval($_GET['page']);
	$limit = intval($_GET['limit']);
	$page=isset($_GET['page'])?intval($_GET['page']):1;
    $offset=$limit*($page - 1);
    $total = $DB->getColumn("SELECT count(*) from ozon_order A WHERE{$sql}");
	$list = $DB->getAll("SELECT A.*,B.storename,B.ClientId,C.stocks,C.commissions FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id LEFT JOIN ozon_products C ON A.sku=C.sku WHERE{$sql} ORDER BY A.in_process_at DESC limit $offset,$limit");
	$list2 = [];
	foreach($list as $row){
	    $stocks = json_decode($row['stocks'],true);
	    $commissions = json_decode($row['commissions'],true);
	    switch($stocks['stocks'][0]['source']){
	        case 'fbo':
                $i=0;
            break;
            case 'fbs':
                $i=1;
            break;
            case 'rfbs':
                $i=2;
            break;
            case 'fbp':
                $i=3;
            break;
	        
	    }
	    $row['percent'] = $commissions[$i]['percent'];
	    if($row['percent']){
	        $percent = $row['percent']/100;
	    }else{
	        $percent = 0.18;
	    }
	    $row['commissions'] = "{$commissions[$i]['value']} ￥";
	    $row['in_process_at'] = preg_replace('/^\d{4}-/', '', $row['in_process_at']);
	    $row['shipment_date'] = preg_replace('/^\d{4}-/', '', $row['shipment_date']);
	    if(!$row['order_name']){
	        $row['order_name'] = Transmart($row['name2']);
	        $DB->update('order', ['order_name'=>$row['order_name']], ['order_id'=>$row['order_id']]);
	    }
	    $row['money'] = $row['price'];
	    $row['price'] = $row['price']*$row['quantity'];
	    $row['weight'] = ($row['weight']/1000)*$row['quantity'];
	    if($row['out_weight']){
	        $row['out_weight'] = round($row['out_weight']/1000,2);
	        $row['delivery'] = Logisticscost($row,$row['out_weight']);
	        $row['profit'] = calculateResult($row['price'],$row['out_weight'],$percent,$row['delivery'],$row['cost']);
	        $DB->update('order', ['delivery'=>$row['delivery'],'profit'=>$row['profit']], ['order_id'=>$row['order_id']]);
	    }else{
	        $row['out_weight'] = "未出库";
	        $row['delivery'] = Logisticscost($row,$row['weight']);
	        $row['profit'] = calculateResult($row['price'],$row['weight'],$percent,$row['delivery'],$row['cost']);
	    }//产品颜色: 灰色,颜色名称: 灰色,尺寸,毫米: 1500*1200
	    if($row['color']){
	        $row['pj'] = ' 颜色: '.text_str_replace($row['color']);
	    }
	    if($row['dimensions']){
	        $row['pj'] .= ' 尺寸mm: '.text_str_replace($row['dimensions']);
	    }
	    if($row['material']){
	        $row['pj'] .= ' 材质: '.text_str_replace($row['material']);
	    }
	    if($row['customer']){
	        $customer = json_decode($row['customer'],true);
	        $row['customer_name'] = $customer['name'];
	        $row['region'] = $customer['address']['region'].($customer['address']['zip_code']?" / ".$customer['address']['zip_code']:'');
	        if($customer['address']['country']=='Rossiya'){
	            $row['country'] = "俄罗斯";
	        }
	    }
	    
		$list2[] = $row;
		$i++;
	}
exit(json_encode(['code'=>0,'count'=>$total,'data'=>$list2]));
break;
case 'orders_updata':
    $posting_number = daddslashes($_POST['posting_number']);
    if($posting_number){
        $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid'=>$uid,':posting_number' => $posting_number]);
        //exit(json_encode($row));
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $json=$client->postingfbsget($posting_number);
	    $importer = new \lib\JsonImporter($DB);
	    if($importer->importorder($json, false, $row)){
	        $code = 0;
	    }else{
	        $code = -1;
	    }
	    exit(json_encode(['code'=>$code]));
    }else{
        $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid={$uid} AND id!=4 ORDER BY addtime ASC");
        foreach($list as $row){
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $filter = [
                'fbpFilter'=>'ALL',
                'since' => date('Y-m-d', strtotime('-30 days')),
                'to' => date('Y-m-d')
            ];
            $data = $client->getFbsPostingListV3($filter);
            foreach ($data['result']['postings'] as $aaa){
                if(!$DB->exec("INSERT INTO `ozon_order` (`order_id`, `uid`, `storeid`, `posting_number`, `name2`, `sku`, `offer_id`, `price`, `quantity`, `currency_code`, `tracking_number`, `warehouse`, `kd_name`, `tpl_provider`, `status`, `delivering_date`, `in_process_at`, `shipment_date`) VALUES 
                ('{$aaa['order_id']}', '{$uid}', '{$row['id']}', '{$aaa['posting_number']}'
                , '{$aaa['products'][0]['name']}', '{$aaa['products'][0]['sku']}', '{$aaa['products'][0]['offer_id']}', '{$aaa['products'][0]['price']}', '{$aaa['products'][0]['quantity']}',
                '{$aaa['products'][0]['currency_code']}', '{$aaa['tracking_number']}', '{$aaa['delivery_method']['warehouse']}', '{$aaa['delivery_method']['name']}',
                '{$aaa['delivery_method']['tpl_provider']}', '{$aaa['status']}', '".replace($aaa['delivering_date'])."', '".replace($aaa['in_process_at'])."', 
                '".replace($aaa['shipment_date'])."')")){
                    $DB->update('order', ['order_id'=>$aaa['order_id'],'tracking_number'=>$aaa['tracking_number'],'quantity'=>$aaa['products'][0]['quantity'],'status'=>$aaa['status']], ['posting_number'=>$aaa['posting_number']]);
                }
            }
        }
        $data['code'] = 0;
        exit(json_encode($data));
    }
break;
case 'update_order':
    $order_id = intval($_POST['order_id']);
    $cost = htmlspecialchars(strip_tags(trim($_POST['cost'])));
    if($DB->update('order', ['cost'=>$cost], ['uid'=>$uid,'order_id'=>$order_id])){
        $data['code'] = 0;
    }else{
        $data['code'] = -1;
    }
    exit(json_encode($data));
break;
case 'order_preview':
    $posting_number = daddslashes($_POST['posting_number']);
    if($posting_number){
        $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid'=>$uid,':posting_number' => $posting_number]);
        if($row){
            $file_path = targetDateTime($row);
            if(file_exists($file_path[0])){
                $result = ['code'=>0,'msg'=>'成功获取面单','url'=>$file_path[1]];
            }else{
                //exit(json_encode($row));
                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                $url = $client->packagelabel($row);
                if($url!=false){
                    $result = ['code'=>0,'msg'=>'成功获取面单','url'=>$url];
                }else{
                    $result = ['code'=>-3,'msg'=>'下载订单面单失败'];
                }
            }
        }else{
            $result = ['code'=>-2,'msg'=>'不存在此订单'];
        }
        
    }else{
        $result = ['code'=>-1,'msg'=>'未提交货物单号'];
    }
    exit(json_encode($result));
break;
case 'profit-analysis'://cost
    $start_date = htmlspecialchars(strip_tags(trim($_POST['start_date'])));
    $end_date = htmlspecialchars(strip_tags(trim($_POST['end_date'])));
    $saless=$DB->getColumn("SELECT SUM(price) FROM ozon_order WHERE uid='{$uid}' AND in_process_at>='{$start_date}' AND in_process_at<='{$end_date}'");
    $sales=$DB->getColumn("SELECT SUM(price) FROM ozon_order WHERE uid='{$uid}' AND out_weight!='' AND in_process_at>='{$start_date}' AND in_process_at<='{$end_date}' AND status='delivering'");
    $data['platform'] = $sales*0.18;
    $cost=$DB->getColumn("SELECT SUM(cost) FROM ozon_order WHERE uid='{$uid}' AND out_weight!='' AND in_process_at>='{$start_date}' AND in_process_at<='{$end_date}' AND status='delivering'");
    $costs=$DB->getColumn("SELECT SUM(cost) FROM ozon_order WHERE uid='{$uid}' AND in_process_at>='{$start_date}' AND in_process_at<='{$end_date}'");
    $profit=$DB->getColumn("SELECT SUM(profit) FROM ozon_order WHERE uid='{$uid}' AND out_weight!='' AND in_process_at>='{$start_date}' AND in_process_at<='{$end_date}' AND status='delivering'");
    $delivery=$DB->getColumn("SELECT SUM(delivery) FROM ozon_order WHERE uid='{$uid}' AND out_weight!='' AND in_process_at>='{$start_date}' AND in_process_at<='{$end_date}' AND status='delivering'");
    $data['sales'] = $sales;
    $data['purchase'] = $cost;
    $data['profit'] = $profit;
    $data['delivery'] = $delivery;
    $data['saless'] = $saless;
    $data['purchases'] = $costs;
    exit(json_encode(['code'=>0,'data'=>$data]));
break;

case 'productlist':
     $shopIds = [];
    if (isset($_GET['shop_id'])) {
        if (is_array($_GET['shop_id'])) {
            $shopIds = array_map('intval', $_GET['shop_id']);
        } else {
            $shopIds = explode(',', $_GET['shop_id']);
            $shopIds = array_map('intval', $shopIds);
        }
        $shopIds = array_filter($shopIds); // 过滤空值
    }

     $sql = "A.uid = {$uid}";
    if (!empty($shopIds)) {
        $shopIdList = implode(',', $shopIds);
        $sql .= " AND A.storeid IN ({$shopIdList})";
    }
    if ($sku) {
        $sql .= " AND A.sku = '{$sku}'";
    }
    if (!empty($shopIds)) {
        $shopIds = array_map('intval', $shopIds); // 安全过滤
        $shopIdList = implode(',', $shopIds);
        $sql .= " AND A.storeid IN ({$shopIdList})";
    }
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = $limit * ($page - 1);
    $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_products A WHERE {$sql}");
    $list = $DB->getAll("SELECT A.*, B.storename, C.width, C.depth, C.height 
                        FROM ozon_products A 
                        LEFT JOIN ozon_store B ON A.storeid = B.id 
                        LEFT JOIN ozon_weight C ON A.sku = C.sku 
                        WHERE {$sql} 
                        ORDER BY A.created_at DESC 
                        LIMIT $offset, $limit");
	$list2 = [];
	foreach($list as $row){
	    $stocks = json_decode($row['stocks'],true);
	    $row['stock'] = $stocks['stocks'][0]['present'];
	    $row['source'] = $stocks['stocks'][0]['source'];
	    $commissions = json_decode($row['commissions'],true);
	    switch($stocks['stocks'][0]['source']){
	        case 'fbo':
                $i=0;
            break;
            case 'fbs':
                $i=1;
            break;
            case 'rfbs':
                $i=2;
            break;
            case 'fbp':
                $i=3;
            break;
	    }
	    $row['commissions'] = "<P>{$stocks['stocks'][0]['source']}: {$commissions[$i]['percent']}%</P><P>佣金: {$commissions[$i]['value']}</P>";
        $row['return_amount'] = "<p>物流: {$commissions[$i]['return_amount']} {$row['currency_code']}</p>
                                 <p>最后一公里: {$commissions[$i]['delivery_amount']} {$row['currency_code']}</p>
                                 <p>退货或取消: {$commissions[$i]['value']} {$row['currency_code']}</p>";
	    $status_info = json_decode($row['status_info'],true);
        switch($status_info['status_name']){
            case 'Не продается':
                $errors = json_decode($row['errors'],true);
                if($errors[0]['code']=='SPU_ALREADY_EXISTS_IN_ANOTHER_ACCOUNT'){
                    $row['status'] = 6;
                    $row['errors']  ="同样商品在以下卡片和个人中心重复出现：{$errors[0]['texts']['params'][0]['value']}。请在https://docs.ozon.ru/global/products/general-information/errors-with-pdps/?country=CN了解更多信息。";
                }else{
                    $row['status'] = 4;//已停止销售
                }
                
            break;
            case 'Продается':
                $row['status'] = 1;//正常销售
            break;
            case 'Готов к продаже':
                $row['status'] = 2; //已归档
            break;
	        
	    }
	    $list2[] = $row;
	}
	exit(json_encode(['code' => 0, 'data' => $list2, 'count' => $total]));
break;
case 'update_stock':
    $offer_id = daddslashes($_POST['offer_id']);
    $stock = intval($_POST['stock']);
    $storeid = intval($_POST['storeid']);
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key,B.warehouses FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.offer_id=:offer_id LIMIT 1", [':uid'=>$uid,':offer_id' => $offer_id]);
    if($row){
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $warehouses = json_decode($row['warehouses'],true);
        $data = $client->productsstocks(['offer_id'=>$row['offer_id'],'stock'=>$stock,'warehouse_id'=>$warehouses[0]['id']]);
        if($data){
            $result = ['code'=>0];
            $importer = new \lib\JsonImporter($DB);
            $items[] = $offer_id;
            $data2 = $client->productinfolist($items,'offer_id');
            $importer->importProducts($data2, false, $row);
        } else{
            $result = ['code'=>-2,'msg'=>'添加库存失败'];
        }
    }else{
        $result = ['code'=>-1,'msg'=>'获取数据失败'];
    }
    exit(json_encode($result));
break;
case 'update_price':
    $offer_id = daddslashes($_POST['offer_id']);
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key,B.currency_code FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.offer_id=:offer_id LIMIT 1", [':uid'=>$uid,':offer_id' => $offer_id]);
    
    if($row){
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $row['price'] = $_POST['price'];
        $data = $client->importprices($row);
        if($data){
            $result = ['code'=>0,'msg'=>'修改成功'];
            $importer = new \lib\JsonImporter($DB);
            $items[] = $offer_id;
            $data2 = $client->productinfolist($items,'offer_id');
            $importer->importProducts($data2, false, $row);
        }else{
            $result = ['code'=>-2,'msg'=>'修改价格失败'];
        }
    }else{
        $result = ['code'=>-1,'msg'=>'获取数据失败'];
    }
    exit(json_encode($result));
break;
case 'productsync':
    if (function_exists("set_time_limit")) {
        @set_time_limit(0);
    }
    $importer = new \lib\JsonImporter($DB);
    
    if($_POST['offer_id']){
        $storeid = intval($_POST['storeid']);
        $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid={$uid} AND id='{$storeid}'");
        $client = new \lib\OzonApiClient($list[0]['ClientId'], $list[0]['key']);
        $items[] = $_POST['offer_id'];
        $data2 = $client->productinfolist($items,'offer_id');
        $result = $importer->importProducts($data2, false, $list);
        if($result['message']=='没有可导入的产品数据'){
            $DB->exec("DELETE FROM ozon_products WHERE offer_id='{$_POST['offer_id']}'");
        }
    }else{
        
        $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid = ? AND id != 4 ORDER BY addtime ASC", [$uid]);
        foreach ($list as $row) {
            $DB->exec("DELETE FROM ozon_products WHERE storeid='{$row['id']}'");
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $lastId = null; // 每个店铺的分页标识独立
            $totalImported = 0;
            do {
                $params = [
                    'visibility' => 'ALL',
                    'limit' => 1000
                ];
                if ($lastId) {
                    $params['last_id'] = $lastId;
                }
                // 获取产品列表
                $response = $client->productlist($params);
                if (empty($response['result']['items'])) {
                    break; // 无数据时终止分页
                }
                // 提取当前页产品ID
                $productIds = [];
                foreach ($response['result']['items'] as $item) {
                    $productIds[] = (string)$item['product_id'];
                }
                // 获取产品详情并导入
                $productDetails = $client->productinfolist($productIds);
                $importResult = $importer->importProducts($productDetails, false, $row);
                if ($importResult) {
                    $totalImported += count($response['result']['items']);
                }
                // 更新分页标识
                $lastId = $response['result']['last_id'] ?? null;
            } while ($lastId !== null); // 存在分页标识时继续循环
            // 可在此记录日志（如导入总数）
        }
    }
exit(json_encode(['code'=>0,'msg'=>'success']));
break;
case 'getShops':
    // 安全查询
    $list = $DB->getAll("SELECT id, storename, ClientId, `key`, warehouses FROM ozon_store WHERE uid = ?", [$uid]);
    $list2 = [];
    
    foreach ($list as $row) {
        // 触发异步更新仓库（如果缺失）
        if (empty($row['warehouses']) || $row['warehouses'] === 'null') {
            async_update_warehouse($row['id'], $row['ClientId'], $row['key']);
            $row['warehouses'] = []; // 前端展示空数组
        } else {
            $row['warehouses'] = json_decode($row['warehouses'], true);
        }
        
        // 仅返回必要字段
        $list2[] = [
            'id' => $row['id'],
            'storename' => $row['storename'],
'warehouses' => $row['warehouses']
        ];
    }
    
    exit(json_encode(['code' => 0, 'data' => $list2]));
break;
case 'cron_list':
    $status = trim($_GET['status']);
    $posting_number = trim($_GET['posting_number']);
    $sku = intval($_GET['sku']);
    $sql=" A.uid={$uid}";
    if($sku){
        $sql.=" AND A.sku='{$sku}'";
    }
    
    $page = intval($_GET['page']);
	$limit = intval($_GET['limit']);
	$page=isset($_GET['page'])?intval($_GET['page']):1;
    $offset=$limit*($page - 1);
    $total = $DB->getColumn("SELECT count(*) from ozon_cron A WHERE{$sql}");
	$list = $DB->getAll("SELECT A.*,B.storename FROM ozon_cron A LEFT JOIN ozon_store B ON A.storeid=B.id LEFT JOIN ozon_weight C ON A.sku=C.sku WHERE{$sql} ORDER BY A.addtime DESC limit $offset,$limit");
	$list2 = [];
	foreach($list as $row){
	    $list2[] = $row;
	}
	exit(json_encode(['code'=>0,'msg'=>'success','count'=>$total,'data'=>$list2]));
break;
case 'cron_reupload':
    $id = intval($_POST['id']);
    if($DB->update('cron', ['status'=>'准备中','msg'=>'','time'=>0], ['id'=>$id,'uid'=>$uid])){ #$uid 隐藏在全局文件
        $result = ['code'=>0,'msg'=>'重传任务成功'];
    }else{
        $result = ['code'=>-1,'msg'=>'重传失败或数据不存在'];
    }
    exit(json_encode($result));
break;
case 'reupload':
    $id = intval($_POST['id']);
    if($DB->update('cron', ['status'=>'准备中','msg'=>''], ['id'=>$id,'uid'=>$uid])){ #$uid 隐藏在全局文件
        $result = ['code'=>0,'msg'=>'重传任务成功'];
    }else{
        $result = ['code'=>-1,'msg'=>'重传失败或数据不存在'];
    }
    exit(json_encode($result));
break;
case 'bestsellers':
    $offset = $_GET['page']-1;
    $filter['stock'] = "any_stock";
    $filter['period'] = "monthly";
    if($_GET['sku']){
        $filter['sku'] = $_GET['sku'];
    }
    if($_GET['categorie']){
        $filter['categories'] = [$_GET['categorie']];
    }
    if($_GET['company_id']){
        $filter['company_ids'] = [$_GET['company_id']];
    }
    $array  = [
        'limit'=>$_GET['limit']?$_GET['limit']:50,
        'offset'=>$offset,
        'filter'=>$filter,
        'sort'=>['key'=>'sum_gmv_desc']
    ];
    $url = "https://seller.ozon.ru/api/site/seller-analytics/what_to_sell/data/v3";
    $referer = "https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers";
    $cookie = "__Secure-ETC=290ef098c7a1f09c98d789cc53efd5a3; __Secure-access-token=7.*********.FNG9zGuaRhSubB805rMhEw.97.Ab6gSpyzTGe3Zt4uNYassXIlkIWOY9kBKSrPIYgo7ITaLeTsikh5ono47Buci6OitWHLkGRhdsXuYw9uQnnrmNWqTqoduxiT5aTDWTPB3Rk-.20250312092657.20250504093414.4K12XCJkk8GYAul9AaU09QBRRWlHBBIykki6josBm6Q.1f07490c96adb1456; __Secure-refresh-token=7.*********.FNG9zGuaRhSubB805rMhEw.97.Ab6gSpyzTGe3Zt4uNYassXIlkIWOY9kBKSrPIYgo7ITaLeTsikh5ono47Buci6OitWHLkGRhdsXuYw9uQnnrmNWqTqoduxiT5aTDWTPB3Rk-.20250312092657.20250504093414.BtfP8giA4oBwqbpzuLT_v_I-XOgsKm4YTsvTm0nzyfc.1f4c0dfaec2ada957; __Secure-user-id=*********; abt_data=7.MQjCrBfsYBOzUCQNQF8K9VUDlsNv7Hy3Q161UuD5S1zeEqqrzcPUTmeR_Dz0hd07GtXGhC1A_i-crxKNUVcrKXVvLN6a3WdPWsqywG6-6RtxsMRK-VWnFdk5sYH6Pmma3qrUNk_Q4adqQUGlq5lRRFhnN-6iMMSCfMnVeZ6TTn3aS_r04bOP7zS9SwsE_qYNu5L42knWqh8y9HCP-0VUw2GNDeLyKLcme96CHGKySqoLfraJWYDrGkxG0USrGQgLWPfUVrNeYZxgQwKYeTYkGqzku4klq6lhq4oYzTXQ-OWr6hOB_T1-5j4ZoMRPWx3Zjkc-bBpuXTqBnrPeDWRBwEpTzDUE0DUlTxvZV7Gh6xyQEmWvrFOqP2-3O-mGRjTIkQotCO_kG4akncOZTKfhyBglrNBpeM6ZOgoS_hiOF_p38XcJBAuSD_Y7C-fxYSnPT7qu6WIZVUTOoTZ5wZ4Ta6rDgMGumxAIlDcL-jC6Z49f-Z9hTURhJWmLojzq_j8L; sc_company_id=2749292";
    //json_encode($array)
    $res = get_ozoncurl($url,$array, $referer, $cookie);
    exit($res);
break;
case 'sellerbestsellers':
    $sku = intval($_GET['sku']);
    $seller_id = intval($_GET['seller_id']);
    $money = intval($_GET['money']);
    $moneys = intval($_GET['moneys']);
    $g = intval($_GET['g']);
    $gs = intval($_GET['gs']);
    $sl = intval($_GET['sl']);
    $sls = intval($_GET['sls']);
    $gm = intval($_GET['gm']);
    $sql=" uid={$uid}";
    if($sku){
        $sql.=" AND A.sku='{$sku}'";
    }
    if($seller_id){
        $sql.=" AND A.seller_id='{$seller_id}'";
    }
    if($money and $moneys){
        $sql.=" AND A.money>='{$money}' AND A.money<='{$moneys}'";
    }
    if($sl and $sls){
        $sql.=" AND A.sold_count>='{$sl}' AND A.sold_count<='{$sls}'";
    }
    if($g and $gs){
        $sql.=" AND B.weight>='{$g}' AND B.weight<='{$gs}'";
    }
    if($gm){
        $sql.=" AND A.gm='{$gm}'";
    }
    $page = intval($_GET['page']);
	$limit = intval($_GET['limit']);
	$page=isset($_GET['page'])?intval($_GET['page']):1;
    $offset=$limit*($page - 1);
    $total = $DB->getColumn("SELECT count(*) from ozon_seller A WHERE{$sql}");
	$list = $DB->getAll("SELECT A.*,B.weight,B.depth,B.height,B.width FROM ozon_seller A LEFT JOIN ozon_weight B ON A.sku=B.sku WHERE{$sql} ORDER BY A.create_date DESC limit $offset,$limit");
	$list2 = [];
	foreach($list as $row){
	    $row['rmb'] = round($row['price']/$fx['ru']['num'],2);
	    $row['us'] = round($row['rmb']*$fx['us']['num'],2);
	    $list2[] = $row;
	}
	exit(json_encode(['code'=>0,'msg'=>'success','count'=>$total,'data'=>$list2]));
break;
case 'getselle':
    $list = $DB->getAll("SELECT * FROM ozon_sellerjk WHERE uid = ?", [$uid]);
    $list2 = [];
    
    foreach ($list as $row) {
        $list2[] = $row;
    }
    exit(json_encode(['code' => 0, 'data' => $list2]));
break;
case 'getstore':
    $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid={$uid}");
	$list2 = [];
	foreach($list as $row){
	    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
	    $client->productinfolimit($row);
	    if(!$row['warehouses'] or $row['warehouses']=='null'){
	        $row['warehouses']=$client->Ozonwarehouses();
	        $DB->update('store', ['warehouses'=>json_encode($row['warehouses'])], ['id'=>$row['id']]);
	    }else{
	        $row['warehouses']=json_decode($row['warehouses'],true);
	    }
	    unset($row['apistatus']);
	    unset($row['key']);
	    unset($row['cookie']);
	    unset($row['addtime']);
	    unset($row['status']);
	    unset($row['uid']);
		$list2[] = $row;
		$i++;
	}
	exit(json_encode(['code'=>0,'stores'=>$list2,'fx'=>$fx['ru']]));
break;
case 'production_list':
    $status = trim($_GET['status']);
    $posting_number = trim($_GET['posting_number']);
    $sku = intval($_GET['sku']);
    $sql=" uid={$uid}";
    if($sku){
        $sql.=" AND sku='{$sku}'";
    }
    
    $page = intval($_GET['page']);
	$limit = intval($_GET['limit']);
	$page=isset($_GET['page'])?intval($_GET['page']):1;
    $offset=$limit*($page - 1);
    $total = $DB->getColumn("SELECT count(*) from ozon_production WHERE{$sql}");
	$list = $DB->getAll("SELECT * FROM ozon_production WHERE{$sql} ORDER BY addtime DESC limit $offset,$limit");
	$list2 = [];
	foreach($list as $row){
	    $row['category'] = json_decode($row['category_chain'],true)['texts'];
	    $list2[] = $row;
	}
	exit(json_encode(['code'=>0,'msg'=>'success','count'=>$total,'data'=>$list2]));
break;
case 'get_category_attributes':
    $client = new \lib\OzonApiClient(2763302, '0a1f6874-fe81-4b30-97ce-4a3339eb077e');
    $data = $client->attribute(['description_category_id'=>$_POST['category2'],'type_id'=>$_POST['category3']]);
    exit(json_encode($data?['code'=>0,'data'=>$data]:['code'=>-1]));
break;
case 'get_sku':
    // 初始化代理轮换器
    $sku = (string) get_sku($_POST['skuurl']);
    $rotator = new \lib\ProxyRotator();
    $result = $rotator->ozoncurl(
        'https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku',
        json_decode('{"skus":["'.$sku.'"]}',true),
        'https://seller.ozon.ru/app/products/1708109702/edit/general-info',
        $cookie2
    );
    $json = json_decode($result['body'],true);
   
    $data = $json['resolved_categories_by_sku'][$sku];
    $data['sku'] = $sku;
    $data['code']=0;
    //exit(json_encode($result2));
    exit(json_encode($data));
break;
case 'get_sku_attributes':
    // 初始化代理轮换器
    $sku = (string) get_sku($_POST['skuurl']);
    $rotator = new \lib\ProxyRotator();
    $result = $rotator->ozoncurl(
        'https://seller.ozon.ru/api/v1/search-variant-model',
        json_decode('{"name":"'.$sku.'","limit":"10"}',true),
        'https://seller.ozon.ru/app/products/create',
        $cookie
    );
    $json = json_decode($result['body'],true);
    
    $data = $json['items'][0];
    $data['sku'] = $sku;
    $data['code']=0;
    //exit(json_encode($result2));
    exit(json_encode($data));
break;
case 'get_sku_attributevalues':
    $url = SYSTEM_ROOT.'../assets/ozon/attributes/attribute_'.$_POST['attribute_id'].'.json';
    $redisdata = file_get_contents($url);
    if($redisdata){
        exit(json_encode(['code'=>0,'data'=>json_decode($redisdata,true)]));
    }
    $item = ['attribute_id'=>$_POST['attribute_id'],'description_category_id'=>$_POST['category2'],'type_id'=>$_POST['category3']];
    $client = new \lib\OzonApiClient(2763302, '0a1f6874-fe81-4b30-97ce-4a3339eb077e');
    $data = $client->attributevalues($item);
    foreach ($data as $imte){
        $json3[$imte['id']] = $imte['value'];
    }
    $item = ['attribute_id'=>$_POST['attribute_id'],'description_category_id'=>$_POST['category2'],'type_id'=>$_POST['category3'],'language'=>'DEFAULT'];
    $client = new \lib\OzonApiClient(2763302, '0a1f6874-fe81-4b30-97ce-4a3339eb077e');
    $data = $client->attributevalues($item);
    foreach ($data as $imte){
        $json4[] = ['id'=>$imte['id'],'name'=>$json3[$imte['id']],'value'=>$imte['value'],'info'=>$imte['info'],'picture'=>$imte['picture']];
    }
    if($data){
        file_put_contents($url,json_encode($json4, JSON_UNESCAPED_UNICODE));
    }
    exit(json_encode($data?['code'=>0,'data'=>$json4]:['code'=>-1]));
break;
case 'save_production':
    $json_data = file_get_contents('php://input');
    $data = json_decode(trim($json_data,':'), true);
    $id = intval($data['id']);
    $shop_id = intval($data['shop_id']);
    
    $attributes = [];
    if($data['attributes[85]']=="" or empty($data['attributes[85]'])){
        $attributes[]=['id'=>85,
            'complex_id'=>0,
            'values'=>[['dictionary_value_id'=>126745801,'value'=>'Нет бренда']]
        ];
    }
    if($data['height']){
        $datas['height'] = $data['height'];
        $attributesdata[] = ['attributes[9456]'=>$data['height']];
    }
    if($data['depth']){
        $datas['depth'] = $data['depth'];
        $attributesdata[] = ['attributes[9454]'=>$data['depth']];
    }
    if($data['width']){
        $datas['width'] = $data['width'];
        $attributesdata[] = ['attributes[9455]'=>$data['width']];
    }
    if($data['weight']){
        $datas['weight'] = $data['weight'];
        $attributesdata[] = ['attributes[4497]'=>$data['weight']];
    }
    foreach ($data as $key => $value) {
        if(empty($value))continue;
        $values = [];
        if (strpos($key, 'attributes[') === 0) {
            preg_match('/attributes\[(\d+)\]/', $key, $matches);
            $attrId = $matches[1];
            if(is_array($value)){
                foreach ($value as $item){
                    $attribute_id_value = attribute_id_value($attrId,$item);
                    if($attribute_id_value){
                        $values[] = $attribute_id_value;
                    }else{
                        $values[] = ['dictionary_value_id'=>0,'value'=>$item];
                    }
                }
            }else{
                $attribute_id_value = attribute_id_value($attrId,$value);
                if($attribute_id_value){
                    $values[] = $attribute_id_value;
                }else{
                    $values[] = ['dictionary_value_id'=>0,'value'=>$value];
                }
            }
            if($attrId){
                $attributes[] = ['id'=>$attrId,
                    'complex_id'=>0,
                    'values'=>$values
                ];
                $attributesdata[] = [$key=>$value];
            }
        }else{
            $array[$key] = $value;
        }
    }
    if($data['title']){
        $datas['title'] = htmlspecialchars($data['title']);
    }
    if($data['images']){
        $images = array_filter(explode(';', $data['images']));
        $images = array_map('htmlspecialchars', $images);
        $images2 = $images;
        $images_str = implode(';', $images);
        $datas['images'] = $images_str;
        $attributes[] = ['id'=>4194,'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$images2[0]]]];
        $datas['mainImage'] = $images2[0];
        unset($images2[0]);
        $values = [];
        foreach ($images2 as $img){
            $values[]=['dictionary_value_id'=>0,'value'=>$img];
        }
        $attributes[] = ['id'=>4195,'complex_id'=>0,'values'=>$values];
    }
    $array['attributes'] = $attributes;
    $datas['attributes'] = json_encode($attributes);
    $datas['attributesdata'] = json_encode($attributesdata);
    $datas['category_chain'] = json_encode($data['category_chain']);
    if($data['price']){
        $datas['price'] = trim(daddslashes($data['price']));
    }
    if($data['old_price']){
        $datas['old_price'] = trim(daddslashes($data['old_price']));
    }
    if($data['offer_id']){
        $datas['offer_id'] = trim(daddslashes($data['offer_id']));
    }
    
    if($data['is_publish']==1){
        $rows = $DB->getRow("SELECT * FROM ozon_production WHERE id={$id} AND uid={$uid}");
        $attributes[] = ['id'=>11254,'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$rows['json']]]];
        $row = $DB->getRow("SELECT * FROM ozon_store WHERE id={$shop_id} AND uid={$uid}");
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $gmdata = $client->productimports($array,$attributes);
        if($gmdata['result']['task_id']){
            $datas['task_id'] = $gmdata['result']['task_id'];
        }
    }
    if($DB->update('production', $datas, ['id' => $id])){
        $result = ['code'=>0];
    }else{
        $result = ['code'=>-1];
    }
    exit(json_encode($result));
break;
case 'translate':
    $text = htmlspecialchars($_POST['text']);
    $source = $_POST['source'];
    $target = $_POST['target'];
    $translated = [
        'code' => 0,
        'data' => [
            'translated_text' => Transmart($text,$source,$target) // 示例返回
        ]
    ];
    exit(json_encode($translated));
break;
case 'submit_ImgTranslate':
    $json_data = file_get_contents('php://input');
    //exit(urldecode($json_data));
    $data = json_decode(trim($json_data,':'), true);
    switch($data['service']){
    case 1:
        $code = '9897275551';#阿里标识码
    break;
    case 2:
        $code = '9407294493';#谷歌标识码
    break;
    case 3:
        $code = '2985122323';#Papago标识码
    break;
    case 4:
        $code = '9440763900';#DeepL标识码
    break;
    case 5:
        $code = '9897275551';#ChatGPT标识码
    break;
    case 5:
        $code = '8107816915';#百度标识码
    break;
    default:
        $code = '9897275551';#阿里标识码
    break;
    }
    $xjai = new \lib\XiangJiAI('1569555821'); // 用户密钥
    $xjai->setImgTransKey($code); // 图片翻译服务阿里云标识码
    foreach ($data['images'] as $originUrl){
        $response = $xjai->getImageTranslate($originUrl, 'CHS', 'RUS');
        $images[] = [$originUrl,$response['Data']['Url']];
    }
    exit(json_encode(['code'=>0,'images'=>$images]));
break;
case 'json_translate':
    $json_data = file_get_contents('php://input');
    //exit(urldecode($json_data));
    $data = json_decode(trim($json_data,':'), true);
    $url = $data['url'];
    $code = '9897275551';#阿里标识码
    $xjai = new \lib\XiangJiAI('1569555821'); // 用户密钥
    $xjai->setImgTransKey($code); // 图片翻译服务阿里云标识码
    $response = $xjai->getImageTranslate($url, 'CHS', 'RUS');
    exit(json_encode(['code'=>0,'translatedUrl'=>$response['Data']['Url']]));
break;
case 'save_json':
    $json_data = file_get_contents('php://input');
    $data = json_decode(trim($json_data,':'), true);
    $id = intval($data['id']);
    $i=0;
    foreach ($data['data']['content'][0]['blocks'] as $item){
        $url = convertToOzonImage($item['img']['src']);
        //exit($url);
        if($url){
            $data['data']['content'][0]['blocks'][$i]['img']['src'] = $url;
            $data['data']['content'][0]['blocks'][$i]['img']['srcMobile'] = $url;
            unset($data['data']['content'][0]['blocks'][$i]['img']['translated']);
            unset($data['data']['content'][0]['blocks'][$i]['img']['translatedMobile']);
        }else{
            unset($data['data']['content'][0]['blocks'][$i]);
        }
        $i++;
    }
    if($DB->update('production', ['json'=>json_encode($data['data'])], ['id' => $id])){
        $result = ['code'=>0];
    }else{
        $result = ['code'=>-1];
    }
    exit(json_encode($result));
break;
case 'delete_production':
    $id = intval(trim($_POST['id']));
    if($DB->exec("DELETE FROM ozon_production WHERE id='$id' AND uid='$uid'")){
        $result = ['code'=>0];
    }else{
        $result = ['code'=>-1];
    }
    exit(json_encode($result));
break;
case 'get_shops':
    $list = $DB->getAll("SELECT * FROM ozon_store WHERE uid={$uid}");
	$list2 = [];
	foreach($list as $row){
	    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);

	    unset($row['apistatus']);
	    unset($row['key']);
	    unset($row['cookie']);
	    unset($row['addtime']);
	    unset($row['status']);
	    unset($row['uid']);
		$list2[] = $row;
		$i++;
	}
	exit(json_encode(['code'=>0,'data'=>$list2]));
break;
case 'get_warehouses':
    $id = intval($_POST['id']);
    $row = $DB->getRow("SELECT * FROM ozon_store WHERE id={$id} AND uid={$uid}");
	$list2 = [];
	$client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
    //$client->productinfolimit($row);
    if(!$row['warehouses'] or $row['warehouses']=='null'){
        $list2=$client->Ozonwarehouses();
        $DB->update('store', ['warehouses'=>json_encode($row['warehouses'])], ['id'=>$row['id']]);
    }else{
        $list2=json_decode($row['warehouses'],true);
    }
	exit(json_encode(['code'=>0,'data'=>$list2]));
break;
case 'queries':
    $offset = $_GET['page']-1;
    $filter['stock'] = "any_stock";
    $filter['period'] = "monthly";
    if($_GET['sku']){
        $filter['sku'] = $_GET['sku'];
    }
    //{"text":"","limit":"50","offset":"0","sort_by":"count","sort_dir":"desc","period":"days_7"}
    $array  = [
        'text'=>$_GET['text']?$_GET['text']:'',
        'limit'=>$_GET['limit']?$_GET['limit']:50,
        'offset'=>$offset,
        'sort_by'=>'count',
        'sort_dir'=>'desc',
        'period'=>$period?$period:'monthly'
    ];
    $url = "https://seller.ozon.ru/api/site/searchteam/Stats/queries/search/v2";
    $referer = "https://seller.ozon.ru/app/analytics/what-to-sell/all-queries";
    $cookie = "__Secure-ETC=290ef098c7a1f09c98d789cc53efd5a3; __Secure-access-token=7.*********.FNG9zGuaRhSubB805rMhEw.97.Ab6gSpyzTGe3Zt4uNYassXIlkIWOY9kBKSrPIYgo7ITaLeTsikh5ono47Buci6OitWHLkGRhdsXuYw9uQnnrmNWqTqoduxiT5aTDWTPB3Rk-.20250312092657.20250504093414.4K12XCJkk8GYAul9AaU09QBRRWlHBBIykki6josBm6Q.1f07490c96adb1456; __Secure-refresh-token=7.*********.FNG9zGuaRhSubB805rMhEw.97.Ab6gSpyzTGe3Zt4uNYassXIlkIWOY9kBKSrPIYgo7ITaLeTsikh5ono47Buci6OitWHLkGRhdsXuYw9uQnnrmNWqTqoduxiT5aTDWTPB3Rk-.20250312092657.20250504093414.BtfP8giA4oBwqbpzuLT_v_I-XOgsKm4YTsvTm0nzyfc.1f4c0dfaec2ada957; __Secure-user-id=*********; abt_data=7.MQjCrBfsYBOzUCQNQF8K9VUDlsNv7Hy3Q161UuD5S1zeEqqrzcPUTmeR_Dz0hd07GtXGhC1A_i-crxKNUVcrKXVvLN6a3WdPWsqywG6-6RtxsMRK-VWnFdk5sYH6Pmma3qrUNk_Q4adqQUGlq5lRRFhnN-6iMMSCfMnVeZ6TTn3aS_r04bOP7zS9SwsE_qYNu5L42knWqh8y9HCP-0VUw2GNDeLyKLcme96CHGKySqoLfraJWYDrGkxG0USrGQgLWPfUVrNeYZxgQwKYeTYkGqzku4klq6lhq4oYzTXQ-OWr6hOB_T1-5j4ZoMRPWx3Zjkc-bBpuXTqBnrPeDWRBwEpTzDUE0DUlTxvZV7Gh6xyQEmWvrFOqP2-3O-mGRjTIkQotCO_kG4akncOZTKfhyBglrNBpeM6ZOgoS_hiOF_p38XcJBAuSD_Y7C-fxYSnPT7qu6WIZVUTOoTZ5wZ4Ta6rDgMGumxAIlDcL-jC6Z49f-Z9hTURhJWmLojzq_j8L; sc_company_id=2749292";
    //json_encode($array)
    $res = get_ozoncurl($url,$array, $referer, $cookie);
    $json = json_decode($res,true);
    foreach($json['data'] as $row){
	    $row['gmvrmb'] = round($row['gmv']/$fx['ru']['num'],2);
	    $row['gmvus'] = round($row['gmvrmb']*$fx['us']['num'],2);
	    $row['avgCaRubrmb']  = round($row['avgCaRub']/$fx['ru']['num'],2);
	    $row['avgCaRubus'] = round($row['avgCaRubrmb']*$fx['us']['num'],2);
	    $list2[] = $row;
	}
    exit(json_encode(['code'=>0,'msg'=>'success','count'=>$json['total'],'data'=>$list2]));
break;
default:
	exit('{"code":-4,"msg":"No Act"}');
break;
}
// 辅助函数
function getCurrencyCode($value) {
    $map = [
        '1' => 'CNY',
        '2' => 'RUB',
        '3' => 'USD'
    ];
    return $map[$value] ?? '';
}