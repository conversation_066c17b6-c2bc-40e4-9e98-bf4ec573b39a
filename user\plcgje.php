

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ozon订单金额更新</title>
    <link rel="stylesheet" href="//unpkg.com/layui@2.6.8/dist/css/layui.css">
    <style>
        .layui-upload-drag {
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .layui-form-label {
            width: 120px;
        }
        .layui-input-block {
            margin-left: 150px;
        }
        .instructions {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 2px;
        }
        .error-line {
            background-color: #ffebee;
            padding: 2px 5px;
            border-radius: 2px;
            margin: 2px 0;
        }
    </style>
</head>
<body>
<div class="layui-container" style="margin-top: 30px;">
    <div class="layui-card">
        <div class="layui-card-header">Ozon订单金额更新</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="upload-form">
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">订单号 金额</label>
                    <div class="layui-input-block">
                        <textarea name="text_data" id="text_data" placeholder="每行一条，格式：订单号 金额" class="layui-textarea" style="height: 200px;"></textarea>
                        <button type="button" id="filter-spaces-btn" class="layui-btn layui-btn-sm" style="margin-top: 10px;">过滤空格</button>
                        <button type="button" id="validate-btn" class="layui-btn layui-btn-sm layui-btn-normal" style="margin-top: 10px; margin-left: 10px;">验证格式</button>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="submit-btn">提交更新</button>
                    </div>
                </div>
            </form>
            
            <div class="instructions">
                <p><strong>使用说明：</strong></p>
                <p>1. 请按"订单号 金额"格式输入，每行一条记录</p>
                <p>2. 订单号和金额之间用空格分隔</p>
                <p>3. 只会更新当前账号的订单</p>
            </div>
            
            <div id="result-container" style="display: none; margin-top: 20px;">
                <fieldset class="layui-elem-field">
                    <legend>更新结果</legend>
                    <div class="layui-field-box">
                        <div id="result-content"></div>
                        <div id="debug-info" style="margin-top: 15px; padding: 10px; background-color: #f8f8f8; display: none;">
                            <h4>调试信息</h4>
                            <pre id="debug-details" style="white-space: pre-wrap; word-wrap: break-word; font-size: 12px;"></pre>
                        </div>
                    </div>
                </fieldset>
            </div>
            
            <div id="validation-result" style="display: none; margin-top: 15px; padding: 10px; border-radius: 2px;"></div>
        </div>
    </div>
</div>

<script src="//unpkg.com/layui@2.6.8/dist/layui.js"></script>
<script>
layui.use(['upload', 'form', 'layer'], function(){
    var upload = layui.upload;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;
    
    // 表单提交
    form.on('submit(submit-btn)', function(data){
        var textData = $('#text_data').val().trim();
        if(!textData){
            layer.msg('请先输入订单号和金额', {icon: 2});
            return false;
        }
        
        // 显示加载状态
        var loading = layer.load(2);
        $('#result-container').hide();
        $('#validation-result').hide();
        
        // 发送请求
        $.ajax({
            url: 'ajax.php?act=update_ozon_orders',
            type: 'POST',
            data: {
                text_data: textData
            },
            dataType: 'json',
            success: function(res){
                layer.close(loading);
                
                // 构建结果HTML
                var html = '';
                if(res.code === 0){
                    html = '<div style="color: #01AAED"><i class="layui-icon layui-icon-ok-circle"></i> ' + 
                               res.msg + '</div>';
                    if(res.updated > 0){
                        html += '<div style="margin-top: 10px;">成功更新 <span style="color: #FF5722; font-weight: bold">' + 
                                res.updated + '</span> 条记录</div>';
                    }
                    if(res.errors && res.errors.length > 0){
                        html += '<div style="margin-top: 10px; color: #FF5722;">更新错误：<ul>';
                        res.errors.forEach(function(error, index){
                            html += '<li>' + (index+1) + '. ' + error + '</li>';
                        });
                        html += '</ul></div>';
                        
                        // 显示调试信息
                        $('#debug-details').text(JSON.stringify(res, null, 2));
                        $('#debug-info').show();
                    }
                } else {
                    html = '<div style="color: #FF5722"><i class="layui-icon layui-icon-close-fill"></i> ' + 
                          (res.msg || '更新失败') + '</div>';
                }
                
                // 显示结果
                $('#result-content').html(html);
                $('#result-container').show();
            },
            error: function(xhr, status, error){
                layer.close(loading);
                var msg = '请求失败，请重试';
                if(xhr.responseJSON && xhr.responseJSON.msg){
                    msg = xhr.responseJSON.msg;
                }
                $('#result-content').html('<div style="color: #FF5722"><i class="layui-icon layui-icon-close-fill"></i> ' + msg + '</div>');
                $('#result-container').show();
            }
        });
        
        return false;
    });
    
    // 过滤空格按钮点击事件
    $('#filter-spaces-btn').on('click', function(){
        var text = $('#text_data').val();
        var lines = text.split('\n');
        var filteredLines = lines.map(function(line){
            line = line.trim();
            // 替换多个空格为一个空格
            line = line.replace(/\s+/g, ' ');
            return line;
        });
        $('#text_data').val(filteredLines.join('\n'));
        layer.msg('空格已过滤', {icon: 1});
    });
    
    // 格式验证按钮点击事件
    $('#validate-btn').on('click', function(){
        var textData = $('#text_data').val().trim();
        if(!textData){
            layer.msg('请先输入订单号和金额', {icon: 2});
            return;
        }
        
        var lines = textData.split('\n');
        var validCount = 0;
        var invalidCount = 0;
        var invalidDetails = [];
        
        lines.forEach(function(line, index){
            line = line.trim();
            if(line){
                var parts = line.split(/\s+/);
                if(parts.length != 2){
                    invalidCount++;
                    invalidDetails.push({
                        line: index + 1,
                        content: line,
                        error: '格式错误：应为"订单号 金额"格式，且中间只能有一个空格'
                    });
                } else {
                    var amount = parseFloat(parts[1]);
                    if(isNaN(amount) || amount <= 0){
                        invalidCount++;
                        invalidDetails.push({
                            line: index + 1,
                            content: line,
                            error: '金额无效：必须是大于0的数字'
                        });
                    } else {
                        validCount++;
                    }
                }
            }
        });
        
        var resultHtml = '';
        $('#result-container').hide();
        
        if(invalidCount === 0){
            resultHtml = '<div style="color: #5FB878; background-color: #f0f9eb; padding: 10px;">' +
                         '<i class="layui-icon layui-icon-ok-circle"></i> 格式验证通过！共 ' + validCount + ' 条有效记录</div>';
            $('#validation-result').attr('class', 'layui-elem-quote');
        } else {
            resultHtml = '<div style="color: #f56c6c; background-color: #fef0f0; padding: 10px;">' +
                         '<i class="layui-icon layui-icon-close-fill"></i> 格式验证失败：共 ' + invalidCount + ' 条记录格式错误</div>';
            resultHtml += '<div style="margin-top: 10px;">';
            
            invalidDetails.forEach(function(item, index){
                resultHtml += '<div class="error-line">' +
                             '<span style="font-weight: bold;">第 ' + item.line + ' 行</span>: ' + 
                             '<span style="color: #333;">' + item.content + '</span><br>' +
                             '<span style="color: #f56c6c; font-size: 12px;">' + item.error + '</span>' +
                             '</div>';
            });
            
            resultHtml += '</div>';
            $('#validation-result').attr('class', 'layui-elem-quote layui-quote-nm');
        }
        
        $('#validation-result').html(resultHtml).show();
    });
});
</script>
</body>
</html>