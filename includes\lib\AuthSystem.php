<?php
namespace lib;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use DomainException;
use UnexpectedValueException;
use InvalidArgumentException;

class AuthSystem {
    private $secretKey;
    private $algorithm = 'HS256';

    public function __construct() {
        // 推荐从环境变量获取密钥
        $this->secretKey = base64_decode(
            getenv('JWT_SECRET') ?: 
            'c3ZyYmYvLy4uJiYjQC0tc2NiYnItZ3cqLWtleS0xMjMhQCM=' // base64编码后的原密钥
        );

        // 验证密钥长度
        if (strlen($this->secretKey) < 32) {
            throw new \RuntimeException('JWT密钥长度不足256位');
        }
    }

    public function generateToken($userId, $username) {
        $payload = [
            'iat' => time(),
            'exp' => time() + 691200,
            'data' => [
                'uid' => (int)$userId,
                'username' => filter_var($username, FILTER_SANITIZE_STRING)
            ]
        ];
        return JWT::encode($payload, $this->secretKey, $this->algorithm);
    }

    public function validateToken($token) {
        try {
            $decoded = JWT::decode(
                $token,
                new Key($this->secretKey, $this->algorithm)
            );
            return [
                'valid' => true,
                'data' => (array)$decoded->data
            ];
        } catch (ExpiredException $e) {
            return ['valid' => false, 'error' => 'TOKEN_EXPIRED'];
        } catch (SignatureInvalidException $e) {
            return ['valid' => false, 'error' => 'INVALID_SIGNATURE'];
        } catch (DomainException | UnexpectedValueException | InvalidArgumentException $e) {
            return ['valid' => false, 'error' => 'INVALID_TOKEN'];
        } catch (\Throwable $e) {
            error_log("JWT Error: {$e->getMessage()}"); // 记录到日志
            return ['valid' => false, 'error' => 'AUTH_FAILED'];
        }
    }
}