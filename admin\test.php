        <?php
        session_start();

        echo "<!DOCTYPE html><html><head><meta charset='utf-8'><title>管理员登录状态测试</title></head><body>";
        echo "<h1>管理员登录状态测试</h1>";

        echo "<h2>Session 信息:</h2>";
        echo "<pre>";
        print_r($_SESSION);
        echo "</pre>";

        echo "<h2>检查登录状态:</h2>";
        if (isset($_SESSION['admin_islogin']) && $_SESSION['admin_islogin'] === true) {
            echo "<p style='color: green;'>✅ 管理员已登录</p>";
            echo "<p>管理员UID: " . ($_SESSION['admin_uid'] ?? '未设置') . "</p>";
            echo "<p>管理员用户名: " . ($_SESSION['admin_username'] ?? '未设置') . "</p>";
        } else {
            echo "<p style='color: red;'>❌ 管理员未登录</p>";
        }

        echo "<h2>测试API:</h2>";

    // 测试配置接口
    echo "<h3>配置接口测试:</h3>";
    $config_url = 'ajax.php?act=config';
    $config_result = @file_get_contents($config_url);
    if ($config_result) {
        echo "<p style='color: green;'>✅ 配置接口正常</p>";
        echo "<details><summary>查看配置数据</summary><pre>" . htmlspecialchars($config_result) . "</pre></details>";
    } else {
        echo "<p style='color: red;'>❌ 配置接口失败</p>";
    }

    // 测试菜单接口
    echo "<h3>菜单接口测试:</h3>";
    $menu_url = 'ajax.php?act=menu';
    $menu_result = @file_get_contents($menu_url);
    if ($menu_result) {
        echo "<p style='color: green;'>✅ 菜单接口正常</p>";
        echo "<details><summary>查看菜单数据</summary><pre>" . htmlspecialchars($menu_result) . "</pre></details>";
    } else {
        echo "<p style='color: red;'>❌ 菜单接口失败</p>";
    }

    echo "<h3>链接测试:</h3>";
    echo "<p><a href='ajax.php?act=config&debug=1' target='_blank'>测试配置接口（带调试）</a></p>";
    echo "<p><a href='ajax.php?act=menu&debug=1' target='_blank'>测试菜单接口（带调试）</a></p>";
    echo "<p><a href='login.html'>返回登录页面</a></p>";
    echo "<p><a href='index.php'>返回管理后台</a></p>";

        echo "</body></html>";
        ?> 