<?php
header('Content-Type: application/json');

// 物流数据（原始数据为卢布，将在计算时转换为人民币）
$logisticsData = [
    // OYX
    ['provider' => "OYX", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Standard", 'basePrice' => 3, 'perGramPrice' => 0.035],
    ['provider' => "OYX", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Economy", 'basePrice' => 2.9, 'perGramPrice' => 0.025],
    ['provider' => "OYX", 'method' => "Budget", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 501, 'maxWeight' => 25000, 'speed' => "Standard", 'basePrice' => 23, 'perGramPrice' => 0.025],
    ['provider' => "OYX", 'method' => "Small", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 1, 'maxWeight' => 2000, 'speed' => "Standard", 'basePrice' => 15.6, 'perGramPrice' => 0.035],
    ['provider' => "OYX", 'method' => "Small", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 1, 'maxWeight' => 2000, 'speed' => "Economy", 'basePrice' => 15.6, 'perGramPrice' => 0.025],
    ['provider' => "OYX", 'method' => "Big", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 2001, 'maxWeight' => 25000, 'speed' => "Standard", 'basePrice' => 35.6, 'perGramPrice' => 0.025],
    ['provider' => "OYX", 'method' => "Premium Small", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 1, 'maxWeight' => 5001, 'speed' => "Standard", 'basePrice' => 21.6, 'perGramPrice' => 0.035],
    ['provider' => "OYX", 'method' => "Premium Small", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 1, 'maxWeight' => 5001, 'speed' => "Economy", 'basePrice' => 21.6, 'perGramPrice' => 0.025],
    ['provider' => "OYX", 'method' => "Premium Big", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 5001, 'maxWeight' => 25000, 'speed' => "Standard", 'basePrice' => 62, 'perGramPrice' => 0.028],

    // RETS
    ['provider' => "RETS", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Standard", 'basePrice' => 2.8, 'perGramPrice' => 0.335],
    ['provider' => "RETS", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Economy", 'basePrice' => 2.85, 'perGramPrice' => 0.0235],
    ['provider' => "RETS", 'method' => "Budget", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 501, 'maxWeight' => 25000, 'speed' => "Standard", 'basePrice' => 22.9, 'perGramPrice' => 0.0246],
    ['provider' => "RETS", 'method' => "Budget", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 501, 'maxWeight' => 25000, 'speed' => "Economy", 'basePrice' => 22.5, 'perGramPrice' => 0.0168],
    ['provider' => "RETS", 'method' => "Small", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 1, 'maxWeight' => 2000, 'speed' => "Standard", 'basePrice' => 15, 'perGramPrice' => 0.033],
    ['provider' => "RETS", 'method' => "Small", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 1, 'maxWeight' => 2000, 'speed' => "Economy", 'basePrice' => 15.9, 'perGramPrice' => 0.023],
    ['provider' => "RETS", 'method' => "Premium Small", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 1, 'maxWeight' => 5001, 'speed' => "Standard", 'basePrice' => 21.9, 'perGramPrice' => 0.034],
    ['provider' => "RETS", 'method' => "Premium Small", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 1, 'maxWeight' => 5001, 'speed' => "Economy", 'basePrice' => 21.9, 'perGramPrice' => 0.024],
    ['provider' => "RETS", 'method' => "Premium Big", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 5001, 'maxWeight' => 25000, 'speed' => "Economy", 'basePrice' => 62, 'perGramPrice' => 0.0227],

    // ATC
    ['provider' => "ATC", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Standard", 'basePrice' => 3, 'perGramPrice' => 0.035],
    ['provider' => "ATC", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Economy", 'basePrice' => 3, 'perGramPrice' => 0.024],
    ['provider' => "ATC", 'method' => "Budget", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 501, 'maxWeight' => 25000, 'speed' => "Standard", 'basePrice' => 22, 'perGramPrice' => 0.025],
    ['provider' => "ATC", 'method' => "Budget", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 501, 'maxWeight' => 25000, 'speed' => "Economy", 'basePrice' => 22.7, 'perGramPrice' => 0.0168],
    ['provider' => "ATC", 'method' => "Small", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 1, 'maxWeight' => 2000, 'speed' => "Standard", 'basePrice' => 16, 'perGramPrice' => 0.033],
    ['provider' => "ATC", 'method' => "Small", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 1, 'maxWeight' => 2000, 'speed' => "Economy", 'basePrice' => 16, 'perGramPrice' => 0.238],
    ['provider' => "ATC", 'method' => "Big", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 2001, 'maxWeight' => 25000, 'speed' => "Standard", 'basePrice' => 36, 'perGramPrice' => 0.025],
    ['provider' => "ATC", 'method' => "Big", 'minPriceRUB' => 1501, 'maxPriceRUB' => 7000, 'minWeight' => 2001, 'maxWeight' => 25000, 'speed' => "Economy", 'basePrice' => 36, 'perGramPrice' => 0.017],
    ['provider' => "ATC", 'method' => "Premium Small", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 1, 'maxWeight' => 5001, 'speed' => "Standard", 'basePrice' => 21, 'perGramPrice' => 0.034],
    ['provider' => "ATC", 'method' => "Premium Small", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 1, 'maxWeight' => 5001, 'speed' => "Economy", 'basePrice' => 21.5, 'perGramPrice' => 0.024],
    ['provider' => "ATC", 'method' => "Premium Big", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 5001, 'maxWeight' => 25000, 'speed' => "Standard", 'basePrice' => 62, 'perGramPrice' => 0.025],
    ['provider' => "ATC", 'method' => "Premium Big", 'minPriceRUB' => 7001, 'maxPriceRUB' => 250000, 'minWeight' => 5001, 'maxWeight' => 25000, 'speed' => "Economy", 'basePrice' => 61.98, 'perGramPrice' => 0.017],

    // Tanais
    ['provider' => "Tanais", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Express", 'basePrice' => 3, 'perGramPrice' => 0.045],
    ['provider' => "Tanais", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Standard", 'basePrice' => 2.9, 'perGramPrice' => 0.0349],
    ['provider' => "Tanais", 'method' => "Extra Small", 'minPriceRUB' => 0, 'maxPriceRUB' => 1500, 'minWeight' => 1, 'maxWeight' => 500, 'speed' => "Economy", 'basePrice' => 2.8, 'perGramPrice' => 0.0249],
    [ 'provider' =>  "Tanais", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  23, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "Tanais", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  22.9, 'perGramPrice' =>  0.0245 ],
    [ 'provider' =>  "Tanais", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  22.8, 'perGramPrice' =>  0.0165 ],
    [ 'provider' =>  "Tanais", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Express", 'basePrice' =>  16, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "Tanais", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  15.5, 'perGramPrice' =>  0.0345 ],
    [ 'provider' =>  "Tanais", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  15.5, 'perGramPrice' =>  0.0245 ],
    [ 'provider' =>  "Tanais", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Express", 'basePrice' =>  22, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "Tanais", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  21.9, 'perGramPrice' =>  0.0345 ],
    [ 'provider' =>  "Tanais", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  21.9, 'perGramPrice' =>  0.024 ],

    // ABT
    [ 'provider' =>  "ABT", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  2.95, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "ABT", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  2.95, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ABT", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  22.8, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ABT", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  15.8, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "ABT", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  15.8, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ABT", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  35.8, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ABT", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  21.8, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "ABT", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  21.8, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ABT", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  61.8, 'perGramPrice' =>  0.028 ],

    // GUOO
    [ 'provider' =>  "GUOO", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Express", 'basePrice' =>  3, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  3, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  2.8, 'perGramPrice' =>  0.024 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  23, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  23, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Express", 'basePrice' =>  16, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  16, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  16, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  36, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  36, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Express", 'basePrice' =>  22, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  22, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  22, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  62, 'perGramPrice' =>  0.028 ],
    [ 'provider' =>  "GUOO", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  62, 'perGramPrice' =>  0.023 ],

    // XY
    [ 'provider' =>  "XY", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Express", 'basePrice' =>  2.95, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "XY", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  2.95, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "XY", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  3, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "XY", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  23, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "XY", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  16, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "XY", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  16, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "XY", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  36, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "XY", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  22, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "XY", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  22, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "XY", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  62, 'perGramPrice' =>  0.028 ],

    // Leader
    [ 'provider' =>  "Leader", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  3, 'perGramPrice' =>  0.0345 ],
    [ 'provider' =>  "Leader", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  3, 'perGramPrice' =>  0.0245 ],
    [ 'provider' =>  "Leader", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  15.5, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "Leader", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Express", 'basePrice' =>  22, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "Leader", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  21.9, 'perGramPrice' =>  0.0345 ],
    [ 'provider' =>  "Leader", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  21.5, 'perGramPrice' =>  0.025 ],

    // ZTO
    [ 'provider' =>  "ZTO", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Express", 'basePrice' =>  3, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  3, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  3, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  23, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  23, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  23, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Express", 'basePrice' =>  16, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  16, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  16, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  36, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Express", 'basePrice' =>  22, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  22, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  22, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  62, 'perGramPrice' =>  0.028 ],
    [ 'provider' =>  "ZTO", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  62, 'perGramPrice' =>  0.023 ],

    // IML
    [ 'provider' =>  "IML", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Express", 'basePrice' =>  2.95, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "IML", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  2.95, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "IML", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  2.95, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "IML", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  22.95, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "IML", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  22.95, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "IML", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Express", 'basePrice' =>  15.95, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "IML", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  15.95, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "IML", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  15.95, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "IML", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  35.95, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "IML", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  21.95, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "IML", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  61.9, 'perGramPrice' =>  0.028 ],

    // Ural
    [ 'provider' =>  "Ural", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Express", 'basePrice' =>  2.9, 'perGramPrice' =>  0.042 ],
    [ 'provider' =>  "Ural", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  2.8, 'perGramPrice' =>  0.032 ],
    [ 'provider' =>  "Ural", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  3, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "Ural", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  23, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "Ural", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  23, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "Ural", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Express", 'basePrice' =>  16, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "Ural", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  16, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "Ural", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  16, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "Ural", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  36, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "Ural", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  36, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "Ural", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Express", 'basePrice' =>  22, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "Ural", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  22, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "Ural", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  22, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "Ural", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  62, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "Ural", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  62, 'perGramPrice' =>  0.028 ],
    [ 'provider' =>  "Ural", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  62, 'perGramPrice' =>  0.023 ],

    // UNI
    [ 'provider' =>  "UNI", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Express", 'basePrice' =>  3, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "UNI", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  3, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "UNI", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  3, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "UNI", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  23, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "UNI", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  23, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "UNI", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  23, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "UNI", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Express", 'basePrice' =>  16, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "UNI", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  16, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "UNI", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  16, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "UNI", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  36, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "UNI", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  36, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "UNI", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  36, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "UNI", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Express", 'basePrice' =>  22, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "UNI", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  22, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "UNI", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  22, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "UNI", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  62, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "UNI", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  62, 'perGramPrice' =>  0.028 ],
    [ 'provider' =>  "UNI", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  62, 'perGramPrice' =>  0.023 ],

    // CEL
    [ 'provider' =>  "CEL", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Express", 'basePrice' =>  3, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "CEL", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Standard", 'basePrice' =>  3, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "CEL", 'method' =>  "Extra Small", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  1, 'maxWeight' =>  500, 'speed' =>  "Economy", 'basePrice' =>  3, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "CEL", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  23, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "CEL", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  23, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "CEL", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  23, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "CEL", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Express", 'basePrice' =>  16, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "CEL", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  16, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "CEL", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  16, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "CEL", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  36, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "CEL", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  36, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "CEL", 'method' =>  "Big", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  2001, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  36, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "CEL", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Express", 'basePrice' =>  22, 'perGramPrice' =>  0.045 ],
    [ 'provider' =>  "CEL", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  22, 'perGramPrice' =>  0.035 ],
    [ 'provider' =>  "CEL", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  22, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "CEL", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  62, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "CEL", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  62, 'perGramPrice' =>  0.028 ],
    [ 'provider' =>  "CEL", 'method' =>  "Premium Big", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  5001, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  62, 'perGramPrice' =>  0.023 ],

    // GBS
    [ 'provider' =>  "GBS", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Express", 'basePrice' =>  21.85, 'perGramPrice' =>  0.033 ],
    [ 'provider' =>  "GBS", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Standard", 'basePrice' =>  21.85, 'perGramPrice' =>  0.025 ],
    [ 'provider' =>  "GBS", 'method' =>  "Budget", 'minPriceRUB' =>  0, 'maxPriceRUB' =>  1500, 'minWeight' =>  501, 'maxWeight' =>  25000, 'speed' =>  "Economy", 'basePrice' =>  22.4, 'perGramPrice' =>  0.017 ],
    [ 'provider' =>  "GBS", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Express", 'basePrice' =>  16, 'perGramPrice' =>  0.04275 ],
    [ 'provider' =>  "GBS", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Standard", 'basePrice' =>  15.2, 'perGramPrice' =>  0.034 ],
    [ 'provider' =>  "GBS", 'method' =>  "Small", 'minPriceRUB' =>  1501, 'maxPriceRUB' =>  7000, 'minWeight' =>  1, 'maxWeight' =>  2000, 'speed' =>  "Economy", 'basePrice' =>  15.2, 'perGramPrice' =>  0.02375 ],
    [ 'provider' =>  "GBS", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Express", 'basePrice' =>  22, 'perGramPrice' =>  0.04275 ],
    [ 'provider' =>  "GBS", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Standard", 'basePrice' =>  21, 'perGramPrice' =>  0.034 ],
    [ 'provider' =>  "GBS", 'method' =>  "Premium Small", 'minPriceRUB' =>  7001, 'maxPriceRUB' =>  250000, 'minWeight' =>  1, 'maxWeight' =>  5001, 'speed' =>  "Economy", 'basePrice' =>  23, 'perGramPrice' =>  0.02375 ]

];

// 获取所有物流商
function getProviders() {
    global $logisticsData;
    $providers = array_unique(array_column($logisticsData, 'provider'));
    sort($providers);
    return array_values($providers); // 确保返回索引数组
}

// 获取所有物流方式
function getMethods() {
    global $logisticsData;
    $methods = array_unique(array_column($logisticsData, 'method'));
    sort($methods);
    return array_values($methods); // 确保返回索引数组
}

// 计算运费
function calculateShippingCost($option, $weight) {
    if (!isset($option['basePrice']) || !isset($option['perGramPrice'])) {
        return null;
    }
    return $option['basePrice'] + ($weight * $option['perGramPrice']);
}

// 计算利润
function calculateProfit($price, $cost, $shippingCost, $commissionRate, $labelFee = 0, $withdrawalFeeRate = 0) {
    $commission = $price * ($commissionRate / 100);
    $withdrawalFee = $price * ($withdrawalFeeRate / 100);
    $totalCost = $cost + $shippingCost + $commission + $labelFee + $withdrawalFee;
    $profit = $price - $totalCost;
    $profitMargin = ($profit / $price) * 100;
    
    return [
        'commission' => $commission,
        'withdrawalFee' => $withdrawalFee,
        'totalCost' => $totalCost,
        'profit' => $profit,
        'profitMargin' => $profitMargin
    ];
}

// 根据目标利润率反推售价
function calculateTargetPrice($cost, $shippingCost, $targetProfitMargin, $commissionRate, $labelFee = 0, $withdrawalFeeRate = 0) {
    // 售价 = (成本 + 运费 + 贴单费) / (1 - 佣金比例 - 提现手续费比例 - 目标利润率)
    return ($cost + $shippingCost + $labelFee) / (1 - ($commissionRate / 100) - ($withdrawalFeeRate / 100) - ($targetProfitMargin / 100));
}

// 处理请求
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'get_providers':
        echo json_encode(getProviders());
        break;
        
    case 'get_methods':
        echo json_encode(getMethods());
        break;
        
    case 'calculate':
        $weight = floatval($_POST['weight']);
        $price = floatval($_POST['price']);
        $cost = floatval($_POST['cost']);
        $commission = floatval($_POST['commission']);
        $labelFee = floatval($_POST['label_fee']);
        $withdrawalFee = floatval($_POST['withdrawal_fee']);
        $exchangeRate = floatval($_POST['exchange_rate']);
        
        // 获取筛选条件
        $selectedProviders = isset($_POST['providers']) ? $_POST['providers'] : [];
        $selectedMethods = isset($_POST['methods']) ? $_POST['methods'] : [];
        $selectedSpeeds = isset($_POST['speeds']) ? $_POST['speeds'] : [];
        
        // 筛选符合条件的物流选项
        $filteredOptions = array_filter($logisticsData, function($option) use ($weight, $price, $exchangeRate, $selectedProviders, $selectedMethods, $selectedSpeeds) {
            $minPriceCNY = $option['minPriceRUB'] * $exchangeRate;
            $maxPriceCNY = $option['maxPriceRUB'] * $exchangeRate;
            
            return (empty($selectedProviders) || in_array($option['provider'], $selectedProviders)) &&
                   (empty($selectedMethods) || in_array($option['method'], $selectedMethods)) &&
                   (empty($selectedSpeeds) || in_array($option['speed'], $selectedSpeeds)) &&
                   $weight >= $option['minWeight'] && $weight <= $option['maxWeight'] &&
                   $price >= $minPriceCNY && $price <= $maxPriceCNY;
        });
        
        // 计算每种选项的利润
        $results = [];
        foreach ($filteredOptions as $option) {
            $shippingCost = calculateShippingCost($option, $weight);
            if ($shippingCost === null) continue;
            
            $profitData = calculateProfit($price, $cost, $shippingCost, $commission, $labelFee, $withdrawalFee);
            $minPriceCNY = $option['minPriceRUB'] * $exchangeRate;
            $maxPriceCNY = $option['maxPriceRUB'] * $exchangeRate;
            
            $results[] = [
                'provider' => $option['provider'],
                'method' => $option['method'],
                'speed' => $option['speed'],
                'minPriceCNY' => $minPriceCNY,
                'maxPriceCNY' => $maxPriceCNY,
                'minWeight' => $option['minWeight'],
                'maxWeight' => $option['maxWeight'],
                'shippingCost' => $shippingCost,
                'commission' => $profitData['commission'],
                'labelFee' => $labelFee,
                'withdrawalFee' => $profitData['withdrawalFee'],
                'totalCost' => $profitData['totalCost'],
                'profit' => $profitData['profit'],
                'profitMargin' => $profitData['profitMargin']
            ];
        }
        
        // 按利润率排序
        usort($results, function($a, $b) {
            return $b['profitMargin'] <=> $a['profitMargin'];
        });
        
        echo json_encode($results);
        break;
        
    case 'reverse_calculate':
        $weight = floatval($_POST['weight']);
        $cost = floatval($_POST['cost']);
        $commission = floatval($_POST['commission']);
        $labelFee = floatval($_POST['label_fee']);
        $withdrawalFee = floatval($_POST['withdrawal_fee']);
        $targetProfit = floatval($_POST['target_profit']);
        $exchangeRate = floatval($_POST['exchange_rate']);
        
        // 获取筛选条件
        $selectedProviders = isset($_POST['providers']) ? $_POST['providers'] : [];
        $selectedMethods = isset($_POST['methods']) ? $_POST['methods'] : [];
        $selectedSpeeds = isset($_POST['speeds']) ? $_POST['speeds'] : [];
        
        // 筛选符合条件的物流选项
        $filteredOptions = array_filter($logisticsData, function($option) use ($weight, $selectedProviders, $selectedMethods, $selectedSpeeds) {
            return (empty($selectedProviders) || in_array($option['provider'], $selectedProviders)) &&
                   (empty($selectedMethods) || in_array($option['method'], $selectedMethods)) &&
                   (empty($selectedSpeeds) || in_array($option['speed'], $selectedSpeeds)) &&
                   $weight >= $option['minWeight'] && $weight <= $option['maxWeight'];
        });
        
        // 计算每种选项的目标售价
        $results = [];
        foreach ($filteredOptions as $option) {
            $shippingCost = calculateShippingCost($option, $weight);
            if ($shippingCost === null) continue;
            
            $targetPrice = calculateTargetPrice($cost, $shippingCost, $targetProfit, $commission, $labelFee, $withdrawalFee);
            $minPriceCNY = $option['minPriceRUB'] * $exchangeRate;
            $maxPriceCNY = $option['maxPriceRUB'] * $exchangeRate;
            
            // 检查售价是否在物流方式的售价范围内
            if ($targetPrice < $minPriceCNY || $targetPrice > $maxPriceCNY) continue;
            
            $profitData = calculateProfit($targetPrice, $cost, $shippingCost, $commission, $labelFee, $withdrawalFee);
            
            $results[] = [
                'provider' => $option['provider'],
                'method' => $option['method'],
                'speed' => $option['speed'],
                'shippingCost' => $shippingCost,
                'targetPrice' => $targetPrice,
                'commission' => $profitData['commission'],
                'labelFee' => $labelFee,
                'withdrawalFee' => $profitData['withdrawalFee'],
                'totalCost' => $profitData['totalCost'],
                'profit' => $profitData['profit'],
                'profitMargin' => $profitData['profitMargin'],
                'targetProfit' => $targetProfit,
                'commissionRate' => $commission,
                'withdrawalFeeRate' => $withdrawalFee
            ];
        }
        
        // 按售价排序
        usort($results, function($a, $b) {
            return $a['targetPrice'] <=> $b['targetPrice'];
        });
        
        echo json_encode($results);
        break;
        
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
} 