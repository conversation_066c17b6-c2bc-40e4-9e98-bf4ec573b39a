<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>兑换码生成器</title>
<link rel="stylesheet" href="/assets/component/layui/css/layui.css">
<style>
  body {
    background-color: #f2f6fc;
    padding: 20px;
  }
  .main-container {
    max-width: 800px;
    margin: 0 auto;
  }
  .layui-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  }
  .layui-card-header {
    font-weight: 600;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #ebeef5;
  }
  #generated-codes {
      margin-top: 15px;
      display: none;
  }
  #codes-textarea {
      margin-top: 10px;
      min-height: 200px;
      font-family: Consolas, 'Courier New', monospace;
      white-space: pre;
      line-height: 1.6;
  }
</style>
</head>
<body>

<div class="main-container">
  <div class="layui-card">
    <div class="layui-card-header">
      <i class="layui-icon layui-icon-auz" style="margin-right: 8px;"></i>兑换码生成器 (仅管理员)
    </div>
    <div class="layui-card-body">
      <div class="layui-form layui-form-pane">
        <div class="layui-form-item">
          <label class="layui-form-label">会员等级</label>
          <div class="layui-input-block">
            <select id="user-level" lay-verify="required">
              <option value=""></option>
              <option value="1">普通用户</option>
              <option value="2">VIP用户</option>
              <option value="3">SVIP用户</option>
              <option value="4">企业用户</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">有效天数</label>
          <div class="layui-input-block">
            <input type="number" id="days" value="30" placeholder="请输入有效天数" autocomplete="off" class="layui-input">
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">生成数量</label>
          <div class="layui-input-block">
            <input type="number" id="num" value="10" placeholder="请输入生成数量" autocomplete="off" class="layui-input">
          </div>
        </div>
        <div class="layui-form-item">
          <div class="layui-input-block">
            <button class="layui-btn layui-btn-normal" id="generate-btn">立即生成</button>
          </div>
        </div>
      </div>
      
      <div id="generated-codes">
        <label class="layui-form-label" style="text-align: left; width: auto; padding-left: 10px;">已生成兑换码:</label>
        <textarea id="codes-textarea" readonly class="layui-textarea"></textarea>
      </div>

    </div>
  </div>
</div>

<script src="/assets/component/layui/layui.js"></script>
<script>
layui.use(['layer', 'form', 'jquery'], function(){
  var layer = layui.layer;
  var form = layui.form;
  var $ = layui.jquery;

  // 生成按钮点击事件
  $('#generate-btn').on('click', function(){
      var user_level = $('#user-level').val();
      var days = $('#days').val();
      var num = $('#num').val();
      
      if(user_level === '' || days <= 0 || num <= 0){
          layer.msg('请填写完整的生成参数', {icon: 7});
          return;
      }
      
      var loadingIndex = layer.load(1, {shade: [0.1,'#fff']});
      
      $.ajax({
          url: 'ajax.php?act=generate_codes',
          type: 'POST',
          contentType: 'application/json',
          dataType: 'json',
          data: JSON.stringify({ 
              user_level: parseInt(user_level),
              days: parseInt(days),
              num: parseInt(num)
          }),
          xhrFields: { withCredentials: true },
          success: function(res){
              layer.close(loadingIndex);
              if(res.code === 1){
                  layer.msg(res.msg, {icon: 1, time: 2000});
                  $('#generated-codes').show();
                  $('#codes-textarea').val(res.data.join('\n'));
              } else {
                  layer.msg(res.msg || '生成失败，请检查权限或参数', {icon: 5});
                  $('#generated-codes').hide();
                  $('#codes-textarea').val('');
              }
          },
          error: function(xhr){
              layer.close(loadingIndex);
              let errorMsg = '网络请求失败，请稍后再试';
              if(xhr.status === 403 || (xhr.responseJSON && xhr.responseJSON.code === -10)) {
                  errorMsg = '您没有权限执行此操作';
              }
              layer.msg(errorMsg, {icon: 2});
          }
      });
  });

});
</script>
</body>
</html> 