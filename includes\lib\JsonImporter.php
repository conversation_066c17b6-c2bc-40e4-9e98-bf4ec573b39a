<?php
namespace lib;
class JsonImporter
{
    private $pdoHelper;
    
    public function __construct(PdoHelper $pdoHelper)
    {
        $this->pdoHelper = $pdoHelper;
    }
    
    /**
     * 导入产品JSON数据
     * @param mixed $data JSON字符串、文件路径或已解析的数组
     * @param bool $isFilePath 是否为文件路径
     * @param int|null $localId 本地关联ID
     * @return array 导入结果
     */
    public function importProducts($data, $isFilePath = false, $localId = null)
    {
        // 如果已经是数组，直接使用
        if (is_array($data)) {
            $parsedData = $data;
        } 
        // 如果是文件路径
        elseif ($isFilePath) {
            if (!file_exists($data)) {
                return ['success' => false, 'message' => 'JSON文件不存在'];
            }
            $jsonData = file_get_contents($data);
            $parsedData = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'JSON解析错误: ' . json_last_error_msg()];
            }
        }
        // 否则认为是JSON字符串
        else {
            $parsedData = json_decode($data, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'JSON解析错误: ' . json_last_error_msg()];
            }
        }
        
        // 处理不同的API响应结构
        if (isset($parsedData['result'])) {
            $items = $parsedData['result']['items'] ?? $parsedData['result'];
        } else {
            $items = $parsedData['items'] ?? $parsedData;
        }
        if($items['error']){
            return ['success' => false, 'message' => '没有可导入的产品数据'];
        }
        
        if (empty($items)) {
            return ['success' => false, 'message' => '没有可导入的产品数据'];
        }
        
        // 如果items不是多维数组，转换为统一格式
        if (!isset($items[0]) && !empty($items)) {
            $items = [$items];
        }
        
        $results = [
            'total' => count($items),
            'success' => 0,
            'failed' => 0,
            'errors' => [],
            'local_id' => $localId
        ];
        
        // 开启事务
        $this->pdoHelper->beginTransaction();
        
        try {
            foreach ($items as $index => $item) {
                try {
                    $result = $this->importSingleProduct($item, $localId);
                    if ($result) {
                        $results['success']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][$index] = $this->pdoHelper->error();
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][$index] = $e->getMessage();
                }
            }
            
            // 提交事务
            $this->pdoHelper->commit();
            
            return ['success' => true, 'data' => $results];
        } catch (\Exception $e) {
            // 回滚事务
            $this->pdoHelper->rollBack();
            return ['success' => false, 'message' => '导入过程中发生错误: ' . $e->getMessage()];
        }
    }
    
    /**
     * 导入单个产品
     * @param array $item 产品数据
     * @param int|null $localId 本地关联ID
     * @return bool 是否成功
     */
    private function importSingleProduct($item, $localId = null)
    {
        // 准备基础数据
        $productData = [
            'sku' =>$item['stocks']['stocks'][0]['sku'],
            'product_id'=>$item['id'],
            'name' => $item['name'],
            'offer_id' => $item['offer_id'],
            'description_category_id' => $item['description_category_id'] ?? null,
            'type_id' => $item['type_id'] ?? null,
            'created_at' => $this->formatDateTime($item['created_at']),
            'updated_at' => isset($item['updated_at']) ? $this->formatDateTime($item['updated_at']) : null,
            'currency_code' => $item['currency_code'] ?? null,
            'price' => $item['price'] ?? null,
            'old_price' => $item['old_price'] ?? null,
            'min_price' => $item['min_price'] ?? null,
            'marketing_price' => $item['marketing_price'] ?? null,
            'volume_weight' => $item['volume_weight'] ?? null,
            'vat' => $item['vat'] ?? '0.00',
            'primary_image' => !empty($item['primary_image']) ? (is_array($item['primary_image']) ? $item['primary_image'][0] : $item['primary_image']) : null,
            
            // 布尔值字段
            'is_archived' => isset($item['is_archived']) ? ($item['is_archived'] ? 1 : 0) : 0,
            'is_autoarchived' => isset($item['is_autoarchived']) ? ($item['is_autoarchived'] ? 1 : 0) : 0,
            'is_prepayment_allowed' => isset($item['is_prepayment_allowed']) ? ($item['is_prepayment_allowed'] ? 1 : 0) : 0,
            'is_discounted' => isset($item['is_discounted']) ? ($item['is_discounted'] ? 1 : 0) : 0,
            'is_kgt' => isset($item['is_kgt']) ? ($item['is_kgt'] ? 1 : 0) : 0,
            'is_super' => isset($item['is_super']) ? ($item['is_super'] ? 1 : 0) : 0,
            'is_seasonal' => isset($item['is_seasonal']) ? ($item['is_seasonal'] ? 1 : 0) : 0,
            // JSON字段
            'images' => json_encode($item['images'] ?? []),
            'stocks' => json_encode($item['stocks'] ?? []),
            'commissions' => json_encode($item['commissions'] ?? []),
            'attributes' => json_encode($item['attributes'] ?? []),
            'price_indexes' => json_encode($item['price_indexes'] ?? []),
            'status_info' => json_encode($item['statuses'] ?? $item['status_info'] ?? []),
            'status_name' => $item['statuses']['status_name']??'',
            'status_description'=>$item['statuses']['status_description']??'',
            'errors'    => json_encode($item['errors'] ?? []),
            // 本地关联ID
            'storeid' => $localId['id']?$localId['id']:$localId['storeid'],
            'uid'     => $localId['uid']
        ];
        
        // 检查产品是否已存在
        $exists = $this->pdoHelper->find('products', 'offer_id', ['offer_id' => $item['offer_id']]);
        
        if ($exists) {
            // 更新现有产品
            return $this->pdoHelper->update('products', $productData, ['offer_id' => $item['offer_id']]) !== false;
        } else {
            // 插入新产品
            return $this->pdoHelper->insert('products', $productData) !== false;
        }
    }
    
    /**
     * 导入产品JSON数据
     * @param mixed $data JSON字符串、文件路径或已解析的数组
     * @param bool $isFilePath 是否为文件路径
     * @param int|null $localId 本地关联ID
     * @return array 导入结果
     */
    public function importorder($data, $isFilePath = false, $localId = null)
    {
        // 如果已经是数组，直接使用
        if(empty($localId)){
            return true;
        }
        if (is_array($data)) {
            $parsedData = $data;
        } 
        // 如果是文件路径
        elseif ($isFilePath) {
            if (!file_exists($data)) {
                return ['success' => false, 'message' => 'JSON文件不存在'];
            }
            $jsonData = file_get_contents($data);
            $parsedData = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'JSON解析错误: ' . json_last_error_msg()];
            }
        }
        // 否则认为是JSON字符串
        else {
            $parsedData = json_decode($data, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'JSON解析错误: ' . json_last_error_msg()];
            }
        }
        
        // 处理不同的API响应结构
        if (isset($parsedData['result'])) {
            $items = $parsedData['result']['items'] ?? $parsedData['result'];
        } else {
            $items = $parsedData['items'] ?? $parsedData;
        }
        
        if (empty($items)) {
            return ['success' => false, 'message' => '没有可导入的产品数据'];
        }
        
        $results = [
            'total' => count($items),
            'success' => 0,
            'failed' => 0,
            'errors' => [],
            'local_id' => $localId
        ];
        
        // 开启事务
        $this->pdoHelper->beginTransaction();
        
        try {
            $result = $this->importSingleorder($items, $localId);
            if ($result) {
                $results['success']++;
                    
            } else {
                $results['failed']++;
                $results['errors'][$index] = $this->pdoHelper->error();
            }
            // 提交事务
            $this->pdoHelper->commit();
            
            return ['success' => true, 'data' => $results];
        } catch (\Exception $e) {
            // 回滚事务
            $this->pdoHelper->rollBack();
            return ['success' => false, 'message' => '导入过程中发生错误: ' . $e->getMessage()];
        }
    }
    
    /**
     * 导入单个产品
     * @param array $item 产品数据
     * @param int|null $localId 本地关联ID
     * @return bool 是否成功
     */
    private function importSingleorder($item, $localId = null)
    {
        // 准备基础数据
        //exit(json_encode($item));
        $productData = [
            'order_id' =>$item['order_id'],
            'uid'      =>$localId['uid'],
            'storeid' => $localId['id']?$localId['id']:$localId['storeid'],
            'posting_number'=> $item['posting_number'],
            'name2'=>$item['products'][0]['name'],
            'sku'=>$item['products'][0]['sku'],
            'offer_id'=>$item['products'][0]['offer_id'],
            'products_num'=>$item['products']?count($item['products']):1,
            'price'=>$item['products'][0]['price'],
            'quantity'=>$item['products'][0]['quantity'],
            'currency_code'=>$item['products'][0]['currency_code'],
            'tracking_number'=>$item['tracking_number'],
            'warehouse'=>$item['delivery_method']['warehouse'],
            'kd_name'=>$item['delivery_method']['name'],
            'tpl_provider'=>$item['delivery_method']['tpl_provider'],
            'status'=>$item['status'],
            'delivering_date'=>$item['delivering_date']?$this->formatDateTime($item['delivering_date']):'NULL',
            'in_process_at'=>$this->formatDateTime($item['in_process_at']),
            'shipment_date'=>$this->formatDateTime($item['shipment_date']),
            'date' =>date('Y-m-d', strtotime($this->formatDateTime($item['in_process_at']))),
        ];
        if(isset($item['products'])){
            if(count($item['products'])>1){
                $quantity = 0;
                $price = 0;
                $productData['products'] = json_encode($item['products']);
                foreach ($item['products'] as $row){
                    $price = ($row['price']*$row['quantity'])+$price;
                    $quantity = $quantity+$row['quantity'];
                }
                $productData['price'] = round($price,2);
                $productData['quantity'] = $quantity;
            }
        }
        if($item['products'][0]['dimensions']['weight']){
            $productData['weight']=$item['products'][0]['dimensions']['weight'];
        }
        if($item['status']=='cancelled_from_split_pending'){
            if($productData['shipment_date']<date("Y-m-d H:i:s")){
                $productData = ['status'=>'cancelled'];
            }
        }
        if (isset($item['customer']) && (is_array($item['customer']) || is_object($item['customer']))) {
            $customerJson = json_encode($item['customer'], JSON_UNESCAPED_UNICODE);
            if ($customerJson === false) {
                // 如果 JSON 编码失败，记录错误或设为 NULL
                error_log("Failed to encode customer data: " . print_r($item['customer'], true));
                $customerJson = null;
            }
        } else {
            $customerJson = null; // 如果不是有效数据，设为 NULL
        }
        if(isset($item['financial_data']['products'])){
            if(count($item['financial_data']['products'])==1){
                $productData['commission_percent'] = $item['financial_data']['products'][0]['commission_percent'];
            }
        }
        
        $productData['customer'] = $customerJson;
        $productData['time'] = time();
        //exit(json_encode($productData));
        // 检查产品是否已存在
        $exists = $this->pdoHelper->find('order', 'posting_number', ['posting_number' => $item['posting_number']]);
        
        if ($exists) {
            // 更新现有产品
            return $this->pdoHelper->update('order', $productData, ['posting_number' => $item['posting_number']]) !== false;
        } else {
            // 插入新产品
            return $this->pdoHelper->insert('order', $productData) !== false;
        }
    }
    
        /**
     * 导入Ozon商品完整数据
     * @param mixed $data JSON数据
     * @param bool $isFilePath 是否为文件路径
     * @param array|null $localId 本地关联参数
     * @return array 导入结果
     */
    public function importFullOzonProducts($data, $isFilePath = false, $localId = null)
    {
        // 如果已经是数组，直接使用
        if (is_array($data)) {
            $parsedData = $data;
        } 
        // 如果是文件路径
        elseif ($isFilePath) {
            if (!file_exists($data)) {
                return ['success' => false, 'message' => 'JSON文件不存在'];
            }
            $jsonData = file_get_contents($data);
            $parsedData = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'JSON解析错误: ' . json_last_error_msg()];
            }
        }
        // 否则认为是JSON字符串
        else {
            $parsedData = json_decode($data, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'JSON解析错误: ' . json_last_error_msg()];
            }
        }
        
        // 处理不同的API响应结构
        if (isset($parsedData['result'])) {
            $items = $parsedData['result']['items'] ?? $parsedData['result'];
        } else {
            $items = $parsedData['items'] ?? $parsedData;
        }
        
        if (empty($items)) {
            return ['success' => false, 'message' => '没有可导入的产品数据'];
        }
        
        $results = [
            'total' => count($items),
            'success' => 0,
            'failed' => 0,
            'errors' => [],
            'local_id' => $localId
        ];
        
        // 开启事务
        $this->pdoHelper->beginTransaction();
        
        try {
            foreach ($items as $index => $item) {
                try {
                    $result = $this->processOzonProductItem($item, $localId);
                    if ($result) {
                        $results['success']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][$index] = $this->pdoHelper->error();
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][$index] = $e->getMessage();
                }
            }
            // 提交事务
            $this->pdoHelper->commit();
            
            return ['success' => true, 'data' => $results];
        } catch (\Exception $e) {
            // 回滚事务
            $this->pdoHelper->rollBack();
            return ['success' => false, 'message' => '导入过程中发生错误: ' . $e->getMessage()];
        }
    }
    
    /**
     * 处理单个商品数据
     */
    private function processOzonProductItem($item, $localParams)
    {
        $variantId = $item['variantId'] ?? null;
        if (!$variantId) {
            throw new \Exception('缺少variantId');
        }
        // 基础字段映射
        $baseData = [
            'uid'               => $localParams['uid'],
            'variant_id'        => $item['variantId'],
            'sku'               => $item['sku'],
            'name'              => $item['name'],
            'brand'             => $item['brand'],
            'category1'         => $item['category1'],
            'category3'         => $item['category3'],
            'price'             => $item['avgPrice'],
            'min_price'         => $item['minSellerPrice'],
            'stock'             => $item['stock'],
            'fbs_stock'         => $item['fbsStock'],
            'fbo_stock'         => $item['fboStock'],
            'volume'            => $item['volume'],
            'session_count'     => $this->parseNumber($item['sessionCount']),
            'conv_to_cart'      => $this->parsePercentage($item['convToCart']),
            'pdp_conv_rate'     => $this->parsePercentage($item['convToCartPdp']),
            'sales_dynamics'    => $this->parsePercentage($item['salesDynamics']),
            'drr'               => $this->parsePercentage($item['drr']),
            'gmv_sum'           => $item['gmvSum'],
            'sold_count'        => $item['soldCount'],
            'seller_id'         => $item['sellerId'],
            'sellerName'        => $item['sellerName'],
            'sales_schema'      => $item['salesSchema'],
            'promo_days'        => $item['daysInPromo'],
            'trafaret_days'     => $item['daysWithTrafarets'],
            'discount'          => $this->parsePercentage($item['discount']),
            'views'             => $this->parseNumber($item['views']),
            'local_index'       => $item['localIndex'],
            'avg_delivery_days' => $item['avgDeliveryDays'],
            'create_date'       => $this->formatDateTime($item['nullableCreateDate']),
            'update_date'       => date('Y-m-d'), // 使用当前时间作为更新时间
            'photo_url'         => $item['photo'],
            'product_url'       => $item['link']
        ];
        

        
        $jsonFields = [
            'sources'          => $item['sources'],
            'stock_info'       => [
                'fbs' => $item['fbsStock'],
                'fbo' => $item['fboStock'],
                'cb'  => $item['cbStock'],
                'retail' => $item['retailStock']
            ],
            'status_info'      => [
                'blocked_by_seller' => $item['blockedBySeller'],
                'blocked_as_yours' => $item['blockedAsYourGood']
            ],
            'analytics'        => [
                'session_count_search' => $item['sessionCountSearch'],
                'conv_search' => $item['convToCartSearch'],
                'redemption_rate' => $item['nullableRedemptionRate']
            ]
        ];
        
        foreach ($jsonFields as $field => $value) {
            $baseData[$field] = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        $exists = $this->pdoHelper->find('seller', 'variantId', ['variantId' => $item['variantId']]);
        
        if ($exists) {
            // 更新现有产品
            unset($baseData['variant_id']);
            return $this->pdoHelper->update('seller', $baseData, ['variantId' => $item['variantId']]) !== false;
        } else {
            // 插入新产品
            return $this->pdoHelper->insert('seller', $baseData) !== false;
        }
    }
    
     /**
     * 导入Ozon商品完整数据
     * @param mixed $data JSON数据
     * @param bool $isFilePath 是否为文件路径
     * @param array|null $localId 本地关联参数
     * @return array 导入结果
     */
    public function importFullOzonCnProducts($data,$data2,$data3)
    {
        // 如果已经是数组，直接使用
        if (is_array($data)) {
            $parsedData = $data;
        }
        // 否则认为是JSON字符串
        else {
            $parsedData = json_decode($data, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'message' => 'JSON解析错误: ' . json_last_error_msg()];
            }
        }
        
        // 处理不同的API响应结构
        if (isset($parsedData['result'])) {
            $items = $parsedData['result']['items'] ?? $parsedData['result'];
        } else {
            $items = $parsedData['items'] ?? $parsedData;
        }
        
        if (empty($items)) {
            return ['success' => false, 'message' => '没有可导入的产品数据'];
        }
        
        $results = [
            'total' => count($items),
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        // 开启事务
        $this->pdoHelper->beginTransaction();
        
        try {
            foreach ($items as $index => $item) {
                try {
                    $result = $this->processOzonCnProductItem($item, $data2, $data3);
                    if ($result) {
                        $results['success']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][$index] = $this->pdoHelper->error();
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][$index] = $e->getMessage();
                }
            }
            // 提交事务
            $this->pdoHelper->commit();
            
            return ['success' => true, 'data' => $results];
        } catch (\Exception $e) {
            // 回滚事务
            $this->pdoHelper->rollBack();
            return ['success' => false, 'message' => '导入过程中发生错误: ' . $e->getMessage()];
        }
    }
    
    /**
     * 处理单个商品数据
     */
    private function processOzonCnProductItem($item, $data2, $data3)
    {
        $variantId = $item['variantId'] ?? null;
        if (!$variantId) {
            throw new \Exception('缺少variantId');
        }
        // 基础字段映射
        $baseData = [
            'variant_id'=>$item['variantId'],
            'photo'=>$item['photo'],
            'name'=>$item['name'],
            'link'=>$item['link'],
            'brand'=>$item['brand'],
            'category1'=>$item['category1'],
            'category3'=>$item['category3'],
            'volume'=>$item['volume'],
            'sessionCount'=>$this->parsePercentage($item['sessionCount']),
            'convToCart'=>$this->parsePercentage($item['convToCart']),
            'avgPrice'=>$item['avgPrice'],
            'sku'=>$item['sku'],
            'convToCartPdp'=>$this->parsePercentage($item['convToCartPdp']),
            'sources'=>json_encode($item['sources']),
            'blockedBySeller'=>$item['blockedBySeller'],
            'blockedAsYourGood'=>$item['blockedAsYourGood'],
            'soldSum'=>$item['soldSum'],
            'soldCount'=>$item['soldCount'],
            'minSellerPrice'=>$item['minSellerPrice'],
            'salesDynamics'=>$this->parsePercentage($item['salesDynamics']),
            'stock'=>$item['stock'],
            'fbsStock'=>$item['fbsStock'],
            'fboStock'=>$item['fboStock'],
            'cbStock'=>$item['cbStock'],
            'retailStock'=>$item['retailStock'],
            'gmvSum'=>$item['gmvSum'],
            'sumItemsInStock'=>$item['sumItemsInStock'],
            'daysInStock'=>$item['daysInStock'],
            'avgGmv'=>$item['avgGmv'],
            'accessibility'=>$item['accessibility'],
            'avgGmvOnAccDays'=>$item['avgGmvOnAccDays'],
            'avgOrdersOnAccDays'=>$item['avgOrdersOnAccDays'],
            'sumMissedGmv'=>$item['sumMissedGmv'],
            'avgDeliveryDays'=>$item['avgDeliveryDays'],
            'qtyViewPdp'=>$item['qtyViewPdp'],
            'pdpToCartConversion'=>$item['pdpToCartConversion'],
            'skuName'=>$item['skuName'],
            'isSuper'=>$item['isSuper'],
            'sellerId'=>$item['sellerId'],
            'salesSchema'=>$item['salesSchema'],
            'sessionCountSearch'=>$item['sessionCountSearch'],
            'convToCartSearch'=>$item['convToCartSearch'],
            'drr'=>$this->parsePercentage($item['drr']),
            'nullableRedemptionRate'=>$item['nullableRedemptionRate'],
            'nullableCreateDate'=>$this->formatDateTime($item['nullableCreateDate']),
            'localIndex'=>$item['localIndex'],
            'daysInPromo'=>$item['daysInPromo'],
            'promoRevenueShare'=>$item['promoRevenueShare'],
            'daysWithTrafarets'=>$item['daysWithTrafarets'],
            'discount'=>$this->parsePercentage($item['discount']),
            'views'=>$this->parseNumber($item['views']),
            'convViewToOrder'=>$item['convViewToOrder'],
            'sellerName'=>$item['sellerName'],
            'article'=>$item['article'],
            'bin'=>$item['bin'],
            'avgDeliveryTime'=>$item['avgDeliveryTime'],
            'accessibilityByDays'=>$item['accessibilityByDays'],
            'update_date'       => date('Y-m-d'), // 使用当前时间作为更新时间
        ];
        if($data2['countryCode']){
            $baseData['countryCode']=$data2['countryCode'];
        }
        if($data2['sellerlength']){
            $baseData['sellerlength']=$data2['sellerlength'];
        }
        if($data3['categories']){
            $baseData['categories1'] = $data3['categories'][1]['id'];
            $baseData['categories2'] = $data3['categories'][2]['id'];
            $baseData['categories3'] = $data3['categories'][3]['id'];
        }
        $baseData['time'] = time();
        $exists = $this->pdoHelper->find('cnproducts', 'variant_id', ['variant_id' => $item['variantId']]);
        
        if ($exists) {
            // 更新现有产品
            unset($baseData['variant_id']);
            return $this->pdoHelper->update('cnproducts', $baseData, ['variant_id' => $item['variantId']]) !== false;
        } else {
            // 插入新产品
            return $this->pdoHelper->insert('cnproducts', $baseData) !== false;
        }
    }
    
    /**
     * 数字解析
     */
    private function parseNumber($value)
    {
        return is_numeric($value) ? $value + 0 : 0;
    }
    
    /**
     * 百分比解析
     */
    private function parsePercentage($value)
    {
        if (is_string($value) && strpos($value, '%') !== false) {
            return floatval(str_replace('%', '', $value));
        }
        return $this->parseNumber($value);
    }

    
    /**
     * 格式化日期时间
     * @param string $dateTime ISO 8601格式的日期时间
     * @return string MySQL格式的日期时间
     */
    private function formatDateTime($dateTime)
    {
        try {
            $date = new \DateTime($dateTime);// 转换为中国时区（可选）
            $date->setTimezone(new \DateTimeZone('Asia/Shanghai'));
            // 格式化为中式日期时间（去除前导零）
            $chineseDate = $date->format('Y-m-d H:i:s');
            return $chineseDate;
        } catch (\Exception $e) {
            return date('Y-m-d H:i:s');
        }
        
    }
}