<?php
include("../includes/common.php");

// 商品管理模式：从 ozon_products 表获取数据
$offer_id = $_GET['offer_id'] ?? '';
$storeid = intval($_GET['storeid']);

if (!$offer_id || !$storeid) {
    die('参数错误');
}

// 从本地数据库获取数据
$row = $DB->getRow("SELECT * FROM ozon_products WHERE offer_id=:offer_id AND storeid=:storeid limit 1", [':offer_id'=>$offer_id, ':storeid'=>$storeid]);
if (!$row) {
    die('商品不存在');
}

// 获取店铺信息
$store = $DB->getRow("SELECT * FROM ozon_store WHERE id=:id limit 1", [':id'=>$storeid]);
if (!$store) {
    die('店铺不存在');
}

$api_error_message = ''; // 用于在UI上显示API错误

// 恢复try-catch并修正cURL请求方式
try {
    $apiUrl = 'https://api-seller.ozon.ru/v4/product/info/attributes';
    $headers = [
        'Client-Id: ' . $store['ClientId'],
        'Api-Key: ' . $store['key'],
        'Content-Type: application/json'
    ];
    $payload = [
        'filter' => [
            'sku' => [intval($row['sku'])],
            'visibility' => 'ALL'
        ],
        'limit' => 1
    ];

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL            => $apiUrl,
        CURLOPT_CUSTOMREQUEST  => 'POST', // 修正为与项目中其他地方一致的写法
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER     => $headers,
        CURLOPT_POSTFIELDS     => json_encode($payload, JSON_UNESCAPED_UNICODE),
        CURLOPT_TIMEOUT        => 20,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false // 在某些环境下需要
    ]);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        throw new Exception("cURL Error: " . $curl_error);
    }

    $api_product_info = json_decode($response, true);
    
    if ($http_code !== 200 || !isset($api_product_info['result'][0])) {
        $api_error_message = "无法从Ozon获取最新商品属性。HTTP状态码: {$http_code}。响应: " . $response;
        error_log("Ozon API call failed for offer_id {$offer_id}. " . $api_error_message);
    } else {
        $api_product = $api_product_info['result'][0];
        
        // 使用API数据更新$row变量
        if (isset($api_product['attributes'])) {
            $row['attributes'] = json_encode($api_product['attributes']);
        }
        if (isset($api_product['description_category_id'])) {
            $row['description_category_id'] = $api_product['description_category_id'];
        }
        if (isset($api_product['type_id'])) {
            $row['type_id'] = $api_product['type_id'];
        }
        // 新增：直接从API响应中获取尺寸和重量
        if (isset($api_product['height'])) $row['height'] = $api_product['height'];
        if (isset($api_product['depth'])) $row['depth'] = $api_product['depth'];
        if (isset($api_product['width'])) $row['width'] = $api_product['width'];
        if (isset($api_product['weight'])) $row['weight'] = $api_product['weight'];
        
        // 更新本地数据库以保持数据同步
        $DB->update('ozon_products', [
            'attributes' => $row['attributes'],
            'description_category_id' => $row['description_category_id'],
            'type_id' => $row['type_id']
        ], ['offer_id' => $offer_id, 'storeid' => $storeid]);
    }
} catch (Exception $e) {
    $api_error_message = "请求Ozon API时发生异常: " . $e->getMessage();
    error_log("Failed to fetch product attributes from Ozon API for offer_id {$offer_id}: " . $e->getMessage());
}

// 转换字段格式，使其兼容现有的编辑页面
$row['title'] = $row['name'];
$row['mainImage'] = $row['primary_image'];
if ($row['images']) {
    $images = json_decode($row['images'], true);
    $row['images'] = is_array($images) ? implode(';', $images) : '';
}

// 获取分类信息 - 完全依赖本地数据
$Attributes = '[]';
if ($row['description_category_id'] && $row['type_id']) {
    $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
    $attr_result = $client->attribute(['description_category_id'=>$row['description_category_id'],'type_id'=>$row['type_id']]);
    $Attributes = json_encode($attr_result);
}

$categoryData = file_get_contents('./config/中文类目.json');
$row['price'] = $row['price'] ?: "";
$row['old_price'] = $row['old_price'] ?: "";

// Extract and format rich content for display
$rich_content_json_display = '';
if (!empty($row['attributes'])) {
    $attributes_array = json_decode($row['attributes'], true);
    if (is_array($attributes_array)) {
        foreach ($attributes_array as $attr) {
            if (isset($attr['id']) && $attr['id'] == 11254 && isset($attr['values'][0]['value'])) {
                $raw_json = $attr['values'][0]['value'];
                // Attempt to decode and re-encode with pretty print for better readability
                $decoded_json = json_decode($raw_json);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $rich_content_json_display = json_encode($decoded_json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                } else {
                    // If it's not valid JSON, just display the raw string
                    $rich_content_json_display = $raw_json;
                }
                break;
            }
        }
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商品编辑 - <?=$row['title']?></title>
    <link rel="stylesheet" href="../assets/component/layui/css/layui.css">
    <link href="../assets/css/select2.min.css" rel="stylesheet" />
    <script src="../assets/js/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/Sortable.min.js"></script>
    <script src="../assets/js/select2.min.js"></script>
    <script src="../assets/component/layui/layui.js"></script>
    <!-- 美图设计室图片编辑器SDK -->
    <script src="https://public.static.meitudata.com/xiuxiu-pc/image-editor-sdk/3.0.0/dist/index.min.js"></script>
    
    <style>
    /* 蓝色主题风格样式 */
    body {
        background: #f0f8ff;
        font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .optimized-form {
        padding-bottom: 100px;
        max-width: 1200px;
        margin: 0 auto;
        background: #fff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }
    .layui-form-pane .layui-form-label {
        background: #fafafa;
        color: #333;
        font-weight: 500;
        border: 1px solid #e8e8e8;
    }
    
    .form-section {
        margin-bottom: 24px;
        border: 1px solid #e8e8e8;
        background: #fff;
    }
    
    .form-section legend {
        background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
        color: #fff;
        font-size: 15px;
        font-weight: 500;
        padding: 12px 20px;
        margin: 0;
        border: none;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .layui-field-box {
        padding: 20px;
    }
    
    /* 图片预览区域现代商务风格 */
    .image-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        padding: 16px;
        background: #f8f9fa;
        border: 1px dashed #d0d7de;
        border-radius: 4px;
        min-height: 120px;
    }
    
    .image-item {
        position: relative;
        border: 1px solid #d0d7de;
        border-radius: 4px;
        padding: 4px;
        background: #fff;
        transition: all 0.2s ease;
    }
    
    .image-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-color: #0066cc;
    }
    
    .image-remove {
        position: absolute;
        right: 5px;
        top: 5px;
        cursor: pointer;
        color: #ff5722;
        background: rgba(255,255,255,0.8);
        border-radius: 50%;
        padding: 2px;
        z-index: 3;
    }
    
    /* 美图编辑按钮样式 */
    .image-edit-btn {
        position: absolute;
        right: 5px;
        bottom: 5px;
        cursor: pointer;
        color: #1890ff;
        background: rgba(255,255,255,0.9);
        border-radius: 4px;
        padding: 4px;
        transition: all 0.3s;
        z-index: 4;
    }
    
    .image-edit-btn:hover {
        background: #1890ff;
        color: #fff;
        transform: scale(1.1);
    }
    
    /* 商品属性布局 - 一行4个 */
    .attribute-group {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin: 20px 0;
    }
    
    @media (max-width: 1200px) {
        .attribute-group {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    @media (max-width: 768px) {
        .attribute-group {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 480px) {
        .attribute-group {
            grid-template-columns: 1fr;
        }
    }
    
    .attribute-input-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
        border: 1px solid #bfdbfe;
        border-radius: 4px;
        background: #fff;
        overflow: hidden;
    }
    
    .attribute-label {
        font-size: 12px;
        color: #1e40af;
        font-weight: 600;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        padding: 4px 8px;
        border-bottom: 1px solid #93c5fd;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 12px;
        line-height: 1.2;
    }
    
    .attribute-label > span:first-child {
        font-weight: 600;
        color: #1e3a8a;
    }
    
    .attribute-input-group .layui-input,
    .attribute-input-group select {
        border: none;
        border-radius: 0 0 4px 4px;
        margin: 0;
        padding: 6px 8px;
        background: #fff;
        font-size: 13px;
        height: 20px;
        line-height: 14px;
    }
    
    .attribute-input-group .layui-input:focus,
    .attribute-input-group select:focus {
        outline: none;
        box-shadow: inset 0 0 0 2px #2563eb;
    }
    
    /* 类目选择器蓝色主题风格 */
    .category-selector {
        display: flex;
        gap: 16px;
        align-items: center;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        padding: 16px;
        border: 1px solid #bfdbfe;
        margin-bottom: 16px;
        border-radius: 6px;
    }
    
    .category-selector .layui-input-inline {
        flex: 1;
        min-width: 180px;
    }
    
    .category-selector select {
        height: 36px;
        border: 1px solid #93c5fd;
        background: #fff;
        color: #1e40af;
    }
    
    /* 底部操作栏商务风格 */
    .form-actions {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        padding: 16px 30px;
        box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
        z-index: 999;
        text-align: center;
        border-top: 1px solid #e8e8e8;
        margin-top: 0 !important;
    }
    
    .form-actions button {
        margin: 0 6px;
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.2s ease;
        height: 36px;
        padding: 0 16px;
        font-size: 14px;
    }
    
    .form-actions .layui-btn-normal {
        background: #2563eb;
        border-color: #2563eb;
    }
    
    .form-actions .layui-btn-normal:hover {
        background: #1d4ed8;
        border-color: #1d4ed8;
    }
    
    /* 页面头部样式 */
    .page-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: #fff;
        padding: 24px 32px;
        border-bottom: 3px solid #2563eb;
    }
    
    .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .page-title i {
        font-size: 26px;
        opacity: 0.9;
    }
    
    .page-subtitle {
        font-size: 14px;
        margin: 0;
        opacity: 0.8;
        color: #dbeafe;
    }
    
    /* Select2商务风格 */
    .select2-container--default .select2-selection--single,
    .select2-container--default .select2-selection--multiple {
        border: 1px solid #93c5fd;
        border-radius: 4px;
        min-height: 36px;
        background: #fff;
    }
    
    .select2-container {
        width: 100% !important;
    }
    
    /* 属性提示样式 */
    .attribute-tip {
        color: #666;
        margin-left: 6px;
        cursor: help;
        position: relative;
        display: inline-block;
    }
    
    .attribute-tip .layui-icon {
        font-size: 14px;
        transition: color 0.2s ease;
    }
    
    .attribute-tip:hover .layui-icon {
        color: #2563eb;
    }
    
    .custom-tooltip {
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 240px;
        max-width: 320px;
        background: #fff;
        border: 1px solid #d0d7de;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        padding: 12px;
        font-size: 12px;
        line-height: 1.5;
        color: #586069;
        border-radius: 4px;
        z-index: 999;
        display: none;
        word-wrap: break-word;
    }
    
    .attribute-tip:hover .custom-tooltip {
        display: block;
    }
    </style>
</head>
<body>
<div class="optimized-form">
    <?php if(!empty($api_error_message)): ?>
    <div class="layui-alert layui-alert-error" style="margin: 15px;">
        <strong>API错误:</strong> <?= htmlspecialchars($api_error_message, ENT_QUOTES, 'UTF-8') ?>
    </div>
    <?php endif; ?>
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="layui-icon layui-icon-edit"></i>
            商品信息编辑
        </h1>
        <p class="page-subtitle">SKU: <?=$row['sku']?> | 店铺: <?=$store['storename']?> | 货币: <?=$store['currency_code'] ?: 'RUB'?></p>
    </div>
    
    <form class="layui-form layui-form-pane">
        <input type="hidden" name="offer_id" value="<?=$offer_id?>">
        <input type="hidden" name="storeid" value="<?=$storeid?>">
        
        <!-- 基本信息 -->
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-form"></i> 基本信息</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <label class="layui-form-label">商品名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" lay-verify="required" 
                               placeholder="请输入商品名称" class="layui-input" value="<?=htmlspecialchars($row['title'])?>">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">商品描述</label>
                    <div class="layui-input-block">
                        <textarea name="description" class="layui-textarea" rows="3" 
                                  placeholder="请输入商品描述"><?=htmlspecialchars($row['description'] ?: '')?></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md4">
                            <label class="layui-form-label">销售价格</label>
                            <div class="layui-input-block">
                                <input type="number" name="price" lay-verify="required|number" 
                                       class="layui-input" placeholder="0.00" value="<?=$row['price']?>">
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">商品原价</label>
                            <div class="layui-input-block">
                                <input type="number" name="old_price" lay-verify="number" 
                                       class="layui-input" placeholder="折扣前的价格" value="<?=$row['old_price']?>">
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">商品货号</label>
                            <div class="layui-input-block">
                                <input type="text" name="offer_id_display" 
                                       class="layui-input layui-bg-gray" value="<?=$offer_id?>" readonly>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md3">
                            <label class="layui-form-label">包装长度</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="depth" 
                                           class="layui-input" value="<?=$row['depth'] ?? ''?>">
                                    <span class="layui-input-suffix">毫米</span>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">包装宽度</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="width" 
                                           class="layui-input" value="<?=$row['width'] ?? ''?>">
                                    <span class="layui-input-suffix">毫米</span>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">包装高度</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="height" 
                                           class="layui-input" value="<?=$row['height'] ?? ''?>">
                                    <span class="layui-input-suffix">毫米</span>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">含包装重量</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="weight" 
                                           class="layui-input" value="<?=$row['weight'] ?? ''?>">
                                    <span class="layui-input-suffix">克</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- 类目属性 -->
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-app"></i> 类目属性</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <label class="layui-form-label">商品类目</label>
                    <div class="layui-input-block category-selector">
                        <div class="layui-input-inline">
                            <select name="category1" lay-filter="category">
                                <option value="">一级类目</option>
                            </select>
                        </div>
                        <i class="layui-icon layui-icon-right"></i>
                        <div class="layui-input-inline">
                            <select name="category2" lay-filter="category">
                                <option value="">二级类目</option>
                            </select>
                        </div>
                        <i class="layui-icon layui-icon-right"></i>
                        <div class="layui-input-inline">
                            <select name="category3" lay-filter="category">
                                <option value="">三级类目</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">商品属性</label>
                    <div class="layui-input-block">
                        <div id="specBox" class="attribute-group"></div>
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- 多媒体信息 -->
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-picture"></i> 多媒体信息</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <input type="hidden" name="images" value="<?= $row['images'] ?? '' ?>">
                    <label class="layui-form-label">产品图片</label>
                    <div class="layui-input-block">
                        <div class="layui-upload">
                            <button type="button" class="layui-btn" id="uploadImg">
                                <i class="layui-icon layui-icon-upload"></i>上传图片
                            </button>
                            <button type="button" class="layui-btn layui-btn-normal" id="uploadImgUrl" style="margin-left: 10px;">
                                <i class="layui-icon layui-icon-link"></i>上传图片链接
                            </button>
                            <div class="layui-upload-list image-preview" id="previewBox">
                                <?php if($row['images']): ?>
                                    <?php foreach(explode(';', $row['images']) as $index => $img): ?>
                                        <?php if(trim($img)): ?>
                                        <div class="image-item" data-url="<?= trim($img) ?>">
                                            <i class="layui-icon image-remove">&#x1006;</i>
                                            <div class="image-edit-btn" onclick="openMTImageEditor('<?= trim($img) ?>', '<?= $index ?>')">
                                                <i class="layui-icon layui-icon-picture"></i>
                                            </div>
                                            <img class="images" src="<?= trim($img) ?>" 
                                                 style="width:120px;height:120px;object-fit:contain;">
                                        </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- 内容丰富 (Rich Content) -->
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-template"></i> 内容丰富 (Rich Content)</legend>
            <div class="layui-field-box">
                <textarea name="rich_content_html" class="layui-textarea" rows="15" 
                          placeholder="请输入JSON格式的Rich Content"><?= htmlspecialchars($rich_content_json_display, ENT_QUOTES, 'UTF-8') ?></textarea>
            </div>
        </fieldset>

        <!-- 表单操作 -->
        <div class="layui-form-item form-actions">
            <button class="layui-btn layui-btn-lg layui-btn-normal" lay-submit lay-filter="submitSave">
                <i class="layui-icon layui-icon-ok"></i>保存商品
            </button>
            <button type="button" class="layui-btn layui-btn-lg layui-btn-primary" onclick="parent.layer.closeAll()">
                <i class="layui-icon layui-icon-close"></i>关闭
            </button>
        </div>
    </form>
</div>

<script>
// 优化后的JS代码
layui.use(['form', 'upload', 'layer'], function(){
    var form = layui.form,
        upload = layui.upload,
        layer = layui.layer;
    
    var previewBox = document.getElementById('previewBox');
    var sortable = new Sortable(previewBox, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        handle: '.images',
        onUpdate: function(evt) {
            updateImageFormData();
        }
    });
    
    // 类目数据
    var categoryData = <?=$categoryData?>;
    var currentCategory = {1: null, 2: null, 3: null};
    
    // 查找类目节点
    function findCategoryNode(categoryId) {
        function find(nodes) {
            for(const key in nodes){
                const node = nodes[key];
                if(node.descriptionCategoryId == categoryId) return node;
                if(node.nodes && Object.keys(node.nodes).length > 0){
                    const found = find(node.nodes);
                    if(found) return found;
                }
            }
            return null;
        }
        return find(categoryData.result);
    }

    // 加载子类目方法
    function loadSubCategories(level, parentId) {
        const $nextSelect = $(`select[name="category${level}"]`);
        $nextSelect.empty().append(`<option value="">请选择</option>`);
        
        if(parentId) {
            const parentNode = findCategoryNode(parentId);
            if(parentNode && parentNode.nodes) {
                Object.values(parentNode.nodes).forEach(item => {
                    if(!item.disabled && level<3) {
                        $nextSelect.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
                    }else{
                        $nextSelect.append(new Option(item.descriptionTypeName, item.descriptionTypeId));
                    }
                });
            }
        }
        
        layui.form.render('select');
    }
    
    // 初始化一级类目
    function initFirstCategory() {
        var $select = $('select[name="category1"]');
        $select.empty().append('<option value="">一级类目</option>');
        
        Object.values(categoryData.result).forEach(item => {
            if(!item.disabled) {
                $select.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
            }
        });
        
        layui.form.render('select');
    }
    
    // 新增：回显类目
    function echoCategories(level2Id, level3Id) {
        let level1Id = null;

        // 1. 查找 level 1 ID
        for (const key in categoryData.result) {
            const l1Node = categoryData.result[key];
            if (l1Node.nodes) {
                for (const subKey in l1Node.nodes) {
                    const l2Node = l1Node.nodes[subKey];
                    if (l2Node.descriptionCategoryId == level2Id) {
                        level1Id = l1Node.descriptionCategoryId;
                        break;
                    }
                }
            }
            if (level1Id) break;
        }
        
        if (level1Id) {
            // 2. 设置一级并加载二级
            $('select[name="category1"]').val(level1Id);
            loadSubCategories(2, level1Id);
            
            // 3. 设置二级并加载三级
            $('select[name="category2"]').val(level2Id);
            loadSubCategories(3, level2Id);
            
            // 4. 设置三级
            $('select[name="category3"]').val(level3Id);

            // 5. 统一渲染
            form.render('select');
        } else {
            console.error('无法找到父类目，回显失败');
        }
    }
    
    // 初始化类目
    initFirstCategory();
    
    // 如果有已选类目，回显
    <?php if($row['description_category_id'] && $row['type_id']): ?>
    const savedCategory2 = <?=$row['description_category_id']?>;
    const savedCategory3 = <?=$row['type_id']?>;
    const savedProductAttributes = <?= !empty($row['attributes']) ? $row['attributes'] : '[]' ?>;

    // 调用回显函数
    echoCategories(savedCategory2, savedCategory3);
    
    // 渲染属性
    const Attributes = <?=$Attributes?>;
    renderAttributes(Attributes, savedProductAttributes);
    <?php endif; ?>
    
    // 类目选择事件
    form.on('select(category)', function(data){
        const level = parseInt(data.elem.name.replace('category',''));
        const value = data.value;
        
        currentCategory[level] = value;
        
        // 清空后续选择
        for(let l = level + 1; l <= 3; l++) {
            currentCategory[l] = null;
            $(`select[name="category${l}"]`).val('').trigger('change');
        }
        
        if(level < 3 && value) {
            loadSubCategories(level + 1, value);
        }
        
        if (level === 3 && value) {
            // 获取类目属性
            var category2 = $(`select[name="category2"]`).val();
            layer.load(2);
            $.ajax({
                url: './ajax.php?act=get_category_attributes',
                type: 'POST',
                data: { category2, category3: value },
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if(res.code === 0) {
                        // 注意：这里传入空的 savedAttributes，因为这是新选择的类目，还没有保存值
                        renderAttributes(res.data, []); 
                    } else {
                        layer.msg(res.msg || '获取属性失败');
                    }
                }
            });
        }
    });
    
    // 渲染属性
    function renderAttributes(attributes, savedAttributes = []) {
        const container = $('#specBox');
        container.empty();
        
        const savedValues = savedAttributes.reduce((acc, curr) => {
            if (curr.id && curr.values && curr.values.length > 0) {
                const values = curr.values.map(v => v.dictionary_value_id || v.value);
                acc[curr.id] = values.length > 1 ? values : values[0];
            } else if (curr.attribute_id && curr.value) { // 兼容旧格式
                 acc[curr.attribute_id] = curr.value;
            }
            return acc;
        }, {});

        // 如果API返回的属性定义为空，但我们有已保存的属性，则根据已保存的属性来渲染
        if (attributes.length === 0 && savedAttributes.length > 0) {
            attributes = savedAttributes.map(attr => {
                return {
                    id: attr.id,
                    name: `属性ID: ${attr.id}`, // 由于没有API数据，我们只能显示ID
                    description: '属性名称和描述未知，因为数据来自本地缓存。',
                    dictionary_id: (attr.values && attr.values[0] && attr.values[0].dictionary_value_id) ? 1 : 0, // 猜测是否是字典
                    is_collection: (attr.values && attr.values.length > 1) ? true : false,
                };
            });
        }

        attributes.forEach(attr => {
            if(attr.id != 4559 && attr.id != 21531 && attr.id != 4497 && attr.id != 11254 && attr.id != 8229 && attr.id != 9048){
                const savedValue = savedValues[attr.id];

                if (attr.dictionary_id > 0) {
                    // 下拉选择属性
                    attributes_select(attr.id, function(options) {
                        const selectHtml = `
                            <div class="attribute-input-group">
                                <div class="attribute-label">
                                    <span>${attr.name}</span>
                                    ${attr.description ? `
                                        <span class="attribute-tip">
                                            <i class="layui-icon layui-icon-tips"></i>
                                            <div class="custom-tooltip">${attr.description}</div>
                                        </span>` : ''}
                                </div>
                                <select name="attributes[${attr.id}]" lay-ignore ${attr.is_collection ?  'multiple' : ''}>
                                    <option value="">-- 请选择 --</option>
                                    ${options.map(opt => `<option value="${opt.id}">${opt.name}</option>`).join('')}
                                </select>
                            </div>
                        `;
                        container.append(selectHtml);

                        const $select = $(`select[name="attributes[${attr.id}]"]`);
                        
                        if (savedValue) {
                           $select.val(savedValue);
                        }

                        $select.select2({
                            multiple: attr.is_collection,
                            placeholder: `请选择${attr.name}`,
                            width: '100%'
                        });
                        
                        if (savedValue) {
                           $select.trigger('change');
                        }
                    });
                } else {
                   // 普通输入框
                    const inputHtml = `
                        <div class="attribute-input-group">
                            <div class="attribute-label">
                                <span>${attr.name}</span>
                                ${attr.description ? `
                                    <span class="attribute-tip">
                                        <i class="layui-icon layui-icon-tips"></i>
                                        <div class="custom-tooltip">${attr.description}</div>
                                    </span>` : ''}
                            </div>
                            <input type="text" name="attributes[${attr.id}]" 
                                 placeholder="请输入${attr.name}" class="layui-input" value="${savedValue || ''}">
                        </div>
                    `;
                    container.append(inputHtml);
                }
            }
        });
    }
    
    // 图片上传
    layui.upload.render({
        elem: '#uploadImg',
        url: './ajax.php?act=upload_image',
        done: function(res) {
            if(res.code === 0) {
                const imageIndex = $('#previewBox .image-item').length;
                const $item = $(`
                    <div class="image-item" data-url="${res.data.src}">
                        <i class="layui-icon image-remove">&#x1006;</i>
                        <div class="image-edit-btn" onclick="openMTImageEditor('${res.data.src}', '${imageIndex}')">
                            <i class="layui-icon layui-icon-picture"></i>
                        </div>
                        <img class="images" src="${res.data.src}" 
                             style="width:120px;height:120px;object-fit:contain;">
                    </div>
                `);
                $('#previewBox').append($item);
                updateImageFormData();
            }
        }
    });
    
    // 添加图片链接上传功能
    $('#uploadImgUrl').click(function() {
        layer.prompt({
            title: '请输入图片链接（支持多个链接，每行一个）',
            formType: 2,
            value: '',
            area: ['600px', '200px'],
            btn: ['确定', '取消']
        }, function(value, index, elem) {
            if (!value || !value.trim()) {
                layer.msg('请输入有效的图片链接', {icon: 2});
                return;
            }
            
            const imageUrls = value.trim().split('\n').map(url => url.trim()).filter(url => url);
            
            if (imageUrls.length === 0) {
                layer.msg('请输入有效的图片链接', {icon: 2});
                return;
            }
            
            layer.close(index);
            processBatchImageUrls(imageUrls);
        });
    });
    
    // 批量处理图片链接
    function processBatchImageUrls(imageUrls) {
        let successCount = 0;
        let failCount = 0;
        let processedCount = 0;
        const totalCount = imageUrls.length;
        
        layer.load(2);
        
        imageUrls.forEach((imageUrl, index) => {
            if (!isValidImageUrl(imageUrl)) {
                processedCount++;
                failCount++;
                checkComplete();
                return;
            }
            
            const img = new Image();
            img.onload = function() {
                const imageIndex = $('#previewBox .image-item').length;
                const $item = $(`
                    <div class="image-item" data-url="${imageUrl}">
                        <i class="layui-icon image-remove">&#x1006;</i>
                        <div class="image-edit-btn" onclick="openMTImageEditor('${imageUrl}', '${imageIndex}')">
                            <i class="layui-icon layui-icon-picture"></i>
                        </div>
                        <img class="images" src="${imageUrl}" 
                             style="width:120px;height:120px;object-fit:contain;">
                    </div>
                `);
                
                $('#previewBox').append($item);
                successCount++;
                processedCount++;
                checkComplete();
            };
            
            img.onerror = function() {
                failCount++;
                processedCount++;
                checkComplete();
            };
            
            img.src = imageUrl;
        });
        
        function checkComplete() {
            if (processedCount === totalCount) {
                layer.closeAll('loading');
                updateImageFormData();
                
                if (successCount > 0 && failCount === 0) {
                    layer.msg(`成功添加 ${successCount} 张图片`, {icon: 1});
                } else if (successCount > 0 && failCount > 0) {
                    layer.msg(`成功添加 ${successCount} 张图片，${failCount} 张图片添加失败`, {icon: 3});
                } else {
                    layer.msg('所有图片链接都无法访问，请检查链接是否正确', {icon: 2});
                }
            }
        }
    }
    
    // 验证图片URL格式
    function isValidImageUrl(url) {
        try {
            const urlObj = new URL(url);
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                return false;
            }
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
            const pathname = urlObj.pathname.toLowerCase();
            return imageExtensions.some(ext => pathname.endsWith(ext)) || 
                   pathname.includes('/') || 
                   url.includes('image') || 
                   url.includes('img');
        } catch (e) {
            return false;
        }
    }
    
    // 删除图片
    $('#previewBox').on('click', '.image-remove', function() {
        $(this).closest('.image-item').remove();
        updateImageFormData();
    });
    
    // 更新表单中的图片数据
    function updateImageFormData() {
        var images = [];
        $('#previewBox .images').each(function() {
            images.push($(this).attr('src'));
        });
        $('input[name="images"]').val(images.join(';'));
    }
    
    // 图片放大查看
    $('#previewBox').on('dblclick', '.images', function(e){
        var src = $(this).attr('src');
        layer.photos({
            photos: {
                data: [{src: src}]
            },
            anim: 5
        });
        e.stopPropagation();
        return false;
    });
    
    // 获取属性值
    function attributes_select(attribute_id, callback){
        var category2 = <?=$row['description_category_id'] ?? 'null'?>;
        var category3 = <?=$row['type_id'] ?? 'null'?>;
        
        if (!category2 || !category3) {
            category2 = $(`select[name="category2"]`).val();
            category3 = $(`select[name="category3"]`).val();
        }
        
        $.ajax({
            url: './ajax.php?act=get_sku_attributevalues',
            type: 'POST',
            dataType: 'json',
            data: { 
                attribute_id: attribute_id,
                category2: category2,
                category3: category3 
            },
            success: function(res) {
                if (res.code === 0) {
                    callback(res.data);
                } else {
                    layer.msg(res.msg || '获取属性失败');
                    callback([]);
                }
            },
            error: function() {
                layer.msg('请求失败');
                callback([]);
            }
        });
    }
    
    // 表单提交
    form.on('submit(submitSave)', function(data){
        layer.load(2);
        
        $.ajax({
            url: './ajax.php?act=save_product_management',
            type: 'POST',
            dataType: 'json',
            data: data.field,
            success: function(res) {
                layer.closeAll('loading');
                if(res.code === 0) {
                    // 从返回消息中提取任务ID
                    var taskIdMatch = res.msg.match(/任务ID：(\d+)/);
                    if (taskIdMatch && taskIdMatch[1]) {
                        var taskId = taskIdMatch[1];
                        layer.msg('保存成功，正在检查同步状态...', {icon: 1, time: 2000});
                        
                        // 延迟2秒后开始检查任务状态
                        setTimeout(function() {
                            checkTaskStatus(taskId, data.field.storeid);
                        }, 2000);
                        
                        // 通知父页面刷新表格
                        if (parent.layui && parent.layui.table) {
                            parent.layui.table.reload('productTable');
                        }
                    } else {
                        layer.msg('保存成功', {icon: 1});
                        // 通知父页面刷新表格
                        if (parent.layui && parent.layui.table) {
                            parent.layui.table.reload('productTable');
                        }
                    }
                } else {
                    layer.msg(res.msg || '保存失败', {icon: 2});
                }
            },
            error: function(xhr) {
                layer.closeAll('loading');
                layer.msg('请求失败: ' + xhr.statusText, {icon: 2});
            }
        });
        return false;
    });
    
    // 检查任务状态的函数
    function checkTaskStatus(taskId, storeid, retryCount = 0) {
        $.ajax({
            url: './ajax.php?act=check_task_status',
            type: 'POST',
            dataType: 'json',
            data: {
                task_id: taskId,
                storeid: storeid
            },
            success: function(res) {
                if (res.code === 0) {
                    // 任务完成
                    layer.msg('Ozon平台同步完成！', {icon: 1, time: 3000});
                } else if (res.code === 1) {
                    // 任务还在处理中，继续检查（最多重试10次）
                    if (retryCount < 10) {
                        setTimeout(function() {
                            checkTaskStatus(taskId, storeid, retryCount + 1);
                        }, 3000); // 每3秒检查一次
                    } else {
                        layer.msg('任务处理时间较长，请稍后手动检查Ozon平台', {icon: 3, time: 5000});
                    }
                } else {
                    // 任务失败
                    layer.msg('Ozon平台同步失败：' + res.msg, {icon: 2, time: 5000});
                }
            },
            error: function() {
                if (retryCount < 3) {
                    // 网络错误，重试3次
                    setTimeout(function() {
                        checkTaskStatus(taskId, storeid, retryCount + 1);
                    }, 5000);
                } else {
                    layer.msg('无法检查同步状态，请稍后手动确认', {icon: 3, time: 5000});
                }
            }
        });
    }
    
    // 美图设计室SDK初始化
    function initMTImageEditor() {
        MTImageEditor.init({
            moduleName: 'image-editor-sdk',
            accessKey: 'cQwdx9FXKfEcwgu7txxxJkPrSXvrLMGk',
            title: '美图设计室Web版',
            el: '',
            fullscreen: true,
            resizeAble: true
        });
        
        MTImageEditor.saveImage((base64, type, id) => {
            layer.load(2);
            $.ajax({
                url: './ajax.php?act=save_edited_image',
                type: 'POST',
                data: {
                    image_data: base64,
                    image_type: type,
                    record_id: id,
                    product_id: '<?=$offer_id?>',
                    original_index: window.currentEditingImageIndex
                },
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        if (window.currentEditingImageIndex !== null) {
                            const $imageItem = $(`#previewBox .image-item:eq(${window.currentEditingImageIndex})`);
                            $imageItem.find('img.images').attr('src', res.data.new_image_url);
                            $imageItem.attr('data-url', res.data.new_image_url);
                            updateImageFormData();
                        }
                        
                        layer.msg('图片编辑完成！', {icon: 1});
                        MTImageEditor.close();
                    } else {
                        layer.msg(res.msg || '保存失败', {icon: 2});
                    }
                },
                error: function(xhr) {
                    layer.closeAll('loading');
                    layer.msg('保存失败: ' + xhr.statusText, {icon: 2});
                }
            });
        });
        
        MTImageEditor.onClose(() => {
            window.currentEditingImageIndex = null;
        });
    }
    
    // 页面加载完成后初始化
    $(document).ready(function() {
        initMTImageEditor();
    });
});

// 打开美图编辑器 - 全局函数
function openMTImageEditor(imageUrl, imageIndex) {
    if (!imageUrl) {
        layer.msg('图片地址无效', {icon: 2});
        return;
    }
    
    window.currentEditingImageIndex = imageIndex;
    MTImageEditor.openImage(imageUrl);
}
</script>
</body>
</html> 