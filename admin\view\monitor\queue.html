<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>任务队列监控</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
    <style>
        .queue-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .queue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .queue-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .queue-status {
            display: flex;
            gap: 15px;
        }
        
        .status-item {
            text-align: center;
        }
        
        .status-value {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>任务队列监控</span>
            <div style="float: right;">
                <button class="layui-btn layui-btn-sm" id="refresh-btn">
                    <i class="layui-icon layui-icon-refresh"></i> 刷新
                </button>
                <span id="last-update" style="margin-left: 10px; color: #666; font-size: 12px;"></span>
            </div>
        </div>
        <div class="layui-card-body">
            <div id="queue-container">
                <div class="layui-text" style="text-align: center; padding: 40px; color: #999;">
                    正在加载队列信息...
                </div>
            </div>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <script>
        layui.use(['jquery'], function() {
            var $ = layui.jquery;
            
            function updateTime() {
                var now = new Date();
                var timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0') + ':' + 
                             now.getSeconds().toString().padStart(2, '0');
                $('#last-update').text('最后更新: ' + timeStr);
            }
            
            function loadQueueStatus() {
                $.ajax({
                    url: '../../ajax.php?act=queue_status',
                    type: 'GET',
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            renderQueues(res.data);
                            updateTime();
                        } else {
                            $('#queue-container').html(`
                                <div class="layui-text" style="text-align: center; padding: 40px; color: #f56c6c;">
                                    获取队列状态失败: ${res.msg || '未知错误'}
                                </div>
                            `);
                        }
                    },
                    error: function() {
                        $('#queue-container').html(`
                            <div class="layui-text" style="text-align: center; padding: 40px; color: #f56c6c;">
                                网络异常，请检查连接
                            </div>
                        `);
                    }
                });
            }
            
            function renderQueues(queues) {
                if (!queues || queues.length === 0) {
                    $('#queue-container').html(`
                        <div class="layui-text" style="text-align: center; padding: 40px; color: #909399;">
                            暂无队列信息
                        </div>
                    `);
                    return;
                }
                
                var html = '';
                queues.forEach(function(queue) {
                    var statusColor = queue.status === 'running' ? '#67c23a' : '#f56c6c';
                    var messagesColor = queue.messages > 10 ? '#e6a23c' : '#409eff';
                    
                    html += `
                        <div class="queue-card">
                            <div class="queue-header">
                                <div class="queue-name">${queue.name}</div>
                                <div style="color: ${statusColor};">
                                    <i class="layui-icon ${queue.status === 'running' ? 'layui-icon-ok-circle' : 'layui-icon-close-circle'}"></i>
                                    ${queue.status === 'running' ? '运行中' : '已停止'}
                                </div>
                            </div>
                            <div class="queue-status">
                                <div class="status-item">
                                    <div class="status-value" style="color: ${messagesColor};">${queue.messages}</div>
                                    <div class="status-label">等待消息</div>
                                </div>
                                <div class="status-item">
                                    <div class="status-value" style="color: #409eff;">${queue.consumers}</div>
                                    <div class="status-label">消费者数量</div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                $('#queue-container').html(html);
            }
            
            // 初始化
            loadQueueStatus();
            
            // 刷新按钮
            $('#refresh-btn').on('click', function() {
                loadQueueStatus();
            });
            
            // 自动刷新 - 每30秒
            setInterval(loadQueueStatus, 30000);
        });
    </script>
</body>
</html> 