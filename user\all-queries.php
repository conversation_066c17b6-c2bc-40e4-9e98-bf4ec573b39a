<?php
include("../includes/common.php");
$categoryData = file_get_contents('./config/中文类目.json');
?>
<style>
    .category-selector {
        display: flex;
        gap: 15px;
        align-items: center;
    }
    .category-selector .layui-input-inline {
        flex: 1;
    }
    
    /* SKU反查区域样式 */
    .form-section .layui-inline {
        margin-bottom: 10px;
    }
    
    .form-section .layui-form-label {
        width: 100px;
        text-align: right;
        padding-right: 10px;
    }
    
    .form-section .layui-input-inline {
        width: 200px;
    }
    
    #get_sku_keywords {
        margin-left: 10px;
        background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
        border: none;
        border-radius: 4px;
        transition: all 0.3s ease;
    }
    
    #get_sku_keywords:hover {
        background: linear-gradient(135deg, #138d75 0%, #17a2b8 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    input[name="sku_url"] {
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: border-color 0.3s ease;
    }
    
    input[name="sku_url"]:focus {
        border-color: #1abc9c;
        box-shadow: 0 0 5px rgba(26, 188, 156, 0.3);
    }
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <form class="layui-form layui-form-pane"  lay-filter="queriesForm">
                <fieldset class="layui-elem-field form-section">
                    <legend><i class="layui-icon layui-icon-chart-screen" style="color:#1E9FFF"></i> 热销关键词看板</legend>
                    <!-- 关键词搜索 -->
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-inline">
                            <input type="text" name="text" class="layui-input">
                        </div>
                    </div>
                    
                    <!-- SKU关键词反查 -->
                    <div class="layui-inline">
                        <label class="layui-form-label">SKU反查</label>
                        <div class="layui-input-inline">
                            <input type="text" name="sku_url" placeholder="输入SKU链接" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-inline">
                        <button type="button" class="layui-btn layui-btn-normal" id="get_sku_keywords">获取SKU关键词</button>
                    </div>
    
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </fieldset>
            </form>
            <!-- 表格区域 -->
            <table id="queriesTable" lay-filter="queriesTable"></table>
        </div>
    </div>
</div>
<script>
layui.use(['table', 'form', 'util', 'dropdown'], function(){
    var form = layui.form,
        table = layui.table,
        util = layui.util,
        $ = layui.$,
        dropdown = layui.dropdown; // 🔥 新增下拉菜单组件
    
    // 新版表格配置
    table.render({
        elem: '#queriesTable',
        url: 'ajax.php?act=queries',
        height: 'full-180', // 🔥 智能高度计算
        loading: true,
        text: { none: '<i class="layui-icon layui-icon-nodata"></i> 暂无数据' }, // 🔥 新图标
        cols: [[
             {field: 'query', title: '关键词', width: 180, fixed: 'left'},
            // 销售子列
            {field: 'count', title: '搜索查询热门度', width: 120, sort: true, 
             templet: '<span class="layui-font-16 layui-font-bold">{{ d.count }}</span>'},
            {field: 'uniqQueriesWCa', title: '加入购物车', width: 120, sort: true},
            {field: 'ca', title: '加购转化率', width: 120, sort: true, 
             templet: d => '<div class="layui-table-trend"><span class="layui-'+ (d.ca>0?'up':'down') +'">'+d.ca+'%</span></div>'},
            {field: 'ord', title: '订购商品', width: 120, sort: true},
            
            // 库存子列
            {field: 'searchUsersToOrdUsers', title: '订单转化率', width: 120},
            {field: 'gmv', title: '订购金额', width: 130, sort: true, templet: d => '<span class="layui-font-money">'+d.gmv+' ₽</span></br><span class="layui-font-money">'+d.gmvrmb+' ￥</span></br><span class="layui-font-money">'+d.gmvus+' $</span>'},
            {field: 'avgCaRub', title: '平均价格', width: 100, templet: d => '<span class="layui-font-money">'+d.avgCaRub+' ₽</span></br><span class="layui-font-money">'+d.avgCaRubrmb+' ￥</span></br><span class="layui-font-money">'+d.avgCaRubus+' $</span>'},
            
            // 促销子列
            {field: 'itemsViews', title: '商品浏览量', width: 120, sort: true},
            {field: 'uniqSellers', title: '竞争对手', width: 80},
            {field: 'usersWithoutInterectionCount', title: '无操作搜索查询', width: 100},
            {field: 'usersWithoutInterectionShare', title: '无操作搜索查询份额', width: 80, templet: d => '<span class="layui-badge layui-bg-red">'+d.usersWithoutInterectionShare+'%</span>'},
            {field: 'softQueryCount', title: '具有类似结果的搜索查询', width: 80},
            {field: 'softQueryShare', title: '具有类似结果的搜索查询份额', width: 80},
            {field: 'zrCount', title: '无结果搜索查询', width: 80},
            {field: 'zrShare', title: '无结果搜索查询份额', width: 80}
        ]],
        page: {
            layout: ['prev', 'page', 'next', 'count', 'skip', 'limit'],
            limits: [10, 20, 50], // 🔥 新增100条选项
            theme: '#1E9FFF',
            groups: 3
        },
        limit: 20,
        done: function(){
            
        },
        parseData: function(res){ // 数据解析
                return {
                    "code": 0,
                    "msg": "",
                    "count": res.count,
                    "data": res.data
                };
            }
        
    });
    table.reload('queriesTable', {
        lineStyle: 'height: 100px;'
    });
    
    form.on('submit(search)', function (data) {
        var searchParams = {
            text: data.field.text,
        };

        table.reload('queriesTable',
        {
            where: searchParams,
            page: {curr: 1}
        });
        return false;
    });
    
    form.on('reset(queriesForm)', function(){
        $('input[name="text"]').val(''); // 清空关键词输入框
        $('input[name="sku_url"]').val(''); // 清空SKU链接输入框
        table.reload('queriesTable', {
            where: {},
            page: { curr: 1 }
        });
    });
    
    // SKU关键词获取功能
    $('#get_sku_keywords').click(function(){
        var skuUrl = $('input[name="sku_url"]').val();
        if(!skuUrl.trim()){
            layer.msg('请输入SKU链接', {icon: 2});
            return;
        }
        
        var loadIndex = layer.load(2, {content: '正在获取SKU关键词...'});
        
        $.ajax({
            url: 'ajax.php?act=get_sku_keywords',
            type: 'POST',
            data: {skuurl: skuUrl},
            dataType: 'json',
            success: function(res){
                layer.close(loadIndex);
                if(res.code == 0 && res.keywords){
                     // 将获取到的关键词填入搜索框
                     var keywords = res.keywords;
                     var keywordList = [];
                     
                     // 处理多个关键词，支持多种分隔符
                     if(keywords.indexOf(',') > -1){
                         keywordList = keywords.split(',');
                     } else if(keywords.indexOf('，') > -1){
                         keywordList = keywords.split('，');
                     } else if(keywords.indexOf(';') > -1){
                         keywordList = keywords.split(';');
                     } else if(keywords.indexOf('；') > -1){
                         keywordList = keywords.split('；');
                     } else if(keywords.indexOf('|') > -1){
                         keywordList = keywords.split('|');
                     } else if(keywords.indexOf(' ') > -1){
                         keywordList = keywords.split(' ');
                     } else {
                         keywordList = [keywords];
                     }
                     
                     // 清理关键词列表，去除空白
                     keywordList = keywordList.map(function(item){
                         return item.trim();
                     }).filter(function(item){
                         return item.length > 0;
                     });
                     
                     if(keywordList.length > 0){
                         // 如果有多个关键词，显示选择对话框
                         if(keywordList.length > 1){
                             var optionsHtml = '';
                             keywordList.forEach(function(keyword, index){
                                 optionsHtml += '<div style="margin: 5px 0;"><input type="radio" name="keyword_choice" value="' + keyword + '" id="kw_' + index + '"' + (index === 0 ? ' checked' : '') + '> <label for="kw_' + index + '" style="margin-left: 5px;">' + keyword + '</label></div>';
                             });
                             
                             layer.open({
                                 type: 1,
                                 title: '选择关键词进行查询',
                                 content: '<div style="padding: 20px;"><div style="margin-bottom: 15px;">检测到多个关键词，请选择一个进行查询：</div>' + optionsHtml + '</div>',
                                 btn: ['确定查询', '取消'],
                                 yes: function(index, layero){
                                     var selectedKeyword = layero.find('input[name="keyword_choice"]:checked').val();
                                     $('input[name="text"]').val(selectedKeyword);
                                     layer.msg('已选择关键词: ' + selectedKeyword, {icon: 1});
                                     
                                     // 自动执行搜索
                                     var searchParams = {
                                         text: selectedKeyword
                                     };
                                     table.reload('queriesTable', {
                                         where: searchParams,
                                         page: {curr: 1}
                                     });
                                     layer.close(index);
                                 }
                             });
                         } else {
                             // 只有一个关键词，直接使用
                             var selectedKeyword = keywordList[0];
                             $('input[name="text"]').val(selectedKeyword);
                             layer.msg('已获取SKU关键词: ' + selectedKeyword, {icon: 1});
                             
                             // 自动执行搜索
                             var searchParams = {
                                 text: selectedKeyword
                             };
                             table.reload('queriesTable', {
                                 where: searchParams,
                                 page: {curr: 1}
                             });
                         }
                     } else {
                         layer.msg('未能解析出有效的关键词', {icon: 2});
                     }
                 } else {
                    layer.msg('获取SKU关键词失败，请检查链接是否正确', {icon: 2});
                }
            },
            error: function(){
                layer.close(loadIndex);
                layer.msg('请求失败，请重试', {icon: 2});
            }
        });
    });

});
</script>