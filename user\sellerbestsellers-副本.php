<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">店铺监控</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <form class="layui-form layui-form-pane" lay-filter="sellerForm">
                <!-- SKU -->
                <div class="layui-inline">
                    <label class="layui-form-label" style="width:80px;">SKU</label>
                    <div class="layui-input-inline">
                        <input type="text" name="sku" placeholder="SKU" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">价格区间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="money" style="border-radius: var(--global-border-radius);width:80px;height:33px;">到<input type="text" name="moneys" style="border-radius: var(--global-border-radius);width:80px;height:33px;"> 
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">销量区间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="sl" style="border-radius: var(--global-border-radius);width:80px;height:33px;">到<input type="text" name="sls" style="border-radius: var(--global-border-radius);width:80px;height:33px;"> 
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">克重区间</label>
                    <div class="layui-input-inline">
                        <input type="text" name="g" style="border-radius: var(--global-border-radius);width:80px;height:33px;">到<input type="text" name="gs" style="border-radius: var(--global-border-radius);width:80px;height:33px;"> 
                    </div>
                </div>
                <br>
                <!-- 动态店铺选择 -->
                <div class="layui-inline">
                    <label class="layui-form-label" style="width:80px;">店铺</label>
                    <div class="layui-input-inline" style="width:150px;">
                        <select name="seller_id" lay-search multiple>
                            <option value="">加载中...</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width:80px;">状态</label>
                    <div class="layui-input-inline" style="width:100px;">
                        <select name="gm" lay-search multiple>
                            <option value="">选择状态</option>
                            <option value="0">未跟卖</option>
                            <option value="1">已跟卖</option>
                        </select> 
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    
                </div>
            </form>
            <!-- 表格区域 -->
            <button class="layui-btn layui-btn-danger" id="sellerOperation">
                <i class="layui-icon layui-icon-edit"></i> 批量跟卖
            </button>
            <table id="sellerTable" lay-filter="sellerTable"></table>
        </div>
    </div>
</div>
<script>
layui.use(['table', 'util', 'dropdown', 'form', 'jquery'], function(){
    var table = layui.table,
        form = layui.form,
        util = layui.util,
        $ = layui.$,
        dropdown = layui.dropdown;

    // 新版表格配置

    var tableIns = table.render({
        elem: '#sellerTable',
        url: 'ajax.php?act=sellerbestsellers',
        toolbar: '#toolbarDemo',
        height: 'full-180', // 🔥 智能高度计算
        loading: true,
        text: { none: '<i class="layui-icon layui-icon-nodata"></i> 暂无数据' }, // 🔥 新图标
        cols: [[
            {field: 'sku', type: 'checkbox', fixed: 'left'},
            {field: 'photo_url', title: '商品图片', width: 100, fixed: 'left', 
             templet: '<a href="{{ d.product_url }}" target="_blank">\
              <div class="layui-table-cell-img" >\
                <img src="{{ d.photo_url }}" style="object-fit:cover;width:80px;height:80px;padding:0;box-sizing:border-box" alt="点击访问原 商品" lay-on="preview">\
              </div>\
            </a>'},
            
            {field: 'name', title: '商品标题/品牌', width: 180, fixed: 'left',
             templet: function(d){
                 return '<div class="layui-table-cell-main"><div class="title">'+d.name+'</div><div class="sub-info">'
                    + '<span class="layui-badge-dot layui-bg-orange"></span>'+ d.brand
                    + '</div></div>'
                 ;
             }},
            {field: 'name', title: '1/3级类目', width: 150, fixed: 'left',
             templet: function(d){
                 return '<span class="layui-badge-dot layui-bg-orange"></span>'+ d.category1+ '</br><span class="layui-badge-dot layui-bg-blue"></span>'+ d.category3;
             }},
            {field: 'sku', title: 'SKU', width: 110, templet: '<a href="{{ d.product_url }}" target="_blank">{{ d.sku }}</a>'},
            {field: 'sellerName', title: '卖家', width: 90, templet: '<a href="https://www.ozon.ru/seller/{{ d.seller_id }}/" target="_blank">{{ d.sellerName }}</a>'},
            // 销售子列
            {field: 'sold_count', title: '销量', width: 80, sort: true, 
             templet: '<span class="layui-font-16 layui-font-bold">{{ d.sold_count }}</span>'},
            {field: 'zprice', title: '前端价格', width: 110, templet: d => d.zprice?d.zprice+' ￥':'无数据'},
            {field: 'price', title: '均价', width: 110, templet: d => '<span class="layui-font-money">'+d.price+' ₽</span></br><span class="layui-font-money">'+d.rmb+' ￥</span></br><span class="layui-font-money">'+d.us+' $</span>'},
            {field: 'weight', title: '重量/g', width: 85, sort: true, templet: function(d){
                return (d.weight?d.weight:"无数据");
            }},
            {field: '', title: '长高宽mm', width: 130, templet: function(d){
                return (d.depth?d.depth+'x'+d.height+'x'+d.width:"无数据");
            }},
            {field: 'sales_dynamics', title: '趋势', width: 80, 
             templet: d => '<div class="layui-table-trend"><span class="layui-'+ (d.sales_dynamics>0?'up':'down') +'">'+d.sales_dynamics+'%</span></div>'},
            {field: 'pdp_conv_rate', title: '加购率', width: 80, templet: d => progressCircle(d.pdp_conv_rate)},
            {field: 'conv_to_cart', title: '促销占比', width: 90, templet: d => progressCircle(d.conv_to_cart)},
            {field: 'drr', title: '推广占比', width: 90, templet: d => progressCircle(d.drr)},
            {field: 'discount', title: '折扣', width: 80, templet: d => '<span class="layui-badge layui-bg-red">'+d.discount+'%</span>'},
            {field: 'session_count', title: '会话', width: 80, sort: true},
            // 库存子列
            {field: 'stock', title: '库存', width: 80},
            {field: 'avg_delivery_days', title: '配送', width: 80, templet: d => '<i class="layui-icon layui-icon-log"></i> '+d.avg_delivery_days},
            
            // 促销子列
            {field: 'trafaret_days', title: '促销天', width: 80},
            {field: 'update_date', title: '更新/创建日期', width: 160, templet: function(d){
                return d.update_date+'</br>'+d.create_date;
            }}
        ]],
        page: {
            layout: ['prev', 'page', 'next', 'count', 'skip', 'limit'],
            limits: [10, 20, 50, 100], // 🔥 新增100条选项
            theme: '#1E9FFF',
            groups: 3
        },
        limit: 20,
        done: function(){
            
        },
        parseData: function(res){ // 数据解析
                return {
                    "code": 0,
                    "msg": "",
                    "count": res.count,
                    "data": res.data
                };
            }
        
    });
    table.reload('sellerTable', {
        lineStyle: 'height: 100px;'
    });
    

    // 🔥 新版工具函数
    const formatGMV = num => {
        return num >= 1e8 ? (num/1e8).toFixed(2)+'亿' 
             : num >= 1e4 ? (num/1e4).toFixed(1)+'万' 
             : num.toLocaleString();
    }

    const stockStatus = d => {
        const total = parseInt(d.stock) || 0;
        return total < 50 ? `<span class="layui-font-red">${total}⚠️</span>` 
             : total < 200 ? `<span class="layui-font-orange">${total}</span>` 
             : `<span class="layui-font-green">${total}</span>`;
    };
    const progressCircle = percent => `
        <div class="layui-progress-circle" style="width:36px;height:36px" data-percent="${percent}">
            <span>${percent}%</span>
        </div>`;
    
    // ✅ 修复店铺选择框加载逻辑
    function initShopSelect() {
        var $shopSelect = $('select[name="seller_id"]');
        $shopSelect.html('<option value="">加载中...</option>');
        form.render('select'); // ✅ 关键：立即渲染空状态

        $.ajax({
            url: 'ajax.php?act=getselle',
            type: 'GET',
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    var html = '<option value="">所有店铺</option>';
                    res.data.forEach(function (shop) {
                        html += `<option value="${shop.seller_id}">${shop.sellerName}</option>`;
                    });
                    $shopSelect.html(html);
                    form.render('select'); // ✅ 关键：数据加载后必须重新渲染
                } else {
                    layer.msg(res.msg || '店铺加载失败', {icon: 2});
                    $shopSelect.html('<option value="">加载失败，点击重试</option>');
                    form.render('select');
                }
            },
            error: function(xhr) {
                layer.msg('网络错误：'+xhr.status, {icon: 2});
                $shopSelect.html('<option value="">网络错误，点击重试</option>');
                form.render('select');
            }
        });
    }

    // ✅ 修复搜索提交事件
    form.on('submit(search)', function (data) {
        // 处理多选店铺参数为逗号分隔字符串
        var params = $.extend({}, data.field, {
            seller_id: data.field.seller_id.join(','), // ✅ 关键：处理多选值,
            gm: data.field.gm.join(',')
        });
        
        table.reload('sellerTable', {
            where: params,
            page: { curr: 1 }
        });
        return false;
    });

    // ✅ 修复重置功能
    form.on('reset(sellerForm)', function(){
        $('select[name="seller_id"]').val(''); // 清空多选值
        $('select[name="gm"]').val(''); // 清空多选值
        form.render('select'); // ✅ 必须重新渲染
        table.reload('sellerTable', {
            where: { sku: '', seller_id: '', g: '', gs: '', sl: '',sls: '', money:'', moneys:'', gm:''},
            page: { curr: 1 }
        });
    });

    // ✅ 初始化页面（必须放在layui.use内部）
    $(function(){
        initShopSelect();
        // 绑定店铺选择框点击重试
        $('select[name="seller_id"]').on('click', function(){
            if($(this).find('option:first').text().includes('失败')) {
                initShopSelect();
            }
        });
    });

    // 批量跟卖按钮事件（最终修正版）
    $('#sellerOperation').on('click', function () {
        var checkStatus = table.checkStatus('sellerTable');
        if (checkStatus.data.length === 0) {
            layer.msg('请至少选择一条数据', {icon: 2});
            return;
        }
    
        // 获取选中的SKU列表
        var skus = checkStatus.data.map(function(item) {
            return item.sku;
        });
    
        // 获取店铺列表
        $.ajax({
            url: 'ajax.php?act=getstore',
            type: 'GET',
            dataType: 'json',
            success: function (shopRes) {
                layer.close();
                if (shopRes.code !== 0) {
                    layer.msg('获取店铺列表失败', {icon: 2});
                    return;
                }
                
                // 存储店铺仓库数据
                var storeWarehouses = {};
                shopRes.stores.forEach(function(shop) {
                    storeWarehouses[shop.id] = shop.warehouses;
                });
    
                // 构建店铺选项
                var shopOptions = shopRes.stores.map(function(shop) {
                    return `<option value="${shop.id}">${shop.storename}</option>`;
                }).join('');
    
                // 弹窗配置
                var layerIndex = layer.open({
                    title: '批量跟卖',
                    content: '<div style="padding:20px;">'
                        + '<div class="layui-form-item">'
                        + '<label class="layui-form-label">目标店铺</label>'
                        + '<div class="layui-input-block">'
                        + `<select name="targetShop" lay-verify="required">${shopOptions}</select>`
                        + '</div></div>'
                        + '<div class="layui-form-item">'
                        + '<label class="layui-form-label">目标仓库</label>'
                        + '<div class="layui-input-block">'
                        + '<select name="targetWarehouse" lay-verify="required"></select>'
                        + '</div></div>'
                        + '<div class="layui-form-item">'
                        + '<label class="layui-form-label">价格策略</label>'
                        + '<div class="layui-input-block">'
                        + '<select name="priceStrategy" lay-verify="required">'
                        + '<option value="original">原价</option>'
                        + '<option value="discount">折扣比例</option>'
                        + '<option value="fixed">固定价格</option>'
                        + '</select></div></div>'
                        + '<div class="layui-form-item" id="priceAdjust" style="display:none;">'
                        + '<label class="layui-form-label">调整值</label>'
                        + '<div class="layui-input-block">'
                        + '<input type="text" name="priceValue" placeholder="例: -10% 或 99" class="layui-input">'
                        + '</div></div>'
                        + '<div class="layui-form-item">'
                        + '<label class="layui-form-label">库存数量</label>'
                        + '<div class="layui-input-block">'
                        + '<input type="number" name="stock" value="10" class="layui-input">'
                        + '</div></div></div>',
                    btn: ['提交', '取消'],
                    area: ['500px', '500px'],
                    // 修改后的批量跟卖弹窗代码
                    success: function (layero, index) {
                        // 初始化第一个店铺的仓库
                        var firstStore = shopRes.stores[0];
                        if(firstStore) {
                            var warehouseSelect = layero.find('select[name="targetWarehouse"]');
                            warehouseSelect.empty();
                            firstStore.warehouses.forEach(function(warehouse) {
                                warehouseSelect.append($('<option>', {
                                    value: warehouse.id,
                                    text: warehouse.name
                                }));
                            });
                        }
                        
                        // 关键点1：必须重新渲染表单
                        form.render('select');
                        
                        // 关键点2：使用Layui的模块作用域绑定事件
                        layui.form.on('select(targetShop)', function(data){
                            console.log('店铺选择变化:', data.value); // 调试日志
                            
                            var selectedStoreId = data.value;
                            var warehouseSelect = layero.find('select[name="targetWarehouse"]');
                            warehouseSelect.empty();
                            
                            // 获取对应仓库
                            var warehouses = storeWarehouses[selectedStoreId] || [];
                            warehouses.forEach(function(warehouse) {
                                warehouseSelect.append($('<option>', {
                                    value: warehouse.id,
                                    text: warehouse.name
                                }));
                            });
                            
                            // 关键点3：必须重新渲染select
                            layui.form.render('select');
                        });
                    
                        // 监听价格策略变化
                        layui.form.on('select(priceStrategy)', function(data){
                            $('#priceAdjustContainer').toggle(data.value !== 'original');
                        });
                    },
                    yes: function(index, layero) {
                        // 数据收集
                        var formData = {
                            act: 'batchFollowSell',
                            skus: skus.join(','),
                            shop: layero.find('[name="targetShop"]').val(),
                            warehouse: layero.find('[name="targetWarehouse"]').val(),
                            price_type: layero.find('[name="priceStrategy"]').val(),
                            price_val: layero.find('[name="priceValue"]').val(),
                            stock: layero.find('[name="stock"]').val()
                        };
    
                        // 数据验证
                        if(!formData.shop || !formData.warehouse) {
                            layer.msg('请选择店铺和仓库', {icon: 2});
                            return;
                        }
                        if(formData.price_type !== 'original' && !formData.price_val) {
                            layer.msg('请填写价格调整值', {icon: 2});
                            return;
                        }
    
                        // 提交请求
                        layer.load(2);
                        $.ajax({
                            url: 'ajax.php',
                            method: 'POST',
                            data: formData,
                            success: function(res){
                                layer.closeAll('loading');
                                if(res.code === 0) {
                                    layer.msg('操作成功', {icon: 1});
                                    table.reload('sellerTable');
                                } else {
                                    layer.msg(res.msg || '操作失败', {icon: 2});
                                }
                            },
                            error: function(){
                                layer.closeAll('loading');
                                layer.msg('网络错误', {icon: 2});
                            }
                        });
                    }
                });
            },
            error: function() {
                layer.msg('数据加载失败', {icon: 2});
            }
        });
    });
});

// 🔥 新增CSS3动画
layui.$('<style>\
.layui-progress-circle { position:relative; border-radius:50%; background:#f8f8f8; }\
.layui-progress-circle span { position:absolute; left:50%; top:50%; transform:translate(-50%,-50%); font-size:12px }\
.layui-progress-circle:after { content:""; display:block; padding-top:100%; }\
.layui-table-trend .layui-up { color:#5FB878 }\
.layui-table-trend .layui-down { color:#FF5722 }\
.layui-table-grid td { transition: all 0.3s; }\
.layui-table-grid tr:hover td { background-color:#fbfbfb !important; }\
</style>').appendTo('head');
</script>