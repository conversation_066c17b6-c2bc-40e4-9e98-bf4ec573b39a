<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>跨境巴士系统设置</title>

  <style>
    .warehouse-item {
      border: 1px solid #eee;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 4px;
    }
    .layui-form-selects .layui-form-selects-title {
      min-height: 38px !important;
    }
  </style>
</head>
<body>
<div class="layui-container" style="margin-top: 20px;">
  <form class="layui-form">
   <!-- 仓库设置（保持不变） -->
    <div class="layui-card">
      <div class="layui-card-header">跨境巴士仓库设置</div>
      <div class="layui-card-body">
        <div class="warehouse-list">
          <div class="warehouse-item">
            <div class="layui-form-item">
              <label class="layui-form-label">选择仓库</label>
              <div class="layui-input-inline">
                <select name="warehouse" lay-verify="required">
                  <option value="">请选择仓库</option>
                  <option value="1">上海仓</option>
                  <option value="2">北京仓</option>
                  <option value="3">广州仓</option>
                </select>
              </div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">仓库账号</label>
              <div class="layui-input-inline">
                <input type="text" name="account" lay-verify="required" placeholder="请输入账号" class="layui-input">
              </div>
            </div>

            <div class="layui-form-item">
              <label class="layui-form-label">仓库密码</label>
              <div class="layui-input-inline">
                <input type="password" name="password" lay-verify="required" placeholder="请输入密码" class="layui-input">
              </div>
              <button type="button" class="layui-btn layui-btn-danger layui-btn-sm remove-warehouse">删除</button>
            </div>
          </div>
        </div>
        <button type="button" class="layui-btn layui-btn-primary" id="addWarehouse">+ 添加仓库</button>
      </div>
    </div>


    <!-- 默认物流计算设置 -->
    <div class="layui-card" style="margin-top: 20px;">
      <div class="layui-card-header">默认物流计算设置</div>
      <div class="layui-card-body">
        <div class="layui-form-item">
          <label class="layui-form-label">物流商</label>
          <div class="layui-input-inline" style="width: 300px;">
            <select name="logistics_providers" xm-select="providers" lay-verify="required" multiple lay-search>
             <option value="guoo">物流商1 - GUOO</option>
<option value="ural">物流商2 - Ural</option>
<option value="uni">物流商3 - UNI</option>
<option value="cel">物流商4 - CEL</option>
<option value="oyx">物流商5 - OYX</option>
<option value="rets">物流商6 - RETS</option>
<option value="atc">物流商7 - ATC</option>
<option value="tanais">物流商8 - Tanais</option>
<option value="abt">物流商9 - ABT</option>
<option value="xy">物流商10 - XY</option>
<option value="leader">物流商11 - Leader</option>
<option value="zto">物流商12 - ZTO</option>
<option value="iml">物流商13 - IML</option>
<option value="gbs">物流商14 - GBS</option>
<option value="china_post">物流商15 - China Post</option>
            </select>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">物流方式</label>
          <div class="layui-input-inline" style="width: 300px;">
            <select name="logistics_methods" xm-select="methods" lay-verify="required" multiple lay-search>
              <option value="Express">空运Express</option>
              <option value="Standard">陆空Standard</option>
              <option value="Economy">陆运Economy</option>
              <option value="ALL">默认全选</option>
            </select>
          </div>
        </div>

       <div class="layui-form-item">
          <label class="layui-form-label">贴单费用</label>
          <div class="layui-input-inline" style="width: 150px;">
            <input type="number" name="label_fee" step="0.01" placeholder="请输入贴单费用" class="layui-input" lay-verify="number">
          </div>
          <div class="layui-form-mid">元</div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">提现手续费 (%)</label>
          <div class="layui-input-inline" style="width: 150px;">
            <input type="number" name="withdraw_fee" step="0.01" placeholder="请输入提现手续费百分比" class="layui-input" lay-verify="number">
          </div>
          <div class="layui-form-mid">%</div>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="layui-form-item" style="margin-top: 20px;">
      <div class="layui-input-block">
        <button class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>
        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
      </div>
    </div>
  </form>
</div>

<script src="/assets/component/layui/layui.js"></script>

<script>
layui.use(['form', 'formSelects'], function(){
  var form = layui.form;
  var formSelects = layui.formSelects;

  // 初始化多选组件
  formSelects.render({
    elem: '#logistics-providers',
    search: true,
    placeholder: '请选择物流商',
    selectedAll: false
  });

  formSelects.render({
    elem: '#logistics-methods',
    search: true,
    placeholder: '请选择物流方式',
    selectedAll: false
  });

  // 表单提交处理
  form.on('submit(formDemo)', function(data){
    // 获取多选值
    var providers = formSelects.value('logistics_providers', 'val');
    var methods = formSelects.value('logistics_methods', 'val');
    
    console.log('物流商：', providers);
    console.log('物流方式：', methods);
    console.log('完整数据：', data.field);
    
    return false;
  });
});
</script>
</body>
</html>