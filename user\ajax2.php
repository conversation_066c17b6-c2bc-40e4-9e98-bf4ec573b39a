<?php
include("../includes/common.php");
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;

if(!checkRefererHost())exit('{"code":403}');
if($act!='login' and $act!='reg'){
    if($islogin2==1){}else exit('{"code":-3,"msg":"No Login"}');
}
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
@header('Content-Type: application/json; charset=UTF-8');

switch($act){
case 'config':
    echo file_get_contents('./config/pear.config.json');exit;
break;
case 'menu':
    echo file_get_contents('./config/menu.json');exit;
break;
case 'category':
    echo file_get_contents('./config/中文类目.json');exit;
break;
case 'login':
	$username=trim($_POST['username']);
	$password=trim($_POST['password']);
	if(empty($username) || empty($password))exit('{"code":-1,"msg":"请确保各项不能为空"}');
	$userrow=$DB->getRow("SELECT * FROM ozon_user WHERE username=:username limit 1", [':username'=>$username]);
	if($userrow && password_verify($password, $userrow['password'])) {
	    $auth = new \lib\AuthSystem();
	    $token = $auth->generateToken($userrow['uid'], $username);
		ob_clean();
		$host = $_SERVER['HTTP_HOST'];
        $domainParts = explode('.', $host);
        
        // 处理本地开发环境（如localhost）
        if (count($domainParts) === 1 || in_array(end($domainParts), ['localhost', 'test', 'local'])) {
            $mainDomain = $host;
        } 
        // 处理正式域名（支持二级国家域名如.co.uk）
        else {
            $mainDomain = implode('.', array_slice($domainParts, -2, 2));
        }
        
        // 设置全局Cookie参数
        setcookie(
            "Authorization",          // Cookie名称
            $token,                   // Token值
            time() + 691200,          // 过期时间（8天）
            "/",                      // 全路径有效
            ".{$mainDomain}",         // 支持所有子域名
            isset($_SERVER['HTTPS']), // 自动启用HTTPS安全传输
            true                      // 禁止JavaScript访问
        );
		$result=array("code"=>1,"msg"=>"登录成功！正在跳转到用户中心");
		
		unset($_SESSION['csrf_token']);
	}else {
		$result=array("code"=>-1,"msg"=>"用户名或密码不正确！");
	}
	exit(json_encode($result));
break;
case 'reg':
    $username=htmlspecialchars(strip_tags(trim($_POST['username'])));
    $password=trim($_POST['password']);
    if (strlen($password) < 6) {
		exit('{"code":-1,"msg":"密码不能低于6位"}');
	}elseif (is_numeric($password)) {
		exit('{"code":-1,"msg":"密码不能为纯数字"}');
	}
    $row=$DB->getRow("select * from ozon_user where username=:username limit 1", [':username'=>$username]);
	if($row){
		exit('{"code":-1,"msg":"该用户名已经注册过，如需找回信息，请返回登录页面找回"}');
	}else{
	    $password = password_hash($password, PASSWORD_DEFAULT);
	    $sds=$DB->exec("INSERT INTO `ozon_user` (`uid`, `username`, `password`, `addtime`, `status`) VALUES (NULL, '{$username}', '{$password}',NOW(),1)");
	    if($sds){
			$result=array("code"=>1,"msg"=>"注册成功！");
		}else{
			$result=array("code"=>-1,"msg"=>"注册失败！".$DB->error());
		}
	}
    exit(json_encode($result));
break;
case 'getUserInfo':
    $userId = $GLOBALS['uid'] ?? 0;
    
    // 验证用户登录状态
    if ($userId === 0) {
        exit(json_encode([
            'code' => 0,
            'msg' => '未登录',
            'errorCode' => 401
        ]));
    }
    
    // 修正变量名并使用正确的查询方法，增加expiry_date字段
    $userInfo = $DB->getRow("SELECT uid, username, user_level, expiry_date FROM ozon_user WHERE uid = ?", [$userId]);
    
    // 检查用户是否存在
    if (!$userInfo) {
        exit(json_encode([
            'code' => 0,
            'msg' => '用户不存在',
            'errorCode' => 404
        ]));
    }
    
    // 返回用户信息
    exit(json_encode([
        'code' => 1,
        'data' => $userInfo,
        'msg' => '获取成功'
    ]));
    
    break;
// 新增产品列表接口，分页查询产品信息
case 'new_product_list':
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, intval($_GET['limit'])) : 20;
    $offset = ($page - 1) * $limit;
    $uid = $GLOBALS['uid'];

    $sqlCount = "SELECT COUNT(*) FROM products WHERE uid = ?";
    $total = $DB->getColumn($sqlCount, [$uid]);

    $sql = "SELECT * FROM products WHERE uid = ? ORDER BY created_at DESC LIMIT ?, ?";
    $list = $DB->getAll($sql, [$uid, $offset, $limit]);

    // 处理规格JSON字段，转为数组
    foreach ($list as &$item) {
        if (!empty($item['specs'])) {
            $item['specs'] = json_decode($item['specs'], true);
        } else {
            $item['specs'] = [];
        }
    }

    exit(json_encode(['code' => 0, 'count' => $total, 'data' => $list]));
    break;

case 'new_product_add':
    $uid = $GLOBALS['uid'];
    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        exit(json_encode(['code' => -1, 'msg' => '无效的请求数据']));
    }
    // 必填字段校验
    $requiredFields = ['title', 'sku', 'unit_price', 'status'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            exit(json_encode(['code' => -1, 'msg' => "缺少必填字段: $field"]));
        }
    }
    // 处理规格字段，转为JSON字符串
    $specsJson = null;
    if (!empty($data['specs']) && is_array($data['specs'])) {
        $specsJson = json_encode($data['specs'], JSON_UNESCAPED_UNICODE);
    }
    // 插入数据库
    $insertData = [
        'uid' => $uid,
        'image_url' => $data['image_url'] ?? null,
        'title' => $data['title'],
        'size' => $data['size'] ?? null,
        'weight' => isset($data['weight']) ? floatval($data['weight']) : null,
        'sku' => $data['sku'],
        'specs' => $specsJson,
        'status' => intval($data['status']),
        'matched_platform_product' => $data['matched_platform_product'] ?? null,
        'unit_price' => floatval($data['unit_price']),
        'remarks' => $data['remarks'] ?? null,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    $res = $DB->insert('products', $insertData);
    if ($res) {
        exit(json_encode(['code' => 0, 'msg' => '新增产品成功']));
    } else {
        exit(json_encode(['code' => -1, 'msg' => '新增产品失败']));
    }
    break;

case 'new_product_edit':
    $uid = $GLOBALS['uid'];
    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data || empty($data['id'])) {
        exit(json_encode(['code' => -1, 'msg' => '无效的请求数据']));
    }
    $id = intval($data['id']);
    // 处理规格字段，转为JSON字符串
    $updateData = [];
    $fields = ['image_url', 'title', 'size', 'weight', 'sku', 'status', 'matched_platform_product', 'unit_price', 'remarks'];
    foreach ($fields as $field) {
        if (isset($data[$field])) {
            $updateData[$field] = $data[$field];
        }
    }
    if (isset($data['specs']) && is_array($data['specs'])) {
        $updateData['specs'] = json_encode($data['specs'], JSON_UNESCAPED_UNICODE);
    }
    $updateData['updated_at'] = date('Y-m-d H:i:s');
    $res = $DB->update('products', $updateData, ['id' => $id, 'uid' => $uid]);
    if ($res) {
        exit(json_encode(['code' => 0, 'msg' => '更新产品成功']));
    } else {
        exit(json_encode(['code' => -1, 'msg' => '更新产品失败或无变化']));
    }
    break;

// 获取仓库列表接口
case 'new_warehouse_list':
    $uid = $GLOBALS['uid'];
    $list = $DB->getAll("SELECT * FROM warehouses WHERE uid = ?", [$uid]);
    exit(json_encode(['code' => 0, 'data' => $list]));
    break;

// 新增仓库接口
case 'new_warehouse_add':
    $uid = $GLOBALS['uid'];
    $name = trim($_POST['name']);
    $type = trim($_POST['type']);
    if (!$name) {
        exit(json_encode(['code' => -1, 'msg' => '仓库名称不能为空']));
    }
    $res = $DB->insert('warehouses', [
        'uid' => $uid,
        'name' => $name,
        'type' => $type,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    if ($res) {
        exit(json_encode(['code' => 0, 'msg' => '新增仓库成功']));
    } else {
        exit(json_encode(['code' => -1, 'msg' => '新增仓库失败']));
    }
    break;

// 库存入库/出库接口
case 'new_stock_in_out':
    $uid = $GLOBALS['uid'];
    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        exit(json_encode(['code' => -1, 'msg' => '无效的请求数据']));
    }
    $requiredFields = ['product_id', 'warehouse_id', 'quantity_change', 'type'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field])) {
            exit(json_encode(['code' => -1, 'msg' => "缺少字段: $field"]));
        }
    }
    $product_id = intval($data['product_id']);
    $warehouse_id = intval($data['warehouse_id']);
    $quantity_change = intval($data['quantity_change']);
    $type = $data['type'] === 'in' ? 'in' : 'out';
    $remark = $data['remark'] ?? '';

    // 事务处理，更新库存并插入变动记录
    try {
        $DB->beginTransaction();

        // 查询当前库存
        $stockRow = $DB->getRow("SELECT * FROM stock WHERE product_id = ? AND warehouse_id = ? FOR UPDATE", [$product_id, $warehouse_id]);
        if ($stockRow) {
            $newQuantity = $stockRow['quantity'] + ($type === 'in' ? $quantity_change : -$quantity_change);
            if ($newQuantity < 0) {
                $DB->rollBack();
                exit(json_encode(['code' => -1, 'msg' => '库存不足，无法出库']));
            }
            $DB->update('stock', ['quantity' => $newQuantity, 'updated_at' => date('Y-m-d H:i:s')], ['id' => $stockRow['id']]);
        } else {
            if ($type === 'out') {
                $DB->rollBack();
                exit(json_encode(['code' => -1, 'msg' => '库存不足，无法出库']));
            }
            $DB->insert('stock', [
                'product_id' => $product_id,
                'warehouse_id' => $warehouse_id,
                'quantity' => $quantity_change,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }

        // 插入库存变动记录
        $DB->insert('stock_movements', [
            'product_id' => $product_id,
            'warehouse_id' => $warehouse_id,
            'quantity_change' => $type === 'in' ? $quantity_change : -$quantity_change,
            'type' => $type,
            'remark' => $remark,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $DB->commit();
        exit(json_encode(['code' => 0, 'msg' => '库存操作成功']));
    } catch (Exception $e) {
        $DB->rollBack();
        exit(json_encode(['code' => -1, 'msg' => '库存操作失败: ' . $e->getMessage()]));
    }
    break;

// 获取库存变动明细接口
case 'new_stock_movements':
    $uid = $GLOBALS['uid'];
    $product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
    if ($product_id <= 0) {
        exit(json_encode(['code' => -1, 'msg' => '无效的产品ID']));
    }
    $sql = "SELECT sm.*, w.name AS warehouse_name FROM stock_movements sm 
            LEFT JOIN warehouses w ON sm.warehouse_id = w.id 
            WHERE sm.product_id = ? ORDER BY sm.created_at DESC";
    $list = $DB->getAll($sql, [$product_id]);
    exit(json_encode(['code' => 0, 'data' => $list]));
    break;

    echo file_get_contents('./config/pear.config.json');exit;
break;
default:
	exit('{"code":-4,"msg":"No Act"}');
break;
}
// 辅助函数
function getCurrencyCode($value) {
    $map = [
        '1' => 'CNY',
        '2' => 'RUB',
        '3' => 'USD'
    ];
    return $map[$value] ?? '';
}