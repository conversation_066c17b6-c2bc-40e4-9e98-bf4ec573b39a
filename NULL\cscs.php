<?php
include("./includes/common.php");
$h = ['Authorization: EPYON3VGDYZ95O984TOQ9X5EGS4SLDWG','Content-Type: application/json'];
$res = get_curl('https://ozon.ssss00.me/api/sku-query',json_encode(['sku'=>'1965676792','limit'=>10,'language'=>'ru']),0,0,0,0,0,$h);
//exit($res);
$json = json_decode($res,true,JSON_THROW_ON_ERROR);

foreach($json['items'][0]['attributes'] as $item){
    //echo $item['key'];
    $values=[];
    if($item['key']==9456){//高
        $height = $item['value'];
    }
    if($item['key']==9454){//长
        $depth = $item['value'];
    }
    if($item['key']==9455){//宽
        $width = $item['value'];
    }
    if($item['key']==85){ //品牌
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==9048){ //型号名称
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==4383){ //商品重量
        $weight = $item['value'];
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==4191){ //简介
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==22336){ //关键字
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6008){ //最大功率，瓦特
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6236){ //第一档的最大转速，转数/分钟
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6237){ //第2档最大转速，转数/分钟
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6242){ //扭矩，牛米
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6239){ //墨盒类型
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==9403){ //卡盘最小直径，毫米
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6334){ //卡盘最大直径，毫米
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6337){ //钢孔最大直径，毫米
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6243){ //混凝土孔最大直径，毫米
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6338){ //木孔最大直径，毫米
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6244){ //螺钉最大直径，毫米
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6245){ //节拍数，节拍/分钟
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==10845){ //速度数
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==10060){ //电池电压
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>[['dictionary_value_id'=>0,'value'=>$item['value']]]];
    }
    if($item['key']==6248){ //结构特点
        if($item['collection']){
            foreach($item['collection'] as $x){
                $values[] = ['dictionary_value_id'=>0,'value'=>$x];
            }
        }else {
            $values[] = ['dictionary_value_id'=>0,'value'=>$item['value']];
        }
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>$values];
    }
    if($item['key']==6247){ //功能特点 例如:无级调速,无 刷
        if($item['collection']){
            foreach($item['collection'] as $x){
                $values[] = ['dictionary_value_id'=>0,'value'=>$x];
            }
        }else {
            $values[] = ['dictionary_value_id'=>0,'value'=>$item['value']];
        }
        $attributes[] = ['id'=>$item['key'],'complex_id'=>0,'values'=>$values];
    }
}
$jsonData = file_get_contents('./assets/json/俄语类目.json');
$data = json_decode($jsonData, true);

$categoryId = $json['items'][0]['categories'][2]['id']; // 示例：蜂蜜制作分类ID
//exit($categoryId);
$typeName = $json['items'][0]['attributes'][0]['value'];   // 要查找的类型名称

$typeId = findTypeIdByCategoryAndName($data['result'], $categoryId, $typeName);
$body = [
    'name'=>$json['items'][0]['name'],
    'height'=>$height,
    'depth'=>$depth,
    'width'=>$width,
    'dimension_unit'=>'mm',
    'weight'=>$weight,
    'weight_unit'=>'g',
    'description_category_id'=>$categoryId,
    'type_id'=>$typeId,
    'primary_image'=>$json['items'][0]['images'][0]['url'],
    'color_image'=>'',
    'attributes'=>$attributes,
];

exit(json_encode($body,JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE));

function findTypeIdByCategoryAndName($data, $targetCategoryId, $targetTypeName) {
    foreach ($data as $item) {
        // 匹配目标分类
        if (isset($item['description_category_id']) && 
            $item['description_category_id'] == $targetCategoryId) {
            // 在匹配分类的子项中查找名称
            foreach ($item['children'] as $child) {
                if (isset($child['type_name']) && 
                    $child['type_name'] == $targetTypeName) {
                    return $child['type_id'];
                }
            }
            return null; // 分类存在但名称不存在
        }
        // 递归搜索子节点
        if (!empty($item['children'])) {
            $result = findTypeIdByCategoryAndName(
                $item['children'], 
                $targetCategoryId, 
                $targetTypeName
            );
            if ($result !== null) return $result;
        }
    }
    return null; // 分类不存在
}
