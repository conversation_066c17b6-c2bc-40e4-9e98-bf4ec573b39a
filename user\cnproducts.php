<?php
include("../includes/common.php");
$categoryData = file_get_contents('./config/中文类目.json');
?>
<style>
    .category-selector {
        display: flex;
        gap: 15px;
        align-items: center;
    }
    .category-selector .layui-input-inline {
        flex: 1;
    }
    .filter-compact {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 8px;
        padding: 10px 0;
    }
    .filter-compact .layui-inline {
        margin-right: 0;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        flex-shrink: 0;
    }
    .filter-compact .layui-form-label {
        padding: 9px 6px;
        text-align: center;
        font-size: 12px;
        min-width: auto;
    }
    .filter-compact .layui-input {
        height: 30px;
        line-height: 30px;
        font-size: 12px;
        padding: 0 8px;
    }
    .filter-compact .layui-form-mid {
        margin: 0 4px;
        line-height: 30px;
        font-size: 12px;
    }
    .filter-compact .layui-btn {
        height: 30px;
        line-height: 30px;
        padding: 0 10px;
        font-size: 12px;
        margin: 0 2px;
    }
    .filter-compact .layui-input-inline {
        margin: 0 2px;
    }
    @media (max-width: 1200px) {
        .filter-compact {
            gap: 6px;
        }
        .filter-compact .layui-form-label {
            padding: 9px 4px;
            font-size: 11px;
        }
        .filter-compact .layui-input {
            font-size: 11px;
            padding: 0 6px;
        }
    }
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <form class="layui-form layui-form-pane">
                <fieldset class="layui-elem-field form-section">
                    <legend><i class="layui-icon layui-icon-chart-screen" style="color:#1E9FFF"></i> 中国卖家热销</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <label class="layui-form-label">商品类目</label>
                            <div class="layui-input-block category-selector">
                                <div class="layui-input-inline">
                                    <select name="category1" lay-filter="category" lay-search>
                                        <option value="">一级类目</option>
                                    </select>
                                </div>
                                <i class="layui-icon layui-icon-right"></i>
                                <div class="layui-input-inline">
                                    <select name="category2" lay-filter="category" lay-search>
                                        <option value="">二级类目</option>
                                    </select>
                                </div>
                                <i class="layui-icon layui-icon-right"></i>
                                <div class="layui-input-inline">
                                    <select name="category3" lay-filter="category" lay-search>
                                        <option value="">三级类目</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- 筛选条件 -->
                        <div class="layui-form-item filter-compact">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width:50px;">标题</label>
                                <div class="layui-input-inline" style="width:100px;">
                                    <input type="text" name="title" placeholder="关键词" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width:50px;">均价</label>
                                <div class="layui-input-inline" style="width:65px;">
                                    <input type="number" name="price_min" placeholder="最低" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width:65px;">
                                    <input type="number" name="price_max" placeholder="最高" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width:50px;">销量</label>
                                <div class="layui-input-inline" style="width:65px;">
                                    <input type="number" name="sold_min" placeholder="最小" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width:65px;">
                                    <input type="number" name="sold_max" placeholder="最大" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width:60px;">跟卖数</label>
                                <div class="layui-input-inline" style="width:65px;">
                                    <input type="number" name="seller_min" placeholder="最小" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width:65px;">
                                    <input type="number" name="seller_max" placeholder="最大" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width:60px;">访客数</label>
                                <div class="layui-input-inline" style="width:65px;">
                                    <input type="number" name="session_min" placeholder="最小" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width:65px;">
                                    <input type="number" name="session_max" placeholder="最大" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width:60px;">创建时间</label>
                                <div class="layui-input-inline" style="width:90px;">
                                    <input type="text" name="create_start" id="create_start" placeholder="开始日期" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline" style="width:90px;">
                                    <input type="text" name="create_end" id="create_end" placeholder="结束日期" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-sm" lay-submit lay-filter="search">
                                    <i class="layui-icon layui-icon-search"></i> 搜索
                                </button>
                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                                    <i class="layui-icon layui-icon-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </form>
            <!-- 表格区域 -->
            <table id="goodsTable" lay-filter="goodsTable"></table>
        </div>
    </div>
</div>
<script>
layui.use(['table', 'form', 'util', 'dropdown', 'laydate'], function(){
    var form = layui.form,
        table = layui.table,
        util = layui.util,
        $ = layui.$,
        dropdown = layui.dropdown,
        laydate = layui.laydate;
    
    // 类目数据
    var categoryData = <?=$categoryData?>;
    var currentCategory = {1: null, 2: null, 3: null}; // 当前选择的各类目ID
    var currentSort = {field: 'nullableCreateDate', order: 'desc'}; // 当前排序状态

    // 查找类目节点
    function findCategoryNode(categoryId) {
        function find(nodes) {
            for(const key in nodes){
                const node = nodes[key];
                if(node.descriptionCategoryId == categoryId) return node;
                if(node.nodes && Object.keys(node.nodes).length > 0){
                    const found = find(node.nodes);
                    if(found) return found;
                }
            }
            return null;
        }
        return find(categoryData.result);
    }

    // 加载子类目方法
    function loadSubCategories(level, parentId) {
        const $nextSelect = $(`select[name="category${level}"]`);
        $nextSelect.empty().append(`<option value="">请选择</option>`);
        
        if(parentId) {
            const parentNode = findCategoryNode(parentId);
            if(parentNode && parentNode.nodes) {
                Object.values(parentNode.nodes).forEach(item => {
                    if(!item.disabled && level<3) {
                        $nextSelect.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
                    }else{
                        $nextSelect.append(new Option(item.descriptionTypeName, item.descriptionTypeId));
                    }
                });
            }
        }
        
        layui.form.render('select');
    }
    
    // 初始化一级类目
    function initFirstCategory() {
        var $select = $('select[name="category1"]');
        $select.empty().append('<option value="">一级类目</option>');
        
        Object.values(categoryData.result).forEach(item => {
            if(!item.disabled) {
                $select.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
            }
        });
        
        layui.form.render('select');
    }
    
    // 初始化类目
    initFirstCategory();
    
    // 初始化日期选择器
    laydate.render({
        elem: '#create_start',
        type: 'date',
        format: 'yyyy-MM-dd',
        max: '2099-12-31'
    });
    
    laydate.render({
        elem: '#create_end', 
        type: 'date',
        format: 'yyyy-MM-dd',
        max: '2099-12-31'
    });
    
    // 类目选择事件
    form.on('select(category)', function(data){
        const level = parseInt(data.elem.name.replace('category',''));
        const value = data.value;
        
        currentCategory[level] = value;
        
        // 清空后续选择
        for(let l = level + 1; l <= 3; l++) {
            currentCategory[l] = null;
            $(`select[name="category${l}"]`).val('').trigger('change');
        }
        
        if(level < 3 && value) {
            loadSubCategories(level + 1, value);
        }
        
        // 类目变化时总是重新搜索（保持其他筛选条件）
        window.performSearch();
    });

    // 统一搜索函数（暴露到全局作用域用于调试）
    window.performSearch = function performSearch() {
        var whereData = {};
        
        // 类目筛选
        if(currentCategory[1]) whereData.categorie1 = currentCategory[1];
        if(currentCategory[2]) whereData.categorie2 = currentCategory[2];
        if(currentCategory[3]) whereData.categorie3 = currentCategory[3];
        
        // 获取表单输入值
        var title = $('input[name="title"]').val();
        var price_min = $('input[name="price_min"]').val();
        var price_max = $('input[name="price_max"]').val();
        var sold_min = $('input[name="sold_min"]').val();
        var sold_max = $('input[name="sold_max"]').val();
        var seller_min = $('input[name="seller_min"]').val();
        var seller_max = $('input[name="seller_max"]').val();
        var session_min = $('input[name="session_min"]').val();
        var session_max = $('input[name="session_max"]').val();
        var create_start = $('input[name="create_start"]').val();
        var create_end = $('input[name="create_end"]').val();
        
        // 筛选条件
        if(title) whereData.title = title;
        if(price_min) whereData.price_min = price_min;
        if(price_max) whereData.price_max = price_max;
        if(sold_min) whereData.sold_min = sold_min;
        if(sold_max) whereData.sold_max = sold_max;
        if(seller_min) whereData.seller_min = seller_min;
        if(seller_max) whereData.seller_max = seller_max;
        if(session_min) whereData.session_min = session_min;
        if(session_max) whereData.session_max = session_max;
        if(create_start) whereData.create_start = create_start;
        if(create_end) whereData.create_end = create_end;
        
        // 添加当前排序参数
        whereData.field = currentSort.field;
        whereData.order = currentSort.order;
        
        table.reload('goodsTable', {
            where: whereData,
            page: { curr: 1 }
        });
    }

    // 搜索表单提交
    form.on('submit(search)', function(data){
        window.performSearch();
        return false;
    });
    
    // 表格配置
    table.render({
        elem: '#goodsTable',
        url: 'ajax.php?act=cnproducts',
        height: 'full-220',
        loading: true,
        
        text: { none: '<i class="layui-icon layui-icon-nodata"></i> 暂无数据' },
              toolbar: ['filter', 'exports'],
        initSort: {
            field: 'nullableCreateDate',
            type: 'desc'
        },
        autoSort: false,
        where: {
            field: 'nullableCreateDate',
            order: 'desc'
        },
        cols: [[
             {field: 'photo', title: '商品图片', width: 120, fixed: 'left', 
             templet: '<a href="{{ d.link }}" target="_blank">\
              <div class="layui-table-cell-img" style="padding:5px;text-align:center;">\
                <img src="{{ d.photo }}" style="object-fit:cover;width:100px;height:100px;border-radius:4px;border:1px solid #eee;" alt="点击访问原商品">\
              </div>\
            </a>'},
            
            {field: 'name', title: '商品标题/品牌', width: 180, fixed: 'left',
             templet: function(d){
                 return '<div class="layui-table-cell-main"><div class="title">'+d.name+'</div><div class="sub-info">'
                    + '<span class="layui-badge-dot layui-bg-orange"></span>'+ d.brand
                    + '</div></div>';
             }},
            {field: 'name', title: '1/3级类目', width: 150, fixed: 'left',
             templet: function(d){
                 return '<span class="layui-badge-dot layui-bg-orange"></span>'+ d.category1+ '</br><span class="layui-badge-dot layui-bg-blue"></span>'+ d.category3;
             }},
            // 销售子列
            {field: 'soldCount', title: '月销量', width: 90, sort: 'desc', 
             templet: '<span class="layui-font-16 layui-font-bold">{{ d.soldCount }}</span>'},
            {field: 'sellerlength', title: '跟卖数量', width: 105, sort: 'desc', 
             templet: '<span class="layui-font-16 layui-font-bold">{{ d.sellerlength }}</span>'},
            {field: 'gmvSum', title: '月售额', width: 90, sort: 'desc', templet: d => formatGMV(d.gmvSum)},
            {field: 'avgGmv', title: '均价', width: 80, sort: 'desc', templet: d => '<span class="layui-font-money">₽'+d.avgGmv+'</span>'},
            {field: 'salesDynamics', title: '月销售变化', width: 80, sort: 'desc',
             templet: d => '<div class="layui-table-trend"><span class="layui-'+ (d.salesDynamics>0?'up':'down') +'">'+d.salesDynamics+'%</span></div>'},
             {field: 'convToCartPdp', title: '搜索转换率/加购率', width: 160, templet: d => progressCircle(d.convToCartSearch)+progressCircle(d.convToCartPdp)},
            {field: 'sessionCount', title: '详情页访问', width: 120, sort: 'desc'},
            
            // 库存子列
            {field: 'avgDeliveryDays', title: '配送', width: 80, sort: 'asc', templet: d => '<i class="layui-icon layui-icon-log"></i> '+d.avgDeliveryDays},
            {field: 'accessibility', title: '可售率', width: 80, sort: 'desc', templet: d => progressCircle(d.accessibility*100)},
            
            // 促销子列
            {field: 'daysInPromo', title: '促销天', width: 80, sort: 'desc'},
            {field: 'promoRevenueShare', title: '促销占比', width: 100, sort: 'desc', templet: d => progressCircle(d.promoRevenueShare)},
            {field: 'drr', title: '推广费用占比', width: 100, sort: 'desc', templet: d => progressCircle(d.drr)},
            {field: 'discount', title: '折扣', width: 80, sort: 'desc', templet: d => '<span class="layui-badge layui-bg-red">'+d.discount+'%</span>'},
            {field: 'blockedBySeller', title: '是否允许跟', width: 80, sort: 'asc', templet: function(d){
                if(d.blockedBySeller==true || d.blockedBySeller==1){
                    return '不允许';
                }else{
                    return '可以';
                }
            }},
            {field: 'nullableCreateDate', title: '创建日期', width: 110, sort: 'desc', templet: d => util.toDateString(d.nullableCreateDate || new Date(), 'yyyy-MM-dd')}
        ]],
        page: {
            layout: ['prev', 'page', 'next', 'count', 'skip', 'limit'],
            limits: [10, 20, 50],
            theme: '#1E9FFF',
            groups: 3
        },
        limit: 20,
        done: function(){
            // 重置表单事件
            $('button[type="reset"]').off('click').on('click', function(){
                // 重置类目选择
                currentCategory = {1: null, 2: null, 3: null};
                
                // 重置排序状态
                currentSort = {field: 'nullableCreateDate', order: 'desc'};
                
                // 清空类目下拉框
                $('select[name="category1"]').val('');
                $('select[name="category2"]').empty().append('<option value="">二级类目</option>');
                $('select[name="category3"]').empty().append('<option value="">三级类目</option>');
                form.render('select');
                
                // 清空所有筛选输入框
                $('input[name="title"]').val('');
                $('input[name="price_min"]').val('');
                $('input[name="price_max"]').val('');
                $('input[name="sold_min"]').val('');
                $('input[name="sold_max"]').val('');
                $('input[name="seller_min"]').val('');
                $('input[name="seller_max"]').val('');
                $('input[name="session_min"]').val('');
                $('input[name="session_max"]').val('');
                $('input[name="create_start"]').val('');
                $('input[name="create_end"]').val('');
                
                // 重新搜索
                window.performSearch();
            });
        },
        parseData: function(res){ 
                return {
                    "code": 0,
                    "msg": "",
                    "count": res.count,
                    "data": res.data
                };
            }
        
    });

    // 监听排序事件
    table.on('sort(goodsTable)', function(obj){
        // 获取当前的筛选条件
        var whereData = {};
        
        // 类目筛选
        if(currentCategory[1]) whereData.categorie1 = currentCategory[1];
        if(currentCategory[2]) whereData.categorie2 = currentCategory[2];
        if(currentCategory[3]) whereData.categorie3 = currentCategory[3];
        
        // 获取表单输入值
        var title = $('input[name="title"]').val();
        var price_min = $('input[name="price_min"]').val();
        var price_max = $('input[name="price_max"]').val();
        var sold_min = $('input[name="sold_min"]').val();
        var sold_max = $('input[name="sold_max"]').val();
        var seller_min = $('input[name="seller_min"]').val();
        var seller_max = $('input[name="seller_max"]').val();
        var session_min = $('input[name="session_min"]').val();
        var session_max = $('input[name="session_max"]').val();
        var create_start = $('input[name="create_start"]').val();
        var create_end = $('input[name="create_end"]').val();
        
        // 筛选条件
        if(title) whereData.title = title;
        if(price_min) whereData.price_min = price_min;
        if(price_max) whereData.price_max = price_max;
        if(sold_min) whereData.sold_min = sold_min;
        if(sold_max) whereData.sold_max = sold_max;
        if(seller_min) whereData.seller_min = seller_min;
        if(seller_max) whereData.seller_max = seller_max;
        if(session_min) whereData.session_min = session_min;
        if(session_max) whereData.session_max = session_max;
        if(create_start) whereData.create_start = create_start;
        if(create_end) whereData.create_end = create_end;
        
        // 更新当前排序状态
        currentSort.field = obj.field;
        currentSort.order = obj.type;
        
        // 添加排序参数
        whereData.field = obj.field;
        whereData.order = obj.type;
        
        // 重新加载表格
        table.reload('goodsTable', {
            where: whereData,
            page: { curr: 1 }
        });
    });

    // 设置表格行高，让图片不被折叠
    table.reload('goodsTable', {
        cellMinWidth: 60,
        lineStyle: 'height: 120px;'
    });

    // 工具函数
    const formatGMV = num => {
        return num >= 1e8 ? (num/1e8).toFixed(2)+'亿' 
             : num >= 1e4 ? (num/1e4).toFixed(1)+'万' 
             : num.toLocaleString();
    }

    const progressCircle = percent => `
        <div class="layui-progress-circle" style="width:36px;height:36px;display:inline-block;margin:2px" data-percent="${percent}">
            <span style="font-size:10px">${percent}%</span>
        </div>`;
});

// 样式优化
layui.$('<style>\
.layui-progress-circle { position:relative; border-radius:50%; background:#f8f8f8; }\
.layui-progress-circle span { position:absolute; left:50%; top:50%; transform:translate(-50%,-50%); font-size:12px }\
.layui-progress-circle:after { content:""; display:block; padding-top:100%; }\
.layui-table-trend .layui-up { color:#5FB878 }\
.layui-table-trend .layui-down { color:#FF5722 }\
.layui-table-grid td { transition: all 0.3s; }\
.layui-table-grid tr:hover td { background-color:#fbfbfb !important; }\
.layui-font-money { color:#FF5722; font-weight:bold }\
.title { font-size:12px; line-height:1.4; margin-bottom:4px }\
.sub-info { font-size:11px; color:#999 }\
.layui-table-cell-img { height:110px; display:flex; align-items:center; justify-content:center; }\
.layui-table-cell-img img { transition: transform 0.3s; }\
.layui-table-cell-img img:hover { transform: scale(1.1); }\
.layui-table tbody tr { height: 120px !important; }\
.layui-table td { vertical-align: middle !important; }\
.layui-table-sort { cursor: pointer; transition: color 0.3s; }\
.layui-table-sort:hover { color: #1E9FFF; }\
.layui-table th .layui-table-sort { font-size: 14px; margin-left: 5px; }\
.layui-table-sort-asc { color: #5FB878 !important; }\
.layui-table-sort-desc { color: #FF5722 !important; }\
</style>').appendTo('head');
</script>