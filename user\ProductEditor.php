<?php
include("../includes/common.php");
$id = intval($_GET['id']);
$row=$DB->getRow("SELECT * FROM ozon_production WHERE id=:id AND uid=:uid limit 1", [':id'=>$id,':uid'=>$uid]);
$categoryData = file_get_contents('./config/中文类目.json');
$row['category_ids'] = json_decode($row['category_chain'],true)['ids'];
if($row['category_ids']){
    list($category1, $category2, $category3) = explode(",", $row['category_ids']);
    $client = new \lib\OzonApiClient(2763302, '0a1f6874-fe81-4b30-97ce-4a3339eb077e');
    $Attributes = $client->attribute(['description_category_id'=>$category2,'type_id'=>$category3]);
    $Attributes = json_encode($Attributes);
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商品编辑</title>
    <link rel="stylesheet" href="/assets/component/layui/css/layui.css">
</head>
</script>
<body>
<form class="layui-form">
    <!-- 基本信息 -->
    <input type="hidden" name="id" value="<?=$id?>">
    <fieldset class="layui-elem-field">
        <legend>基本信息</legend>
        <div class="layui-field-box">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">商品名称</label>
                    <div class="layui-input-inline" style="width: 500px;">
                        <input type="text" name="title" lay-verify="required" autocomplete="off" class="layui-input" value="<?=$row['title']?>">
                    </div>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">商品卖点</label>
                <div class="layui-input-block">
                    <textarea name="selling_point" placeholder="请输入内容" class="layui-textarea"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">价格</label>
                    <div class="layui-input-inline">
                        <input type="number" name="price" lay-verify="required|number" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">库存</label>
                    <div class="layui-input-inline">
                        <input type="number" name="stock" lay-verify="number" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">重量</label>
                    <div class="layui-input-inline">
                        <input type="number" name="weight" class="layui-input">
                    </div>
                    <div class="layui-form-mid">kg</div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">单位</label>
                    <div class="layui-input-inline">
                        <select name="unit">
                            <option value="件">件</option>
                            <option value="个">个</option>
                            <option value="套">套</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </fieldset>

    <!-- 类目&属性 -->
    <fieldset class="layui-elem-field" style="margin-top: 20px;">
        <legend>类目&属性</legend>
        <div class="layui-field-box">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">商品类目</label>
                    <div class="layui-input-inline">
                        <select name="category1" lay-filter="category">
                            <option value="">一级类目</option>
                        </select>
                    </div>
                    <div class="layui-input-inline">
                        <select name="category2" lay-filter="category">
                            <option value="">二级类目</option>
                        </select>
                    </div>
                    <div class="layui-input-inline">
                        <select name="category3" lay-filter="category">
                            <option value="">三级类目</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-md1">
                        <label class="layui-form-label">商品属性</label>
                    </div>
                    <div class="layui-col-md5">
                        <div id="specBox">
                            <div class="layui-row spec-item">
                                <div class="layui-col-md5">
                                    <input type="text" name="specName" placeholder="属性名称" class="layui-input">
                                </div>
                                <div class="layui-col-md5">
                                    <input type="text" name="specValue" placeholder="属性值" class="layui-input">
                                </div>
                                <div class="layui-col-md2">
                                    <button type="button" class="layui-btn layui-btn-danger layui-btn-xs">删除</button>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="layui-btn layui-btn-sm" id="addSpec">添加属性</button>
                    </div>
                </div>
            </div>
        </div>
    </fieldset>
    <!-- 产品图片 -->
    <fieldset class="layui-elem-field" style="margin-top: 20px;">
        <legend>产品图片</legend>
        <div class="layui-field-box">
            <div class="layui-upload">
                <button type="button" class="layui-btn" id="uploadImg">上传图片</button>
                <div class="layui-upload-list" id="previewBox"></div>
            </div>
        </div>
    </fieldset>

    <!-- 产品视频 -->
    <fieldset class="layui-elem-field" style="margin-top: 20px;">
        <legend>产品视频</legend>
        <div class="layui-field-box">
            <div class="layui-form-item">
                <div class="layui-input-inline" style="width: 500px;">
                    <input type="text" name="video" placeholder="请输入视频地址" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
    </fieldset>

    <!-- 货源信息 -->
    <fieldset class="layui-elem-field" style="margin-top: 20px;">
        <legend>货源信息</legend>
        <div class="layui-field-box">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">供货商</label>
                    <div class="layui-input-inline">
                        <select name="supplier">
                            <option value="">请选择供货商</option>
                            <option value="1">供应商A</option>
                            <option value="2">供应商B</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">供货价格</label>
                    <div class="layui-input-inline">
                        <input type="number" name="supply_price" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">供货库存</label>
                    <div class="layui-input-inline">
                        <input type="number" name="supply_stock" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">联系方式</label>
                    <div class="layui-input-inline">
                        <input type="text" name="contact" class="layui-input">
                    </div>
                </div>
            </div>
        </div>
    </fieldset>

    <div class="layui-form-item" style="margin-top: 20px;">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit="" lay-filter="submitBtn">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>

<script src="/assets/component/layui/layui.js"></script>
<script>
layui.use(['form', 'upload','layer'], function(){
    var form = layui.form,
        upload = layui.upload,
        $ = layui.$;
    // 类目数据（替换为你的API返回数据）
    var categoryData = {};
    var currentSelecting = {}; // 记录当前选择状态
    
    // 初始加载
    // 类目数据
    var categoryData = <?=$categoryData?>;
    var currentCategory = {1: null, 2: null, 3: null, 4: null}; // 当前选择的各类目ID

    // 查找类目节点
    function findCategoryNode(categoryId) {
        function find(nodes) {
            for(const key in nodes){
                const node = nodes[key];
                if(node.descriptionCategoryId == categoryId) return node;
                if(node.nodes && Object.keys(node.nodes).length > 0){
                    const found = find(node.nodes);
                    if(found) return found;
                }
            }
            return null;
        }
        return find(categoryData.result);
    }

    // 修改加载子类目方法
    function loadSubCategories(level, parentId) {
        const $nextSelect = $(`select[name="category${level}"]`);
        $nextSelect.empty().append(`<option value="">请选择</option>`);
        
        if(parentId) {
            const parentNode = findCategoryNode(parentId);
            if(parentNode && parentNode.nodes) {
                Object.values(parentNode.nodes).forEach(item => {
                    if(!item.disabled && level<3) {
                        $nextSelect.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
                    }else{
                        $nextSelect.append(new Option(item.descriptionTypeName, item.descriptionTypeId));
                    }
                });
            }
        }
        
        // 修改渲染方式（统一使用全量渲染）
        layui.form.render('select');
    }
    
    // 修改初始化方法
    function initFirstCategory() {
        var $select = $('select[name="category1"]');
        $select.empty().append('<option value="">一级类目</option>');
        
        Object.values(categoryData.result).forEach(item => {
            if(!item.disabled) {
                $select.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
            }
        });
        
        // 统一初始化渲染
        layui.form.render('select');
    }

    // 类目选择事件
    form.on('select(category)', function(data){
        const level = parseInt(data.elem.name.replace('category',''));
        const value = data.value;
        
        currentCategory[level] = value;
        
        // 清空后续选择
        for(let l = level + 1; l <= 3; l++) {
            currentCategory[l] = null;
            $(`select[name="category${l}"]`).val('').trigger('change');
        }
        
        if(level < 3 && value) {
            loadSubCategories(level + 1, value);
        }
        
        if (level === 3 && value) {
            // 获取类目属性
            var category2 = $(`select[name="category2"]`).val();
            $.ajax({
                url: './ajax.php?act=get_category_attributes',
                type: 'POST',
                data: { category2, category3: value },
                dataType: 'json',
                success: function(res) {
                    if(res.code === 0) {
                        renderAttributes(res.data); 
                    } else {
                        layer.msg(res.msg || '获取属性失败');
                    }
                }
            });
        }
    });

    // 初始化
    initFirstCategory();
    
    // 如果有编辑数据，初始化已选类目
    <?php if($row['category_ids']):?>
    var savedCategories = "<?=$row['category_ids']?>".split(',');
    const Attributes = <?=$Attributes?>;
    renderAttributes(Attributes);
    savedCategories.forEach((id, index) => {
        const level = index + 1;
        if(level > 3) return;
        
        currentCategory[level] = id;
        $(`select[name="category${level}"]`).val(id);
        
        if(level < 3) {
            loadSubCategories(level + 1, id);
        }
    });
    form.render('select');
    <?php endif;?>
    
    function renderAttributes(attributes) {
        const container = $('#specBox');
        container.empty(); // 清空现有属性
        
        attributes.forEach(attr => {
            
            const itemHtml = `
                        ${generateAttributeField(attr)}
                    `;
            /*
            const itemHtml = `
                <div class="layui-form-item">
                    <label class="layui-form-label">${attr.name}</label>
                    <div class="layui-input-block">
                        ${generateAttributeField(attr)}
                    </div>
                </div>
            `;
            const itemHtml = `
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">${attr.name}</label>
                        <div class="layui-input-block">
                            ${generateAttributeField(attr)}
                        </div>
                    </div>
                </div>
            `;*/
            container.append(itemHtml);
        });
        
        form.render(); // 重新渲染表单元素
    }
    
    function generateAttributeField(attr) {
        if (attr.type === 'SELECT') {
            let options = attr.options.map(opt => 
                `<option value="${opt.value}">${opt.label}</option>`
            ).join('');
            return `<select name="attributes[${attr.id}]" lay-verify="required">${options}</select>`;
        } else {
            return `<input type="text" name="attributes[${attr.id}]" 
                     placeholder="${attr.name}" class="layui-input">`;
                     
            /*return `<input type="text" 
                        name="attributes[${attr.id}]" 
                        placeholder="请输入${attr.name}"
                        class="layui-input">`;*/
        }
    }
    
    // 添加属性
    $('#addSpec').click(function(){
        var html = '<div class="layui-row spec-item" style="margin-top: 10px;">' +
            '<div class="layui-col-md5"><input type="text" name="specName" placeholder="属性名称" class="layui-input"></div>' +
            '<div class="layui-col-md5"><input type="text" name="specValue" placeholder="属性值" class="layui-input"></div>' +
            '<div class="layui-col-md2"><button type="button" class="layui-btn layui-btn-danger layui-btn-xs">删除</button></div>' +
            '</div>';
        $('#specBox').append(html);
    });

    // 删除属性
    $('#specBox').on('click', '.layui-btn-danger', function(){
        $(this).closest('.spec-item').remove();
    });

    // 图片上传
    upload.render({
        elem: '#uploadImg',
        url: '/upload/',
        multiple: true,
        done: function(res){
            $('#previewBox').append(
                '<div style="display: inline-block; margin-right: 10px;">' +
                '<img src="' + res.data.src + '" style="width: 100px; height: 100px;">' +
                '<div class="layui-progress" lay-filter="demo-' + res.index + '"><div class="layui-progress-bar" lay-percent="100%"></div></div>' +
                '</div>'
            );
        }
    });

    form.on('submit(submitBtn)', function(data){
        // 获取类目链的改进方案
        const categoryData = {
            ids: [],
            texts: []
        };
    
        [1,2,3].forEach(level => {
            const $select = $(`select[name="category${level}"]`);
            const selectedOption = $select.find('option:selected');
            
            if(selectedOption.val()) {
                categoryData.ids.push(selectedOption.val());
                categoryData.texts.push(selectedOption.text());
            }
        });
    
        // 添加到提交数据（推荐使用更结构化的数据格式）
        data.field.category_chain = {
            ids: categoryData.ids.join(','),
            texts: categoryData.texts.join(' > ')
        };
    
        // 调试输出
        console.log('类目ID链：', categoryData.ids);
        console.log('类目名称链：', categoryData.texts);
        console.log('结构化数据：', data.field.category_chain);
    
        // 提交前的验证示例
        if(categoryData.ids.length < 3) {
            layer.msg('请完整选择3级类目');
            return false;
        }
    
        // 这里添加你的提交逻辑
        // 建议使用JSON格式保存更结构化的数据
        data.field.category_full = JSON.stringify({
            level1: {id: categoryData.ids[0], name: categoryData.texts[0]},
            level2: {id: categoryData.ids[1], name: categoryData.texts[1]},
            level3: {id: categoryData.ids[2], name: categoryData.texts[2]}
        });
        
        const attributes = {};
        $('[name^="attributes["]').each(function() {
            const name = $(this).attr('name');
            const id = name.match(/\[(.*?)\]/)[1];
            attributes[id] = $(this).val();
        });
        
        data.field.attributes = JSON.stringify(attributes);
    
        console.log('完整提交数据：', data.field);
        $.ajax({
            url: './ajax.php?act=save_production',
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function(res) {
                layer.closeAll();
                if(res.code === 0) {
                    layer.msg('保存成功', {icon: 1});
                    setTimeout(() => {
                        location.href = 'product_list.php'; // 跳转到列表页
                    }, 1500);
                } else {
                    layer.msg(res.msg || '保存失败', {icon: 2});
                }
            },
            error: function(xhr) {
                layer.closeAll();
                layer.msg('请求失败: ' + xhr.statusText, {icon: 2});
            }
        });
        return false;
    });
});
</script>
</body>
</html>