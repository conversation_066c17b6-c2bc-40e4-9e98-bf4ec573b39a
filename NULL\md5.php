<?php
@header('Content-Type: application/json; charset=UTF-8');
$timestamp = (int)(microtime(true) * 1000);
$token = "9eff691ecf627ac673db9a8c63257613";
$time = 1748899256207??$timestamp;
$h = 12574478;
$data = '{"serviceId":"OrderListDataLineService.buyerOrderList","param":"{\"spm\":\"a260k.home2024.topmenu.dmyorder.663335e4O5KwF9\",\"page\":1,\"pageSize\":10}"}';
echo md5("$token&$time&$h&$data");


$curl = curl_init();

curl_setopt_array($curl, array(
   CURLOPT_URL => 'https://h5api.m.1688.com/h5/mtop.1688.trading.dataline.service/1.0/?jsv=2.7.0&appKey=12574478&t=1748715487860&sign=19eb83f76fac7968e36dccd883f97650&ecode=1&type=json&valueType=string&trackerConfig=%255Bobject%2520Object%255D&api=mtop.1688.trading.dataline.service&v=1.0&preventFallback=true&dataType=jsonp',
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'POST',
   CURLOPT_POSTFIELDS => 'data=%7B%22serviceId%22%3A%22OrderListDataLineService.buyerOrderList%22%2C%22param%22%3A%22%7B%5C%22page%5C%22%3A1%2C%5C%22pageSize%5C%22%3A10%7D%22%7D',
   CURLOPT_HTTPHEADER => array(
      'accept: application/json',
      'accept-language: zh-CN,zh;q=0.9',
      'origin: https://air.1688.com',
      'priority: u=1, i',
      'referer: https://air.1688.com/',
      'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
      'sec-ch-ua-mobile: ?0',
      'sec-ch-ua-platform: "Windows"',
      'sec-fetch-dest: empty',
      'sec-fetch-mode: cors',
      'sec-fetch-site: same-site',
      'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Cookie: taklid=2eac0f5f72f74542952214f141d11179; inquiry_preference=webIM; cookie2=1e39b661cd9099a25c70200f4581ea6f; t=29c904899f1976ed08631d4fa360b22a; _tb_token_=e8793b81657e6; ali_apache_tracktmp=c_w_signed=Y; xlly_s=1; leftMenuLastMode=EXPEND; leftMenuModeTip=shown; _m_h5_c=41cb00555c1f6303f6cf5c447245cbc4_1748674465942%3B7cf3fa36b888da386bdc97f874cae1ca; _samesite_flag_=true; tracknick=; mtop_partitioned_detect=1; _m_h5_tk=0458fc5b5f230c5c3bd6d2979ab9cbee_1748722462779; _m_h5_tk_enc=02d4ae9f2eef178be426e9fb9145048f; cna=4TpGILtz6FEBASQKQscqGq08; cookie1=UtU8%2BYIpmKO3X2ZAldFdXpnjWjmHUOPOPcVQzkE51bU%3D; cookie17=UUphyuwKIvl%2Ft5iAog%3D%3D; sgcookie=E100dSlUuuHzQsd3YPOx3v1FEICIhZjPRzMeJ0ZamdvnWfm%2FhlNkJFhXx7zPofDal%2BUMrMa%2BFymdfeqmNni6ejmRcuKeZbCUKFhLaUZS%2F91WbuYVWhaYBPHdpwIHQiY50u31; sg=20c; csg=76200c92; lid=tb972052812; unb=2200539667150; uc4=id4=0%40U2grEaqHxefPPDr4ewYfRZVlpmi7b8d3&nk4=0%40FY4HWy3OqrtRTsWCgY3HqwMJNNbKmw%3D%3D; _nk_=tb972052812; __cn_logon__=true; __cn_logon_id__=tb972052812; ali_apache_track=c_mid=b2b-2200539667150491c7|c_lid=tb972052812|c_ms=1; last_mid=b2b-2200539667150491c7; _csrf_token=1748713525755; isg=BF9fYk7mSy35gEC1qrVKACfL7rPpxLNmaAE0s_GscY5VgH8C-ZVQttxTQhD-b4ve; tfstk=g9aK55DNpOXHhUUKsWSMEdQVRa5GoGVU_JPXrYDHVReTEJhoVDGW1CMTUvmnRMP7NAwyruVIr4p8nA8oZMWyyPe_ZzqnqW1Ry7Njd_lHxUnSIRlur6ouW_3USkYoK9u8N-0RntbcoWPED46cnCg7zp0SNaD5jD_LBsg5ntbcSWPEz460Ep6WgOhxG4GIVXg16jlmP3TQFAisGjvIP8wW55GxM3tIdDg16bksF4g7FV1tZAMSaDtKUHMDyo2h6gFBU8YWPPhKfGVICy4FZXndIWH9P3zt9TMbOALWPvbY-RVTt9L4s2zbHj2h5eeYNSrSXzp1HYzT1yZ7_pBI_kc8Nj3fuIH_J7UtYlvBAj3KpmaIxZ18O0ZTW24CUnPg6vixSljNK00LpokqAGWae5H3Fyn6pODum54IX-6MW8o8Vy0_RO_A4y40kRwDnxhkv1C943oI_MGZsnlSKbYI6xfgs3-rXGltn1C943oI_fHcsAxy4cIN.; _user_vitals_session_data_={"user_line_track":true,"ul_session_id":"r0q4yr4v2eo","last_page_id":"air.1688.com%2Fodbhb6wbkni"}',
      'content-type: application/x-www-form-urlencoded'
   ),
));

$response = curl_exec($curl);

curl_close($curl);
$json = json_decode($response,true);
exit($json['data']['data']['result']);
echo $response;
