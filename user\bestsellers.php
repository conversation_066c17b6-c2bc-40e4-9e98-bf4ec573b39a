<?php
include("../includes/common.php");
$categoryData = file_get_contents('./config/中文类目.json');
?>
<style>
    .category-selector {
        display: flex;
        gap: 15px;
        align-items: center;
    }
    .category-selector .layui-input-inline {
        flex: 1;
    }
    .price-range {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .price-range .layui-input {
        width: 120px;
    }
    .filter-row {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }
    .filter-item {
        display: flex;
        align-items: center;
        gap: 10px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <form class="layui-form layui-form-pane">
                <fieldset class="layui-elem-field form-section">
                    <legend><i class="layui-icon layui-icon-chart-screen" style="color:#1E9FFF"></i> 热销商品看板</legend>
                    <div class="layui-field-box">
                        <!-- 类目选择 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">商品类目</label>
                            <div class="layui-input-block category-selector">
                                <div class="layui-input-inline">
                                    <select name="category1" lay-filter="category" lay-search>
                                        <option value="">一级类目</option>
                                    </select>
                                </div>
                                <i class="layui-icon layui-icon-right"></i>
                                <div class="layui-input-inline">
                                    <select name="category2" lay-filter="category" lay-search>
                                        <option value="">二级类目</option>
                                    </select>
                                </div>
                                <i class="layui-icon layui-icon-right"></i>
                                <div class="layui-input-inline">
                                    <select name="category3" lay-filter="category" lay-search>
                                        <option value="">三级类目</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 标题搜索 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">商品标题</label>
                            <div class="layui-input-block">
                                <input type="text" name="title_keyword" placeholder="请输入商品标题关键词" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        
                        <!-- 筛选条件 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">筛选条件</label>
                            <div class="layui-input-block">
                                <div class="filter-row">
                                    <!-- 均价区间 -->
                                    <div class="filter-item">
                                        <span>均价：</span>
                                        <div class="price-range">
                                            <input type="number" name="min_price" placeholder="最低价" class="layui-input">
                                            <span>-</span>
                                            <input type="number" name="max_price" placeholder="最高价" class="layui-input">
                                        </div>
                                    </div>
                                    
                                    <!-- 销量区间 -->
                                    <div class="filter-item">
                                        <span>销量：</span>
                                        <div class="price-range">
                                            <input type="number" name="min_sold" placeholder="最少销量" class="layui-input">
                                            <span>-</span>
                                            <input type="number" name="max_sold" placeholder="最多销量" class="layui-input">
                                        </div>
                                    </div>
                                    
                                    <!-- 加购率区间 -->
                                    <div class="filter-item">
                                        <span>加购率(%)：</span>
                                        <div class="price-range">
                                            <input type="number" name="min_cart_rate" placeholder="最低" class="layui-input" step="0.1">
                                            <span>-</span>
                                            <input type="number" name="max_cart_rate" placeholder="最高" class="layui-input" step="0.1">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 排序选择 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">排序方式</label>
                            <div class="layui-input-block">
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <span>排序字段：</span>
                                        <select name="sort_field" lay-filter="sort_field">
                                            <option value="">默认排序</option>
                                            <option value="soldCount">销量</option>
                                            <option value="avgPrice">均价</option>
                                            <option value="gmvSum">GMV</option>
                                            <option value="convToCartPdp">加购率</option>
                                            <option value="sessionCount">会话数</option>
                                            <option value="accessibility">可售率</option>
                                            <option value="daysInPromo">促销天数</option>
                                            <option value="nullableCreateDate">创建日期</option>
                                        </select>
                                    </div>
                                    
                                    <div class="filter-item">
                                        <span>排序方向：</span>
                                        <select name="sort_order" lay-filter="sort_order">
                                            <option value="desc">降序 (高→低)</option>
                                            <option value="asc">升序 (低→高)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="search"><i class="layui-icon layui-icon-search"></i> 搜索</button>
                                <button type="reset" class="layui-btn layui-btn-primary"><i class="layui-icon layui-icon-refresh"></i> 重置</button>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </form>
            <!-- 表格区域 -->
            <table id="goodsTable" lay-filter="goodsTable"></table>
        </div>
    </div>
</div>
<script>
layui.use(['table', 'form', 'util', 'dropdown'], function(){
    var form = layui.form,
        table = layui.table,
        util = layui.util,
        $ = layui.$,
        dropdown = layui.dropdown; // 🔥 新增下拉菜单组件
    // 类目数据（替换为你的API返回数据）
    var categoryData = {};
    var currentSelecting = {}; // 记录当前选择状态
    
    // 初始加载
    // 类目数据
    var categoryData = <?=$categoryData?>;
    var currentCategory = {1: null, 2: null, 3: null}; // 当前选择的各类目ID

    // 查找类目节点
    function findCategoryNode(categoryId) {
        function find(nodes) {
            for(const key in nodes){
                const node = nodes[key];
                if(node.descriptionCategoryId == categoryId) return node;
                if(node.nodes && Object.keys(node.nodes).length > 0){
                    const found = find(node.nodes);
                    if(found) return found;
                }
            }
            return null;
        }
        return find(categoryData.result);
    }

    // 修改加载子类目方法
    function loadSubCategories(level, parentId) {
        const $nextSelect = $(`select[name="category${level}"]`);
        $nextSelect.empty().append(`<option value="">请选择</option>`);
        
        if(parentId) {
            const parentNode = findCategoryNode(parentId);
            if(parentNode && parentNode.nodes) {
                Object.values(parentNode.nodes).forEach(item => {
                    if(!item.disabled && level<3) {
                        $nextSelect.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
                    }else{
                        $nextSelect.append(new Option(item.descriptionTypeName, item.descriptionTypeId));
                    }
                });
            }
        }
        
        // 修改渲染方式（统一使用全量渲染）
        layui.form.render('select');
    }
    
    // 修改初始化方法
    function initFirstCategory() {
        var $select = $('select[name="category1"]');
        $select.empty().append('<option value="">一级类目</option>');
        
        Object.values(categoryData.result).forEach(item => {
            if(!item.disabled) {
                $select.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
            }
        });
        
        // 统一初始化渲染
        layui.form.render('select');
    }
    
    // 初始化
    initFirstCategory();
    
    // 类目选择事件
    form.on('select(category)', function(data){
        const level = parseInt(data.elem.name.replace('category',''));
        const value = data.value;
        
        currentCategory[level] = value;
        
        // 清空后续选择
        for(let l = level + 1; l <= 3; l++) {
            currentCategory[l] = null;
            $(`select[name="category${l}"]`).val('').trigger('change');
        }
        
        if(level < 3 && value) {
            loadSubCategories(level + 1, value);
        }
        if(level==3){
            // 搜索时包含所有筛选条件
            searchGoods();
        }
        console.log(value);
    });
    
    // 获取搜索参数
    function getSearchParams() {
        var params = {
            categorie: $('select[name="category3"]').val() || '',
            title_keyword: $('input[name="title_keyword"]').val() || '',
            min_price: $('input[name="min_price"]').val() || '',
            max_price: $('input[name="max_price"]').val() || '',
            min_sold: $('input[name="min_sold"]').val() || '',
            max_sold: $('input[name="max_sold"]').val() || '',
            min_cart_rate: $('input[name="min_cart_rate"]').val() || '',
            max_cart_rate: $('input[name="max_cart_rate"]').val() || '',
            sort_field: $('select[name="sort_field"]').val() || '',
            sort_order: $('select[name="sort_order"]').val() || 'desc'
        };
        
        // 清理空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '') {
                delete params[key];
            }
        });
        
        console.log('搜索参数:', params); // 调试信息
        return params;
    }
    
    // 搜索商品
    function searchGoods() {
        var params = getSearchParams();
        console.log('🔍 准备发送搜索请求，参数：', params);
        
        table.reload('goodsTable', {
            where: params,
            page: { curr: 1 }
        });
    }
    
    // 搜索表单提交
    form.on('submit(search)', function(data){
        searchGoods();
        return false;
    });
    
    // 重置表单
    $('button[type="reset"]').on('click', function(){
        // 清空所有输入框
        $('input[name="title_keyword"]').val('');
        $('input[name="min_price"]').val('');
        $('input[name="max_price"]').val('');
        $('input[name="min_sold"]').val('');
        $('input[name="max_sold"]').val('');
        $('input[name="min_cart_rate"]').val('');
        $('input[name="max_cart_rate"]').val('');
        
        // 重置选择框
        $('select[name="category1"]').val('');
        $('select[name="category2"]').val('');
        $('select[name="category3"]').val('');
        $('select[name="sort_field"]').val('');
        $('select[name="sort_order"]').val('desc');
        
        // 重新渲染表单
        form.render();
        
        // 重新加载表格（清空筛选）
        table.reload('goodsTable', {
            where: {},
            page: { curr: 1 }
        });
        
        return false;
    });
    
    // 排序选择事件
    form.on('select(sort_field)', function(data){
        if(data.value) {
            searchGoods();
        }
    });
    
    form.on('select(sort_order)', function(data){
        if($('select[name="sort_field"]').val()) {
            searchGoods();
        }
    });
    
    // 实时搜索 - 输入框失去焦点时搜索
    $('input[name="title_keyword"], input[name="min_price"], input[name="max_price"], input[name="min_sold"], input[name="max_sold"], input[name="min_cart_rate"], input[name="max_cart_rate"]').on('blur', function(){
        // 延迟500毫秒执行搜索，避免频繁请求
        clearTimeout(window.searchTimeout);
        window.searchTimeout = setTimeout(function(){
            searchGoods();
        }, 500);
    });
    
    // 回车键搜索
    $('input[name="title_keyword"], input[name="min_price"], input[name="max_price"], input[name="min_sold"], input[name="max_sold"], input[name="min_cart_rate"], input[name="max_cart_rate"]').on('keypress', function(e){
        if(e.which === 13) { // 回车键
            searchGoods();
        }
    });
    
    // 新版表格配置
    table.render({
        elem: '#goodsTable',
        url: 'ajax.php?act=bestsellers',
        toolbar: '#toolbarDemo',
        height: 'full-280', // 🔥 调整高度以适应更多搜索条件
        loading: true,
        text: { none: '<i class="layui-icon layui-icon-nodata"></i> 暂无数据' }, // 🔥 新图标
        cols: [[
             {field: 'photo', title: '商品图片', width: 100, fixed: 'left', 
             templet: '<a href="{{ d.link }}" target="_blank">\
              <div class="layui-table-cell-img" >\
                <img src="{{ d.photo }}" style="object-fit:cover;width:80px;height:80px;padding:0;box-sizing:border-box" alt="点击访问原 商品" lay-on="preview">\
              </div>\
            </a>'},
            
            {field: 'name', title: '商品标题/品牌', width: 180, fixed: 'left',
             templet: function(d){
                 return '<div class="layui-table-cell-main"><div class="title">'+d.name+'</div><div class="sub-info">'
                    + '<span class="layui-badge-dot layui-bg-orange"></span>'+ d.brand
                    + '</div></div>'
                 ;
             }},
            {field: 'name', title: '1/3级类目', width: 150, fixed: 'left',
             templet: function(d){
                 return '<span class="layui-badge-dot layui-bg-orange"></span>'+ d.category1+ '</br><span class="layui-badge-dot layui-bg-blue"></span>'+ d.category3;
             }},
            // 销售子列
            {field: 'soldCount', title: '销量', width: 80, sort: true, 
             templet: '<span class="layui-font-16 layui-font-bold">{{ d.soldCount }}</span>'},
            {field: 'gmvSum', title: 'GMV', width: 100, sort: true, templet: d => formatGMV(d.gmvSum)},
            {field: 'avgPrice', title: '均价', width: 80, sort: true, templet: d => '<span class="layui-font-money">¥'+d.avgPrice+'</span>'},
            {field: 'salesDynamics', title: '趋势', width: 80, sort: true,
             templet: d => '<div class="layui-table-trend"><span class="layui-'+ (d.salesDynamics>0?'up':'down') +'">'+d.salesDynamics+'%</span></div>'},
            {field: 'convToCartPdp', title: '加购率', width: 80, sort: true, templet: d => progressCircle(d.convToCartPdp)},
            {field: 'sessionCount', title: '会话', width: 80, sort: true},
            
            // 库存子列
            {field: 'stock', title: '库存', width: 80, sort: true},
            {field: 'avgDeliveryDays', title: '配送', width: 80, templet: d => '<i class="layui-icon layui-icon-log"></i> '+d.avgDeliveryDays+'</br>'+(d.salesSchema=='FBO'?'本土店':'<img src="https://docs.ozon.ru/global/flags/china.svg">')},
            {field: 'accessibility', title: '可售率', width: 80, sort: true, templet: d => progressCircle(d.accessibility*100)},
            
            // 促销子列
            {field: 'daysInPromo', title: '促销天数', width: 100, sort: true},
            {field: 'promoRevenueShare', title: '促销占比', width: 100, sort: true, templet: d => progressCircle(d.convToCart)},
            {field: 'drr', title: '推广费用占比', width: 100, sort: true, templet: d => progressCircle(d.drr)},
            {field: 'discount', title: '折扣', width: 80, sort: true, templet: d => '<span class="layui-badge layui-bg-red">'+d.discount+'%</span>'},
            {field: 'localIndex', title: '本地化', width: 80, sort: true},
            {field: 'nullableCreateDate', title: '创建日期', width: 110, sort: true, templet: d => util.toDateString(d.nullableCreateDate || new Date(), 'yyyy-MM-dd')}
        ]],
        page: {
            layout: ['prev', 'page', 'next', 'count', 'skip', 'limit'],
            limits: [10, 20, 50], // 🔥 新增100条选项
            theme: '#1E9FFF',
            groups: 3
        },
        limit: 20,
        done: function(){
            
        },
        parseData: function(res){ // 数据解析
                return {
                    "code": 0,
                    "msg": "",
                    "count": res.totals,
                    "data": res.items
                };
            }
        
    });
    table.reload('goodsTable', {
        lineStyle: 'height: 100px;'
    });

    // 🔥 新版工具函数
    const formatGMV = num => {
        return num >= 1e8 ? (num/1e8).toFixed(2)+'亿' 
             : num >= 1e4 ? (num/1e4)+'万' 
             : num.toLocaleString();
    }

    const stockStatus = d => {
        const total = parseInt(d.stock) || 0;
        return total < 50 ? `<span class="layui-font-red">${total}⚠️</span>` 
             : total < 200 ? `<span class="layui-font-orange">${total}</span>` 
             : `<span class="layui-font-green">${total}</span>`;
    };
    const progressCircle = percent => `
        <div class="layui-progress-circle" style="width:36px;height:36px" data-percent="${percent}">
            <span>${percent}%</span>
        </div>`;

});
// 树形结构样式优化
layui.$(`<style>
.layui-tree {
  padding-left: 0;
}
.category-node .layui-tree-entry {
  padding: 8px 15px;
  border-radius: 4px;
}
.category-node.active .layui-tree-entry {
  background: #e8f4ff;
}
.layui-tree li ul {
  padding-left: 25px;
}
.root-node > .layui-tree-entry {
  font-weight: bold;
}
.node-label {
  margin-left: 8px;
}
</style>`).appendTo('head');
// 🔥 新增CSS3动画
layui.$('<style>\
.layui-progress-circle { position:relative; border-radius:50%; background:#f8f8f8; }\
.layui-progress-circle span { position:absolute; left:50%; top:50%; transform:translate(-50%,-50%); font-size:12px }\
.layui-progress-circle:after { content:""; display:block; padding-top:100%; }\
.layui-table-trend .layui-up { color:#5FB878 }\
.layui-table-trend .layui-down { color:#FF5722 }\
.layui-table-grid td { transition: all 0.3s; }\
.layui-table-grid tr:hover td { background-color:#fbfbfb !important; }\
</style>').appendTo('head');
</script>