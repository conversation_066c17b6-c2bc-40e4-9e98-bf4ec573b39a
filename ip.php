<?php
require './includes/common.php';

$url = 'https://reproxy.network/api/buy.php';
$data = array(
    'type' => 'ipv6',
    'count' => 2,
    'threads_count' => 50,
    'auth_type' => 'ip',
    'auth_ip' => '*********',
    'renewal' => true
);
$options = array(
    'http' => array(
        'header'  => "reproxy-auth-token: 29f5514836ce0dd984637ef4a7e252c1\r\n".
                    "Content-Type: application/json; charset=UTF-8\r\n",
        'method'  => 'POST',
        'content' => json_encode($data)
    )
);
$context  = stream_context_create($options);
$result = file_get_contents($url, false, $context);
exit($result);