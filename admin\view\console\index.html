<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>仪表盘</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-icon {
            font-size: 40px;
            margin-bottom: 10px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
        }
        
        .system-info {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="welcome-card">
                <h2>欢迎来到海豚ERP管理后台</h2>
                <p>您好，管理员！当前系统运行正常。</p>
                <p id="current-time"></p>
            </div>
        </div>
    </div>
    
    <div class="layui-row layui-col-space15" style="margin-top: 20px;">
        <div class="layui-col-md12">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon layui-icon layui-icon-user" style="color: #1890ff;"></div>
                    <div class="stat-value" id="total-users">-</div>
                    <div class="stat-label">总用户数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon layui-icon layui-icon-username" style="color: #52c41a;"></div>
                    <div class="stat-value" id="today-users">-</div>
                    <div class="stat-label">今日新增用户</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon layui-icon layui-icon-home" style="color: #fa8c16;"></div>
                    <div class="stat-value" id="total-stores">-</div>
                    <div class="stat-label">总店铺数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon layui-icon layui-icon-component" style="color: #722ed1;"></div>
                    <div class="stat-value" id="total-products">-</div>
                    <div class="stat-label">总商品数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon layui-icon layui-icon-form" style="color: #eb2f96;"></div>
                    <div class="stat-value" id="total-orders">-</div>
                    <div class="stat-label">总订单数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon layui-icon layui-icon-template" style="color: #f5222d;"></div>
                    <div class="stat-value" id="today-orders">-</div>
                    <div class="stat-label">今日订单数</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="layui-row layui-col-space15" style="margin-top: 20px;">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <i class="layui-icon layui-icon-engine"></i> 系统状态
                </div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <tbody>
                            <tr>
                                <td>数据库连接</td>
                                <td><span class="layui-badge layui-bg-green">正常</span></td>
                            </tr>
                            <tr>
                                <td>队列服务</td>
                                <td><span class="layui-badge layui-bg-green" id="queue-status">检查中...</span></td>
                            </tr>
                            <tr>
                                <td>文件系统</td>
                                <td><span class="layui-badge layui-bg-green">正常</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <i class="layui-icon layui-icon-set"></i> 快捷操作
                </div>
                <div class="layui-card-body">
                    <div class="layui-btn-group">
                        <button class="layui-btn layui-btn-sm" onclick="openPage('view/user/list.html')">
                            <i class="layui-icon layui-icon-user"></i> 用户管理
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="openPage('view/store/list.html')">
                            <i class="layui-icon layui-icon-home"></i> 店铺管理
                        </button>
                    </div>
                    <br><br>
                    <div class="layui-btn-group">
                        <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="openPage('view/product/list.html')">
                            <i class="layui-icon layui-icon-component"></i> 商品管理
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="openPage('view/monitor/logs.html')">
                            <i class="layui-icon layui-icon-file"></i> 系统日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    <script>
        layui.use(['jquery'], function() {
            var $ = layui.jquery;
            
            // 更新当前时间
            function updateTime() {
                var now = new Date();
                var timeStr = now.getFullYear() + '年' + 
                             (now.getMonth() + 1) + '月' + 
                             now.getDate() + '日 ' + 
                             now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0') + ':' + 
                             now.getSeconds().toString().padStart(2, '0');
                $('#current-time').text(timeStr);
            }
            
            updateTime();
            setInterval(updateTime, 1000);
            
            // 加载统计数据
            function loadStats() {
                $.ajax({
                    url: 'ajax.php?act=dashboard_stats',
                    type: 'GET',
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            $('#total-users').text(res.data.total_users);
                            $('#today-users').text(res.data.today_users);
                            $('#total-stores').text(res.data.total_stores);
                            $('#total-products').text(res.data.total_products);
                            $('#total-orders').text(res.data.total_orders);
                            $('#today-orders').text(res.data.today_orders);
                        } else {
                            console.error('获取统计数据失败:', res.msg);
                        }
                    },
                    error: function() {
                        console.error('请求统计数据失败');
                    }
                });
            }
            
            // 检查队列状态
            function checkQueueStatus() {
                $.ajax({
                    url: 'ajax.php?act=queue_status',
                    type: 'GET',
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            $('#queue-status').removeClass('layui-bg-orange layui-bg-red').addClass('layui-bg-green').text('正常');
                        } else {
                            $('#queue-status').removeClass('layui-bg-green layui-bg-orange').addClass('layui-bg-red').text('异常');
                        }
                    },
                    error: function() {
                        $('#queue-status').removeClass('layui-bg-green layui-bg-red').addClass('layui-bg-orange').text('检查失败');
                    }
                });
            }
            
            // 初始化
            loadStats();
            checkQueueStatus();
            
            // 定时刷新
            setInterval(loadStats, 30000); // 30秒刷新一次统计数据
            setInterval(checkQueueStatus, 60000); // 1分钟检查一次队列状态
        });
        
        // 打开页面的函数，供快捷操作按钮使用
        function openPage(url) {
            if (parent && parent.layui && parent.layui.admin) {
                parent.layui.admin.tab.add({
                    title: '页面',
                    href: url
                });
            } else {
                // 如果不在iframe中，直接跳转
                window.location.href = url;
            }
        }
    </script>
</body>
</html> 