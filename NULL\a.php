<?php
@header('Content-Type: application/json; charset=UTF-8;');
include("../includes/common.php");
$row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.posting_number=:posting_number LIMIT 1", [':posting_number' => '0180556424-0109-1']);
if ($row) {
    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
    $url = $client->packagelabel($row);
    if ($url != false) {
        $result = ['code' => 0, 'msg' => '成功获取面单', 'url' => $url];
    } else {
        $result = ['code' => -3, 'msg' => '下载订单面单失败'];
    }
}
exit(json_encode($result));
$client = new \lib\OzonApiClient(2637028,'dedf5441-d6c7-41d6-b7ac-0b04627078de');
$resurn = $client->returnsrfbslist(['from'=>date('Y-m-d H:i:s', strtotime('-120 days')),'to'=>date('Y-m-d H:i:s')]);
exit;
exit(json_encode(ali1688data('https://detail.1688.com/offer/891486987194.html?spm=a1688g.extenstion&kj_agent_plugin=aibuy&kjSource=pc&lang=zh')));
$data = $DB->find('dailiip', '*', ['id' => 12]);
$cookie = '__Secure-ext_xcid=14ccb1f7ca87166ff37ffbbac07986c7; sc_company_id=2972702; bacntid=5586614; __Secure-ETC=273e4b3aef78e20243a97002859546de; xcid=47933aaf1fc2044087e0bed4528c72f7; __Secure-access-token=8.0.gZRcNSKtSn6A3SZ2u5CQRQ.34.AbsUT-aPbo6-IjyQCoMk5ljQrpPqYnQQG10lDq7HeFADYApECjUTbiKiNp2vzRGA4zBxTbyayjvjoVIkpX5dNIXb_rJTKxKgUBGN5UKOlHA1..20250701120655.Tku1_wg5wtvpKPS0yioGFx6KWrh7bqtoNXr5jmCx4rw.1712d660b4ce1d51a; __Secure-refresh-token=8.0.gZRcNSKtSn6A3SZ2u5CQRQ.34.AbsUT-aPbo6-IjyQCoMk5ljQrpPqYnQQG10lDq7HeFADYApECjUTbiKiNp2vzRGA4zBxTbyayjvjoVIkpX5dNIXb_rJTKxKgUBGN5UKOlHA1..20250701120655.S1dubsePmuU7koqePIBvi2BUAHvpYkU__o27wAieybQ.1b9437f75e171b8bd; __Secure-ab-group=34; __Secure-user-id=0; abt_data=7.v0Pett3OkZsOF-dDRNux41nk8VzrJjX5ciDLRZjQ1nDPTlRQ1kUm9FF8l89xS5kNld6bLp-WDzYi2ytk8guNIsXyTklgwOwD1Cn4XVWFw3sVmecB8KYjKioG3rbSOhEDFrqaPeZ55sjoeCZPL115t29jdPzsZMRjsQLTak5LXRXAOzpOIZyUUKavk49qGt4Sd0o3uS4yzDOx10hrBSRYruOxaF7HN6Sp9y33RSmODOvO4nLqISW0jnOhoKVp6Djfbu-j-xOr3ClUuR1E3yk7TT_OYqpS6xczCbuTPR7bx97JEci3sow8CnXE-v328ERmW-9sJAKgeMNJd9N9waXB9JScnoZYw5E41zfeyAczzvL2LqonH2sIx4NUyu0RTIMuBtrDSMOZWSU-lb4V-6FwLnSlSGkPiHZf24mi_DmfbTy8UVSxTMvJq-s-mpBShwMOgMaozRGe2DpierqHiJL_UUH5VkAyTG4DWo4u';
$data['cookie'] = $cookie;
$res = get_curl('http://127.0.0.1:11/api/v2',json_encode(['url'=>urldecode('%2F%2Fwww.ozon.ru%2Fproduct%2F1744626946%2F%3Flayout_container%3D').'pdpPage2column&layout_page_index=2&__rr=2&abt_att=3&origin_referer=www.ozon.ru','data'=>$data]),0,0,0,0,0,['Content-Type: application/json']);
//$res = get_curl('http://127.0.0.1:11/api/sku',json_encode(['sku'=>strval(2157941929),'limit'=>10,'language'=>'ru','data'=>$data]),0,0,0,0,0,['Content-Type: application/json']);
exit($res);

$curl = curl_init();

curl_setopt_array($curl, array(
   CURLOPT_URL => 'https://overseaplugin.1688.com/offer/getOverseasOfferDetail',
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'POST',
   CURLOPT_POSTFIELDS =>'{"offerId":"865599123925","language":"zh_CN","currency":"CNY"}',
   
   CURLOPT_HTTPHEADER => array(
      'priority: u=1, i',
      'sec-fetch-storage-access: active',
      'content-type: application/json'
   ),
));

$response = curl_exec($curl);

curl_close($curl);
echo $response;exit;


$curl = curl_init();

curl_setopt_array($curl, array(
   CURLOPT_URL => 'https://overseaplugin.1688.com/recommend/sameOfferRecommend',
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'POST',
   CURLOPT_POSTFIELDS =>'{"beginPage":1,"pageSize":20,"keyword":"","language":"zh_CN","currency":"CNY","region":"HK","source":"www.ozon.ru","terminalId":"Chrome_136.0.0.0","version":"0.0.16","imageId":"1817608448862653217","priceStart":"","priceEnd":""}',
   CURLOPT_HTTPHEADER => array(
      'priority: u=1, i',
      'sec-fetch-storage-access: active',
      'content-type: application/json'
   ),
));

$response = curl_exec($curl);

curl_close($curl);
echo $response;exit;