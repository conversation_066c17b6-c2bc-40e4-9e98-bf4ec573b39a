<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="format-detection" content="telephone=no">
    <title>智能物流管理系统</title>
    <style>
        :root {
            --primary-color: #2196F3;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
        }

        body {
            margin: 0;
            padding: 8px;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-y: auto;
            padding-bottom: 90px;
            font-size: 14px;
            line-height: 1.4;
        }

        #camera-container {
            position: relative;
            width: 100%;
            height: 35vh;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            margin: 8px 0 12px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        #camera-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .scan-overlay {
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 85%;
            height: 80px;
            border: 2px solid #FF5722;
            border-radius: 12px;
            animation: pulse 2s infinite;
            box-shadow: 0 0 15px rgba(255, 87, 34, 0.3);
        }

        .scan-tip {
            color: #FF5722;
            text-align: center;
            position: absolute;
            width: 100%;
            top: 100%;
            font-size: 12px;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }

        .input-group {
            margin: 12px 0;
            display: flex;
            gap: 8px;
            align-items: stretch;
        }

        .form-input {
            flex: 1;
            padding: 14px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .product-info {
            background: white;
            padding: 8px;
            border-radius: 12px;
            margin: 12px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            max-height: 55vh;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .product-image {
            width: 100%;
            height: 120px;
            object-fit: contain;
            margin: 8px 0;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .button-group {
            display: flex;
            gap: 6px;
            justify-content: center;
            position: fixed;
            bottom: 8px;
            left: 8px;
            right: 8px;
            background: white;
            padding: 12px 8px;
            border-radius: 16px;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            z-index: 100;
            border-top: 1px solid #f0f0f0;
        }

        .action-button {
            flex: 1;
            padding: 14px 8px;
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            min-width: 80px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .action-button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 4px rgba(0,0,0,0.2);
        }

        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 0.4; }
            100% { opacity: 0.8; }
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.85);
            color: white;
            padding: 20px 30px;
            border-radius: 16px;
            display: none;
            z-index: 1001;
            font-size: 16px;
            font-weight: 500;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
        }

        .product-item {
            background: white;
            padding: 12px;
            border-radius: 12px;
            margin: 8px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
            transition: all 0.2s ease;
        }

        .product-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .product-item.selected {
            background: #e0f7fa; /* 勾选后卡片颜色 */
        }

        /* 折叠状态样式 */
        .product-item.shipped-collapsed {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #4CAF50;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
        }

        .collapsed-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 8px 8px 35px; /* 左边距留给checkbox */
            gap: 8px;
            min-height: 44px; /* 确保触摸友好的最小高度 */
        }

        .status-icon {
            font-weight: 600;
            font-size: 12px;
            white-space: nowrap;
            padding: 2px 6px;
            border-radius: 8px;
            background: rgba(255,255,255,0.8);
            min-width: 60px;
            text-align: center;
        }

        .simplified-info {
            flex: 1;
            font-size: 13px;
            color: #555;
            line-height: 1.3;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .expand-btn {
            padding: 8px 12px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            white-space: nowrap;
            min-height: 32px;
            transition: all 0.2s ease;
        }

        .expand-btn:active {
            background: #1976D2;
            transform: scale(0.95);
        }

        /* 展开状态 */
        .product-item.shipped-collapsed.expanded .details-container {
            display: block !important;
        }

        .details-container {
            transition: all 0.3s ease;
        }

        /* 产品详情样式 */
        .product-details {
            margin-top: 10px;
        }

        .product-name {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #333;
        }

        .product-info-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .product-info-list li {
            padding: 6px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            color: #555;
            display: flex;
            align-items: center;
        }

        .product-info-list li:last-child {
            border-bottom: none;
        }

        .product-info-list li:before {
            content: "•";
            margin-right: 8px;
            color: var(--primary-color);
            font-weight: bold;
        }

        @media screen and (max-width: 600px) {
            body { 
                padding: 6px; 
                padding-bottom: 100px; 
            }
            
            #camera-container { 
                height: 32vh; 
                margin: 6px 0 10px 0; 
            }
            
            .form-input { 
                font-size: 16px; /* 保持16px避免iOS缩放 */
                padding: 12px; 
            }
            
            .input-group { 
                margin: 10px 0; 
            }
            
            .action-button { 
                font-size: 13px; 
                padding: 12px 6px; 
                min-width: 70px;
            }
            
            .product-image { 
                height: 100px; 
            }
            
            .product-item {
                margin: 6px 0;
                padding: 10px;
            }
            
            .button-group { 
                bottom: 6px; 
                padding: 10px 6px; 
                gap: 4px;
            }
            
            .collapsed-info {
                padding: 6px 6px 6px 30px;
                gap: 6px;
                min-height: 40px;
            }
            
            .status-icon {
                font-size: 11px;
                min-width: 55px;
            }
            
            .simplified-info {
                font-size: 12px;
            }
            
            .expand-btn {
                padding: 6px 10px;
                font-size: 10px;
                min-height: 28px;
            }
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 180px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            z-index: 1000;
            bottom: 100%;
            margin-bottom: 8px;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            overflow: hidden;
        }

        .dropdown-content a {
            color: #333;
            padding: 14px 16px;
            text-decoration: none;
            display: block;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f5f5f5;
        }

        .dropdown-content a:last-child {
            border-bottom: none;
        }

        .dropdown-content a:active {
            background-color: #e3f2fd;
            color: var(--primary-color);
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }

        .show {
            display: block;
        }

        .exception-modal {
            display: none;
            position: fixed;
            z-index: 300;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        .exception-modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border: none;
            width: 90%;
            max-width: 400px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .exception-form label {
            display: block;
            margin: 10px 0 5px;
            font-weight: bold;
        }

        .exception-form select,
        .exception-form textarea {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            box-sizing: border-box;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .exception-form select:focus,
        .exception-form textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .exception-form button {
            margin-top: 15px;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.2s ease;
            min-height: 48px;
        }

        .exception-form button:active {
            transform: translateY(1px);
        }

        .btn-submit {
            background-color: var(--success-color);
        }

        .btn-cancel {
            background-color: #f44336;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div id="camera-container">
        <video id="camera-preview" playsinline autoplay></video>
        <div class="scan-overlay">
            <div class="scan-tip">请将条码置于橙色框内扫描</div>
        </div>
    </div>

    <div class="input-group">
        <input type="text" class="form-input" placeholder="输入物流单号" id="trackingNumber">
        <button class="action-button" style="background: var(--success-color)" 
                onclick="handleManualInput()">提交</button>
    </div>

    <div class="product-info" id="productInfo">
        <div id="product-list"></div>
    </div>

    <div class="button-group">
        <button class="action-button" style="background: var(--primary-color)" 
                onclick="handlePrint()">打印标签</button>
        <button class="action-button" style="background: var(--success-color)" 
                onclick="openMaterialCostModal()">标记发货</button>
        <div class="dropdown">
            <button class="action-button" style="background: var(--warning-color)" 
                    onclick="toggleDropdown()">异常问题</button>
            <div id="dropdown-content" class="dropdown-content">
                <a href="#" onclick="openExceptionModal('query_exception')">查询不到物流单号</a>
                <a href="#" onclick="openExceptionModal('tracking_number_error')">国际单号有问题</a>
                <a href="#" onclick="openExceptionModal('other_exception')">其他异常</a>
            </div>
        </div>
    </div>

    <!-- Material Cost Modal -->
    <div id="materialCostModal" class="exception-modal" style="z-index: 400;">
        <div class="exception-modal-content">
            <span class="close" onclick="closeMaterialCostModal()">&times;</span>
            <h2>选择打包物料费</h2>
            <form id="materialCostForm" class="exception-form">
                <div style="padding: 10px 0;">
                    <label><input type="checkbox" class="material-cost-checkbox" value="0" checked /> 物料费: 0元</label><br/>
                    <label><input type="checkbox" class="material-cost-checkbox" value="1" /> 物料费: 1元</label><br/>
                    <label><input type="checkbox" class="material-cost-checkbox" value="2" /> 物料费: 2元</label><br/>
                    <label><input type="checkbox" class="material-cost-checkbox" value="3" /> 物料费: 3元</label><br/>
                    <label><input type="checkbox" class="material-cost-checkbox" value="5" /> 物料费: 5元</label>
                </div>
                <div class="button-group" style="position: static; margin-top: 20px;">
                    <button type="button" class="btn-cancel" onclick="closeMaterialCostModal()">取消</button>
                    <button type="button" class="btn-submit" onclick="confirmMaterialCost()">确认</button>
                </div>
            </form>
        </div>
    </div>

    <div id="exceptionModal" class="exception-modal">
        <div class="exception-modal-content">
            <span class="close" onclick="closeExceptionModal()">&times;</span>
            <h2>标记异常</h2>
            <form id="exceptionForm" class="exception-form">
                <input type="hidden" id="exceptionType" name="exceptionType" value="">
                <label for="exceptionReason">异常原因：</label>
                <textarea id="exceptionReason" name="exceptionReason" rows="4" placeholder="请输入异常原因..."></textarea>
                <div class="button-group" style="position: static; margin-top: 20px;">
                    <button type="button" class="btn-cancel" onclick="closeExceptionModal()">取消</button>
                    <button type="button" class="btn-submit" onclick="submitException()">提交</button>
                </div>
            </form>
        </div>
    </div>

    <div class="loading" id="loading">加载中...</div>
    <script src="https://label-open.kuaimai.com/cloudPrinterSDK/KM_SDK.umd.js"></script>
    <script src="//lib.baomitu.com/quagga/0.12.1/quagga.min.js"></script>
    <script>
        let currentStream = null;
        let currentTrackingNumber = '';
        let currentExceptionType = '';
        let lastNotifiedTrackingNumber = ''; // 记录最近已提示的条码
        let lastNotifiedTime = 0; // 记录最近提示的时间
        const API_ENDPOINT = './ajax.php';
        const PRINTER_CONFIG_KEY = 'printer_config_cache';
        let printerConfig = null;

        // 系统初始化
        async function initSystem() {
            await initCamera();
            initBarcodeScanner();
            setupViewportAdjust();
        }

        // 摄像头初始化
        async function initCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'environment',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    }
                });

                const video = document.getElementById('camera-preview');
                video.srcObject = stream;
                currentStream = stream;

                video.play().catch(() => {
                    showMessage('请允许摄像头访问权限');
                });

            } catch (error) {
                showMessage('摄像头初始化失败: ' + error.message);
            }
        }

        // 条码扫描初始化
        function initBarcodeScanner() {
            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.getElementById('camera-preview'),
                    constraints: {
                        facingMode: 'environment'
                    },
                    area: {
                        top: "0%",
                        right: "0%",
                        left: "0%",
                        bottom: "60%"
                    }
                },
                decoder: {
                    readers: ['code_128_reader']
                },
                locate: true
            }, err => {
                if (err) {
                    console.error('扫码器初始化失败:', err);
                    showMessage('扫码功能初始化失败');
                    return;
                }
                Quagga.start();
            });

            Quagga.onDetected(result => {
                const code = result.codeResult.code;
                if (/^[A-Za-z0-9]+$/.test(code)) {
                    handleScanResult(code);
                }
            });
        }

        // 处理扫描结果
        function handleScanResult(courierNumber) {
            // 防抖处理：如果是同一条码且距离上次扫描不到3秒，忽略本次扫描
            const now = Date.now();
            if (courierNumber === lastNotifiedTrackingNumber && now - lastNotifiedTime < 3000) {
                return;
            }
            
            // 更新记录
            lastNotifiedTrackingNumber = courierNumber;
            lastNotifiedTime = now;
            
            currentTrackingNumber = courierNumber;
            document.getElementById('trackingNumber').value = courierNumber;
            fetchProductInfo(courierNumber);
            scrollToProductInfo();
        }

        // 手动输入处理
        async function handleManualInput() {
            const input = document.getElementById('trackingNumber').value.trim();
            if (!/^[A-Za-z0-9]+$/.test(input)) {
                return showMessage('单号格式错误（只能是字母和数字的组合）');
            }
            currentTrackingNumber = input;
            fetchProductInfo(input);
            scrollToProductInfo();
        }

        // 获取商品信息
        async function fetchProductInfo(courierNumber) {
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=get_product_info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ courierNumber })
                });

                const res = await response.json();
                if (res.code === 404 && res.handled === true) {
                    // 已在后端处理过的异常，使用温和的提示方式
                    showNonIntrusiveMessage(res.msg || '未找到对应商品信息', 'warning');
                    document.getElementById('productInfo').style.display = 'none';
                    return;
                } else if (res.code === 2) {
                    // 未找到对应商品信息，直接标记查询异常
                    await markException(courierNumber, 'query_exception', '查询不到物流单号');
                    throw new Error(res.msg);
                } else if (res.code === 3) {
                    // 查询到物流单号，但国际单号有问题
                    await markException(courierNumber, 'tracking_number_error', '国际单号格式不正确');
                    throw new Error(res.msg);
                } else if (res.code !== 0) {
                    throw new Error(res.msg || '获取信息失败');
                }
                
                updateProductInfo(res.data || []);

            } catch (error) {
                showMessage(error.message);
                document.getElementById('productInfo').style.display = 'none';
            } finally {
                showLoading(false);
            }
        }

        // 更新产品信息显示
        function updateProductInfo(data) {
            const productList = document.getElementById('product-list');
            productList.innerHTML = ''; // 清空之前的内容
        
            if (data.length === 0) {
                document.getElementById('productInfo').style.display = 'none';
                return;
            }
        
            data.forEach(product => {
                // 如果订单关联了多个国内快递单号，则发出警告提示
                if (product.courier_numbers && Array.isArray(product.courier_numbers) && product.courier_numbers.length > 1) {
                    showNonIntrusiveMessage(`注意: 订单 ${product.posting_number} 关联了多个国内快递单号`, 'warning');
                }
                
                const productItem = document.createElement('div');
                productItem.classList.add('product-item');
                productItem.setAttribute('data-posting-number', product.posting_number || '');
                productItem.setAttribute('data-tracking-number', product.tracking_number || '');
                productItem.setAttribute('data-courier-number', product.courierNumber || '');
                
                // 检查是否已发货或已打印
                const isShipped = product.packing_status == 1 || product.packing_status == 2;
                const isPrinted = product.is_printed == 1;
                const mainSku = product.sku || (product.products && product.products.length > 0 && product.products[0].sku ? product.products[0].sku : null);
                
                // 如果订单已标记为异常，显示异常信息
                if (product.is_exception) {
                    productItem.classList.add('exception');
                    productItem.style.border = '2px solid red';
                }
        
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        productItem.classList.add('selected');
                    } else {
                        productItem.classList.remove('selected');
                    }
                });
                
                // 如果是折叠状态，将checkbox放在特殊位置
                if (isShipped || isPrinted) {
                    checkbox.style.position = 'absolute';
                    checkbox.style.top = '10px';
                    checkbox.style.left = '10px';
                    checkbox.style.zIndex = '10';
                    productItem.style.position = 'relative';
                }
                
                // 默认勾选所有商品
                if(product.status === 'awaiting_verification' || product.status === 'cancelled' || isPrinted){
                    checkbox.checked = false;
                }else{
                    checkbox.checked = true;
                }
                
                productItem.classList.add('selected');
                
                productItem.appendChild(checkbox);
        
                // 如果已发货或已打印，显示折叠状态
                if (isShipped || isPrinted) {
                    productItem.classList.add('shipped-collapsed');
                    
                    // 创建折叠后的简化显示
                    const collapsedInfo = document.createElement('div');
                    collapsedInfo.classList.add('collapsed-info');
                    
                    // 状态图标
                    const statusIcon = document.createElement('span');
                    statusIcon.classList.add('status-icon');
                    if (isShipped && isPrinted) {
                        statusIcon.innerHTML = '✅ 已发货 📄 已打印';
                        statusIcon.style.color = '#4CAF50';
                    } else if (isShipped) {
                        statusIcon.innerHTML = '✅ 已发货';
                        statusIcon.style.color = '#4CAF50';
                    } else if (isPrinted) {
                        statusIcon.innerHTML = '📄 已打印';
                        statusIcon.style.color = '#2196F3';
                    }
                    
                    // 简化信息
                    const simplifiedInfo = document.createElement('div');
                    simplifiedInfo.classList.add('simplified-info');
                    
                    // 创建折叠状态下的信息列表
                    const infoDiv = document.createElement('div');
                    
                    // 添加产品名称
                    if (product.order_name) {
                        const nameDiv = document.createElement('div');
                        nameDiv.classList.add('collapsed-product-name');
                        nameDiv.textContent = product.order_name;
                        infoDiv.appendChild(nameDiv);
                    }
                    
                    // 添加主要信息
                    const mainInfoDiv = document.createElement('div');
                    mainInfoDiv.classList.add('collapsed-main-info');
                    
                    const simpleParts = [
                        product.posting_number && `订单号: ${product.posting_number}`,
                        product.tracking_number && `国际单号: ${product.tracking_number}`
                    ].filter(Boolean);
                    
                    mainInfoDiv.textContent = simpleParts.join(' | ');
                    infoDiv.appendChild(mainInfoDiv);
                    
                    simplifiedInfo.appendChild(infoDiv);
                    
                    // 展开按钮
                    const expandButton = document.createElement('button');
                    expandButton.innerHTML = '展开详情';
                    expandButton.classList.add('expand-btn');
                    expandButton.onclick = function() {
                        productItem.classList.toggle('expanded');
                        expandButton.innerHTML = productItem.classList.contains('expanded') ? '收起详情' : '展开详情';
                    };
                    
                    collapsedInfo.appendChild(statusIcon);
                    collapsedInfo.appendChild(simplifiedInfo);
                    collapsedInfo.appendChild(expandButton);
                    productItem.appendChild(collapsedInfo);
                }
        
                // 详细信息容器（在折叠状态下隐藏）
                const detailsContainer = document.createElement('div');
                detailsContainer.classList.add('details-container');
                if (isShipped || isPrinted) {
                    detailsContainer.style.display = 'none';
                }
        
                const productImage = document.createElement('img');
                productImage.classList.add('product-image');
                productImage.src = product.primary_image || 'https://picsum.photos/200/300';
                
                if (mainSku) {
                    const imageLink = document.createElement('a');
                    imageLink.href = `https://www.ozon.ru/product/${mainSku}`;
                    imageLink.target = '_blank';
                    imageLink.appendChild(productImage);
                    detailsContainer.appendChild(imageLink);
                } else {
                    detailsContainer.appendChild(productImage);
                }
        
                const productModel = document.createElement('div');
                productModel.classList.add('product-details');
                
                // 创建名称显示
                const productName = document.createElement('h3');
                productName.classList.add('product-name');
                productName.textContent = product.order_name || '未知型号';
                productModel.appendChild(productName);
                
                // 添加订单状态显示（仅对未发货订单）
                if (!isShipped) {
                    const statusDiv = document.createElement('div');
                    statusDiv.classList.add('order-status');
                    
                    if (product.status === 'awaiting_verification') {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span class="hong">⚠️⚠️⚠️未上传护照(禁止发货)!!!</span></span>';
                    } else if (product.status === 'awaiting_packaging') {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span style="color: #666;">等待备货</span></span>';
                    } else if (product.status === 'awaiting_deliver' && product.packing_status === 1) {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span style="color: #e67e22;">交运平台</span></span>';
                    } else if (product.status === 'awaiting_deliver') {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span style="color: #e67e22;">等待发货</span></span>';
                    } else if (product.status === 'delivering') {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span style="color: #2980b9;">运输中</span></span>';
                    } else if (product.status === 'delivered') {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span style="color: #27ae60;">已送达</span></span>';
                    } else if (product.status === 'cancelled') {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span style="color: #c0392b;">⚠️⚠️⚠️已取消</span></span>';
                    } else if (product.status === 'awaiting_registration') {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span style="color: #8e44ad;">移交给快递</span></span>';
                    } else if (product.status === 'cancelled_from_split_pending') {
                        statusDiv.innerHTML = '<span class="ss">同步状态：<span style="color: #c0392b;">因货件拆分而取消</span></span>';
                    }
                    
                    productModel.appendChild(statusDiv);
                }
                
                // 创建信息列表
                const infoList = document.createElement('ul');
                infoList.classList.add('product-info-list');
                
                // 添加每一项信息
                const infoItems = [];
                
                // 组合数量和颜色
                const quantityAndColor = [];
                if (product.quantity) quantityAndColor.push(`数量: ${product.quantity}`);
                if (product.color) quantityAndColor.push(`颜色: ${product.color}`);
                if (mainSku) quantityAndColor.push(`SKU: ${mainSku}`);
                if (quantityAndColor.length > 0) {
                    infoItems.push(quantityAndColor.join(' | '));
                }
        
                if (product.dimensions) infoItems.push(`尺寸: ${product.dimensions}`);
                if (product.posting_number) infoItems.push(`订单号:<strong> ${product.posting_number}</strong>`);
                if (product.tracking_number) infoItems.push(`国际单号: ${product.tracking_number}`);
                if (product.courierNumber) infoItems.push(`国内快递单号: ${product.courierNumber}`);
                
                infoItems.forEach(item => {
                    const li = document.createElement('li');
                    li.innerHTML = item; // 使用innerHTML以支持strong标签
                    infoList.appendChild(li);
                });
                
                // 显示多个商品信息
                if (product.products && Array.isArray(product.products) && product.products.length > 0) {
                    const productsHeader = document.createElement('li');
                    productsHeader.style.fontWeight = 'bold';
                    productsHeader.style.marginTop = '10px';
                    productsHeader.textContent = `订单包含 ${product.products.length} 个商品:`;
                    infoList.appendChild(productsHeader);
                    
                    product.products.forEach((item, index) => {
                        const productLi = document.createElement('li');
                        productLi.style.paddingLeft = '20px';
                        productLi.style.borderLeft = '2px solid #2196F3';
                        productLi.style.margin = '5px 0';
                        
                        const productInfo = [
                            item.name && `商品名称: ${item.name}`,
                            item.sku && `SKU: ${item.sku}`,
                            item.quantity && `数量: ${item.quantity}`,
                            item.price && `价格: ${item.price} ${item.currency_code || 'CNY'}`,
                            item.offer_id && `商品ID: ${item.offer_id}`
                        ].filter(Boolean).join('<br>');
                        
                        productLi.innerHTML = `<strong>商品 ${index + 1}:</strong><br>${productInfo}`;
                        infoList.appendChild(productLi);
                    });
                }
                
                // 显示多个国内快递单号
                if (product.courier_numbers && Array.isArray(product.courier_numbers) && product.courier_numbers.length > 1) {
                    const courierHeader = document.createElement('li');
                    courierHeader.style.fontWeight = 'bold';
                    courierHeader.style.marginTop = '10px';
                    courierHeader.style.color = '#FF5722';
                    courierHeader.textContent = `⚠️ 注意：此订单有 ${product.courier_numbers.length} 个国内快递单号:`;
                    infoList.appendChild(courierHeader);
                    
                    product.courier_numbers.forEach((number, index) => {
                        const courierLi = document.createElement('li');
                        courierLi.style.paddingLeft = '20px';
                        courierLi.style.borderLeft = '2px solid #FF5722';
                        courierLi.style.margin = '5px 0';
                        courierLi.style.color = '#FF5722';
                        courierLi.textContent = `单号 ${index + 1}: ${number}`;
                        infoList.appendChild(courierLi);
                    });
                }
                
                productModel.appendChild(infoList);
                detailsContainer.appendChild(productModel);
                
                // 如果订单已标记为异常，显示异常信息
                if (product.is_exception) {
                    const exceptionInfo = document.createElement('div');
                    exceptionInfo.style.color = 'red';
                    exceptionInfo.style.marginTop = '10px';
                    const exceptionTypeName = {
                        'query_exception': '查询异常',
                        'tracking_number_error': '国际单号错误',
                        'other_exception': '其他异常'
                    }[product.exception_type] || '未知异常';
                    exceptionInfo.innerHTML = `异常: ${exceptionTypeName} (${product.exception_reason})`;
                    detailsContainer.appendChild(exceptionInfo);
                }
        
                productItem.appendChild(detailsContainer);
                productList.appendChild(productItem);
            });
        
            document.getElementById('productInfo').style.display = 'block';
        }

        // 获取勾选的单号
        function getSelectedPostingNumbers() {
            const checkboxes = document.querySelectorAll('.product-item input[type="checkbox"]:checked');
            const postingNumbers = [];
            checkboxes.forEach(checkbox => {
                const productItem = checkbox.closest('.product-item');
                const postingNumber = productItem.getAttribute('data-posting-number');
                if (postingNumber) {
                    postingNumbers.push(postingNumber);
                }
            });
            return postingNumbers;
        }
        
        // 获取勾选的快递单号
        function getSelectedTrackingNumbers() {
            const checkboxes = document.querySelectorAll('.product-item input[type="checkbox"]:checked');
            const trackingNumbers = [];
            checkboxes.forEach(checkbox => {
                const productItem = checkbox.closest('.product-item');
                const trackingNumber = productItem.getAttribute('data-courier-number') || 
                                       productItem.getAttribute('data-tracking-number');
                if (trackingNumber) {
                    trackingNumbers.push(trackingNumber);
                }
            });
            return trackingNumbers;
        }

        // 标记发货
        function openMaterialCostModal() {
            const postingNumbers = getSelectedPostingNumbers();
            if (postingNumbers.length === 0) {
                return showMessage('请勾选要标记发货的商品');
            }
            document.getElementById('materialCostModal').style.display = 'block';
        }

        function closeMaterialCostModal() {
            document.getElementById('materialCostModal').style.display = 'none';
        }

        async function confirmMaterialCost() {
            const postingNumbers = getSelectedPostingNumbers();
            if (postingNumbers.length === 0) {
                return showMessage('请勾选要标记发货的商品');
            }
            const checkboxes = document.querySelectorAll('#materialCostModal .material-cost-checkbox:checked');
            let materialCost = 0;
            checkboxes.forEach(cb => {
                materialCost += parseFloat(cb.value);
            });
            // 获取对应的trackingNumbers和courierNumbers
            const orders = [];
            postingNumbers.forEach(postingNumber => {
                const productItem = document.querySelector(`.product-item[data-posting-number="${postingNumber}"]`);
                if (productItem) {
                    const trackingNumber = productItem.getAttribute('data-tracking-number') || '';
                    const courierNumber = productItem.getAttribute('data-courier-number') || '';
                    orders.push({
                        postingNumber,
                        trackingNumber,
                        courierNumber
                    });
                }
            });
            closeMaterialCostModal();
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=mark_shipped', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ orders, materialCost })
                });
                const res = await response.json();
                if (res.code !== 0) throw new Error(res.msg || '标记失败');
                showMessage('已成功标记发货');
                // 重新获取产品信息以显示更新后的状态
                fetchProductInfo(currentTrackingNumber);
            } catch (error) {
                showMessage(error.message);
            } finally {
                showLoading(false);
            }
        }

        // 获取打印机配置（带缓存功能）
        async function getPrinterConfig() {
            // 1. 检查本地缓存
            const cachedConfig = localStorage.getItem(PRINTER_CONFIG_KEY);
            if (cachedConfig) {
                try {
                    return JSON.parse(cachedConfig);
                } catch (e) {
                    console.error('解析缓存配置失败', e);
                }
            }
            
            // 2. 从服务器获取配置
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=get_printer_config');
                const res = await response.json();
                
                if (res.code === 0 && res.data) {
                    // 缓存到localStorage
                    localStorage.setItem(PRINTER_CONFIG_KEY, JSON.stringify(res.data));
                    return res.data;
                } else {
                    throw new Error(res.msg || '获取打印机配置失败');
                }
            } catch (error) {
                console.error('获取打印机配置失败:', error);
                showMessage('打印机配置获取失败: ' + error.message);
                return null;
            } finally {
                showLoading(false);
            }
        }

        // 打印标签
        async function handlePrint() {
            const postingNumbers = getSelectedPostingNumbers();
            if (postingNumbers.length === 0) {
                return showMessage('请勾选要打印标签的商品');
            }
            
            // 获取打印机配置
            const config = await getPrinterConfig();
            if (!config) {
                return showMessage('无法获取打印机配置');
            }
            
            showLoading(true);
            try {
                // 1. 请求生成标签
                let result = null;
                let retryCount = 0;
                const maxRetries = 3;
                const retryDelay = 2000; // 2秒后重试
                
                while (retryCount <= maxRetries) {
                    try {
                        // 获取打印文件内容
                        const printResponse = await fetch(API_ENDPOINT + '?act=print_label', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({postingNumbers})
                        });
                        
                        result = await printResponse.json();
                        
                        // 检查是否成功
                        if (result.code === 0 && result.data && result.data.length > 0) {
                            // 成功获取标签数据
                            
                            // 2. 将base64转换为File对象数组
                            const files = result.data.map(item => {
                                const byteCharacters = atob(item.content);
                                const byteNumbers = new Array(byteCharacters.length);
                                for (let i = 0; i < byteCharacters.length; i++) {
                                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                                }
                                const byteArray = new Uint8Array(byteNumbers);
                                return new File([byteArray], item.name, { type: 'application/pdf' });
                            });
                            
                            // 3. 使用打印机SDK打印
                            const KMSDK = new KM_SDK();
                            const params = {
                                appId: config.appId,
                                secret: config.secret,
                                sn: config.sn,
                                file: files,             // 使用文件数组
                                isPDF: true,              // 需要打印文件是否为PDF
                                kmPrintTimes: 1,
                                extra: "0",
                                printPreview: true,
                                previewWidth: config.previewWidth || 80,
                                previewHeight: config.previewHeight || 120,
                                callback: res => {
                                    if (res.code === 0) {
                                        showMessage('打印任务已提交');
                                    } else {
                                       //showMessage('打印失败: ' + res.msg);
                                       //showMessage('打印失败: ' + (res.msg || '未知错误'));
                                    }
                                }
                            };
                            
                            KMSDK.printFile(params);
                            
                            // 打印成功后刷新产品信息显示
                            setTimeout(() => {
                                fetchProductInfo(currentTrackingNumber);
                            }, 1000);
                            
                            return; // 打印成功，直接退出函数
                        }
                        
                        // 未成功，进行重试
                        retryCount++;
                        console.log(`获取标签失败，正在进行第${retryCount}次重试...`);
                        
                        if (retryCount > maxRetries) {
                            break; // 超过最大重试次数，退出循环
                        }
                        
                        // 等待后重试
                        await new Promise(resolve => setTimeout(resolve, retryDelay));
                        
                    } catch (error) {
                        console.error('重试过程中出错:', error);
                        retryCount++;
                        
                        if (retryCount > maxRetries) {
                            break; // 超过最大重试次数，退出循环
                        }
                        
                        // 等待后重试
                        await new Promise(resolve => setTimeout(resolve, retryDelay));
                    }
                }
                
                // 所有重试都失败了，显示最终错误
                showMessage('获取标签失败，请稍后再试');
                
            } catch (error) {
                showMessage('打印过程出现错误: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 显示/隐藏下拉菜单
        function toggleDropdown() {
            document.getElementById('dropdown-content').classList.toggle('show');
        }

        // 打开异常模态框
        function openExceptionModal(exceptionType) {
            currentExceptionType = exceptionType;
            document.getElementById('exceptionType').value = exceptionType;
            document.getElementById('exceptionModal').style.display = 'block';
            
            // 根据异常类型设置默认原因
            const defaultReasons = {
                'query_exception': '系统中未查询到该物流单号',
                'tracking_number_error': '国际单号格式不正确',
                'other_exception': ''
            };
            document.getElementById('exceptionReason').value = defaultReasons[exceptionType] || '';
            
            // 关闭下拉菜单
            document.getElementById('dropdown-content').classList.remove('show');
        }

        // 关闭异常模态框
        function closeExceptionModal() {
            document.getElementById('exceptionModal').style.display = 'none';
        }

        // 提交异常
        async function submitException() {
            const trackingNumbers = getSelectedTrackingNumbers();
            if (trackingNumbers.length === 0) {
                return showNonIntrusiveMessage('请勾选要标记异常的商品', 'warning');
            }
            
            const exceptionType = document.getElementById('exceptionType').value;
            const exceptionReason = document.getElementById('exceptionReason').value.trim();
            
            if (!exceptionReason) {
                return showNonIntrusiveMessage('请输入异常原因', 'warning');
            }
            
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=mark_exception', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        trackingNumbers,
                        exceptionType,
                        exceptionReason
                    })
                });

                const res = await response.json();
                if (res.code !== 0) {
                    // 直接使用服务器返回的错误消息，不再包装
                    showNonIntrusiveMessage(res.msg || '标记异常失败', 'error');
                } else {
                    showNonIntrusiveMessage('已成功标记异常', 'info');
                    closeExceptionModal();
                    fetchProductInfo(currentTrackingNumber);
                }
            } catch (error) {
                // 网络错误等异常情况
                showNonIntrusiveMessage('网络异常，请重试', 'error');
                console.error('异常提交失败:', error);
            } finally {
                showLoading(false);
            }
        }

        // 直接标记异常（用于系统自动检测到的异常）
        async function markException(trackingNumber, exceptionType, exceptionReason) {
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=mark_exception', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        trackingNumbers: [trackingNumber],
                        exceptionType,
                        exceptionReason
                    })
                });

                const res = await response.json();
                if (res.code !== 0) throw new Error(res.msg || '标记异常失败');

            } catch (error) {
                console.error('自动标记异常失败:', error);
            } finally {
                showLoading(false);
            }
        }

        // 通用功能
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showMessage(msg) {
            alert(msg);
        }

        // 移动端视口调整
        function setupViewportAdjust() {
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    window.scrollTo(0, 0);
                    document.body.style.height = 'calc(100% - 300px)';
                });
                
                input.addEventListener('blur', () => {
                    document.body.style.height = 'auto';
                });
            });
        }

        function scrollToProductInfo() {
            document.getElementById('productInfo').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // 点击页面其他地方关闭下拉菜单
        window.onclick = function(event) {
            if (!event.target.matches('.action-button')) {
                const dropdowns = document.getElementsByClassName('dropdown-content');
                for (let i = 0; i < dropdowns.length; i++) {
                    const openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains('show')) {
                        openDropdown.classList.remove('show');
                    }
                }
            }
        }

        // 添加非弹窗式的温和提示
        function showNonIntrusiveMessage(msg, type = 'info') {
            // 检查是否已有提示元素，有则更新，无则创建
            let messageEl = document.getElementById('non-intrusive-message');
            
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'non-intrusive-message';
                messageEl.style.position = 'fixed';
                messageEl.style.top = '10px';
                messageEl.style.left = '50%';
                messageEl.style.transform = 'translateX(-50%)';
                messageEl.style.padding = '10px 20px';
                messageEl.style.borderRadius = '5px';
                messageEl.style.zIndex = '1000';
                messageEl.style.textAlign = 'center';
                messageEl.style.maxWidth = '90%';
                messageEl.style.boxShadow = '0 3px 6px rgba(0,0,0,0.16)';
                document.body.appendChild(messageEl);
            }
            
            // 根据类型设置不同样式
            if (type === 'warning') {
                messageEl.style.backgroundColor = '#FFF3CD';
                messageEl.style.color = '#856404';
                messageEl.style.border = '1px solid #FFEEBA';
            } else if (type === 'error') {
                messageEl.style.backgroundColor = '#F8D7DA';
                messageEl.style.color = '#721C24';
                messageEl.style.border = '1px solid #F5C6CB';
            } else {
                messageEl.style.backgroundColor = '#D1ECF1';
                messageEl.style.color = '#0C5460';
                messageEl.style.border = '1px solid #BEE5EB';
            }
            
            messageEl.textContent = msg;
            messageEl.style.display = 'block';
            
            // 3秒后自动消失
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }

        // 初始化系统
        initSystem();
    </script>
</body>
</html>
