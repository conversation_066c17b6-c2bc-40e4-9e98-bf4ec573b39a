<?php
function getFinalUrl($url) {
    $headers = get_headers($url, 1); // 获取响应头
    
    if ($headers && isset($headers['Location'])) {
        $location = $headers['Location'];
        
        // 处理多个 Location 的情况（如 307 临时重定向）
        if (is_array($location)) {
            $location = end($location); // 取最后一个 Location
        }
        
        return trim($location);
    }
    
    return $url; // 如果没有跳转，返回原链接
}
function get_dailisk5ip($type=null){
    global $DB;
    if($type){
        $daili = $DB->getRow("SELECT * FROM `ozon_dailiip` WHERE `type`= '$type' AND `status` = 0 AND `nums` ='0'");
        $rowa = $DB->exec("update `ozon_dailiip` set `nums` ='1' where `type`= '$type' and `id`='{$daili['id']}'");
    	//最后一张可用的
    	$is=$DB->query("SELECT * FROM `ozon_dailiip` WHERE `nums`='0' and `id`!='{$rowa['id']}' and `type`= '$type' and `status` = 0 order by nums asc limit 1")->fetch();
    	//调用到最后一个可用后重置所有调用排序次数
    	if(!$is)$DB->exec("update `ozon_dailiip` set `nums`='0' WHERE `type`= '$type' and `status` = 0");
        return $daili;
    }else{
        $daili = $DB->getRow("SELECT * FROM `ozon_dailiip` WHERE `status` = 0 AND `nums` ='0'");
        $rowa = $DB->exec("update `ozon_dailiip` set `nums` ='1' where `id`='{$daili['id']}'");
    	//最后一张可用的
    	$is=$DB->query("SELECT * FROM `ozon_dailiip` WHERE `nums`='0' and `id`!='{$rowa['id']}' and `status` = 0 order by nums asc limit 1")->fetch();
    	//调用到最后一个可用后重置所有调用排序次数
    	if(!$is)$DB->exec("update `ozon_dailiip` set `nums`='0' WHERE `status` = 0");
        return $daili;
    }
}

function bysku($sku){
    $sku = (string) get_sku($sku);
    $referer = 'https://seller.ozon.ru/app/products/add/general-info';
    $result = doRequests('https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku',['skus'=>[$sku]], $referer);
    $json = json_decode($result['body'],true);
    if($json){
        return $json['resolved_categories_by_sku'][$sku];
    }
    return false;
}

function apigetattributes($sku,$variant_id=null){
    $variant_ids = null;
    $i = 0;
    do {
        $sku = (string) $sku;
        $referer = 'https://seller.ozon.ru/app/products/create';
        $array = ['name'=>$sku,'limit'=>'10'];
        if($lastId){
            $array['last_id'] = $lastId;
        }
        $result = doRequests('https://seller.ozon.ru/api/v1/search-variant-model',$array,$referer,null,'ru');
        $json = json_decode($result['body'],true);
        if($result['code']==200){
            foreach ($json['items'] as $item){
                if($item['variant_id']==$variant_id and $variant_id){
                    return $item;
                    break; // 无数据时终止分页
                }else{
                    if(empty($variant_ids)){
                        $variant_ids = $item['variant_id'];
                        $data = $item;
                    }else{
                        if($item['variant_id']<$variant_ids){
                            $variant_ids = $item['variant_id'];
                            $data = $item;
                        }
                    }
                }
            }
            $lastId = $response['last_id'] ?? null;
        }else{
            if($i>=3){
                break; // 无数据时终止分页
            }
            $i++;
        }
        if (!isset($json['items'])) {
            return $data??false;
        }
        if(count($json['items'])<10){
            return $data??false;
        }
        //error_log(date("Y-m-d H:i:s").json_encode($json)); // 调试日志
    } while ($lastId !== null and $i<3); // 存在分页标识时继续循环
    return $data??false;
}

function ozonqueries(){
    global $DB,$cookie;
    if(empty($proxys)){
        $proxys = get_dailisk5ip(1);
    }
    if($proxys['cookie']){
        $cookies = $proxys['cookie'];
    }else{
        $cookies = $cookie;
    }
    $sc_company_id = getSubstr($cookies, 'sc_company_id=', ';');
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://seller.ozon.ru/api/site/searchteam/Stats/queries/search/v2');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'accept: application/json, text/plain, */*',
        'accept-language: zh-Hans',
        'content-type: application/json',
        'origin: https://seller.ozon.ru',
        'priority: u=1, i',
        'referer: https://seller.ozon.ru/app/analytics/what-to-sell/all-queries',
        'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile: ?0',
        'sec-ch-ua-platform: "Windows"',
        'sec-fetch-dest: empty',
        'sec-fetch-mode: cors',
        'sec-fetch-site: same-origin',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'x-o3-app-name: seller-ui',
        'x-o3-company-id: '.$sc_company_id,
        'x-o3-language: zh-Hans',
        'x-o3-page-type: analytics_seller',
    ]);
    curl_setopt($ch, CURLOPT_COOKIE, $cookies.'; is_adult_confirmed=true;');
    curl_setopt($ch, CURLOPT_POSTFIELDS, '{"text":"","limit":"50","offset":"0","sort_by":"count","sort_dir":"desc","period":"days_7"}');
    
    $response = curl_exec($ch);
    
    curl_close($ch);
return $response;
}

function doRequests($url, $post, $referer, $proxys=null,$language='zh-Hans') {
    global $DB,$cookie;
    if(empty($proxys)){
        $proxys = get_dailisk5ip(1);
    }
    
    if($proxys['cookie']){
        $cookies = $proxys['cookie'];
    }else{
        $cookies = $cookie;
    }
    
    $ch = curl_init();
    if($proxys){
        // 代理配置
        curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5);
        curl_setopt($ch, CURLOPT_PROXY, $proxys['proxy_server']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $proxys['proxy_port']);
        curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxys['proxy_user'].":".$proxys['proxy_pwd']);
    }
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_HEADER, true);
    if ($referer) {
        curl_setopt($ch, CURLOPT_REFERER, $referer);
    }
    if ($post) {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
    }
    $sc_company_id = getSubstr($cookies, 'sc_company_id=', ';');
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "accept: application/json, text/plain, */*",
        "accept-language: $language",
        "authority: seller.ozon.ru",
        "content-type: application/json",
        "origin: https://seller.ozon.ru",
        "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36",
        "x-o3-app-name: seller-ui",
        "x-o3-company-id: $sc_company_id",
        "x-o3-language: $language",
        "x-o3-page-type: products-other" #analytics_seller
    ]);
    if ($cookies) {
        curl_setopt($ch, CURLOPT_COOKIE, $cookies);
    }
    // 执行请求
    $response = curl_exec($ch);
    $err = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $originalCookies = [];
    if (!empty($cookies)) {
        $pairs = explode('; ', $cookies);
        foreach ($pairs as $pair) {
            if (strpos($pair, '=') !== false) {
                list($name, $value) = explode('=', $pair, 2);
                $originalCookies[trim($name)] = trim($value);
            }
        }
    }

    // 解析新Cookie（从响应头）
    $newCookies = [];
    $headerLines = explode("\r\n", substr($response, 0, $headerSize));
    foreach ($headerLines as $line) {
        if (stripos($line, 'Set-Cookie:') === 0) {
            $cookieStr = trim(substr($line, 11)); // 去掉"Set-Cookie:"
            // 分割键值对
            $parts = explode(';', $cookieStr);
            $cookiePair = trim($parts[0]);
            if (strpos($cookiePair, '=') !== false) {
                list($name, $value) = explode('=', $cookiePair, 2);
                $newCookies[trim($name)] = trim($value);
            }
        }
    }

    // 精准合并（重点处理 __Secure-ETC）
    $mergedCookies = $originalCookies;
    foreach ($newCookies as $key => $value) {
        // 强制更新目标Cookie
        if ($key === '__Secure-ETC') {
            $mergedCookies[$key] = $value;
            //error_log("已更新 __Secure-ETC: $value"); // 调试日志
        }
        // 其他Cookie正常合并
        else {
            $mergedCookies[$key] = $value;
        }
    }
    
    // 生成新Cookie字符串
    $mergedCookieStr = implode('; ', array_map(
        function ($k, $v) { return "$k=$v"; }, 
        array_keys($mergedCookies), 
        $mergedCookies
    ));
    
    curl_close($ch);
    if($httpCode===307 or $httpCode==200){
        $DB->update('dailiip', ['cookie'=>$mergedCookieStr,'msg'=>'','time'=>time()], ['id' => $proxys['id']]);
    }elseif($httpCode==401){
        if (strpos($cookies, 'abt_data=') == false){
            $updata = ['msg'=>'abt_data数据不齐全','status'=>1];
        }else{
            $updata = ['msg'=>'账号已掉线','status'=>1];
        }
        $DB->update('dailiip', $updata, ['id' => $proxys['id']]);
    }elseif($httpCode==403){
        if (strpos($cookies, 'abt_data=') == false){
            $updata = ['msg'=>'abt_data数据不齐全','status'=>1];
        }else{
            $updata = ['msg'=>'账号已掉线','status'=>1];
        }
        $DB->update('dailiip', $updata, ['id' => $proxys['id']]);
    }
    
    return [
        'code' => $httpCode,
        'body' => substr($response, $headerSize),
        'error' => $err,
        'old_cookie' => $cookies,
        'new_cookies' => $newCookies,
        'merged_cookies' => $mergedCookieStr,
    ];
}
function get_curl($url, $post=0, $referer=0, $cookie=0, $header=0, $ua=0, $nobaody=0, $addheader=0)
{
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	$httpheader[] = "Accept: */*";
	$httpheader[] = "Accept-Encoding: gzip,deflate,sdch";
	$httpheader[] = "Accept-Language: zh-CN,zh;q=0.8";
	$httpheader[] = "Connection: close";
	if($addheader){
		$httpheader = array_merge($httpheader, $addheader);
	}
	curl_setopt($ch, CURLOPT_HTTPHEADER, $httpheader);
	if ($post) {
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
	}
	if ($header) {
		curl_setopt($ch, CURLOPT_HEADER, true);
	}
	if ($cookie) {
		curl_setopt($ch, CURLOPT_COOKIE, $cookie);
	}
	if($referer){
		curl_setopt($ch, CURLOPT_REFERER, $referer);
	}
	if ($ua) {
		curl_setopt($ch, CURLOPT_USERAGENT, $ua);
	}
	else {
		curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Linux; U; Android 4.0.4; es-mx; HTC_One_X Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0");
	}
	if ($nobaody) {
		curl_setopt($ch, CURLOPT_NOBODY, 1);
	}
	curl_setopt($ch, CURLOPT_ENCODING, "gzip");
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	$ret = curl_exec($ch);
	curl_close($ch);
	return $ret;
}

function curl_get($url)
{
	global $conf;
	$ch=curl_init($url);
	if($conf['proxy'] == 1){
		$proxy_server = $conf['proxy_server'];
		$proxy_port = intval($conf['proxy_port']);
		if($conf['proxy_type'] == 'https'){
			$proxy_type = CURLPROXY_HTTPS;
		}elseif($conf['proxy_type'] == 'sock4'){
			$proxy_type = CURLPROXY_SOCKS4;
		}elseif($conf['proxy_type'] == 'sock5'){
			$proxy_type = CURLPROXY_SOCKS5;
		}else{
			$proxy_type = CURLPROXY_HTTP;
		}
		curl_setopt($ch, CURLOPT_PROXYAUTH, CURLAUTH_BASIC);
		curl_setopt($ch, CURLOPT_PROXY, $proxy_server);
		curl_setopt($ch, CURLOPT_PROXYPORT, $proxy_port);
		if(!empty($conf['proxy_user']) && !empty($conf['proxy_pwd'])){
			$proxy_userpwd = $conf['proxy_user'].':'.$conf['proxy_pwd'];
			curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_userpwd);
		}
		curl_setopt($ch, CURLOPT_PROXYTYPE, $proxy_type);
	}
	$httpheader[] = "Accept: */*";
	$httpheader[] = "Accept-Language: zh-CN,zh;q=0.8";
	$httpheader[] = "Connection: close";
	curl_setopt($ch, CURLOPT_HTTPHEADER, $httpheader);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36');
	curl_setopt($ch, CURLOPT_TIMEOUT, 5);
	$content=curl_exec($ch);
	curl_close($ch);
	return $content;
}

function curl_get_daili($url,$localProxies)
{
	global $conf;
	$ch=curl_init($url);

	$proxy_server = $localProxies['ip'];
	$proxy_port = intval($localProxies['port']);
	curl_setopt($ch, CURLOPT_PROXYAUTH, CURLAUTH_BASIC);
	curl_setopt($ch, CURLOPT_PROXY, $proxy_server);
	curl_setopt($ch, CURLOPT_PROXYPORT, $proxy_port);
	curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
	
	$httpheader[] = "Accept: */*";
	$httpheader[] = "Accept-Language: zh-CN,zh;q=0.8";
	$httpheader[] = "Connection: close";
	curl_setopt($ch, CURLOPT_HTTPHEADER, $httpheader);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	curl_setopt($ch, CURLOPT_HEADER, true);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	
	curl_setopt($ch, CURLOPT_TIMEOUT, 5);
	curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36');
	
	$content=curl_exec($ch);
	curl_close($ch);
	if(empty($content))return;
	return $content.'IP：'.$localProxies['ip'];
}

function get_ozoncurl($url, $post, $referer, $cookie, $language='zh-Hans', $daili=false) {
    $ch = curl_init();
    if($daili){
        $proxy_server = $daili['ip'];
    	$proxy_port = intval($daili['port']);
    	curl_setopt($ch, CURLOPT_PROXYAUTH, CURLAUTH_BASIC);
    	curl_setopt($ch, CURLOPT_PROXY, $proxy_server);
    	curl_setopt($ch, CURLOPT_PROXYPORT, $proxy_port);
    	curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
    }
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_HEADER, true);
    if ($referer) {
        curl_setopt($ch, CURLOPT_REFERER, $referer);
    }
    if ($post) {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
    }
    $sc_company_id = getSubstr($cookie, 'sc_company_id=', ';');
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "accept: application/json, text/plain, */*",
        "accept-language: ".$language,
        "authority: seller.ozon.ru",
        "content-type: application/json",
        "origin: https://seller.ozon.ru",
        "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36",
        "x-o3-app-name: seller-ui",
        "x-o3-company-id: $sc_company_id",
        "x-o3-language: ".$language,
        "x-o3-page-type: products-other"
    ]);
    if ($cookie) {
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    }
    // 执行请求
    $response = curl_exec($ch);
    $err = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    curl_close($ch);
    return substr($response, $headerSize);
    return substr($response, 0, $headerSize);
}

function ozoncurl($url, $post = 0, $referer = 0, $cookie = 0) {
    $ch = curl_init();
	curl_setopt($ch, CURLOPT_PROXYAUTH, CURLAUTH_BASIC);
	curl_setopt($ch, CURLOPT_PROXY, "***************");
	curl_setopt($ch, CURLOPT_PROXYPORT, "1080");
	curl_setopt($ch, CURLOPT_PROXYUSERPWD, "vip:99999999");
	curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_HEADER, true);
    if ($referer) {
        curl_setopt($ch, CURLOPT_REFERER, $referer);
    }
    if ($post) {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
    }
    $sc_company_id = getSubstr($cookie, 'sc_company_id=', ';');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "accept: application/json, text/plain, */*",
        "accept-language: zh-Hans",
        "authority: seller.ozon.ru",
        "content-type: application/json",
        "origin: https://seller.ozon.ru",
        "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36",
        "x-o3-app-name: seller-ui",
        "x-o3-company-id: $sc_company_id",
        "x-o3-language: zh-Hans",
        "x-o3-page-type: products-other"
    ]);
    if ($cookie) {
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    }
    $response = curl_exec($ch);
    $err = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    // 关闭连接
    curl_close($ch);
    if ($err) {
        return ['error' => $err,'code' => 0];
    } else {
        return ['code' => $httpCode,'headers' => substr($response, 0, $headerSize),'body' => substr($response, $headerSize)];
    }
}

function real_ip($type=0){
$ip = $_SERVER['REMOTE_ADDR'];
if($type<=0 && isset($_SERVER['HTTP_X_FORWARDED_FOR']) && preg_match_all('#\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}#s', $_SERVER['HTTP_X_FORWARDED_FOR'], $matches)) {
	foreach ($matches[0] AS $xip) {
		if (filter_var($xip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
			$ip = $xip;
			break;
		}
	}
} elseif ($type<=0 && isset($_SERVER['HTTP_CLIENT_IP']) && filter_var($_SERVER['HTTP_CLIENT_IP'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
	$ip = $_SERVER['HTTP_CLIENT_IP'];
} elseif ($type<=1 && isset($_SERVER['HTTP_CF_CONNECTING_IP']) && filter_var($_SERVER['HTTP_CF_CONNECTING_IP'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
	$ip = $_SERVER['HTTP_CF_CONNECTING_IP'];
} elseif ($type<=1 && isset($_SERVER['HTTP_X_REAL_IP']) && filter_var($_SERVER['HTTP_X_REAL_IP'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
	$ip = $_SERVER['HTTP_X_REAL_IP'];
}
return $ip;
}

function authcode($string, $operation = 'DECODE', $key = '', $expiry = 0) {
	$ckey_length = 4;
	$key = md5($key);
	$keya = md5(substr($key, 0, 16));
	$keyb = md5(substr($key, 16, 16));
	$keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length): substr(md5(microtime()), -$ckey_length)) : '';
	$cryptkey = $keya.md5($keya.$keyc);
	$key_length = strlen($cryptkey);
	$string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0).substr(md5($string.$keyb), 0, 16).$string;
	$string_length = strlen($string);
	$result = '';
	$box = range(0, 255);
	$rndkey = array();
	for($i = 0; $i <= 255; $i++) {
		$rndkey[$i] = ord($cryptkey[$i % $key_length]);
	}
	for($j = $i = 0; $i < 256; $i++) {
		$j = ($j + $box[$i] + $rndkey[$i]) % 256;
		$tmp = $box[$i];
		$box[$i] = $box[$j];
		$box[$j] = $tmp;
	}
	for($a = $j = $i = 0; $i < $string_length; $i++) {
		$a = ($a + 1) % 256;
		$j = ($j + $box[$a]) % 256;
		$tmp = $box[$a];
		$box[$a] = $box[$j];
		$box[$j] = $tmp;
		$result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
	}
	if($operation == 'DECODE') {
		if(((int)substr($result, 0, 10) == 0 || (int)substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26).$keyb), 0, 16)) {
			return substr($result, 26);
		} else {
			return '';
		}
	} else {
		return $keyc.str_replace('=', '', base64_encode($result));
	}
}

function get_ip_city($ip)
{
    $url = 'https://www.bt.cn/api/panel/get_ip_info?ip=' . $ip;
    $response = get_curl($url);
    $result = json_decode($response, true);
	if(isset($result[$ip])){
		$data = $result[$ip];
		if($data['country'] == '中国'){
			return $data['province'].$data['city'];
		}else{
			return $data['country'].$data['province'].$data['city'];
		}
	}
	return false;
}
function send_mail($to, $sub, $msg) {
	global $conf;
	if($conf['mail_cloud']==1){
		$mail = new \lib\mail\Sendcloud($conf['mail_apiuser'], $conf['mail_apikey']);
		return $mail->send($to, $sub, $msg, $conf['mail_name2'], $conf['sitename']);
	}elseif($conf['mail_cloud']==2){
		$mail = new \lib\mail\Aliyun($conf['mail_apiuser'], $conf['mail_apikey']);
		return $mail->send($to, $sub, $msg, $conf['mail_name2'], $conf['sitename']);
	}else{
		if(!$conf['mail_name'] || !$conf['mail_port'] || !$conf['mail_smtp'] || !$conf['mail_pwd'])return false;
		$port = intval($conf['mail_port']);
		$mail = new \lib\mail\PHPMailer\PHPMailer(true);
		try{
			$mail->SMTPDebug = 0;
			$mail->CharSet = 'UTF-8';
			$mail->Timeout = 5;
			$mail->isSMTP();
			$mail->Host = $conf['mail_smtp'];
			$mail->SMTPAuth = true;
			$mail->Username = $conf['mail_name'];
			$mail->Password = $conf['mail_pwd'];
			if($port == 587) $mail->SMTPSecure = 'tls';
			else if($port >= 465) $mail->SMTPSecure = 'ssl';
			else $mail->SMTPAutoTLS = false;
			$mail->Port = intval($conf['mail_port']);
			$mail->setFrom($conf['mail_name'], $conf['sitename']);
			$mail->addAddress($to);
			$mail->addReplyTo($conf['mail_name'], $conf['sitename']);
			$mail->isHTML(true);
			$mail->Subject = $sub;
			$mail->Body = $msg;
			$mail->send();
			return true;
		} catch (Exception $e) {
			return $mail->ErrorInfo;
		}
	}
}
function send_sms($phone, $code, $scope='reg'){
	global $conf;
	$conf['sms_api']=2;
	$conf['sms_appid']    = 'LTAI5tSRKxoGnHEkAhntdC2e';
	$conf['sms_appkey']   = '******************************';
	$conf['sms_sign']     = '东莞市科极曜网络科技';
	$conf['sms_tpl_reg']  = 'SMS_490835036'; #商户注册模板ID
	$conf['sms_tpl_find'] = 'SMS_490590044'; #找回密码模板ID
	$conf['sms_tpl_edit'] = 'SMS_490800164'; #修改绑定
	if($scope == 'reg'){
		$moban = $conf['sms_tpl_reg'];
	}elseif($scope == 'login'){
		$moban = $conf['sms_tpl_login'];
	}elseif($scope == 'find'){
		$moban = $conf['sms_tpl_find'];
	}elseif($scope == 'edit'){
		$moban = $conf['sms_tpl_edit'];
	}
	if($conf['sms_api']==1){
		$sms = new \lib\sms\Qcloud($conf['sms_appid'], $conf['sms_appkey']);
		$arr = $sms->send($phone, $moban, [$code], $conf['sms_sign']);
		if(isset($arr['result']) && $arr['result']==0){
			return true;
		}else{
			return $arr['errmsg'];
		}
	}elseif($conf['sms_api']==2){
		$sms = new \lib\sms\Aliyun($conf['sms_appid'], $conf['sms_appkey']);
		$arr = $sms->send($phone, $code, $moban, $conf['sms_sign'], $conf['sitename']);
		if(isset($arr['Code']) && $arr['Code']=='OK'){
			return true;
		}else{
			return $arr['Message'];
		}
	}elseif($conf['sms_api']==3){
		$app=$conf['sitename'];
		$url = 'https://api.topthink.com/sms/send';
		$param = ['appCode'=>$conf['sms_appkey'], 'signId'=>$conf['sms_sign'], 'templateId'=>$moban, 'phone'=>$phone, 'params'=>json_encode(['code'=>$code])];
		$data=get_curl($url, http_build_query($param));
		$arr=json_decode($data,true);
		if(isset($arr['code']) && $arr['code']==0){
			return true;
		}else{
			return $arr['message'];
		}
	}elseif($conf['sms_api']==4){
		$sms = new \lib\sms\SmsBao($conf['sms_appid'], $conf['sms_appkey']);
		return $sms->send($phone, $code, $moban, $conf['sms_sign']);
	}else{
		$app=$conf['sitename'];
		$url = 'http://sms.php.gs/sms/send/yzm';
		$param = ['appkey'=>$conf['sms_appkey'], 'phone'=>$phone, 'moban'=>$moban, 'code'=>$code, 'app'=>$app];
		$data=get_curl($url, http_build_query($param));
		$arr=json_decode($data,true);
		if($arr['status']=='200'){
			return true;
		}else{
			return $arr['error_msg_zh'];
		}
	}
}
function daddslashes($string) {
	if(is_array($string)) {
		foreach($string as $key => $val) {
			$string[$key] = daddslashes($val);
		}
	} else {
		$string = addslashes($string);
	}
	return $string;
}

function strexists($string, $find) {
	return !(strpos($string, $find) === FALSE);
}

function dstrpos($string, $arr) {
	if(empty($string)) return false;
	foreach((array)$arr as $v) {
		if(strpos($string, $v) !== false) {
			return true;
		}
	}
	return false;
}

function checkmobile() {
	$useragent = strtolower($_SERVER['HTTP_USER_AGENT']);
	$ualist = array('android', 'midp', 'nokia', 'mobile', 'iphone', 'ipod', 'blackberry', 'windows phone');
	if((dstrpos($useragent, $ualist) || strexists($_SERVER['HTTP_ACCEPT'], "VND.WAP") || strexists($_SERVER['HTTP_VIA'],"wap")))
		return true;
	else
		return false;
}

function extractCookiesFromHeaders($headers) {
    $cookies = [];
    foreach (explode("\n", $headers) as $line) {
        // 用正则匹配 Set-Cookie 行，并提取第一个分号前的内容（自动处理空格）
        if (preg_match('/^Set-Cookie:\s*([^;]+)/i', trim($line), $matches)) {
            $cookies[] = $matches[1];
        }
    }
    return implode('; ', $cookies);
}

function getSid() {
    return md5(uniqid(mt_rand(), true) . microtime());
}
function getMd5Pwd($pwd, $salt=null) {
    return md5(md5($pwd) . md5('1277180438'.$salt));
}

/**
 * 取中间文本
 * @param string $str
 * @param string $leftStr
 * @param string $rightStr
 */
function getSubstr($str, $leftStr, $rightStr)
{
	$left = strpos($str, $leftStr);
	if ($left === false) return '';
	$start = $left+strlen($leftStr);
	$right = strpos($str, $rightStr, $start);
	if($left < 0) return '';
	if($right>0){
		return substr($str, $start, $right-$start);
	}else{
		return substr($str, $start);
	}
}

function isNullOrEmpty($str){
	return $str === null || $str === '';
}

function getSetting($k, $force = false){
	global $DB,$CACHE;
	if($force) return $DB->getColumn("SELECT v FROM pre_config WHERE k=:k LIMIT 1", [':k'=>$k]);
	$cache = $CACHE->get($k);
	return $cache[$k];
}
function saveSetting($k, $v){
	global $DB;
	return $DB->exec("REPLACE INTO pre_config SET v=:v,k=:k", [':v'=>$v, ':k'=>$k]);
}
function checkGroupSettings($str){
	foreach(explode(',',$str) as $row){
		if(!strpos($row,':'))return false;
	}
	return true;
}

function getdomain($url){
	$arr=parse_url($url);
	$host = $arr['host'];
	if(isset($arr['port']) && $arr['port']!=80 && $arr['port']!=443)$host .= ':'.$arr['port'];
	return $host;
}
function get_host($url){
	$arr=parse_url($url);
	return $arr['host'];
}

function get_main_host($url){
	$arr=parse_url($url);
	$host = $arr['host'];
	if(filter_var($host, FILTER_VALIDATE_IP))return $host;
	if(substr_count($host, '.')>1){
		$host = substr($host, strpos($host, '.')+1);
	}
	return $host;
}

function checkIfActive($string) {
	$array=explode(',',$string);
	$php_self=substr($_SERVER['REQUEST_URI'],strrpos($_SERVER['REQUEST_URI'],'/')+1,strrpos($_SERVER['REQUEST_URI'],'.')-strrpos($_SERVER['REQUEST_URI'],'/')-1);
	if (in_array($php_self,$array)){
		return 'active';
	}else
		return null;
}

function checkRefererHost(){
	if(!$_SERVER['HTTP_REFERER'])return false;
	$url_arr = parse_url($_SERVER['HTTP_REFERER']);
	$http_host = $_SERVER['HTTP_HOST'];
	if(strpos($http_host,':'))$http_host = substr($http_host, 0, strpos($http_host, ':'));
	return $url_arr['host'] === $http_host;
}
function randFloat($min=0, $max=1){
	return $min + mt_rand()/mt_getrandmax() * ($max-$min);
}

function randomFloat($min = 0, $max = 1) {
	$num = $min + mt_rand() / mt_getrandmax() * ($max - $min);
	return sprintf("%.2f",$num);
}

function random($length, $numeric = 0) {
	$seed = base_convert(md5(microtime().$_SERVER['DOCUMENT_ROOT']), 16, $numeric ? 10 : 35);
	$seed = $numeric ? (str_replace('0', '', $seed).'012340567890') : ($seed.'zZ'.strtoupper($seed));
	$hash = '';
	$max = strlen($seed) - 1;
	for($i = 0; $i < $length; $i++) {
		$hash .= $seed[mt_rand(0, $max)];
	}
	return $hash;
}

function checkDomain($domain){
	if(empty($domain) || !preg_match('/^[-$a-z0-9_*.]{2,512}$/i', $domain) || (stripos($domain, '.') === false) || substr($domain, -1) == '.' || substr($domain, 0 ,1) == '.' || substr($domain, 0 ,1) == '*' && substr($domain, 1 ,1) != '.' || substr_count($domain, '*')>1 || strpos($domain, '*')>0 || strlen($domain)<4) return false;
	return true;
}

function get_invite_code($uid){
	$str = (string)$uid;
	$tmp = '';
	for($i=0;$i<strlen($str);$i++){
		$tmp.=substr($str,$i,1) ^ substr(SYS_KEY,$i,1);
	}
	return str_replace('=','',base64_encode($tmp));
}

function get_invite_uid($code){
	$str = base64_decode($code);
	$tmp = '';
	for($i=0;$i<strlen($str);$i++){
		$tmp.=substr($str,$i,1) ^ substr(SYS_KEY,$i,1);
	}
	return $tmp;
}

function currency_convert($from, $to, $amount){
	$param = [
		'from' => $from,
		'to' => $to,
		'amount' => $amount
	];
	$url = 'https://api.exchangerate.host/convert?'.http_build_query($param);
	$data = get_curl($url);
	$arr = json_decode($data, true);
	if($arr['success']===true){
		return $arr['result'];
	}else{
		throw new Exception('汇率转换失败');
	}
}

function showPayVerifyPage($defend_key, $query_arr){
	global $conf, $cdnpublic;
	if($conf['pay_verify_type'] == 0){
		$key = time().$defend_key.rand(111111,999999);
		include PAYPAGE_ROOT.'verify_jump.php';
	}elseif($conf['pay_verify_type'] == 1){
		include PAYPAGE_ROOT.'verify_invisible.php';
	}elseif($conf['pay_verify_type'] == 2){
		include PAYPAGE_ROOT.'verify_slide.php';
	}
	exit;
}

function getDefendKey($pid, $trade_no){
	return md5(SYS_KEY.$pid.'_'.$trade_no.SYS_KEY);
}

//极验3.0服务端验证
function verify_captcha($user_id = 'public'){
	global $conf, $clientip;
	$conf['captcha_id'] = '';
	$conf['captcha_key'] = '';
	$GtSdk = new \lib\GeetestLib($conf['captcha_id'], $conf['captcha_key']);
	$data = array(
		'user_id' => $user_id,
		'client_type' => "web",
		'ip_address' => $clientip
	);
	if ($_SESSION['gtserver'] == 1) {   //服务器正常
		return $GtSdk->success_validate($_POST['geetest_challenge'], $_POST['geetest_validate'], $_POST['geetest_seccode'], $data);
	}else{  //服务器宕机,走failback模式
		return $GtSdk->fail_validate($_POST['geetest_challenge'],$_POST['geetest_validate'],$_POST['geetest_seccode']);
	}
}

//极验4.0服务端验证
function verify_captcha4(){
    if(!isset($_POST['captcha_id']) || !isset($_POST['lot_number']) || !isset($_POST['pass_token']) || !isset($_POST['gen_time']) || !isset($_POST['captcha_output'])) return false;
    $real_ip = real_ip();
    $url = 'http://gt4.geetest.com/demov4/demo/login';
    $param = ['captcha_id'=>$_POST['captcha_id'], 'lot_number'=>$_POST['lot_number'], 'pass_token'=>$_POST['pass_token'], 'gen_time'=>$_POST['gen_time'], 'captcha_output'=>$_POST['captcha_output']];
    $referer = 'http://gt4.geetest.com/demov4/invisible-bind-zh.html';
    $httpheader[] = "X-Real-IP: ".$real_ip;
	$httpheader[] = "X-Forwarded-For: ".$real_ip;
    $data = get_curl($url.'?'.http_build_query($param),0,$referer,0,0,0,0,$httpheader);
    $arr = json_decode($data, true);
    if(isset($arr['result']) && $arr['result'] == 'success'){
        return true;
    }
    return false;
}

function getDevice(){
	if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger')!==false) {
		$device = 'wechat';
	}elseif (strpos($_SERVER['HTTP_USER_AGENT'], 'QQ/')!==false) {
		$device = 'qq';
	}elseif (strpos($_SERVER['HTTP_USER_AGENT'], 'AlipayClient')!==false) {
		$device = 'alipay';
	}elseif (checkmobile()) {
		$device = 'mobile';
	}else{
		$device = 'pc';
	}
	return $device;
}

function ua(){
    $agent = strtolower($_SERVER['HTTP_USER_AGENT']);
    //输出数据
    if(strpos($agent, 'windows nt') or strpos($agent, 'windows')){
        $ua = "PC";
    }elseif(strpos($agent, 'iphone') or strpos($agent, 'ipad') or strpos($agent, 'mac')){
        $ua = "IOS";
    }elseif(strpos($agent, 'harmonyos')){
        $ua = "HarmonyOS";
    }elseif(strpos($agent, 'android') or strpos($agent,'xiaomi') or strpos($agent, 'miuibrowser') or strpos($agent, 'samsungbrowser') or strpos($agent, 'heyTapbrowser')){
        $ua = "Android";
    }elseif(strpos($agent, 'telegrambot')){
        $ua = "Telegram";
    }else{
        $ua = NULL;
    }
    return $ua;
}

function jsonp_decode($jsonp, $assoc = false)
{
	$jsonp = trim($jsonp);
	if(isset($jsonp[0]) && $jsonp[0] !== '[' && $jsonp[0] !== '{') {
		$begin = strpos($jsonp, '(');
		if(false !== $begin)
		{
			$end = strrpos($jsonp, ')');
			if(false !== $end)
			{
				$jsonp = substr($jsonp, $begin + 1, $end - $begin - 1);
			}
		}
	}
	return json_decode($jsonp, $assoc);
}

function Transmart($text,$source='ru',$target='zh'){ #腾讯交互翻译
    $url = "https://transmart.qq.com/api/imt";
    $httpheader = [
        "accept: application/json, text/plain, */*",
        "content-type: application/json",
        "RK=3QX1BWhLWR; ptcz=2cd8da298338a7a9ec5ea3688897f1d14838ba415c557d57cc79cf12ac6d411e; _qpsvr_localtk=0.252088820139453; TSMT_CLIENT_KEY=browser-chrome-135.0.0-Windows_10-09bf5777-d88f-46cd-95a9-387eeb04d071-1745610947719",
        "origin: https://transmart.qq.com",
        "priority: u=1, i",
        "referer: https://transmart.qq.com/zh-CN/index?sourcelang='.$source.'&targetlang='.$target.'&source=PUMA%20Bag%20with%20Handles",
    ];
    $data = '{"header":{"fn":"auto_translation","session":"","client_key":"browser-chrome-135.0.0-Windows_10-09bf5777-d88f-46cd-95a9-387eeb04d071-1745610947719","user":""},"type":"plain","model_category":"normal","text_domain":"general","source":{"lang":"'.$source.'","text_list":["","'.$text.'",""]},"target":{"lang":"'.$target.'"}}';
    $res = get_curl($url,$data,0,0,0,0,0,$httpheader);
    $json = json_decode($res);
    return $json->auto_translation[1];
}

#转换为中国时区
function replace($dateStr){
    // 创建DateTime对象并设置UTC时区
    $date = new DateTime($dateStr);
    // 转换为中国时区（可选）
    $date->setTimezone(new DateTimeZone('Asia/Shanghai'));
    // 格式化为中式日期时间（去除前导零）
    $chineseDate = $date->format('Y-m-d H:i:s');
    return $chineseDate;
}

#当前时间转换俄罗斯时间
function parseDateString($beijingTime = null) 
{
    // 1. 设置北京时区 (UTC+8)
    $beijingTz = new DateTimeZone('Asia/Shanghai');
    
    // 2. 如果未传入时间，使用当前北京时间
    $dateTime = $beijingTime 
        ? DateTime::createFromFormat('Y-m-d H:i:s', $beijingTime, $beijingTz)
        : new DateTime('now', $beijingTz);
    
    // 3. 转换为莫斯科时间 (UTC+3)
    $moscowTz = new DateTimeZone('Europe/Moscow');
    $dateTime->setTimezone($moscowTz);
    
    // 4. 格式化为带毫秒的 ISO 格式
    list($microtime, $timestamp) = explode(' ', microtime());
    $milliseconds = (int) ($microtime * 1000);
    
    return $dateTime->format("Y-m-d\TH:i:s") . '.' . str_pad($milliseconds, 3, '0', STR_PAD_RIGHT) . 'Z';
}

#获取汇率
function ForeignExchange($money=100,$bz="俄罗斯卢布"){
    if($bz=="人民币"){
        $from_money = "%E4%BF%84%E7%BD%97%E6%96%AF%E5%8D%A2%E5%B8%83";
    }else{
        $from_money = "%E4%BA%BA%E6%B0%91%E5%B8%81";
    }
    $ret = get_curl("https://sp0.baidu.com/5LMDcjW6BwF3otqbppnN2DJv/finance.pae.baidu.com/vapi/async/v1?from_money=".$from_money."&to_money=".urlencode($bz)."&from_money_num={$money}&srcid=5293&sid=282626_284830_110085_287513_287067_287700_287836_287168_280169_288370_283782_288270_287981_288710_288713_288717_288742_288747_288748_284553_287634_281879_288152_284820_289082_265881_289541_289948_289955_282932_290205_290178_290365_286491_290555_290562_282553_282805_287977_290976_291233_290521_277936_290424_256739_290666_288253_291481_290056_288559_286862_291710_291726_290567_283016_291948_282228_292167_292082_292247_292250_292251_292355_287174_287718_282466_292508_292345_292710_292773_292786_292413_292460_292454_292822_289739&cb=jsonp_1705301850137_11480");
    $json = jsonp_decode($ret,true);
    if($json['ResultCode']==0){
        if($json['Result'][1]['DisplayData']['resultData']['tplData']['result']['pre_close']){
            $num = round($json['Result'][1]['DisplayData']['resultData']['tplData']['result']['pre_close'],3);
        }elseif ($json['Result'][1]['DisplayData']['resultData']['tplData']['result']['cur']['num']) {
            $num = round($json['Result'][1]['DisplayData']['resultData']['tplData']['result']['cur']['num'],3);
        }
        return ['name'=>$json['Result'][0]['DisplayData']['resultData']['tplData']['content1'],'num'=>$num];
    }
}

function roundedNum($num){
    $multiplier = pow(10, 2);
    $roundedNum = round($num * $multiplier) / $multiplier;
    return $roundedNum;
}

function calculateResult($a2, $b4, $a4, $c7, $b2) {
    // 條件判斷
    return roundedNum($a2 - ($a2 * $a4 + $c7) - $b2);
    $condition1 = $b4 > 25;
    $condition2 = $a2 * 12.58 > 7000;
    $condition3 = $b4 < 2;          // Excel 中的 AND(2>B4) 等價於 B4 < 2
    $condition4 = $a2 * 12.58 < 1500;

    if ($condition1 || $condition2 || $condition3 || $condition4) {
        return "超出限制";
    } else {
        // 計算公式：A2 - (A2*A4 + C7) - B2
        return roundedNum($a2 - ($a2 * $a4 + $c7) - $b2);
    }
}

function kuajing84(){
    $url = 'https://www.kuajing84.com/index/login/login.html';
    $post = 'field%5Busername%5D=17772086090&field%5Bpassword%5D=aA%40123456';
    $referer = 'https://www.kuajing84.com/index/login/login.html';
    $res = get_curl($url,$post,$referer,0,1);
    exit($res);
}

function text_str_replace($text){
    $replacements = array(
        "蓝色蓝色的"=>"蓝色",
        "灰色的灰色"=>"灰色",
        "白色的白色"=>"白色",
        "黑色的人"=>"黑色",
        "的" => "",
        "蓝色蓝色" => "蓝色",
        "灰金属金属"=>"灰金属",
        "银银（银）"=>"银",
        "紫色紫色"=>"紫色"
    );
    $result = str_replace(array_keys($replacements), array_values($replacements), $text);
    return $result;
}

function targetDateTime($row){
    $targetDateTime = $row['in_process_at'];
    $dateTime = new \DateTime($targetDateTime);
    $year  = $dateTime->format('Y');
    $month = $dateTime->format('m');
    $day   = $dateTime->format('d');
    $dateFolder = $year.$month.$day;
    $baseDir = ROOT . 'assets/order/';
    $targetDir = $baseDir . $dateFolder;
    return [$targetDir.'/'.$row['posting_number'].'.pdf','/assets/order/'.$dateFolder.'/'.$row['posting_number'].'.pdf'];
}

function findTypeIdByCategoryAndName($categoryId, $typeName) {
    static $cache = [];
    
    // 内存缓存检查
    $cacheKey = "{$categoryId}_{$typeName}";
    if (isset($cache[$cacheKey])) {
        return $cache[$cacheKey];
    }
    
    // 构建文件路径
    $filePath = ROOT . "assets/ozon/categories/{$categoryId}.json";
    
    // 文件存在检查
    if (!file_exists($filePath)) {
        // 可选：尝试从原始数据重建
        // 实际生产环境应通过后台任务维护
        return null;
    }
    
    // 加载分类数据
    $json = file_get_contents($filePath);
    $categoryData = json_decode($json, true);
    
    // 在子节点中查找匹配类型
    foreach ($categoryData['children'] ?? [] as $child) {
        if (isset($child['type_name']) && $child['type_name'] === $typeName) {
            // 缓存结果
            $cache[$cacheKey] = $child['type_id'];
            return $child['type_id'];
        }
    }
    
    return null;
}

function convertToUtf8($string) {
    // 先尝试检测编码
    $encoding = mb_detect_encoding($string, mb_list_encodings(), true);
    
    // 如果检测失败，尝试常见编码
    if (!$encoding) {
        $encodingsToTry = ['Windows-1251', 'ISO-8859-1', 'UTF-8'];
        foreach ($encodingsToTry as $enc) {
            if (@mb_check_encoding($string, $enc)) {
                $encoding = $enc;
                break;
            }
        }
    }
    
    // 转换编码
    return $encoding !== 'UTF-8' 
        ? mb_convert_encoding($string, 'UTF-8', $encoding ?: 'auto') 
        : $string;
}

/**
 * 智能获取 SKU：支持直接传入整数、数字字符串或 Ozon 商品链接
 * 
 * @param mixed $input 输入值（整数、数字字符串或 URL）
 * @return string|null 成功返回 SKU 字符串，失败返回 null
 */
function get_sku($input): ?string
{
    // 场景1：输入本身是整数或纯数字字符串
    if (is_numeric($input) && ctype_digit((string)$input)) {
        return (string)$input;
    }

    // 场景2：输入是 URL，解析路径中的 SKU
    if (is_string($input)) {
        $parsedUrl = parse_url($input);
        $path = $parsedUrl['path'] ?? '';

        // 匹配两种常见格式：
        // 1. URL 路径以 "-数字" 结尾（如 /product/name-1580335339）
        // 2. URL 路径以纯数字结尾（如 /product/1580335339）
        if (preg_match('/(?:-|\/)(\d+)(?:\/|$)/', $path, $matches)) {
            return $matches[1];
        }
    }

    return null;
}
function convertImageUrl($imageUrl) {
    $imageUrl = preg_replace('/_b\.jpg$/', '_.webp', $imageUrl);
    $imageUrl = preg_replace('/\\\/', '', $imageUrl);
    $imageUrl = str_replace('.jpg/', '.jpg_.webp', $imageUrl);
    return preg_replace('/"/', '', $imageUrl);
}

function attribute_id_value($attribute_id,$id){
    $data = json_decode(file_get_contents(SYSTEM_ROOT.'../assets/ozon/attributes/attribute_'.$attribute_id.'.json'),true);
    if($data){
        foreach ($data as $item){
            if($item['id']==$id){
                return ['dictionary_value_id'=>intval($id),'value'=>$item['value']];
            }
        }
        return false;
    }else{
        return false;
    }
}

function attribute_id_name($attribute_id,$name){
    $data = json_decode(file_get_contents(SYSTEM_ROOT.'../assets/ozon/attributes/attribute_'.$attribute_id.'.json'),true);
    if($data){
        foreach ($data as $item){
            if($item['value']==$name){
                return ['dictionary_value_id'=>intval($item['id']),'value'=>$item['value'],'name'=>$item['name']];
            }
        }
        return false;
    }else{
        return false;
    }
}

function attribute_id_names($attribute_id,$name){
    $data = json_decode(file_get_contents(SYSTEM_ROOT.'../assets/ozon/attributes/attribute_'.$attribute_id.'.json'),true);
    if($data){
        foreach ($data as $item){
            if($item['value']==$name){
                return ['dictionary_value_id'=>intval($item['id']),'value'=>$item['value']];
            }
        }
        return false;
    }else{
        return false;
    }
}

function isFileModifiedWithinDay($filePath) {
    // 检查文件是否存在
    if (!file_exists($filePath)) {
        return false;
    }
    
    // 获取文件最后修改时间戳
    $fileModTime = filemtime($filePath);
    
    // 获取当前时间戳
    $currentTime = time();
    
    // 计算24小时前的时间戳
    $oneDayAgo = $currentTime - (24 * 60 * 60);
    
    // 检查文件是否在24小时内被修改过
    return ($fileModTime >= $oneDayAgo);
}