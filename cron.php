<?php
if (preg_match('/Bai<PERSON>pider/', $_SERVER['HTTP_USER_AGENT']))
    exit;
$nosession = true;
require './includes/common.php';

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
if (function_exists("set_time_limit")) {
    @set_time_limit(0);
}
if (function_exists("ignore_user_abort")) {
    @ignore_user_abort(true);
}
$act = $_GET['act'];
try {
    $redis->connect('127.0.0.1', 6379);
    $redis->setOption(Redis::OPT_READ_TIMEOUT, 5); // 设置读取超时
    register_shutdown_function(function() use ($redis) {
        if ($redis->isConnected()) {
            $redis->close();
        }
    });
    switch ($act) {
    case 'image':
        $date = date("Y-m-d");
        $rs = $DB->getAll("SELECT A.*,<PERSON>.<PERSON>d,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.primary_image IS NULL AND A.date='$date' ORDER BY A.status = 'awaiting_packaging' DESC limit 10");
        $i = 0;
        foreach ($rs as $row) {
            $jsonurl = ROOT.'/assets/json/特征数据库/' . $row['sku'] . '.json';
            $i++;
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $result = $client->characteristics($row);
            weightdata($row['sku'],$result['result'][0]);
            $offs = ['primary_image' => $result['result'][0]['primary_image']];
            foreach ($result['result'][0]['attributes'] as $item) {
                if ($item['id'] == 10096) {          #颜色
                    $ii = 0;
                    $color = ''; // 初始化颜色字符串
                    foreach ($item['values'] as $x) {
                        $colorInfo = attribute_id_name(10096, $x['value']); // 假设$x['value']是颜色值如"красный"
                        if ($colorInfo) {
                            if ($ii >= 1) {
                                $color .= '、' . $colorInfo['name']; // 从第二个开始前面加顿号
                            } else {
                                $color = $colorInfo['name']; // 第一个不加符号
                            }
                            $ii++;
                        }
                    }
                    $offs['color'] = $color;
                } else if ($item['id'] == 4382) {     #尺寸
                    $offs['dimensions'] = $item['values'][0]['value'];
                } else if ($item['id'] == 10051) {    #材质
                    $offs['material'] = Transmart($item['values'][0]['value']);
                }elseif($item['id'] == 4497){
                    $offs['weight'] = $item['values'][0]['value'];
                }elseif($item['id'] == 4295){
                    $offs['size'] = $item['values'][0]['value'];
                }elseif($item['id'] == 9814){
                    $offs['num'] = $item['values'][0]['value'];
                }
            }
            if(empty($offs['weight'])){
                $offs['weight'] = $result['result'][0]['weight'];
            }
            $DB->update('order', $offs, ['order_id' => $row['order_id']]);
            
            file_put_contents($jsonurl, json_encode($result['result'][0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        }
        if ($rs) {
            echo '目前已处理' . $i;
        } else {
            echo '没有可处理的数据';
        }
    break;
    case 'gmcron':
        //exit("SELECT A.*,B.ClientId,B.key,B.currency_code,B.apistatus FROM ozon_cron A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.time<'".time()."' AND A.cron=1 AND A.status NOT IN ('ok', 'no') AND B.apistatus=1 limit 100");
        $rss = $DB->getAll("SELECT A.*,B.ClientId,B.key,B.currency_code,B.apistatus FROM ozon_cron A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.time<'".time()."' AND A.cron=1 AND A.status NOT IN ('ok', 'no') AND B.apistatus=1 limit 50");
        $c = 0;
        foreach ($rss as $row) {
            $time = time()-$row['time'];  # 用于判断长期未执行恢复
            if($time>30){
                if($row['nums']==0){
                    $DB->update('cron', ['time'=>time()+10,'cron'=>0,'nums'=>1], ['id'=>$row['id']]);
                }else{
                    $DB->update('cron', ['time'=>time()+10,'cron'=>0], ['id'=>$row['id']]);
                }
            }
            $c++;
        }
        $row = [];
        $rs = $DB->getAll("SELECT A.*,B.ClientId,B.key,B.currency_code,B.apistatus FROM ozon_cron A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.time<'".time()."' AND A.cron=0 AND A.status NOT IN ('ok', 'no') AND B.apistatus=1 limit 200");
        
        $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
        $channel = $connection->channel();
        $channel->queue_declare('task_queue', false, true, false, false);
        foreach ($rs as $row) {
            $DB->update('cron', ['cron'=>1], ['id' => $row['id']]);
            // 准备数据
            $data = ['type' => 'gmcron','id'=>$row['id']]; // 你的任务数据
            // 创建消息
            $msg = new AMQPMessage(
                json_encode($data),
                ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT] // 使消息持久化
            );
            // 发送消息
            $channel->basic_publish($msg, '', 'task_queue');
            $i++;
        }
        // 关闭连接
        $channel->close();
        $connection->close();
        if ($rs or $rss) {
            echo '目前已处理' . $i .'，重置任务已处理'.$c;
        } else {
            echo '没有可处理的数据';
        }
    break;
    case 'seller':
        // 初始化代理轮换器
        $rotator = new \lib\ProxyRotator();
        $rs = $DB->getAll("SELECT * FROM ozon_sellerjk WHERE date<'" . date("Y-m-d") . "' limit 1");
        $i=0;
        $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
        $channel = $connection->channel();
        $channel->queue_declare('task_queue', false, true, false, false);
    
        foreach ($rs as $row) {
            $result = $rotator->ozoncurl(
                'https://seller.ozon.ru/api/site/seller-analytics/what_to_sell/data/v3',
                json_decode('{"limit":"50","offset":"0","filter":{"stock":"any_stock","period":"monthly","company_ids":["'.$row['seller_id'].'"]},"sort":{"key":"sum_gmv_desc"}}',true),
                'https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers',
                $cookie
            );
            $importer = new \lib\JsonImporter($DB);
            $importer->importFullOzonProducts($result['body'], false, $row);
            $json = json_decode($result['body'],true);
            
            $data['date'] = date("Y-m-d");
            if(empty($row['sellerName'])){
                $data['sellerName'] = $json['items'][0]['sellerName'];
            }
            $DB->update('sellerjk', $data, ['uid'=>$row['uid'],'seller_id'=>$row['seller_id']]);
            foreach ($json['items'] as $item){
                $data = ['type' => 'getprice','id'=>$item['sku']]; // 你的任务数据
                // 创建消息
                $msg = new AMQPMessage(
                    json_encode($data),
                    ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT] // 使消息持久化
                );
                $channel->basic_publish($msg, '', 'task_queue');
            }
            $i++;
        }
        $channel->close();
        $connection->close();
        echo "已成功处理{$i}条任务";
        
    break;
    case 'store':
        // 创建单个连接复用
        $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
        $channel = $connection->channel();
        $channel->queue_declare('productsync', false, true, false, false);
        // 处理产品同步
        try {
            $up = $DB->find('store', '*', ['updata_products'=>1,'apistatus'=>1]);
            if($up) {
                $data = ['type' => 'productsync', 'id' => $up['id']];
                $msg = new AMQPMessage(
                    json_encode($data),
                    ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
                );
                $channel->basic_publish($msg, '', 'productsync');
                $DB->update('store', ['updata_products' => 0], ['id' => $up['id']]);
            }
        } catch (Exception $e) {
            // 错误处理
        }
        
        // 处理订单
        $i = 0;
        try {
            $rs = $DB->getAll("SELECT * FROM ozon_store WHERE apistatus=1 AND time<'" . time() . "' ORDER BY time ASC limit 5");
            foreach ($rs as $row) {
                if($redis->get('store_'.$row['ClientId'].'_UID'.$row['uid']) or $row['ClientId']==1917762 or $row['ClientId']==3012070 or $row['ClientId']==3012123 or $row['ClientId']==3012192 or $row['ClientId']==3012157){
                    $DB->update('store', ['time' => time() + rand(3600, 6200)], ['id' => $row['id']]);
                    continue;
                }
                $DB->update('store', ['time' => time() + rand(180, 1200)], ['id' => $row['id']]);
                $amddata = ['type' => 'orderadd', 'data' => $row];
                $msg = new AMQPMessage(
                    json_encode($amddata),
                    ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
                );
                $channel->basic_publish($msg, '', 'order');
                $i++;
            }
        } catch (Exception $e) {
            // 错误处理
        }
        
        $channel->close();
        $connection->close();
        echo "已成功处理{$i}条店铺任务";
    break;
    case 'xgm':
        // 实例化锁
        $lock = new \lib\NativeRedisLock('order_lock', 30);
        if ($lock->acquire()) {
            try {
                $rs = $DB->getAll("SELECT A.*,B.ClientId,B.key,B.currency_code FROM ozon_cron A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.time<'" . time() . "' AND A.status NOT IN ('ok', 'no') limit 10");
                foreach ($rs as $row) {
                    $jsonurl = './assets/json/a/' . $row['sku'] . '.json';
                    switch ($row['status']) {
                        case '准备中':
                            $client = new \lib\OzonApiClient();
                            $save = ['offer_id' => $client->generate_offer_id(), 'status' => '采集数据'];
                            $h = ['Content-Type: application/json'];
                            $res = get_curl('http://127.0.0.1:3000/api/sku', json_encode(['sku' =>strval($data['sku']), 'limit' => 10, 'language' => 'ru']), 0, 0, 0, 0, 0, $h);
                            $json = json_decode($res, true);
                            if($json['items'][0]['variantId']){
                                $save['variantId'] = $json['items'][0]['variantId'];
                            }
                        break;
                        case '采集数据':
                            $h = ['Content-Type: application/json'];
                            $res = get_curl('http://127.0.0.1:3000/api/sku-query', json_encode(['sku' =>strval($data['sku']), 'limit' => 10, 'language' => 'ru']), 0, 0, 0, 0, 0, $h);
                            $json = json_decode($res, true);
                            if (!$json['msg'] or count($json['items'])!=0) {
                                foreach ($json['items'] as $item){
                                    if($item['variant_id']==$row['variantId']){
                                        $variantIdData = $item;
                                        break; #跳出循环
                                    }
                                }
                                if(!$variantIdData)$variantIdData = $json['items'][0];
                                file_put_contents($jsonurl, json_encode($variantIdData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                                $save = ['return' => json_encode($variantIdData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 'status' => '成功保存数据'];
                            } else {
                                if(count($json['items'])==0){
                                    $save = ['status' => 'no', 'msg' => '官方数据封锁，无销量'];
                                }else{
                                    $save = ['status' => 'no', 'msg' => '第1步保存数据出错'];
                                }
                                
                            }
                        break;
                        case '成功保存数据':
                            $json = json_decode(file_get_contents($jsonurl), true, JSON_THROW_ON_ERROR);
                            $height = $depth = $width = $weight = null;
                            foreach ($json['attributes'] as $item) {
                                switch ($item['key']) {
                                    case 9456: // 高 (毫米)
                                        $height = (int) $item['value'];
                                        break;
                                    case 9454: // 长 (毫米)
                                        $depth = (int) $item['value'];
                                        break;
                                    case 9455: // 宽 (毫米)
                                        $width = (int) $item['value'];
                                        break;
                                    case 4497: // 商品重量 (克)
                                        $weight = (float) $item['value'];
                                        break;
                                    case 8229:
                                        $typeName = $item['value'];
                                        break;
                                }
                            }
                            $categoryId = $json['categories'][2]['id']; // 示例：蜂蜜制作分类ID
                            $typeId = findTypeIdByCategoryAndName(json_decode(file_get_contents('./assets/json/俄语类目.json'), true)['result'], $categoryId, $typeName);
                            if(!$typeId){
                                $save = ['status' => 'no', 'msg' => '采集分类ID,采集失败'];
                            }else{
                                $save = [
                                    'title' => $json['name'],
                                    'height' => $height,
                                    'depth' => $depth,
                                    'width' => $width,
                                    'weight' => $weight,
                                    'description_category_id' => $categoryId,
                                    'type_id' => $typeId,
                                    'primary_image' => $json['images'][0]['url'],
                                    'status' => '数据初始化中'
                                ];
                            }
                            
                        break;
                        case '数据初始化中':
                            $client = new \lib\OzonApiClient();
                            $json = json_decode(file_get_contents($jsonurl), true, JSON_THROW_ON_ERROR);
                            foreach ($json['attributes'] as $item) {
                                $values = [];
                                if ($item['collection']) {
                                    foreach ($item['collection'] as $x) {
                                        $values[] = ['dictionary_value_id' => 0, 'value' => $x];
                                    }
                                } else {
                                    if($item['key']!=22967)$values[] = ['dictionary_value_id' => 0, 'value' => $item['value']];
                                }
                                $attributes[] = ['id' => (int) $item['key'], 'complex_id' => 0, 'values' => $values];
                            }
                            file_put_contents($jsonurl, json_encode(['attributes' => $attributes], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                            $save = ['return' => json_encode(['attributes' => $attributes], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 'status' => '数据处理成功'];
                        break;
                        case '数据处理成功':
                            if (!$row['title'] or !$row['primary_image'] or !$row['description_category_id'] or !$row['weight'] or !$row['type_id']) {
                                $save = ['status' => '采集数据', 'msg' => '数据缺失，重新加载'];
                            } else {
                                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                                $row['return'] = file_get_contents($jsonurl);
                                $gmdata = $client->productimportgm($row);
                                if ($gmdata[0]) {
                                    $save = ['status' => '数据上传中', 'task_id' => $gmdata[0]];
                                    $save['time'] = time() + rand(120, 300);
                                } else {
                                    $save = ['status' => 'no', 'msg' => '数据上传失败', 'log' => $gmdata];
                                }
                            }
                        break;
                        case '数据上传中':
                            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                            $data = $client->productsstocks($row);                               #商品仓库添加库存
                            if ($data)
                            $save = ['data' => 'NULL', 'product_id' => $data, 'status' => 'ok'];
                        break;
                    }
                    if ($save) {
                        if (!$save['time'])
                            $save['time'] = time() + rand(1, 8);
                        $DB->update('cron', $save, ['id' => $row['id']]);
                    } else {
                        //$DB->update('cron', ['status'=>'no'], ['id'=>$row['id']]);
                    }
                    $i++;
                }
                if ($rs) {
                    echo '目前已处理' . $i;
                } else {
                    echo '没有可处理的数据';
                }
                sleep(5);
            } finally {
                $lock->release();
            }
        } else {
            echo "Failed to acquire lock\n";
        }
    break;
    case 'gd': #将商品归档，删除没有SKU的商品
        $clients = $DB->getRow("SELECT * FROM `ozon_client` WHERE deltime<'".time()."' AND status=0 ORDER BY deltime ASC");
        
        if ($clients) {
            $client = new \lib\OzonApiClient($clients['ClientId'], $clients['key']);
            $data = $client->productlist(['visibility' => 'ARCHIVED']);
            
            $array = []; // 显式初始化
            $i = 0;      // 初始化计数器
            
            foreach ($data['result']['items'] as $x) {
                $array[] = ['offer_id' => $x['offer_id']];
                $i++;
            }
            $DB->exec("UPDATE ozon_client SET deltime='" . (time() + rand(180, 600)) . "' WHERE id='{$clients['id']}'");
            if ($array) {
                $data2 = $client->productsdelete($array);
                exit('目前已经处理 ' . $i . ' 多条数据处理删除，'.$clients['id']);
            }
            
        } else {
            exit('目前没有需要处理的，管理账户');
        }
        Clientlimit();
    break;
    case 'ds': #订单状态更新
        $time = time();
        $rs = $DB->getAll("SELECT A.*,B.ClientId,B.key,B.apistatus FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.time<'{$time}' AND A.status!='cancelled' AND A.status!='cancelled_from_split_pending' AND A.status!='delivered' AND B.apistatus=1 ORDER BY A.status = 'awaiting_packaging' DESC, A.time ASC limit 20");
        $list2 = [];
        foreach($rs as $row){
            if(empty($row['ClientId']) or empty($row['key']) or $redis->get('store_'.$row['ClientId'].'_UID'.$row['uid'])){
                $DB->update('order', ['time'=>$time+3600], ['posting_number'=>$row['posting_number']]);
                continue;
            }
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $json=$client->postingfbsget($row['posting_number']);
            if(empty($json))continue;
            if($json['code']==7){
                if($json['message']=='Company is blocked, please contact support'){
                    $store['apistatus'] = 3; # 公司已封锁，请联系支持
                }else if($json['message']=='Api-key is deactivated, use another one or generate a new one'){
                    $store['apistatus'] = 2; # Api-key 已失效
                }
                $DB->update('store', $store, ['ClientId'=>$row['ClientId']]);
            }
            if($json['code']){
                $DB->update('order', ['time'=>time()+3600], ['storeid'=>$row['id']]);
                $redis->set('store_'.$row['ClientId'].'_UID'.$row['uid'],$json['message'],3600);
                echo(json_encode($json).'</br>'.$row['ClientId'].'</br></br>');
                continue;
            }
            $importer = new \lib\JsonImporter($DB);
            $importer->importorder($json, false, $row);
            $i++;
            if($row['status']=='awaiting_packaging' or $row['status']=='awaiting_deliver'){ #等待备货 发货。
                $data['time']=time()+rand(600, 1800); # 随机10~30分钟
            }else{
                $data['time']=time()+rand(21600, 43200); # 随机6个小时到12小时
            }
            if($row['packagelabel']==0 and $row['status']=='awaiting_deliver'){ # 是否下载面单
                if($client->packagelabel($row)){
                    $data['packagelabel'] = 1;
                }
            }
            $DB->update('order', $data, ['posting_number'=>$row['posting_number']]);
        }
        if ($rs) {
            exit('目前已经处理 ' . $i . ' 多条数据');
        } else {
            echo '没有可处理的数据';
        }
    break;
    case 'dailiip':
        $localProxies = parse_proxies(file_get_contents('http://api1.ydaili.cn/tools/BUnlimitedApi.ashx?key=22E47B093F9829E88CC009A16D4BF9685381987F155342AC&action=BUnlimited&qty=1&orderNum=SH20250514172603124&isp=&format=txt&split=1'));
        if($localProxies){
            $dailiget = curl_get_daili('https://seller.ozon.ru/app/analytics/what-to-sell/ozon-bestsellers',$localProxies);
            if($dailiget){
                $cookie = getSubstr($dailiget, '__Secure-ETC=', ';');
        	    if($cookie){
        	        $redis->set('ETC_000000001','__Secure-ETC='.$cookie.';',30);
        	        $result = json_encode($localProxies);
                    $redis->set('dailiip_000000001',$result,30);
        	    }
            }
        }
        exit($result);
    break;
    default:
        $url = 'https://www.kuajing84.com/index/Accountorder/order_list_submit/order_type/6';
        $post = 'page=1&limit=100';
        $res = get_curl($url, $post, 0, 'PHPSESSID=bibf8r6kijepcq39e8d6rsdrk0');
        //
        $json = json_decode($res, true);
        foreach ($json['data'] as $row) {
            if ($row['out_weight']) {
                echo ($row['out_weight'] . '-' . $row['sheet_order_sn'] . '</br>');
                $g = roundedNum($row['out_weight'] * 1000);
                $DB->update('order', ['out_weight' => $g], ['tracking_number' => $row['sheet_order_sn']]);
            }
        }//exit($res); UPDATE `ozon_cron` SET `clientids`='16',`status`='准备中',`cron`=0,`msg`='' WHERE `msg`LIKE'数据获取失败，服务器ID:0' limit 100;
        // UPDATE `ozon_cron` SET `status`='准备中',`cron`=0,`msg`='' WHERE date='2025-06-09' AND `msg`LIKE'数据查询失败' limit 100;
    }
} finally {
    $redis->close();
}
function Clientlimit(){
    global $DB;
    $rs = $DB->getAll("SELECT * FROM ozon_client WHERE time<'".time()."' AND status=0 limit 20");
    $clientids=0;
    foreach ($rs as $row) {
        if($clientids==0 and $row['limit']>50){
            $clientids = $row['id'];
        }

        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $res = $client->productinfolimit(1,true);
        
        if($res['code']==7){$DB->update('client', ['status'=>1], ['id'=>$row['id']]);continue;}
            $daily_create = $res['daily_create']['limit']-$res['daily_create']['usage'];
            $daily_update = $res['daily_update']['limit']-$res['daily_update']['usage'];
            $limit        = $res['total']['limit']-$res['total']['usage'];
            $DB->update('client', ['daily_create'=>$daily_create,'daily_update'=>$daily_update,'limit'=>$limit,'time'=>time()+rand(60, 120)], ['id'=>$row['id']]);
        
    }
    return $clientids;
}