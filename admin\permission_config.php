<?php
include("../includes/common.php");

// 检查管理员登录
if($islogin!=1) {
    header('Location: login.html');
    exit;
}

// 处理配置保存
if(isset($_POST['save_config'])) {
    $enable_store_limit = isset($_POST['enable_store_limit']) ? true : false;
    $enable_menu_control = isset($_POST['enable_menu_control']) ? true : false;
    $enable_status_check = isset($_POST['enable_status_check']) ? true : false;
    
    // 更新配置文件
    $config_content = file_get_contents('../config.php');
    
    // 更新权限配置
    $new_permission_config = "    /*权限系统配置*/
    \$permission_config=array(
        'enable_store_limit' => " . ($enable_store_limit ? 'true' : 'false') . ", // 是否启用店铺数量限制 (true=启用, false=禁用)
        'enable_menu_control' => " . ($enable_menu_control ? 'true' : 'false') . ", // 是否启用菜单权限控制 (true=启用, false=禁用)
        'enable_status_check' => " . ($enable_status_check ? 'true' : 'false') . ", // 是否启用用户状态检查 (true=启用, false=禁用)
    );";
    
    // 替换配置
    $pattern = '/\/\*权限系统配置\*\/\s*\$permission_config=array\([^)]+\);/s';
    if(preg_match($pattern, $config_content)) {
        $config_content = preg_replace($pattern, $new_permission_config, $config_content);
    } else {
        // 如果不存在，在文件末尾添加
        $config_content = rtrim($config_content) . "\n\n" . $new_permission_config . "\n";
    }
    
    if(file_put_contents('../config.php', $config_content)) {
        $success_msg = "配置保存成功！";
    } else {
        $error_msg = "配置保存失败，请检查文件权限！";
    }
}

    // 读取当前配置
    $current_config = [
        'enable_store_limit' => $permission_config['enable_store_limit'] ?? true,
        'enable_menu_control' => $permission_config['enable_menu_control'] ?? true,
        'enable_status_check' => $permission_config['enable_status_check'] ?? true
    ];
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>权限系统配置</title>
    <link rel="stylesheet" href="../assets/component/layui/css/layui.css">
    <style>
        .config-container { padding: 20px; }
        .config-item { margin-bottom: 20px; }
        .config-item label { display: block; margin-bottom: 5px; font-weight: bold; }
        .config-item .description { color: #666; font-size: 12px; margin-top: 5px; }
        .success-msg { color: green; margin-bottom: 10px; }
        .error-msg { color: red; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="config-container">
        <h2>权限系统配置管理</h2>
        
        <?php if(isset($success_msg)): ?>
            <div class="success-msg"><?php echo $success_msg; ?></div>
        <?php endif; ?>
        
        <?php if(isset($error_msg)): ?>
            <div class="error-msg"><?php echo $error_msg; ?></div>
        <?php endif; ?>
        
        <form method="post" class="layui-form">
            <div class="config-item">
                <label>
                    <input type="checkbox" name="enable_store_limit" lay-skin="primary" title="启用店铺数量限制" 
                           <?php echo $current_config['enable_store_limit'] ? 'checked' : ''; ?>>
                    启用店铺数量限制
                </label>
                <div class="description">
                    启用后，用户添加店铺时会检查数量限制。禁用后，用户可以无限制添加店铺。
                </div>
            </div>
            
            <div class="config-item">
                <label>
                    <input type="checkbox" name="enable_menu_control" lay-skin="primary" title="启用菜单权限控制" 
                           <?php echo $current_config['enable_menu_control'] ? 'checked' : ''; ?>>
                    启用菜单权限控制
                </label>
                <div class="description">
                    启用后，禁用状态的用户只能看到基础菜单。禁用后，所有用户都能看到完整菜单。
                </div>
            </div>
            
            <div class="config-item">
                <label>
                    <input type="checkbox" name="enable_status_check" lay-skin="primary" title="启用用户状态检查" 
                           <?php echo $current_config['enable_status_check'] ? 'checked' : ''; ?>>
                    启用用户状态检查
                </label>
                <div class="description">
                    启用后，禁用状态的用户无法进行敏感操作。禁用后，所有用户都能正常操作。
                </div>
            </div>
            

            
            <div class="config-item">
                <button type="submit" name="save_config" class="layui-btn layui-btn-normal">保存配置</button>
                <a href="index.php" class="layui-btn layui-btn-primary">返回管理面板</a>
            </div>
        </form>
        
        <div class="layui-card" style="margin-top: 30px;">
            <div class="layui-card-header">配置说明</div>
            <div class="layui-card-body">
                <p><strong>店铺数量限制：</strong>控制用户最多可以添加多少个店铺（严格按照数据库max_shops字段值）</p>
                <p><strong>菜单权限控制：</strong>控制禁用用户是否能看到完整菜单</p>
                <p><strong>用户状态检查：</strong>控制是否检查用户状态（status字段）</p>
                <p><strong>注意：</strong>修改配置后需要刷新页面才能生效</p>
            </div>
        </div>
    </div>
    
    <script src="../assets/component/layui/layui.js"></script>
    <script>
        layui.use(['form'], function(){
            var form = layui.form;
            form.render();
        });
    </script>
</body>
</html> 