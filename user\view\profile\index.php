<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>会员中心</title>
<link rel="stylesheet" href="/assets/component/layui/css/layui.css">
<style>
  body {
    background-color: #f2f6fc;
    padding: 20px;
  }
  .main-container {
    max-width: 1000px;
    margin: 0 auto;
  }
  .layui-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  }
  .layui-card-header {
    font-weight: 600;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #ebeef5;
  }
  .info-item {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px dashed #e6e6e6;
    align-items: center;
  }
  .info-item:last-child {
    border-bottom: none;
  }
  .info-label {
    width: 120px;
    font-weight: 500;
    color: #606266;
    text-align: right;
    padding-right: 20px;
  }
  .info-value {
    color: #303133;
    flex-grow: 1;
    min-height: 22px;
  }
  .level-vip {
    color: #FFB800;
    font-weight: bold;
  }
  .level-svip {
      color: #FF5722;
      font-weight: bold;
  }
  .level-enterprise {
      color: #a233c6;
      font-weight: bold;
  }
  #expiryDate.expired {
      color: #F56C6C;
      font-weight: bold;
  }
  #redeem-code-input {
      text-transform: uppercase;
  }
</style>
</head>
<body>

<div class="main-container">
  <div class="layui-row layui-col-space15">
    <!-- 用户信息 -->
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">
          <i class="layui-icon layui-icon-username" style="margin-right: 8px;"></i>基本信息
        </div>
        <div class="layui-card-body">
          <div class="info-item">
            <div class="info-label">用户名</div>
            <div class="info-value" id="username"><span class="layui-badge layui-bg-gray">加载中...</span></div>
          </div>
          <div class="info-item">
            <div class="info-label">当前身份</div>
            <div class="info-value" id="userLevel"><span class="layui-badge layui-bg-gray">加载中...</span></div>
          </div>
          <div class="info-item">
            <div class="info-label">会员到期</div>
            <div class="info-value" id="expiryDate"><span class="layui-badge layui-bg-gray">加载中...</span></div>
          </div>
          <div class="info-item">
            <div class="info-label">绑定邮箱</div>
            <div class="info-value" id="email"><span class="layui-badge layui-bg-gray">加载中...</span></div>
          </div>
          <div class="info-item">
            <div class="info-label">绑定手机</div>
            <div class="info-value" id="phone"><span class="layui-badge layui-bg-gray">加载中...</span></div>
          </div>
          <div class="info-item">
            <div class="info-label">最大店铺数</div>
            <div class="info-value" id="maxShops"><span class="layui-badge layui-bg-gray">加载中...</span></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 激活码 -->
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-header">
          <i class="layui-icon layui-icon-vercode" style="margin-right: 8px;"></i>兑换码激活
        </div>
        <div class="layui-card-body">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px; text-align:right; padding-right: 20px;">输入兑换码</label>
            <div class="layui-input-block" style="margin-left: 140px;">
              <input type="text" id="redeem-code-input" placeholder="请输入您的兑换码" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 140px;">
              <button class="layui-btn layui-btn-normal" id="redeem-btn">立即激活</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 激活日志 -->
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-header">
                <i class="layui-icon layui-icon-log" style="margin-right: 8px;"></i>激活日志
            </div>
            <div class="layui-card-body">
                <table class="layui-hide" id="activation-log-table" lay-filter="activation-log-table"></table>
            </div>
        </div>
    </div>
    
  </div>
</div>

<script src="/assets/component/layui/layui.js"></script>
<script>
layui.use(['layer', 'element', 'table', 'jquery'], function(){
  var layer = layui.layer;
  var element = layui.element;
  var table = layui.table;
  var $ = layui.jquery;
  
  // 用户身份映射
  const LEVEL_MAP = {
    1: '普通用户',
    2: '<span class="level-vip">VIP用户</span>',
    3: '<span class="level-svip">SVIP用户</span>',
    4: '<span class="level-enterprise">企业用户</span>'
  };

  // 页面初始化
  $(function(){
    fetchUserInfo();
    renderLogTable();
  });

  // 获取用户信息
  function fetchUserInfo() {
    $.ajax({
      url: '/user/ajax.php?act=get_user_profile',
      type: 'GET',
      dataType: 'json',
      xhrFields: { withCredentials: true },
      success: function(res) {
        if (res.code === 1) {
          updateUI(res.data);
        } else {
          layer.msg(res.msg || '获取用户信息失败', {icon: 5});
        }
      },
      error: function() {
        layer.msg('网络错误，请稍后再试', {icon: 2});
      }
    });
  }

  // 更新UI
  function updateUI(data) {
    const safeData = {
      username: data.username || '未设置',
      user_level: LEVEL_MAP[data.user_level] || '未知身份',
      expiry_date: data.expiry_date || 'N/A',
      email: data.email || '未绑定',
      phone: data.phone || '未绑定',
      max_shops: data.max_shops || 0
    };

    $('#username').text(safeData.username);
    $('#userLevel').html(safeData.user_level);
    $('#expiryDate').text(safeData.expiry_date);
    $('#email').text(safeData.email);
    $('#phone').text(safeData.phone);
    $('#maxShops').text(safeData.max_shops + ' 家');

    // 检查是否过期
    if (new Date(safeData.expiry_date) < new Date()) {
        $('#expiryDate').addClass('expired').append(' (已过期)');
    } else {
        $('#expiryDate').removeClass('expired');
    }
  }

  // 渲染激活日志表格
  function renderLogTable(){
      table.render({
        elem: '#activation-log-table',
        url: '/user/ajax.php?act=get_activation_log',
        method: 'get',
        xhrFields: { withCredentials: true },
        page: true,
        cols: [[
          {field: 'code', title: '兑换码', width: 250},
          {field: 'days_added', title: '增加天数', width: 100, align: 'center'},
          {field: 'activated_at', title: '激活时间', sort: true},
          {field: 'new_expiry_date', title: '激活后有效期至'}
        ]],
        response: {
            statusCode: 0 //规定成功的状态码，默认：0
        },
        parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.count,
                "data": res.data
            };
        }
    });
  }
  
  // 激活按钮点击事件
  $('#redeem-btn').on('click', function(){
      var code = $('#redeem-code-input').val().trim();
      if(code === ''){
          layer.msg('请输入兑换码', {icon: 7});
          return;
      }
      
      var loadingIndex = layer.load(1, {shade: [0.1,'#fff']});
      
      $.ajax({
          url: '/user/ajax.php?act=redeem_code',
          type: 'POST',
          dataType: 'json',
          data: { code: code },
          xhrFields: { withCredentials: true },
          success: function(res){
              layer.close(loadingIndex);
              if(res.code === 1){
                  // 使用 layer.msg 显示成功提示，并设置图标和关闭回调
                  layer.msg(res.msg, {icon: 1, time: 2500}, function(){
                      $('#redeem-code-input').val('');
                      fetchUserInfo(); // 刷新用户信息
                      table.reload('activation-log-table'); // 刷新日志
                  });
              } else {
                  layer.msg(res.msg || '激活失败', {icon: 5});
              }
          },
          error: function(){
              layer.close(loadingIndex);
              layer.msg('网络请求失败，请稍后再试', {icon: 2});
          }
      });
  });

});
</script>
</body>
</html>