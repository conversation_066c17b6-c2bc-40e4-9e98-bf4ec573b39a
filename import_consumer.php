<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>OZON商品单仓库导入系统</title>

    <style>
        :root {
            --primary-color: #1E9FFF;
            --secondary-color: #5FB878;
            --border-color: #e6e6e6;
            --card-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
        }
        
        body {
            background-color: #f8f8f8;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        
        .layui-container {
            padding: 15px;
            max-width: 1600px;
        }
        
        .layui-card {
            border-radius: 4px;
            box-shadow: var(--card-shadow);
            margin-bottom: 15px;
            border: none;
            transition: all 0.3s;
        }
        
        .layui-card-header {
            background-color: #f8f8f8;
            border-bottom: 1px solid var(--border-color);
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .layui-card-header h2, 
        .layui-card-header h3 {
            margin: 0;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .layui-card-body {
            padding: 10px;
        }
        
        .main-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .left-panel, .right-panel {
            flex: 1;
            min-width: 400px;
            max-width: 110%;
        }
        
        /* 商品信息区域 */
        #skuInput {
            
            min-height: 300px;
            font-family: Consolas, Monaco, monospace;
            resize: vertical;
        }
        
        /* 店铺布局 - 网格式 */
        .stores-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
            align-items: stretch;
        }
        
        .store-item {
            position: relative;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            padding: 6px 10px;
            display: flex;
            align-items: center;
            cursor: pointer;
            background: #fff;
            transition: all 0.2s;
            min-width: 100px;
            max-width: none;
            flex: 1 0 calc(25% - 8px); /* 一行显示4个店铺 */
            box-sizing: border-box;
        }
        
        @media (max-width: 1400px) {
            .store-item {
                flex: 1 0 calc(33.333% - 8px); /* 中等屏幕一行显示3个 */
            }
        }
        
        @media (max-width: 992px) {
            .store-item {
                flex: 1 0 calc(50% - 8px); /* 小屏幕一行显示2个 */
            }
        }
        
        @media (max-width: 576px) {
            .store-item {
                flex: 1 0 100%; /* 移动屏幕一行显示1个 */
            }
        }
        
        .store-item:hover {
            border-color: #d9d9d9;
            background-color: #fafafa;
        }
        
        .store-item.selected {
            border-color: var(--primary-color);
            background-color: #3366ff;
        }
        
        /* 完全隐藏勾选框和指示器 */
        .store-item .layui-form-checkbox,
        .store-checkbox-indicator {
            display: none !important;
        }
        
        .store-item input[type="checkbox"] {
            /* 使真实的checkbox不可见但可操作 */
            opacity: 0;
            position: absolute;
            pointer-events: none;
        }
        
        .store-content {
            display: flex;
            align-items: center;
            width: 100%;
            overflow: hidden;
        }
        
        .store-name {
            font-size: 13px;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 4px;
        }
        
        .group-tag {
            margin-left: 4px;
            padding: 1px 5px;
            background-color: #e6f7ff;
            border-radius: 10px;
            font-size: 11px;
            color: #1890ff;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .selected-mark {
            display: none;
            margin-left: 4px;
            color: #52c41a;
            font-size: 12px;
            flex-shrink: 0;
        }
        
        /* 仓库选择下拉 */
        .warehouse-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 200px;
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            margin-top: 4px;
        }
        
        .warehouse-dropdown-title {
            padding: 8px 12px;
            font-size: 13px;
            color: #666;
            background: #f5f5f5;
            border-bottom: 1px solid #e6e6e6;
        }
        
        .warehouse-list {
            max-height: 200px;
            overflow-y: auto;
            padding: 6px 0;
        }
        
        .warehouse-item {
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            display: block;
            width: 100%;
            text-align: left;
            border: none;
            background: none;
        }
        
        .warehouse-item:hover {
            background-color: #f5f5f5;
        }
        
        .warehouse-item.selected {
            background-color: #e6f7ff;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .warehouse-item.disabled {
            color: #ccc;
            cursor: not-allowed;
        }
        
        .warehouse-item.disabled:hover {
            background: none;
        }
        
        /* 选中后显示的标记 */
        .store-item.has-warehouse .selected-mark {
            display: inline-block;
        }
        
        /* 价格策略区域 */
        .price-strategy {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .strategy-item {
            flex: 1;
            min-width: 200px;
        }
        
        /* 按钮组 */
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .button-group .layui-btn {
            flex: 1;
            height: 42px;
            font-size: 16px;
        }
        
        /* 分组筛选器 */
        .group-filter-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }
        
        .group-filter {
            flex: 1;
            max-width: 250px;
        }
        
        /* 统计信息 */
        .stat-box {
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            border-left: 3px solid var(--secondary-color);
        }
        
        /* 响应式调整 */
        @media (max-width: 1400px) {
            .store-card {
                width: calc(50% - 15px); /* 中等屏幕一行两个 */
            }
        }
        
        @media (max-width: 992px) {
            .store-card {
                width: 100%; /* 小屏幕一行一个 */
            }
            
            .left-panel, .right-panel {
                min-width: 100%;
            }
        }
        
        @media (max-width: 768px) {
            .price-strategy {
                flex-direction: column;
            }
            
            .strategy-item {
                min-width: 100%;
            }
        }
        
        /* 优化仓库选择区域 */
        .warehouse-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .warehouse-select-content {
            flex-grow: 1;
            overflow: auto;
        }
        
        /* 优化卡片高度 */
        .store-card .layui-card-body {
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: 0;
        }
    </style>
</head>
<body>
<div class="layui-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2><i class="layui-icon layui-icon-cart"></i> 单仓库商品导入系统</h2>
        </div>
        
        <div class="layui-card-body">
            <div class="layui-form">
                <div class="main-container">
                    <!-- 左侧面板 - 商品信息 -->
                    <div class="left-panel">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <h3><i class="layui-icon layui-icon-form"></i> 1. 商品信息</h3>
                            </div>
                            <div class="layui-card-body">
                                <textarea id="skuInput" class="layui-textarea" 
                                          placeholder="每行格式：SKU 价格&#10;示例：123456 99.99"
                                          style="min-height: 300px;"></textarea>
                                <div class="stat-box">
                                    <span>有效商品：<span id="lineCount" class="layui-badge">0</span> 条</span>
                                    <small style="display: block; margin-top: 5px; color: #666;">
                                        <i class="layui-icon layui-icon-tips"></i> 
                                        自动去重相同SKU，超过50个商品将分批导入
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 价格策略和按钮组 -->
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <h3><i class="layui-icon layui-icon-set"></i> 3. 价格策略</h3>
                            </div>
                            <div class="layui-card-body">
                                <div class="price-strategy">
                                    <div class="strategy-item">
                                        <label class="layui-form-label">价格调整</label>
                                        <div class="layui-input-block">
                                            <select id="priceMode" class="layui-select">
                                                <option value="none">不调整</option>
                                                <option value="加模式">加 (+)</option>
                                                <option value="减模式">减 (-)</option>
                                                <option value="乘模式">乘 (×)</option>
                                                <option value="除模式">除 (÷)</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="strategy-item">
                                        <label class="layui-form-label">调整值</label>
                                        <div class="layui-input-block">
                                            <input type="number" id="priceValue" class="layui-input"
                                                   step="0.0001" min="0"
                                                   placeholder="输入调整值">
                                        </div>
                                    </div>
                                    
                                    <div class="strategy-item">
                                        <label class="layui-form-label">库存设置</label>
                                        <div class="layui-input-block">
                                            <input type="number" id="stockInput" class="layui-input"
                                                   min="1" step="1" 
                                                   placeholder="库存数量">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item" style="margin-top: 15px;">
                                    <label class="layui-form-label" style="width: auto; padding-left:0;">SKU分配</label>
                                    <div class="layui-input-block" style="margin-left: 100px;">
                                        <input type="checkbox" id="distributeSkus" lay-skin="switch" lay-text="均分|不均分" lay-filter="distributeSkus">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width: auto; padding-left:0;">导入模式</label>
                                    <div class="layui-input-block" style="margin-left: 100px;">
                                        <input type="checkbox" id="fastImportMode" lay-skin="switch" lay-text="极速|标准" lay-filter="fastImportMode" title="极速模式：5倍并发速度，适合服务器性能较好的情况">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 按钮组 -->
                        <div class="button-group">
                            <button id="clearCacheBtn" class="layui-btn layui-btn-primary">
                                <i class="layui-icon layui-icon-delete"></i> 清除缓存
                            </button>
                            <button id="resetBtn" class="layui-btn layui-btn-primary">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                            <button id="submitBtn" class="layui-btn layui-btn-disabled" disabled>
                                <i class="layui-icon layui-icon-upload"></i> 开始导入
                            </button>
                        </div>
                    </div>
                    
                    <!-- 右侧面板 - 仓库选择 -->
                    <div class="right-panel">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <div class="group-filter-container">
                                    <h3><i class="layui-icon layui-icon-app"></i> 2. 店铺仓库选择</h3>
                                    <div class="group-filter">
                                        <select id="groupFilter" class="layui-select" lay-filter="groupFilter">
                                            <option value="all">所有分组</option>
                                            <option value="ungrouped">未分组</option>
                                            <!-- 分组选项将通过JS动态填充 -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-card-body">
                                <div class="stores-container" id="storesContainer">
                                    <div class="layui-anim layui-anim-rotate layui-anim-loop">
                                        <i class="layui-icon layui-icon-loading"></i> 加载店铺中...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/static/layui/layui.js"></script>
<script>
layui.use(['form', 'layer', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 存储键名
    const STORAGE_KEY = 'ozon_import_settings';
    
    // 分组映射
    let groupMap = {};
    let shopGroupMap = {}; // 店铺ID到分组ID的映射
    
    // 初始化加载
    $(function(){
        // 检查缓存是否过期（超过24小时）
        const savedData = localStorage.getItem(STORAGE_KEY);
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                const now = Date.now();
                // 如果缓存超过24小时或没有时间戳，清除缓存
                if (!data.timestamp || (now - data.timestamp > 24 * 60 * 60 * 1000)) {
                    console.log('缓存已过期，清除旧数据');
                    localStorage.removeItem(STORAGE_KEY);
                }
            } catch (e) {
                console.error('解析缓存数据失败，清除缓存:', e);
                localStorage.removeItem(STORAGE_KEY);
            }
        }
        
        // 先加载分组数据，并在完成后再加载店铺
        loadShopGroups().then(() => {
            // 然后加载店铺数据
            loadStores();
        }).catch(err => {
            console.error('加载分组数据失败:', err);
            // 如果分组加载失败，仍然加载店铺数据
            loadStores();
        });
        
        $('#skuInput').on('input', updateLineCount);
        loadPriceSettings();

        // 新增：仓库项目点击事件 (事件委托)
        $('#storesContainer').on('click', '.warehouse-item:not(.disabled)', function() {
            const $this = $(this);
            const warehouseSelect = $this.closest('.warehouse-select');
            
            if ($this.hasClass('selected')) {
                // 取消选择
                $this.removeClass('selected');
                warehouseSelect.removeAttr('data-selected-warehouse');
            } else {
                // 选中
                $this.siblings().removeClass('selected');
                $this.addClass('selected');
                warehouseSelect.attr('data-selected-warehouse', $this.data('value'));
            }
            
            saveSettings();
        });
    });

    // 加载分组数据
    function loadShopGroups() {
        return new Promise((resolve, reject) => {
            layer.load(1, {shade: [0.1, '#fff']});
            $.ajax({
                url: 'ajax.php?act=manage_shop_groups&op=get',
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if(res && res.code === 1 && res.data && res.data.groups) {
                        // 创建分组映射
                        groupMap = {};
                        shopGroupMap = {}; // 清空旧映射
                        
                        res.data.groups.forEach(group => {
                            if (group.id && group.name) {
                                groupMap[group.id] = group.name;
                                
                                // 创建店铺到分组的映射
                                if (group.shopIds && Array.isArray(group.shopIds)) {
                                    group.shopIds.forEach(shopId => {
                                        shopGroupMap[shopId] = group.id;
                                    });
                                }
                            }
                        });
                        
                        // 渲染分组筛选器
                        renderGroupFilter(res.data.groups);
                        resolve(res.data.groups);
                    } else {
                        console.warn('分组数据加载失败或为空', res);
                        layer.msg('分组数据加载失败，将使用默认分组', {icon: 2});
                        renderGroupFilter([]); // 使用空数组渲染
                        reject(new Error('分组数据加载失败或为空'));
                    }
                },
                error: function(xhr, status, error) {
                    layer.closeAll('loading');
                    console.error('分组数据请求失败', error);
                    layer.msg('分组数据请求失败，将使用默认分组', {icon: 2});
                    renderGroupFilter([]); // 使用空数组渲染
                    reject(new Error('分组数据请求失败: ' + error));
                }
            });
        });
    }

    // 渲染分组筛选器
    function renderGroupFilter(groups) {
        const $filter = $('#groupFilter');
        $filter.find('option:gt(1)').remove(); // 清除旧分组选项
        
        if (groups && groups.length > 0) {
            groups.forEach(group => {
                if (group.id && group.name) {
                    $filter.append(`<option value="${group.id}">${group.name}</option>`);
                }
            });
            console.log('分组筛选器已更新，包含 ' + groups.length + ' 个分组');
        } else {
            console.log('没有可用的分组，只显示默认选项');
        }
        
        // 强制重新渲染select
        form.render('select');
    }

    // 加载店铺数据 - 修复仓库显示问题
    function loadStores() {
        layer.load(1, {shade: [0.1, '#fff']});
        $.ajax({
            url: '/api/get',
            dataType: 'json',
            success: function(res) {
                layer.closeAll('loading');
                if(res && res.stores && res.stores.length > 0) {
                    // 确保每个店铺都有warehouses数组
                    res.stores.forEach(store => {
                        if (!store.warehouses || !Array.isArray(store.warehouses)) {
                            store.warehouses = []; // 确保warehouses是数组
                        }
                    });
                    
                    renderStores(res.stores);
                    form.render();
                    
                    // 加载缓存的店铺和仓库选择
                    loadStoreSelections();
                    
                    $('#submitBtn').removeClass('layui-btn-disabled').prop('disabled', false);
                } else {
                    showError('店铺加载失败或没有可用店铺');
                    console.error('店铺数据错误:', res);
                }
            },
            error: function() {
                layer.closeAll('loading');
                showError('店铺数据加载失败');
            }
        });
    }

    // 加载价格策略缓存
    function loadPriceSettings() {
        const savedData = localStorage.getItem(STORAGE_KEY);
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                if (data.pricing) {
                    $('#priceMode').val(data.pricing.mode || 'none');
                    if (data.pricing.value) {
                        $('#priceValue').val(data.pricing.value);
                    }
                    form.render('select');
                }
                if (data.stock) {
                    $('#stockInput').val(data.stock);
                }
                if (data.fastImportMode !== undefined) {
                    $('#fastImportMode').prop('checked', data.fastImportMode);
                }
                if (data.distributeSkus !== undefined) {
                    $('#distributeSkus').prop('checked', data.distributeSkus);
                }
                // 重新渲染开关
                form.render('checkbox');
            } catch (e) {
                console.error('解析缓存数据失败:', e);
            }
        }
    }

    // 加载店铺和仓库选择缓存
    function loadStoreSelections() {
        // 先清除所有选择状态
        $('input[name="selectedStore"]').prop('checked', false);
        $('.store-item').removeClass('selected has-warehouse');
        $('.warehouse-dropdown').hide();
        $('.warehouse-item').removeClass('selected');
        $('.warehouse-dropdown').removeAttr('data-selected-warehouse').removeAttr('data-selected-name');
        
        const savedData = localStorage.getItem(STORAGE_KEY);
        if (!savedData) return;

        try {
            const data = JSON.parse(savedData);
            if (!data.stores || !data.stores.length) return;

            // 遍历缓存的店铺选择
            data.stores.forEach(savedStore => {
                const storeId = savedStore.storeId;
                const warehouseId = savedStore.warehouse;
                
                if (!storeId) return;
                
                // 检查店铺是否存在于当前页面
                const storeItem = $(`.store-item[data-store-id="${storeId}"]`);
                if (!storeItem.length) return; // 如果店铺不存在，跳过
                
                const checkbox = storeItem.find('input[type="checkbox"]');
                const warehouseDropdown = $(`.warehouse-dropdown[data-store-id="${storeId}"]`);
                
                if (checkbox.length) {
                    // 选中复选框
                    checkbox.prop('checked', true);
                    storeItem.addClass('selected');
                    
                    // 如果有选择仓库
                    if (warehouseId) {
                        // 找到对应的仓库并选中
                        const warehouseItem = warehouseDropdown.find(`.warehouse-item[data-value="${warehouseId}"]`);
                        if (warehouseItem.length) {
                            warehouseItem.addClass('selected');
                            warehouseDropdown.attr('data-selected-warehouse', warehouseId);
                            warehouseDropdown.attr('data-selected-name', warehouseItem.data('name'));
                            storeItem.addClass('has-warehouse');
                        }
                    }
                }
            });
            
            form.render('checkbox');
        } catch (e) {
            console.error('解析店铺选择缓存失败:', e);
        }
    }

    // 保存当前设置到本地存储
    function saveSettings() {
        try {
            // 清除之前的缓存数据
            localStorage.removeItem(STORAGE_KEY);
            
            const data = {
                stores: [],
                stock: $('#stockInput').val(),
                pricing: {
                    mode: $('#priceMode').val(),
                    value: $('#priceValue').val()
                },
                fastImportMode: $('#fastImportMode').is(':checked'),
                distributeSkus: $('#distributeSkus').is(':checked'),
                timestamp: Date.now() // 添加时间戳以便追踪缓存时间
            };

            // 收集选中的店铺和仓库
            $('input[name="selectedStore"]:checked').each(function() {
                const storeId = $(this).val();
                const warehouseDropdown = $(`.warehouse-dropdown[data-store-id="${storeId}"]`);
                const warehouseId = warehouseDropdown.attr('data-selected-warehouse');
                
                // 只保存有效的仓库选择
                if (warehouseId) {
                    data.stores.push({
                        storeId: storeId,
                        warehouse: warehouseId
                    });
                }
            });

            localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
            console.log('已保存设置，包含', data.stores.length, '个店铺选择');
        } catch (e) {
            console.error('保存设置失败:', e);
        }
    }

    // 重置所有设置
    function resetSettings() {
        // 清空商品输入
        $('#skuInput').val('');
        $('#lineCount').text('0');
        
        // 取消所有店铺选择
        $('input[name="selectedStore"]').prop('checked', false);
        $('.store-item').removeClass('selected has-warehouse');
        $('.warehouse-dropdown').hide();
        $('.warehouse-item').removeClass('selected');
        $('.warehouse-dropdown').removeAttr('data-selected-warehouse').removeAttr('data-selected-name');
        
        // 重置库存
        $('#stockInput').val('');
        
        // 重置价格策略
        $('#priceMode').val('none');
        $('#priceValue').val('').prop('disabled', true).addClass('layui-disabled');
        
        // 重置导入模式开关
        $('#fastImportMode').prop('checked', false);
        $('#distributeSkus').prop('checked', false);
        
        // 重置分组筛选
        $('#groupFilter').val('all');
        form.render('select');
        
        // 显示所有店铺
        $('.store-item').show();
        
        // 重新渲染表单
        form.render();
        
        // 清除本地存储
        localStorage.removeItem(STORAGE_KEY);
        
        layer.msg('已重置所有设置', {icon: 1});
    }

    // 店铺复选框选择事件处理
    form.on('checkbox(storeCheckbox)', function(data){
        const storeId = $(data.elem).val();
        handleStoreSelection(storeId, data.elem.checked);
    });

    // 价格策略切换处理
    form.on('select(priceMode)', function(data){
        const isDisabled = data.value === 'none';
        $('#priceValue')
            .prop('disabled', isDisabled)
            .toggleClass('layui-disabled', isDisabled)
            .val('');
        form.render('input');
        
        // 保存设置
        saveSettings();
    });
    
    // 分组筛选处理 - 修复空白问题
    form.on('select(groupFilter)', function(data) {
        const groupId = data.value;
        
        // 显示所有卡片
        $('.store-item').show();
        
        if (groupId === 'all') {
            return; // 显示所有
        }
        
        if (groupId === 'ungrouped') {
            // 显示未分组的店铺
            $('.store-item').each(function() {
                const cardGroupId = $(this).attr('data-group-id');
                if (cardGroupId && cardGroupId !== 'ungrouped') {
                    $(this).hide();
                }
            });
        } else {
            // 显示指定分组的店铺
            $('.store-item').each(function() {
                const cardGroupId = $(this).attr('data-group-id');
                if (cardGroupId !== groupId) {
                    $(this).hide();
                }
            });
        }
    });
    
    // 价格值和库存输入变化事件
    $('#priceValue, #stockInput').on('input', function() {
        saveSettings();
    });
    
    // 导入模式开关变化事件
    form.on('switch(fastImportMode)', function(data) {
        saveSettings();
    });
    
    form.on('switch(distributeSkus)', function(data) {
        saveSettings();
    });
    
    // 清除缓存按钮点击事件
    $('#clearCacheBtn').click(function() {
        layer.confirm('确定要清除所有缓存数据吗？', {icon: 3, title:'清除缓存确认'}, function(index){
            localStorage.removeItem(STORAGE_KEY);
            layer.msg('缓存已清除', {icon: 1});
            layer.close(index);
            // 重新加载页面以刷新所有数据
            location.reload();
        });
    });
    
    // 重置按钮点击事件
    $('#resetBtn').click(function() {
        layer.confirm('确定要重置所有设置吗？', {icon: 3, title:'重置确认'}, function(index){
            resetSettings();
            layer.close(index);
        });
    });
    
    // 提交处理
    $('#submitBtn').click(function() {
        try {
            const data = validateForm();
            if(data){
                submitData(data);
            }
        } catch (e) {
            showError(e.message);
        }
    });

    // 渲染店铺 - 网格布局
    function renderStores(stores) {
        const container = $('#storesContainer');
        container.empty();
        
        if (!stores || !stores.length) {
            container.html('<div class="layui-empty">没有可用的店铺</div>');
            return;
        }
        
        stores.forEach(store => {
            // 获取分组信息
            const groupId = shopGroupMap[store.id] || 'ungrouped';
            const groupName = groupMap[groupId] || '未分组';
            
            // 确保warehouses是数组
            const warehouses = store.warehouses || [];
            
            // 生成仓库选项
            let warehouseItems = '';
            if (warehouses.length > 0) {
                warehouses.forEach(wh => {
                    warehouseItems += `
                        <div class="warehouse-item ${wh.status === 0 ? 'disabled' : ''}" 
                             data-value="${wh.id}" data-name="${wh.name}">
                            ${wh.name}
                        </div>
                    `;
                });
            } else {
                warehouseItems = '<div class="warehouse-item disabled">暂无可用仓库</div>';
            }
            
            const storeItem = `
                <div class="store-item" data-store-id="${store.id}" data-group-id="${groupId}">
                    <input type="checkbox" name="selectedStore" value="${store.id}" title="" lay-filter="storeCheckbox">
                    <div class="store-content">
                        <span class="store-name" title="${store.storename}">${store.storename}</span>
                        <span class="group-tag">${groupName}</span>
                        <span class="selected-mark"><i class="layui-icon layui-icon-ok-circle"></i></span>
                    </div>
                    <div class="warehouse-dropdown" data-store-id="${store.id}">
                        <div class="warehouse-dropdown-title">选择仓库</div>
                        <div class="warehouse-list">
                            ${warehouseItems}
                        </div>
                    </div>
                </div>
            `;
            
            container.append(storeItem);
        });
        
        // 初始化点击事件
        $('.store-item').on('click', function(e) {
            // 阻止事件冒泡，防止点击仓库选项时也触发店铺点击
            if ($(e.target).closest('.warehouse-dropdown').length) {
                return;
            }
            
            const storeId = $(this).data('store-id');
            const checkbox = $(this).find('input[type="checkbox"]');
            
            // 切换选中状态
            checkbox.prop('checked', !checkbox.prop('checked'));
            
            // 更新选中状态
            handleStoreSelection(storeId, checkbox.prop('checked'));
        });
        
        // 仓库项目点击事件
        $(document).on('click', '.warehouse-item:not(.disabled)', function(e) {
            e.stopPropagation();
            
            const $this = $(this);
            const warehouseDropdown = $this.closest('.warehouse-dropdown');
            const storeId = warehouseDropdown.data('store-id');
            const storeItem = $(`.store-item[data-store-id="${storeId}"]`);
            
            // 更新选中状态
            warehouseDropdown.find('.warehouse-item').removeClass('selected');
            $this.addClass('selected');
            
            // 保存选中的仓库ID和名称
            warehouseDropdown.attr('data-selected-warehouse', $this.data('value'));
            warehouseDropdown.attr('data-selected-name', $this.data('name'));
            
            // 更新店铺项的状态
            storeItem.addClass('has-warehouse');
            
            // 隐藏下拉菜单
            warehouseDropdown.hide();
            
            // 保存设置
            saveSettings();
        });
        
        // 点击文档其他部分关闭仓库下拉
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.warehouse-dropdown').length && 
                !$(e.target).closest('.store-item.selected').length) {
                $('.warehouse-dropdown').hide();
            }
        });
        
        // 加载已保存的选择
        loadStoreSelections();
    }
    
    // 处理店铺选择状态
    function handleStoreSelection(storeId, isChecked) {
        const storeItem = $(`.store-item[data-store-id="${storeId}"]`);
        const warehouseDropdown = $(`.warehouse-dropdown[data-store-id="${storeId}"]`);
        
        // 关闭其他下拉
        $('.warehouse-dropdown').not(warehouseDropdown).hide();
        
        if (isChecked) {
            // 添加选中样式
            storeItem.addClass('selected');
            
            // 显示仓库下拉
            const offset = storeItem.offset();
            warehouseDropdown.css({
                top: '100%',
                left: 0
            }).show();
            
            // 清除之前的仓库选择
            warehouseDropdown.find('.warehouse-item').removeClass('selected');
            warehouseDropdown.removeAttr('data-selected-warehouse');
            warehouseDropdown.removeAttr('data-selected-name');
            storeItem.removeClass('has-warehouse');
        } else {
            // 移除选中样式
            storeItem.removeClass('selected has-warehouse');
            
            // 隐藏下拉并清除选择
            warehouseDropdown.hide();
            warehouseDropdown.find('.warehouse-item').removeClass('selected');
            warehouseDropdown.removeAttr('data-selected-warehouse');
            warehouseDropdown.removeAttr('data-selected-name');
        }
        
        // 保存设置
        saveSettings();
    }

    function validateForm() {
        // 商品数据 - 使用Map去重SKU
        const skuMap = new Map();
        const skuLines = document.getElementById('skuInput').value.split('\n');
        let duplicateCount = 0;
        
        skuLines.forEach(line => {
            const [sku, price] = line.trim().split(/\s+/);
            if (sku && !isNaN(price)) {
                if (skuMap.has(sku)) {
                    duplicateCount++;
                    console.log(`重复SKU: ${sku}, 保留最新价格: ${price}`);
                }
                // 如果SKU重复，使用最新的价格
                skuMap.set(sku, parseFloat(price));
            }
        });
        
        // 转换为数组
        const products = Array.from(skuMap, ([sku, price]) => ({ sku, price }));
        
        if (products.length === 0) throw new Error('至少需要一个有效商品');
        
        // 显示去重信息
        if (duplicateCount > 0) {
            layer.msg(`已自动去重 ${duplicateCount} 个重复SKU，当前有效商品: ${products.length} 个`, {icon: 1, time: 3000});
        }

        // 仓库选择
        const selectedStores = [];
        $('input[name="selectedStore"]:checked').each(function() {
            const storeId = $(this).val();
            const storeName = $(this).closest('.store-item').find('.store-name').text();
            const warehouseDropdown = $(`.warehouse-dropdown[data-store-id="${storeId}"]`);
            const warehouseId = warehouseDropdown.attr('data-selected-warehouse');
            
            if (!warehouseId) {
                throw new Error(`请为店铺 "${storeName}" 选择一个仓库`);
            }
            
            selectedStores.push({
                storeId: storeId,
                warehouse: warehouseId
            });
        });
        
        if (selectedStores.length === 0) throw new Error('请至少选择一个店铺和仓库');

        // 如果开启了均分，检查SKU数量是否足够
        if ($('#distributeSkus').is(':checked') && products.length < selectedStores.length) {
             showError(`SKU数量(${products.length})少于店铺数量(${selectedStores.length})，无法均分。`);
             return null;
        }

        // 库存验证
        var stock = parseInt(document.getElementById('stockInput').value);
        if(!stock){
            stock = 0;
        }

        // 价格策略
        const pricing = { mode: 'none' };
        const priceMode = document.getElementById('priceMode').value;
        if (priceMode !== 'none') {
            const value = parseFloat(document.getElementById('priceValue').value);
            if (isNaN(value)) throw new Error('请输入有效的调整值');
            pricing.mode = priceMode;
            pricing.value = value;
        }

        return {
            products,
            stores: selectedStores,
            stock,
            pricing
        };
    }

    function showError(message) {
        layer.msg(message, {icon: 2, time: 3000});
    }

    function submitData(data) {
        const distributeSkus = $('#distributeSkus').is(':checked');

        if (distributeSkus) {
            submitDistributed(data);
            return;
        }

        const BATCH_SIZE = 30; // 减少批量大小，增加并发效果
        const fastMode = $('#fastImportMode').is(':checked');
        const MAX_CONCURRENT = fastMode ? 5 : 3; // 快速模式5倍并发，标准模式3倍
        const totalProducts = data.products.length;
        
        // 如果商品数量少于批量大小，直接导入
        if (totalProducts <= BATCH_SIZE) {
            submitSingleBatch(data);
            return;
        }
        
        const totalBatches = Math.ceil(totalProducts / BATCH_SIZE);
        const estimatedTime = Math.ceil(totalBatches / MAX_CONCURRENT) * 3; // 预估时间（秒）
        
        // 分批导入
        const modeText = fastMode ? '🚀 极速模式' : '⚡ 标准模式';
        layer.confirm(`检测到 ${totalProducts} 个商品，将并发分批导入以提高速度！<br><br>` +
                     `${modeText}<br>` +
                     `📦 每批 ${BATCH_SIZE} 个商品，共 ${totalBatches} 批<br>` +
                     `🔥 最多 ${MAX_CONCURRENT} 个批次并发处理<br>` +
                     `⏱️ 预计耗时约 ${estimatedTime} 秒<br><br>` +
                     `相比串行导入速度提升约 ${MAX_CONCURRENT} 倍，是否继续？`, {
            icon: 3, 
            title: '🚀 并发批量导入确认',
            area: ['450px', 'auto']
        }, function(index) {
            layer.close(index);
            submitBatches(data, BATCH_SIZE);
        });
    }

    function submitDistributed(data) {
        const products = data.products;
        const stores = data.stores;
        const totalProducts = products.length;
        const totalStores = stores.length;
        const skusPerStore = Math.floor(totalProducts / totalStores);
        let remainder = totalProducts % totalStores;

        let confirmationMsg = `共 ${totalProducts} 个SKU, ${totalStores} 个店铺。<br>每个店铺将分配 ${skusPerStore} 个SKU。`;
        if (remainder > 0) {
            confirmationMsg += `<br>剩余 ${remainder} 个SKU将依次分配给前面的店铺。`;
        }
        confirmationMsg += `<br>是否继续？`;

        layer.confirm(confirmationMsg, {
            icon: 3,
            title: '均分SKU确认',
            area: ['350px', 'auto']
        }, function(index) {
            layer.close(index);
            
            let productIndex = 0;
            let storeIndex = 0;
            
            function importForNextStore() {
                if (storeIndex >= totalStores) {
                    layer.msg('所有店铺均分导入任务已完成！', {icon: 1, time: 5000});
                    return;
                }

                const store = stores[storeIndex];
                const storeName = $(`.store-item[data-store-id="${store.storeId}"] .store-name`).text() || `店铺ID: ${store.storeId}`;
                const numProductsForThisStore = skusPerStore + (remainder > 0 ? 1 : 0);
                const productsForStore = products.slice(productIndex, productIndex + numProductsForThisStore);

                productIndex += numProductsForThisStore;
                if (remainder > 0) {
                    remainder--;
                }
                
                storeIndex++;

                if (productsForStore.length > 0) {
                    const storeData = {
                        ...data,
                        products: productsForStore,
                        stores: [store]
                    };
                    
                    const batchSize = 30; // 与主函数保持一致
                    const totalBatches = Math.ceil(productsForStore.length / batchSize);
                    
                    layer.msg(`开始为店铺【${storeName}】并发导入 ${productsForStore.length} 个SKU (${totalBatches} 批并发处理)`, {icon: 16, shade: 0.1, time: 3000});

                    submitBatches(storeData, batchSize, importForNextStore);
                } else {
                    setTimeout(importForNextStore, 200);
                }
            }
            
            importForNextStore();
        });
    }
    
    function submitBatches(data, batchSize, onComplete) {
        const totalProducts = data.products.length;
        const totalBatches = Math.ceil(totalProducts / batchSize);
        const fastMode = $('#fastImportMode').is(':checked');
        const maxConcurrent = fastMode ? 5 : 3; // 动态设置并发数
        let completedBatches = 0;
        let successCount = 0;
        let failedCount = 0;
        let loadingIndex = null;
        
        // 创建所有批次数据
        const batches = [];
        for (let i = 0; i < totalBatches; i++) {
            const start = i * batchSize;
            const end = Math.min(start + batchSize, totalProducts);
            const batchProducts = data.products.slice(start, end);
            
            batches.push({
                ...data,
                products: batchProducts,
                batchIndex: i + 1,
                startIndex: start + 1,
                endIndex: end
            });
        }
        
        // 显示初始进度
        loadingIndex = layer.load(1, {
            shade: [0.3, '#fff'],
            content: `准备并发导入 ${totalBatches} 批商品...`
        });
        
        // 并发提交函数
        function submitConcurrentBatches() {
            const promises = [];
            
            // 分组处理，每组最多maxConcurrent个并发请求
            for (let i = 0; i < batches.length; i += maxConcurrent) {
                const batchGroup = batches.slice(i, i + maxConcurrent);
                
                const groupPromises = batchGroup.map(batchData => {
                    return new Promise((resolve) => {
                        submitSingleBatchAsync(batchData)
                            .then(result => {
                                completedBatches++;
                                if (result.success) {
                                    successCount += result.count;
                                } else {
                                    failedCount += batchData.products.length;
                                }
                                
                                // 更新进度
                                if (loadingIndex) {
                                    layer.close(loadingIndex);
                                    loadingIndex = layer.load(1, {
                                        shade: [0.3, '#fff'],
                                        content: `正在导入: ${completedBatches}/${totalBatches} 批完成<br>成功: ${successCount}, 失败: ${failedCount}`
                                    });
                                }
                                
                                resolve(result);
                            })
                            .catch(error => {
                                completedBatches++;
                                failedCount += batchData.products.length;
                                console.error(`批次 ${batchData.batchIndex} 导入失败:`, error);
                                resolve({ success: false, count: 0 });
                            });
                    });
                });
                
                promises.push(Promise.all(groupPromises));
            }
            
            // 等待所有批次完成
            Promise.all(promises).then(() => {
                if (loadingIndex) layer.close(loadingIndex);
                
                const storeName = data.stores.length === 1 ? `店铺【${$(`.store-item[data-store-id="${data.stores[0].storeId}"] .store-name`).text()}】的` : '';
                
                layer.msg(`${storeName}并发导入完成！<br>成功: ${successCount}，失败: ${failedCount}<br>总耗时约为串行的 1/${maxConcurrent}`, {
                    icon: failedCount === 0 ? 1 : 2,
                    time: 5000
                });

                if (onComplete) {
                    setTimeout(onComplete, 500); // 减少延迟
                }
            });
        }
        
        // 开始并发提交
        submitConcurrentBatches();
    }
    
    // 异步提交单个批次
    function submitSingleBatchAsync(batchData) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/api/import',
                type: 'POST',
                dataType: 'json',
                data: JSON.stringify(batchData),
                timeout: 60000, // 增加到60秒超时
                success: function(res) {
                    const success = res.code == 0;
                    const count = success ? (res.count || batchData.products.length) : 0;
                    resolve({ success, count, response: res });
                },
                error: function(xhr, status, error) {
                    reject(new Error(`批次 ${batchData.batchIndex} 请求失败: ${error}`));
                }
            });
        });
    }
    
    function submitSingleBatch(data, callback) {
        $.ajax({
            url: '/api/import',
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data),
            timeout: 300000, // 300秒超时
            success: function(res) {
                layer.closeAll('loading');
                if(res.code == 0) {
                    const successCount = res.count || data.products.length;
                    layer.msg(`成功处理 ${successCount} 个商品`, {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
                if(callback) callback(res.code == 0, res.count || 0);
            },
            error: function(xhr, status, error) {
                layer.closeAll('loading');
                showError('导入请求失败: ' + error);
                if(callback) callback(false, 0);
            }
        });
    }

    function updateLineCount() {
        const skuMap = new Map();
        const skuLines = $('#skuInput').val().split('\n');
        let totalLines = 0;
        let validLines = 0;
        
        skuLines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine) {
                totalLines++;
                const [sku, price] = trimmedLine.split(/\s+/);
                if (sku && !isNaN(price)) {
                    skuMap.set(sku, parseFloat(price));
                    validLines++;
                }
            }
        });
        
        const uniqueCount = skuMap.size;
        const duplicateCount = validLines - uniqueCount;
        
        let text = `${uniqueCount}`;
        if (duplicateCount > 0) {
            text += ` (去重${duplicateCount}个)`;
        }
        if (totalLines > validLines) {
            text += ` / ${totalLines}行`;
        }
        
        $('#lineCount').text(text);
    }
});
</script>
</body>
</html>
