<?php
$clientip=real_ip($conf['ip_type']?$conf['ip_type']:0);

if(isset($_COOKIE["admin_token"]))
{
	$token=authcode(daddslashes($_COOKIE['admin_token']), 'DECODE', SYS_KEY);
	list($user, $sid, $expiretime) = explode("\t", $token);
	$session=md5($conf['admin_user'].$conf['admin_pwd'].$password_hash);
	if($session==$sid && $expiretime>time()) {
		$islogin=1;
	}
}

if(isset($_COOKIE["Authorization"]) or getAuthorizationHeader())
{
    $auth = new \lib\AuthSystem();
    $auth = $auth->validateToken($_COOKIE["Authorization"]?$_COOKIE["Authorization"]:getAuthorizationHeader());
    if($auth['valid']===true){
        $userrow=$DB->getRow("SELECT * FROM ozon_user WHERE uid=:uid limit 1", [':uid'=>$auth['data']['uid']]);
        if($userrow) {
            $uid = $userrow['uid'];
            $islogin2=1;
        }
    }
}


//打包员
if(isset($_COOKIE["packerId"]))
{
    $auth = new \lib\AuthSystem();
    $auth = $auth->validateToken($_COOKIE["packerId"]?$_COOKIE["packerId"]:getAuthorizationHeader());
    
    if($auth['valid']===true){
        $packerrow = $DB->getRow("SELECT * FROM packers WHERE packerId=:packerId limit 1", [':packerId'=>$auth['data']['uid']]);
        if($packerrow) {
            $packerId = $packerrow['packerId'];
            $islogin3=1;
        }
    }
}


function getAuthorizationHeader() {
    if (isset($_SERVER['Authorization'])) {
        return trim($_SERVER['Authorization']);
    }
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        return trim($_SERVER['HTTP_AUTHORIZATION']);
    }
    if (function_exists('apache_request_headers')) {
        $headers = apache_request_headers();
        if (isset($headers['Authorization'])) {
            return trim($headers['Authorization']);
        }
    }
    return null;
}

/**
 * 检查用户权限
 * @param int $uid 用户ID
 * @param string $operation 操作类型 ('store_add', 'product_import', etc.)
 * @return array 权限检查结果
 */
function checkUserPermission($uid, $operation = '') {
    global $DB, $permission_config;
    
    if (!$uid) {
        return ['allowed' => false, 'message' => '用户未登录'];
    }
    
    // 获取用户信息
    $user = $DB->getRow("SELECT status, max_shops, user_level FROM ozon_user WHERE uid = ?", [$uid]);
    if (!$user) {
        return ['allowed' => false, 'message' => '用户信息不存在'];
    }
    
    // 检查用户状态（如果启用状态检查）
    if ($permission_config['enable_status_check'] && $user['status'] != 1) {
        return ['allowed' => false, 'message' => '账户已禁用或已到期'];
    }
    
    // 根据操作类型进行特定检查
    switch ($operation) {
        case 'store_add':
            // 如果启用店铺数量限制
            if ($permission_config['enable_store_limit']) {
                $current_shops = $DB->getColumn("SELECT COUNT(*) FROM ozon_store WHERE uid = ?", [$uid]);
                $max_shops = intval($user['max_shops']);
                
                // 严格按照数据库中的max_shops值进行限制
                if ($max_shops > 0 && $current_shops >= $max_shops) {
                    return [
                        'allowed' => false, 
                        'message' => "店铺数量已达上限({$max_shops})，无法继续添加"
                    ];
                }
            }
            break;
            
        case 'product_import':
            // 可以根据需要添加商品导入限制
            break;
            
        default:
            // 默认只检查状态
            break;
    }
    
    return ['allowed' => true, 'message' => '权限检查通过'];
}

/**
 * 获取用户权限信息
 * @param int $uid 用户ID
 * @return array 用户权限信息
 */
function getUserPermissions($uid) {
    global $DB, $permission_config;
    
    if (!$uid) {
        return null;
    }
    
    $user = $DB->getRow("SELECT status, max_shops, user_level, expiry_date FROM ozon_user WHERE uid = ?", [$uid]);
    if (!$user) {
        return null;
    }
    
    $current_shops = $DB->getColumn("SELECT COUNT(*) FROM ozon_store WHERE uid = ?", [$uid]);
    $max_shops = intval($user['max_shops']);
    
    // 计算是否可以添加店铺
    $can_add_store = true;
    if ($permission_config['enable_status_check'] && $user['status'] != 1) {
        $can_add_store = false;
    } elseif ($permission_config['enable_store_limit'] && $max_shops > 0 && $current_shops >= $max_shops) {
        $can_add_store = false;
    }
    
    return [
        'is_active' => $user['status'] == 1,
        'max_shops' => $max_shops,
        'current_shops' => intval($current_shops),
        'user_level' => intval($user['user_level']),
        'expiry_date' => $user['expiry_date'],
        'can_add_store' => $can_add_store,
        'permission_config' => [
            'enable_store_limit' => $permission_config['enable_store_limit'],
            'enable_menu_control' => $permission_config['enable_menu_control'],
            'enable_status_check' => $permission_config['enable_status_check']
        ]
    ];
}
?>