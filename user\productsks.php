<link rel="stylesheet" href="../assets/css/productsks.css">
<!-- Layui风格的回到顶部按钮 -->
<button id="layuiBackToTop" class="layui-btn layui-btn-danger layui-btn-radius" style="position: fixed; right: 20px; bottom: 70px; z-index: 99999; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
    <i class="layui-icon layui-icon-top" style="font-size: 38px; color: #fff;"></i>
</button>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">快速上货</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <form class="layui-form layui-form-pane" lay-filter="searchForm">
                <!-- SKU -->
                <div class="layui-inline">
                    <label class="layui-form-label">SKU</label>
                    <div class="layui-input-inline">
                        <input type="text" name="sku" class="layui-input">
                    </div>
                </div>

                <!-- 货号 -->
                <div class="layui-inline">
                    <label class="layui-form-label">货号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="offer_id" class="layui-input">
                    </div>
                </div>

         
                <!-- 上传状态（固定选项） -->
                <div class="layui-inline">
                    <label class="layui-form-label">上传状态</label>
                    <div class="layui-input-inline">
                        <select name="status" lay-search>
                            <option value="">全部状态</option>
                            <option value="ok">上传成功</option>
                            <option value="no">上传失败</option>
                           <option value="已达到官方每日限制">已达到官方每日限制</option>
                            <option value="数据上传中">数据上传中</option>
                            <option value="数据采集成功">采集成功</option>
                            <option value="准备中">准备中</option>
                            <option value="等待添加库存">等待库存</option>
                        </select>
                    </div>
                </div>

                <!-- 动态店铺选择 -->
                <div class="layui-inline">
                    <label class="layui-form-label">店铺</label>
                    <div class="layui-input-inline">
                        <select name="shop_id" lay-search multiple>
                            <option value="">加载中...</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    
                          </div>
            </form>
            
            <div class="layui-inline" style="margin-top: 10px;">
                <button class="layui-btn layui-bg-purple" id="Batchresend">
                    <i class="layui-icon layui-icon-release"></i> 批量重传
                </button>
            </div>

            <!-- 表格区域 -->
            <table id="cronTable" lay-filter="cronTable"></table>
        </div>
    </div>
</div>
<!-- 表格操作列模板 -->
<script type="text/html" id="cronoperationTpl">
    <div class="operation-btns">
        <button class="layui-btn layui-btn-xs" lay-event="setSource"><i class="layui-icon layui-icon-link"></i>货源</button>
        <button class="layui-btn layui-btn-xs" lay-event="editPrice"><i class="layui-icon layui-icon-rmb"></i>价格</button></br>
        <button class="layui-btn layui-btn-xs" lay-event="editStock"><i class="layui-icon layui-icon-cart"></i>库存</button>
        <button class="layui-btn layui-btn-xs" lay-event="editAttr"><i class="layui-icon layui-icon-set"></i>属性</button></br>
        <button class="layui-btn layui-btn-xs" lay-event="addWatermark"><i class="layui-icon layui-icon-water"></i>水印</button>
        <button class="layui-btn layui-btn-xs" lay-event="cron_reupload"><i class="layui-icon layui-icon-upload"></i>重传</button>
    </div>
</script>
<!-- 货源链接模板 -->
<script type="text/html" id="sourceLinkTpl">
    {{# if(d.source_link){ }}
        <span class="layui-badge layui-bg-green">已设置</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">未设置</span>
    {{# } }}
</script>
<!-- 上传状态模板 -->
<script type="text/html" id="uploadStatusTpl">
    <!--{{# if(d.upload_status === 1){ }}
        <span class="layui-badge layui-bg-green">上传成功</span>
    {{# } else if(d.upload_status === 2){ }}
        <span class="layui-badge layui-bg-orange">上传中</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-red">上传异常</span>
    {{# } }}-->



</script>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="getCheckData">获取选中行数据</button>
    <button class="layui-btn layui-btn-sm" lay-event="getData">获取当前页数据</button>
    <button class="layui-btn layui-btn-sm" id="dropdownButton">
      下拉按钮 
      <i class="layui-icon layui-icon-down layui-font-12"></i>
    </button>
    <button class="layui-btn layui-btn-sm layui-bg-blue" id="reloadTest">
      重载测试 
      <i class="layui-icon layui-icon-down layui-font-12"></i>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-primary" id="rowMode">
      <span>{{= d.lineStyle ? '多行' : '单行' }}模式</span>
      <i class="layui-icon layui-icon-down layui-font-12"></i>
    </button>
  </div>
</script>

<script>
    layui.use(['table', 'form', 'layer', 'util'
    ], function () {
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var util = layui.util;
        var $ = layui.$;
        // 使用Layui的工具函数实现回到顶部
        $('#layuiBackToTop').on('click', function() {
            // 直接获取pear-page作为滚动容器
            var scrollContainer = document.querySelector('.pear-page');
            
            if (scrollContainer && scrollContainer.scrollTop > 0) {
                scrollContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
            return false; 
        });
        initTable();
        // 初始化表格
        function initTable() {
            table.render({
                elem: '#cronTable',
                url: 'ajax.php?act=cron_list',
                toolbar: '#toolbarDemo',
                defaultToolbar: ['filter', 'exports', 'print', { // 右上角工具图标
                    title: '提示',
                    layEvent: 'LAYTABLE_TIPS',
                    icon: 'layui-icon-tips',
                    onClick: function (obj) { // 2.9.12+
                        layer.alert('自定义工具栏图标按钮');
                    }
                }],
                page: true,
                limit: 20,
                limits: [10, 20, 30, 50, 100],
                toolbar: true,
                defaultToolbar: ['filter', 'exports', 'print'],
                cols: [
                    [
                        {
                            type: 'checkbox', fixed: 'left'
                        },
                        {
                            field: 'primary_image', fixed: 'left', title: '采集商品图', width: 130, templet: function (d) {
                                return '<img src="' + (d.primary_image || '/assets/img/syncing.png') + '" class="product-img">';
                            }
                        },
                        {
                            field: 'sku', title: '任务ID/采集SKU/店铺', width: 170, sort: true, templet: function (d) {
                                return d.id+'<br><div class="ee-c">Sku: <a href="https://www.ozon.ru/product/' + d.sku + '/" target="_blank">' + d.sku + '</a></div>店铺: ' + d.storename;
                            }
                        },
                        {
                            field: 'offer_id', title: '货号', width: 150, sort: true, templet: function (d) {
                                return d.offer_id ? d.offer_id : '生成中...';
                            }
                        },
                        {
                            field: 'title', title: '商品名称', width: 200, templet: function (d) {
                                return '<a href="https://www.ozon.ru/product/' + d.sku + '/" target="_blank"> <div class="eeb-t" title="' + d.title + '" style="color: #1E9FFF;">' + (d.title || '数据采集中...') + '</div> </a>';
                            }
                        },
                        {
                            field: 'price', title: '价格', width: 120, sort: true, templet: function (d) {
                                return '<div class="price-detail-trigger" data-sku="' + d.sku + '">'
                                    + '折扣¥: ' + d.old_price
                                    + "</br>售价¥: " + d.price + '</div>';
                            }
                        },
                        {
                            field: 'stock', title: '库存数量', width: 100
                        },
                        {
                            field: 'status',
                            title: '上传状态',
                            width: 100,
                            templet: function (d) {
                               // 优先检查官方限制状态
                                if(d.status === "已达到官方每日限制" || d.msg === "已达到官方每日限制"){
                                    return '<span class="layui-badge layui-bg-red">已达到官方每日限制</span>';
                                } else if (d.status === "ok") {
                                    return '<span class="layui-badge layui-bg-green">上传成功</span>';
                                }else if(d.status === "等待添加库存"){
                                    return '<span class="layui-badge layui-bg-orange">等待库存</span>';
                                } else if(d.status === '数据上传中'){
                                    return '<span class="layui-badge layui-bg-orange">数据上传中</span>';
                                } else if(d.status === '数据采集成功'){
                                    return '<span class="layui-btn-primary layui-bg-purple">采集成功</span>';
                                } else if(d.status === '准备中'){
                                    return '<span class="layui-btn-primary layui-border-red">准备中</span>';
                                } else if(d.status === 'no'){
                                    return '<span class="layui-badge layui-bg-red">上架失败</span>';
                                } else {
                                    return '<span class="layui-badge layui-bg-gray">未知状态</span>';
                                }
                            }
                        },
                        {
                            field: 'msg', title: '上传反馈信息', width: 120
                        },
                        {
                            field: 'task_id', title: 'task_id', width: 100
                        },
                        {
                            field: 'dimensions', title: '货品尺寸(mm)', width: 120, templet: function (d) {
                                return '<div class="dimension-display">'
                                    + '长: ' + (d.depth || 0) + '<br>'
                                    + '宽: ' + (d.width || 0) + '<br>'
                                    + '高: ' + (d.height || 0)
                                    + '</div>';
                            }
                        },
                        {
                            field: 'addtime', title: '创建日期', width: 160, sort: true, templet: function (d) {
                                return d.addtime;
                            }
                        },
                        {
                            title: '操作', width: 200, align: 'center', fixed: 'right', toolbar: '#cronoperationTpl'
                        }
                    ]
                ],
                parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data
                    };
                },
                text: {
                    none: '暂无相关数据'
                },
                done: function (res, curr, count) {
                    // 表格渲染完成后绑定点击事件
                }
            });
        }

        table.reload('cronTable',
            {
                lineStyle: 'height: 120px;'
            });

        // 仅初始化动态店铺选择框
        function initShopSelect() {
            var $shopSelect = $('select[name="shop_id"]');
            $shopSelect.html('<option value="">加载中...</option>');
            form.render('select');

            $.ajax({
                url: 'ajax.php?act=getShops',
                type: 'GET',
                dataType: 'json',
                success: function (res) {
                    if (res.code === 0) {
                        var html = '<option value="">所有店铺</option>';
                        res.data.forEach(function (shop) {
                            html += `<option value="${shop.id}">${shop.storename
                                }</option>`;
                        });
                        $shopSelect.html(html);
                        setTimeout(() => form.render('select', 'searchForm'),
                            50);
                    } else {
                        handleSelectError($shopSelect, res.msg);
                    }
                },
                error: function () {
                    handleSelectError($shopSelect, '店铺加载失败');
                }
            });
        }

        function handleSelectError($select, msg) {
            layer.msg(msg, { icon: 2 });
            $select.html('<option value="">加载失败（点击重试）</option>');
            form.render('select');
            $select.off('click').on('click', function () {
                if ($select.find('option:first').text().includes('失败')) {
                    initShopSelect();
                }
            });
        }
        // 初始化固定选择框
        function initStaticSelects() {
            // 商品状态（固定选项）
            form.val('searchForm', { "status": "" });
            form.render();
        }

        // 初始化页面
        $(function () {
            initShopSelect(); // 仅初始化动态店铺选择
            initStaticSelects(); // 初始化固定选择框
        });

        // 修复后的搜索功能
        form.on('submit(search)', function (data) {
            var searchParams = {
                sku: data.field.sku,
                offer_id: data.field.offer_id,
                status: data.field.status,
                shop_id: data.field.shop_id || '' // 处理空值情况
            };
    
    
    
            table.reload('cronTable',
                {
                    where: searchParams,
                    page: { curr: 1 }
                });
            return false;
        });


    

         $('#Batchresend').on('click', function (e) {
            e.preventDefault(); // 阻止默认行为
            var checkStatus = table.checkStatus('cronTable');
            if (checkStatus.data.length === 0) {
                layer.msg('请至少选择一条数据', { icon: 2 });
                return;
            }
     layer.confirm('确定要批量重传选中的' + checkStatus.data.length + '个商品吗？', { icon: 3, title: '提示' }, function (index) {
                    layer.close(index);
                    layer.load(2);
                    
                    var successCount = 0;
                    var failedCount = 0;
                    var totalCount = checkStatus.data.length;
                    var processedCount = 0;
                    
                    // 使用循环依次处理每个商品，使用与单个重传相同的API
                    function processNextItem(itemIndex) {
                        if (itemIndex >= checkStatus.data.length) {
                            // 所有项目已处理完成
                            layer.closeAll('loading');
                            layer.msg('批量重传完成：成功' + successCount + '个，失败' + failedCount + '个', { icon: 1 });
                            table.reload('cronTable');
                            return;
                        }
                        
                        var currentItem = checkStatus.data[itemIndex];
                        $.ajax({
                            url: 'ajax.php?act=cron_reupload',
                            type: 'POST',
                            dataType: 'json',
                            data: { id: currentItem.id, sku: currentItem.sku},
                            success: function (res) {
                                processedCount++;
                                if (res.code === 0) {
                                    successCount++;
                                } else {
                                    failedCount++;
                                }
                                
                                // 处理下一个
                                processNextItem(itemIndex + 1);
                            },
                            error: function () {
                                processedCount++;
                                failedCount++;
                                
                                // 处理下一个
                                processNextItem(itemIndex + 1);
                            }
                        });
                    }
                    
                    // 开始处理第一个项目
                    processNextItem(0);
                });
            });


        // 表格行工具事件
        table.on('tool(cronTable)', function (obj) {
            var data = obj.data;
            var event = obj.event;

            if (event === 'setSource') {
                layer.open({
                    title: '设置货源链接 - ' + data.sku,
                    type: 1,
                    area: ['800px', '500px'
                    ],
                    content: 'ajax.php?act=productsstocks',
                    data: { storeid: data.storeid, offer_id: data.offer_id },
                    success: function (layero, index) {
                        // iframe加载完成后操作
                    }
                });
            } else if (event === 'editPrice') {
                layer.prompt({
                    title: '修改价格 - ' + data.sku,
                    value: data.price || '',
                    formType: 0,
                    maxlength: 10
                }, function (value, index) {
                    if (!value || isNaN(value)) {
                        layer.msg('请输入有效的价格', { icon: 2 });
                        return;
                    }

                    $.ajax({
                        url: 'ajax.php?act=update_price',
                        type: 'POST',
                        dataType: 'json',
                        data: { offer_id: data.offer_id, price: value },
                        success: function (res) {
                            if (res.code === 0) {
                                layer.msg('修改成功', { icon: 1 });
                                obj.update({ price: parseFloat(value) });
                            } else {
                                layer.msg(res.msg || '修改失败',
                                    {
                                        icon: 2
                                    });
                            }
                        },
                        error: function () {
                            layer.msg('请求失败，请稍后重试', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            } else if (event === 'editStock') {
                layer.prompt({
                    title: '修改库存 - ' + data.sku,
                    value: data.stock || '',
                    formType: 0,
                    maxlength: 10
                }, function (value, index) {
                    if (!value || !/^\d+$/.test(value)) {
                        layer.msg('请输入有效的库存数量', { icon: 2 });
                        return;
                    }

                    $.ajax({
                        url: 'ajax.php?act=update_stock',
                        type: 'POST',
                        dataType: 'json',
                        data: {
                            storeid: data.storeid, offer_id: data.offer_id, stock: value
                        },
                        success: function (res) {
                            if (res.code === 0) {
                                layer.msg('修改成功', { icon: 1 });
                                obj.update({ stock: parseInt(value) });
                            } else {
                                layer.msg(res.msg || '修改失败', { icon: 2 });
                            }
                        },
                        error: function () {
                            layer.msg('请求失败，请稍后重试', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            } else if (event === 'editAttr') {
                layer.open({
                    title: '修改属性 - ' + data.sku,
                    type: 2,
                    area: ['800px', '600px'],
                    content: '/product/edit_attr?id=' + data.id,
                    success: function (layero, index) {
                        // iframe加载完成后操作
                    }
                });
            } else if (event === 'addWatermark') {
                layer.confirm('确定要为该商品添加水印吗？', { icon: 3, title: '提示' }, function (index) {
                    $.ajax({
                        url: '/api/product/add_watermark',
                        type: 'POST',
                        dataType: 'json',
                        data: { id: data.id },
                        success: function (res) {
                            if (res.code === 0) {
                                layer.msg('操作成功', { icon: 1 });
                            } else {
                                layer.msg(res.msg || '操作失败', { icon: 2 });
                            }
                        },
                        error: function () {
                            layer.msg('请求失败，请稍后重试', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            } else if (event === 'cron_reupload') {
                layer.confirm('确定要一键重传该商品吗？', { icon: 3, title: '提示' }, function (index) {
                    layer.load(2);
                    $.ajax({
                        url: 'ajax.php?act=cron_reupload',
                        type: 'POST',
                        dataType: 'json',
                        data: { id: data.id ,sku: data.sku},
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 0) {
                                layer.msg('操作成功', { icon: 1 });
                                obj.update({
                                    upload_status: 2 // 上传中状态
                                });
                            } else {
                                layer.msg(res.msg || '操作失败', { icon: 2 });
                            }
                        },
                        error: function () {
                            layer.closeAll('loading');
                            layer.msg('请求失败，请稍后重试', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            } else if (event === 'sync') {
                layer.confirm('确定要同步该商品数据吗？', { icon: 3, title: '提示' }, function (index) {
                    layer.load(2);
                    $.ajax({
                        url: 'ajax.php?act=productsync',
                        type: 'POST',
                        dataType: 'json',
                        data: {
                            offer_id: data.offer_id, storeid: data.storeid
                        },
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 0) {
                                layer.msg('同步成功', { icon: 1 });
                                table.reload('cronTable');
                            } else {
                                layer.msg(res.msg || '同步失败', { icon: 2 });
                            }
                        }, error: function () {
                            layer.closeAll('loading');
                            layer.msg('请求失败，请稍后重试', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            }
        });
    });
</script>