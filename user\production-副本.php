<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">产品采集快速上架</div>
        <div class="layui-card-body">
            <!-- 搜索区域 -->
            <form class="layui-form layui-form-pane" lay-filter="searchForm">
                <!-- SKU -->
                <div class="layui-inline">
                    <label class="layui-form-label">SKU</label>
                    <div class="layui-input-inline">
                        <input type="text" name="sku" class="layui-input">
                    </div>
                </div>
                
                <!-- 货号 -->
                <div class="layui-inline">
                    <label class="layui-form-label">货号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="offer_id" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <button class="layui-btn layui-btn-danger" id="batchOperation">
                        <i class="layui-icon layui-icon-edit"></i> 批量操作
                    </button>
                </div>
            </form>

            <!-- 表格区域 -->
            <table id="productionTable" lay-filter="productionTable"></table>
        </div>
    </div>
</div>
<!-- 表格操作列模板 -->
<script type="text/html" id="productionoperationTpl">
    <div class="operation-btns">
        <button class="layui-btn layui-btn-xs" lay-event="setSource"><i class="layui-icon layui-icon-upload"></i>编辑上传</button></br>
        <button class="layui-btn layui-btn-xs" lay-event="delete"><i class="layui-icon layui-icon-delete"></i>采集删除</button>
    </div>
</script>

<script>
    layui.use(['table', 'form', 'layer', 'util'], function () {
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var util = layui.util;
        var $ = layui.$;
        initTable();
        // 初始化表格
        function initTable() {
            table.render({
                elem: '#productionTable',
                url: 'ajax.php?act=production_list',
                page: true,
                limit: 20,
                limits: [10,20,30,50,100],
                toolbar: true,
                defaultToolbar: ['filter', 'exports', 'print'],
                cols: [
                    [
                        {
                            type: 'checkbox', fixed: 'left'
                        },
                        {
                            field: 'id', title: 'ID', width: 60
                        },
                        
                        {
                            field: 'mainImage', title: '采集商品图', width: 130, templet: function (d) {
                                return '<img src="' + (d.mainImage || '/assets/img/syncing.png') + '" class="product-img">';
                            }
                        },
                        {
                            field: 'title', title: '商品名称',  templet: function (d) {
                                return '<a href="https://www.ozon.ru/product/' + d.sku + '/" target="_blank"> <div class="eeb-t" title="' + d.title + '" style="color: #1E9FFF;">' + (d.title || '数据采集中...') + '</div> </a>';
                            }
                        },
                        {
                            field: 'offer_id', title: '产品货号', width: 150, sort: true, templet: function (d) {
                                return d.offer_id?d.offer_id:'暂时未设置';
                            }
                        },
                        {
                            field: 'category', title: '类目'
                        },
                        {
                            field: 'dimensions', title: '货品尺寸(mm)', width: 120, templet: function (d) {
                                return '<div class="dimension-display">'
                                    + '长: ' + (d.depth || 0) + '<br>'
                                    + '宽: ' + (d.width || 0) + '<br>'
                                    + '高: ' + (d.height || 0)
                                    + '</div>';
                            }
                        },
                        {
                            field: 'orderUrl', title: '下单链接', width: 130, templet: function (d) {
                                return '<a class="layui-btn layui-btn-primary layui-border-blue" href="'+d.orderUrl+'" target="_blank">点击跳转</a>';
                            }
                        },
                        {
                            field: 'addtime', title: '创建日期', width: 160, sort: true, templet: function (d) {
                                return d.addtime;
                            }
                        },
                        {
                            title: '操作', width: 100, align: 'center', fixed: 'right', toolbar: '#productionoperationTpl'
                        }
                    ]
                ],
                parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data
                    };
                },
                text: {
                    none: '暂无相关数据'
                },
                done: function (res, curr, count) {
                    // 表格渲染完成后绑定点击事件
                }
            });
        }
        
        table.reload('productionTable',
        {
            lineStyle: 'height: 120px;'
        });

        // 修复后的搜索功能
        form.on('submit(search)', function (data) {
            var searchParams = {
                sku: data.field.sku,
                product_name: data.field.product_name,
                status: data.field.status,
                shop_id: data.field.shop_id || '' // 处理空值情况
            };

            table.reload('productionTable',
            {
                where: searchParams,
                page: {curr: 1}
            });
            return false;
        });


        // 批量操作按钮事件
        $('#batchOperation').on('click', function () {
            var checkStatus = table.checkStatus('productionTable');
            if (checkStatus.data.length === 0) {
                layer.msg('请至少选择一条数据',{icon: 2});
                return;
            }

            layer.open({
                title: '批量操作',
                content: '<div style="padding: 20px;">'
                    + '<div class="layui-form-item">'
                    + '<label class="layui-form-label">操作类型</label>'
                    + '<div class="layui-input-block">'
                    + '<select name="batchType" lay-verify="required" class="layui-input">'
                    + '<option value="">请选择操作类型</option>'
                    + '<option value="price">修改价格</option>'
                    + '<option value="stock">修改库存</option>'
                    + '<option value="status">修改状态</option>'
                    + '<option value="sync">同步商品</option>'
                    + '</select>'
                    + '</div>'
                    + '</div>'
                    + '<div class="layui-form-item" id="batchValueContainer" style="display:none;">'
                    + '<label class="layui-form-label">操作值</label>'
                    + '<div class="layui-input-block">'
                    + '<input type="text" name="batchValue" placeholder="请输入操作值" class="layui-input">'
                    + '</div>'
                    + '</div>'
                    + '</div>',
                btn: ['确定', '取消'
                ],
                area: ['500px', '300px'
                ],
                success: function () {
                    form.render();

                    // 监听操作类型变化
                    form.on('select', function (data) {
                        if (data.value === 'sync') {
                            $('#batchValueContainer').hide();
                        } else {
                            $('#batchValueContainer').show();
                        }
                    });
                },
                yes: function (index, layero) {
                    var batchType = layero.find('select[name="batchType"]').val();

                    if (!batchType) {
                        layer.msg('请选择操作类型',{icon: 2});
                        return false;
                    }

                    if (batchType !== 'sync') {
                        var batchValue = layero.find('input[name="batchValue"]').val();
                        if (!batchValue) {
                            layer.msg('请填写操作值',{icon: 2});
                            return false;
                        }
                    }

                    var ids = checkStatus.data.map(function (item) {
                        return item.id;
                    });

                    layer.load(2);
                    $.ajax({
                        url: '/api/product/batch',
                        type: 'POST',
                        dataType: 'json',
                        data: {ids: ids.join(','), type: batchType, value: batchType !== 'sync' ? layero.find('input[name="batchValue"]').val() : ''
                        },
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 0) {
                                layer.msg('操作成功',{icon: 1});
                                table.reload('productionTable');
                            } else {
                                layer.msg(res.msg || '操作失败',{icon: 2});
                            }
                        },
                        error: function () {
                            layer.closeAll('loading');
                            layer.msg('请求失败，请稍后重试',{icon: 2});
                        }
                    });
                    layer.close(index);
                }
            });
        });

        // 表格行工具事件
        table.on('tool(productionTable)', function (obj) {
            var data = obj.data;
            var event = obj.event;

            if (event === 'setSource') {
                layer.open({
                    type: 2,
                    title: '商品编辑/上传',
                    shadeClose: true,
                    maxmin: true, //开启最大化最小化按钮
                    area: ['1300px', '900px'],
                    content: './product_edit.php?id='+data.id
                });
            } else if (event === 'delete') {
                layer.load(2);
                $.ajax({
                    url: './ajax.php?act=delete_production',
                    type: 'POST',
                    data: {id:data.id},
                    success: function(res) {
                        layer.closeAll();
                        if(res.code === 0) {
                            layer.msg('删除成功', {icon: 1});
                            table.reload('productionTable');
                        } else {
                            layer.msg(res.msg || '删除失败', {icon: 2});
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll();
                        layer.msg('请求失败: ' + xhr.statusText, {icon: 2});
                    }
                });
            }
        });
    });
</script>