<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>用户资料</title>

<style>
  body {
    background-color: #f2f6fc;
    padding: 20px;
  }

  .profile-container {
    max-width: 600px;
    margin: 0 auto;
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
  }

  .profile-title {
    font-size: 24px;
    font-weight: 700;
    color: #1E9FFF;
    margin-bottom: 20px;
    text-align: center;
  }

  .info-label {
    font-weight: 600;
    color: #333;
  }

  .info-value {
    color: #666;
    position: relative;
    min-height: 24px; /* 防止加载提示跳动 */
  }

  .loading {
    position: absolute;
    left: 0;
    color: #999;
    font-size: 14px;
  }

  #errorMsg {
    color: #FF5722;
    text-align: center;
    margin-top: 15px;
    display: none;
  }

  .layui-table-view {
    overflow-x: auto;
    margin: 15px 0;
  }
</style>
</head>
<body>
  <div class="profile-container layui-anim layui-anim-fadein">
    <div class="profile-title">用户资料</div>
    
    <div class="layui-table-view">
      <table class="layui-table" lay-size="sm" lay-skin="line">
        <tbody>
          <tr>
            <td class="info-label" style="width: 120px;">用户ID</td>
            <td class="info-value" id="userId">
              <span class="loading">加载中...</span>
              <span class="info-data"></span>
            </td>
          </tr>
          <tr>
            <td class="info-label">用户名</td>
            <td class="info-value" id="username">
              <span class="loading">加载中...</span>
              <span class="info-data"></span>
            </td>
          </tr>
          <tr>
            <td class="info-label">当前身份</td>
            <td class="info-value" id="userLevel">
              <span class="loading">加载中...</span>
              <span class="info-data"></span>
            </td>
          </tr>
          <tr>
            <td class="info-label">会员到期时间</td>
            <td class="info-value" id="expiryDate">
              <span class="loading">加载中...</span>
              <span class="info-data"></span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div id="errorMsg"></div>
  </div>

<!-- 新增jQuery依赖 -->

<script src="/assets/component/layui/layui.js"></script>
<script>
layui.use(['layer', 'element'], function(){
  var layer = layui.layer;
  var element = layui.element;
  var $ = layui.$; // 使用Layui内置的jQuery（可选）
  
  // 初始化组件
  element.init();
  
  // 用户身份映射
  const LEVEL_MAP = {
    1: '普通用户',
    2: 'VIP用户',
    3: 'SVIP用户',
    4: '企业用户'
  };

  $(function(){
    console.log('[Init] 文档加载完成');
    fetchUserInfo();
  });

  function fetchUserInfo() {
    if (window.isLoading) return;
    window.isLoading = true;

    console.log('[Request] 发起用户信息请求');
    $.ajax({
      url: '/user/ajax.php?act=getUserInfo',
      type: 'GET',
      dataType: 'json',
      timeout: 10000, // 10秒超时
      xhrFields: {
        withCredentials: true // 携带跨域Cookie
      },
      beforeSend: function() {
        $('.loading').show();
        $('#errorMsg').hide().empty();
      },
      success: function(res) {
        console.log('[Response]', res);
        if (res.code === 1) {
          updateUI(res.data);
        } else {
          showError(res.msg || '服务器返回异常状态');
        }
      },
      error: function(xhr, type, error) {
        console.error('[Error]', type, error);
        let msg = '网络连接异常';
        if (xhr.status === 401) {
          msg = '请先登录';
        } else if (xhr.responseText) {
          try {
            const res = JSON.parse(xhr.responseText);
            msg = res.msg || msg;
          } catch(e) {
            msg = xhr.responseText.substring(0, 50) + '...';
          }
        }
        showError(msg);
      },
      complete: function() {
        window.isLoading = false;
        console.log('[Request] 请求完成');
      }
    });
  }

  function updateUI(data) {
    console.log('[UI] 更新界面', data);
    const safeData = {
      uid: data.uid || '未知',
      username: data.username || '未设置',
      user_level: LEVEL_MAP[data.user_level] || '未知身份',
      expiry_date: data.expiry_date || '未设置'
    };

    $('#userId').find('.info-data').text(safeData.uid);
    $('#username').find('.info-data').text(safeData.username);
    $('#userLevel').find('.info-data').text(safeData.user_level);
    $('#expiryDate').find('.info-data').text(safeData.expiry_date);
    $('.loading').hide();
  }

  function showError(msg) {
    console.error('[Error]', msg);
    layer.msg('加载失败: ' + msg, {icon: 5, time: 5000});
    $('#errorMsg').html(`
      <i class="layui-icon layui-icon-face-cry"></i>
      <span>${msg}</span>
      <button class="layui-btn layui-btn-sm layui-btn-primary" onclick="location.reload()">重试</button>
    `).show();
  }
});
</script>
</body>
</html>