<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/includes/common.php';
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

// 连接到 RabbitMQ
try {
    $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
    $channel = $connection->channel();
    $channel->queue_declare('task_queue', false, true, false, false); // 持久化队列
} catch (Exception $e) {
    die("连接失败: " . $e->getMessage());
}

echo " [*] 等待消息. 按 CTRL+C 退出\n";

// 定义回调函数
$callback = function ($msg) {
    
    $data = json_decode($msg->body, true);
    echo "\n [x] 收到 ", $data['type']," ID: ", $data['id']??$data['sku'], "  时间: ".date("Y-m-d H:i:s"),"\n";
    switch ($data['type']) {
        case 'production':
            production($data);  #采集商品处理
        break;
        case 'gmcron':
            gmcron($data['id']);
        break;
        case 'getprice':
            getprice($data['id']);
        break;
        case 'Management_reupload':
            Management_reupload($data['sku']);
        break;
        default:
            processSlowTask($data);
        break;
    }
    $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']); // 正确确认方式
};

// 公平分发（同一时间只处理一个消息）
$channel->basic_qos(null, 1, null);

// 订阅队列（修正参数顺序）
$channel->basic_consume('task_queue', '', false, false, false, false, $callback);

// 保持监听
while (count($channel->callbacks)) {
    $channel->wait();
}

// 关闭连接
$channel->close();
$connection->close();

function processSlowTask($data) {
    file_put_contents('log.txt', print_r($data, true) . "\n", FILE_APPEND);
}

function getprice($sku){
    global $fx,$DB;
    $req = file_get_contents("http://43.139.204.216:5000/api/product?sku=$sku");
    $json = json_decode($req,true);
    if($req){
        $json2 = json_decode($json['widgetStates']['webPrice-3121879-default-1'],true);
        if(strpos($json2['price'], '¥')){
            $data['zprice'] = parseAmount($json2['price']);
            $data['originalPrice'] = parseAmount($json2['originalPrice']);
            if($json2['cardPrice'])$data['cardPrice'] = parseAmount($json2['cardPrice']);
        }else{
            $data['zprice'] = parseAmount($json2['price'])*$fx['rmb']['num'];
            $data['originalPrice'] = parseAmount($json2['originalPrice'])*$fx['rmb']['num'];
            if($json2['cardPrice'])$data['cardPrice'] = parseAmount($json2['cardPrice'])*$fx['rmb']['num'];
        }
        echo "\n".'任务执行完毕,'; 
        $DB->update('seller', $data, ['sku'=>$sku]);
    }
}

function production($data){
    global $DB;
    if($data['datatype']=='1688'){
        if($data['orderUrl']){
            $alidata = ali1688data($data['orderUrl']);
            if($alidata['title']){
                $productionData['title'] = $alidata['title'];
            }
            if($alidata['description']){
                $productionData['json'] = json_encode(jsondata($alidata['description']));
            }
            if($alidata['images']){
                $productionData['images'] = $alidata['images'];
            }
            if($alidata['mainImage']){
                $productionData['mainImage'] = $alidata['mainImage'];
            }
            if($alidata['videos']){
                $productionData['videos'] = $alidata['videos'];
            }
            if($alidata['category']['cate3Name']){
                $typeId = get_ozon_type_id($alidata['category']['cate3Name']);
                if($typeId){
                    $texts = findCategoryPathWithNames($typeId);
                    if($texts){
                        $category_chain = ['ids'=>findCategoryPath($typeId),'texts'=>$texts];
                        $productionData['category_chain'] = json_encode($category_chain, JSON_UNESCAPED_UNICODE);
                    }
                }
            }
            file_put_contents('log.txt', print_r($productionData, true) . "\n". ali1688getoffer($data['orderUrl'])."\n", FILE_APPEND);
        }
    }else {
        if($data['jsonimgData']){
            $productionData['json'] = json_encode(jsondata($data['jsonimgData']));
        }
        //file_put_contents('log.txt', print_r($data, true) . "\n", FILE_APPEND);
    }
    
    if($productionData){
        $DB->update('production', $productionData, ['id'=>$data['id']]);
    }
}

function productsstocksss($row){
    if($row['warehouse_id']>=1 and $row['stock']>0){
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        return $client->productsstocks($row);
    }
    return true;
}

function parseAmount($input) {
    $isYen = strpos($input, '¥') !== false;

    // 清理阶段
    $cleaned = preg_replace($isYen ? '/[^\d.]/' : '/[^\d.,]/', '', $input);

    if ($isYen) {
        // 日元专用处理逻辑
        $cleaned = str_replace(',', '', $cleaned); // 移除所有逗号
        $parts = explode('.', $cleaned);
        $integer = $parts[0] ?? '0';
        $decimal = $parts[1] ?? '';

        // 处理无小数点的情况
        if ($integer === '') $integer = '0';
        $decimal = substr($decimal . '00', 0, 2);

        return floatval("$integer.$decimal");
    } else {
        // 通用处理逻辑
        $normalized = str_replace(',', '.', $cleaned);
        $parts = explode('.', $normalized);
        $total = count($parts);

        if ($total > 1) {
            $decimal = array_pop($parts);
            $integer = implode('', $parts);
        } else {
            $integer = $parts[0] ?? '0';
            $decimal = '';
        }

        $decimal = substr($decimal . '00', 0, 2);
        return floatval("$integer.$decimal");
    }
}

function Management_reupload($sku){
    global $DB;
    $data = $DB->find('products', '*', ['sku' => $sku]);
    if($data){
        $store = $DB->find('store', '*', ['id'=>$data['storeid']]);
        $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
        $json = $client->stockswarehouse($sku);
        $productData = ['stock'=>0,'warehouse_id'=>0];
        foreach ($json as $item){
            if($item['present']>$productData['stock']){
                $productData = ['sku'=>$item['sku'],'stock'=>$item['present'],'warehouse_id'=>$item['warehouse_id'],'product_id'=>$item['product_id']];
            }
        }
        $productData['uid'] = $data['uid'];
        $productData['storeid'] = $data['storeid'];
        $productData['status'] = '重新上架';
        $productData['title'] = $data['name'];
        $productData['price'] = $data['price'];
        $productData['old_price'] = $data['old_price'];
        $productData['primary_image'] = $data['primary_image'];
        $productData['date'] = date("Y-m-d");
        $productData['addtime'] = date("Y-m-d H:i:s");
        $productData['type'] = 'reupload';
        $DB->insert('cron', $productData);
        echo "SKU：$sku".$DB->error();
    }
    
    return true;
}