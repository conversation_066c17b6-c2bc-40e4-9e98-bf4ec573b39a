<?php
namespace lib;

class NativeRedisLock
{
    private $redis;
    private $lockKey;
    private $identifier;
    private $ttl;

    public function __construct($lockKey, $ttl = 10, $host = '127.0.0.1', $port = 6379, $timeout = 0)
    {
        $this->redis = new \Redis();
        $this->redis->connect($host, $port, $timeout); // 直接使用原生连接
        $this->lockKey = $lockKey;
        $this->ttl = $ttl;
        $this->identifier = uniqid(gethostname().'_', true); // 唯一标识
    }

    public function acquire($retries = 3, $interval = 100)
    {
        $attempts = 0;
        while ($attempts++ < $retries) {
            // 原子操作：SET key value NX EX ttl
            if ($this->redis->set($this->lockKey, $this->identifier, ['NX', 'EX' => $this->ttl])) {
                return true;
            }
            usleep($interval * 1000);
        }
        return false;
    }

    public function release()
    {
        $script = <<<LUA
if redis.call("GET", KEYS[1]) == ARGV[1] then
    return redis.call("DEL", KEYS[1])
else
    return 0
end
LUA;
        return $this->redis->eval($script, [$this->lockKey, $this->identifier], 1) === 1;
    }

    public function __destruct()
    {
        $this->redis->close();
    }
}