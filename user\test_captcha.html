<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>验证码测试</title>
    <script src="../assets/component/layui/layui.js"></script>
    <script src="../assets/component/pear/pear.js"></script>
    <script>
    // 全局错误处理
    window.onerror = function(msg, url, line, col, error) {
        console.error('Global error:', msg, 'at', url + ':' + line);
        if(msg.indexOf('errors is not defined') !== -1) {
            console.error('Geetest script loading error detected');
            alert('验证码组件加载失败，请刷新页面重试');
        }
        return false;
    };
    
    // 设置脚本安全策略
    window.appendChildOrg = Element.prototype.appendChild;
    Element.prototype.appendChild = function() {
        if(arguments[0].tagName == 'SCRIPT'){
            arguments[0].setAttribute('referrerpolicy', 'no-referrer');
        }
        return window.appendChildOrg.apply(this, arguments);
    };
    </script>
    <script src="//static.geetest.com/static/tools/gt.js" onerror="console.error('Failed to load gt.js')"></script>
    <script src="//static.geetest.com/v4/gt4.js" onerror="console.error('Failed to load gt4.js')"></script>
</head>
<body>
    <h1>验证码测试页面</h1>
    <div id="captcha-container"></div>
    <button id="test-btn">测试验证码</button>
    
    <script>
    layui.use(['jquery', 'layer'], function(){
        var $ = layui.jquery;
        var layer = layui.layer;
        
        var captchaObjInstance = null;
        
        // 检查极验证码库是否加载
        function checkGeetestLibrary() {
            if(typeof initGeetest4 === 'undefined' && typeof initGeetest === 'undefined') {
                console.error('Geetest library not loaded');
                layer.msg('验证码库加载失败，请检查网络连接', {icon: 5});
                return false;
            }
            return true;
        }
        
        var handlerEmbed = function (captchaObj) {
            captchaObjInstance = captchaObj;
            
            if (!captchaObj) {
                console.error('Captcha object is null');
                layer.msg('验证码初始化失败', {icon: 5});
                return;
            }
            
            captchaObj.onReady(function () {
                console.log('Captcha ready');
                layer.msg('验证码加载成功', {icon: 1});
            }).onSuccess(function () {
                var result = captchaObj.getValidate();
                console.log('Captcha result:', result);
                if (!result) {
                    return alert('请完成验证');
                }
                layer.msg('验证成功！', {icon: 1});
            }).onError(function(error){
                console.error('Captcha error:', error);
                layer.msg('验证码加载失败，请刷新页面重试', {icon: 5});
            });
        };
        
        // 延迟初始化验证码
        setTimeout(function() {
            if(!checkGeetestLibrary()) {
                return;
            }
            
            $.ajax({
                url: "ajax.php?act=captcha",
                type: "POST",
                cache: false,
                data: {csrf_token: 'test'},
                success: function (data) {
                    console.log('Captcha config:', data);
                    try {
                        if(data.version == 1){
                            if(typeof initGeetest4 === 'undefined') {
                                throw new Error('initGeetest4 is not defined');
                            }
                            initGeetest4({
                                captchaId: data.gt,
                                product: 'bind',
                                protocol: 'https://',
                                riskType: 'slide',
                                hideSuccess: true,
                            }, handlerEmbed);
                        }else{
                            if(typeof initGeetest === 'undefined') {
                                throw new Error('initGeetest is not defined');
                            }
                            initGeetest({
                                width: '100%',
                                gt: data.gt,
                                challenge: data.challenge,
                                new_captcha: data.new_captcha,
                                product: "bind",
                                offline: !data.success
                            }, handlerEmbed);
                        }
                    } catch(e) {
                        console.error('Captcha init error:', e);
                        layer.msg('验证码初始化失败: ' + e.message, {icon: 5});
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Captcha config error:', xhr.responseText);
                    layer.msg('验证码配置加载失败', {icon: 5});
                }
            });
        }, 1000);
        
        $('#test-btn').click(function() {
            if(!captchaObjInstance) {
                layer.msg('验证码未初始化', {icon: 5});
                return;
            }
            
            try {
                if(typeof captchaObjInstance.showCaptcha === 'function'){
                    captchaObjInstance.showCaptcha();
                }else if(typeof captchaObjInstance.verify === 'function'){
                    captchaObjInstance.verify();
                }else{
                    console.error('Captcha object methods not available');
                    layer.msg('验证码功能异常', {icon: 5});
                }
            } catch(e) {
                console.error('Captcha error:', e);
                layer.msg('验证码调用失败', {icon: 5});
            }
        });
    });
    </script>
</body>
</html>
