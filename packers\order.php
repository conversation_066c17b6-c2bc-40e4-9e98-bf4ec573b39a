<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>打包员订单管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../assets/component/pear/css/pear.css" />
    <link rel="stylesheet" href="../assets/admin/css/reset.css" />
    <style>
        /* 颜色变量 */
        :root {
            --primary-color: #2563eb;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --text-color: #374151;
            --text-light: #6b7280;
            --border-color: #e5e7eb;
            --bg-color: #f9fafb;
            --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            --border-radius: 8px;
            --transition: all 0.2s ease;
        }

        /* 基础样式 */
        .loading-msg {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .error-msg {
            text-align: center;
            padding: 20px;
            color: #FF5722;
        }

        /* 打包员信息区域 */
        .packer-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 20px;
            margin: 15px 0;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
            box-shadow: var(--card-shadow);
        }
        .packer-info h4 {
            margin: 0 0 8px 0;
            color: var(--text-color);
            font-size: 16px;
            font-weight: 600;
        }
        .packer-info p {
            margin: 0;
            color: var(--text-light);
            font-size: 14px;
        }

        /* 表格样式优化 */
        .layui-table-cell {
            height: auto !important;
            overflow: visible !important;
            padding: 12px 8px !important;
            vertical-align: middle !important;
            white-space: normal !important;
            line-height: 1.4 !important;
        }
        .layui-table tbody tr {
            height: auto !important;
            min-height: 170px !important;
            border-bottom: 1px solid var(--border-color);
        }
        /* 勾选框对齐修复 - 使用layui原生方式 */
        .layui-table .layui-table-cell {
            vertical-align: middle !important;
        }

        /* 相同订单号行的视觉分组 */
        .same-order-group {
            border-left: 3px solid var(--primary-color);
            background-color: rgba(37, 99, 235, 0.02);
        }
        
        /* 订单号显示优化 */
        .order-number {
            font-weight: 600;
            color: var(--primary-color);
        }
        .order-number.duplicate {
            color: var(--text-light);
            font-weight: normal;
            font-style: italic;
        }
        .order-number.duplicate::before {
            content: "└ ";
        }
        .layui-table tbody tr:hover {
            background-color: #f8f9ff !important;
        }
        .layui-table thead tr {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
        .layui-table th {
            background: transparent !important;
            color: var(--text-color) !important;
            font-weight: 600 !important;
        }

        /* 图片样式 */
        .product-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            transition: var(--transition);
            cursor: pointer;
        }
        .product-image:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }
        .product-image.thumbnail:hover::after {
            content: "点击放大";
            position: absolute;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            z-index: 1000;
            margin-top: -20px;
            margin-left: 5px;
            pointer-events: none;
        }
        .no-image {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            font-size: 11px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        /* 商品名称样式 */
        .product-name {
            max-width: 240px;
            word-wrap: break-word;
            white-space: normal;
            line-height: 1.4;
            color: var(--text-color);
            font-size: 13px;
            transition: var(--transition);
        }
        .product-name:hover {
            color: var(--primary-color);
        }
        
        /* 商品名称链接样式 */
        a .product-name {
            color: #1E9FFF !important;
        }
        a .product-name:hover {
            color: #0984e3 !important;
            text-decoration: underline;
            transform: translateX(2px);
        }
        a:hover .product-name::after {
            content: " 🔗";
            font-size: 10px;
            opacity: 0.7;
        }

        /* 价格样式 */
        .price-value {
            color: #e74c3c;
            font-weight: 600;
            font-size: 14px;
        }
        .cost-value {
            color: #27ae60;
            font-weight: 600;
            font-size: 14px;
        }
        .cost-empty {
            color: var(--text-light);
            font-style: italic;
            font-size: 12px;
        }

        /* 状态标签样式 */
        .status-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: white;
            display: inline-block;
            text-align: center;
            min-width: 60px;
        }
        .status-pending { 
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        .status-awaiting_deliver { 
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        }
        .status-delivering { 
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        }
        .status-delivered { 
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }
        .status-cancelled { 
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }
        .status-awaiting_packaging { 
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        /* 文本样式 */
        .text-break {
            word-break: break-all;
            font-size: 13px;
            line-height: 1.3;
        }
        .text-small {
            font-size: 12px;
            color: var(--text-light);
        }
        .text-empty {
            color: var(--text-light);
            font-style: italic;
            font-size: 12px;
        }

        /* 店铺名称样式 */
        .store-name {
            color: var(--text-color);
            font-size: 13px;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .store-name:hover {
            color: var(--primary-color);
            overflow: visible;
            white-space: normal;
        }

        /* 订单计数标签 */
        .order-count-badge {
            background: var(--error-color);
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: 500;
            margin-left: 5px;
            min-width: 18px;
            text-align: center;
            display: inline-block;
        }

        /* 标签页样式 */
        .layui-tab-title {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 2px solid var(--border-color);
            padding: 0 15px;
        }
        .layui-tab-title li {
            font-weight: 500;
            color: var(--text-color);
            transition: var(--transition);
            padding: 12px 20px;
            margin-right: 5px;
            border-radius: 8px 8px 0 0;
        }
        .layui-tab-title li:hover {
            background: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
        }
        .layui-tab-title .layui-this {
            background: white;
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
            font-weight: 600;
        }

        /* 二级筛选标签样式 */
        .sub-tabs {
            background: #ffffff;
            border-bottom: 1px solid var(--border-color);
            padding: 0 15px;
            display: none;
        }
        .sub-tabs.show {
            display: block;
        }
        .sub-tabs .layui-tab {
            margin-top: 0;
        }
        .sub-tabs .layui-tab-title {
            background: transparent;
            border-bottom: none;
            padding: 0;
            margin-bottom: 10px;
        }
        .sub-tabs .layui-tab-title li {
            font-size: 13px;
            padding: 8px 16px;
            margin-right: 10px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            background: #f8f9fa;
            color: var(--text-light);
            cursor: pointer;
        }
        .sub-tabs .layui-tab-title li:hover {
            background: rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        .sub-tabs .layui-tab-title li.layui-this {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            border-bottom: 1px solid var(--primary-color);
        }

        /* 表单样式 */
        .layui-form-pane .layui-input {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            transition: var(--transition);
        }
        .layui-form-pane .layui-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .layui-form-pane .layui-form-label {
            background: transparent;
            color: var(--text-color);
            font-weight: 500;
        }

        /* 按钮样式 */
        .layui-btn {
            border-radius: 6px;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        .layui-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .layui-btn[lay-submit] {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
            border-color: var(--primary-color);
        }
        .layui-btn[lay-submit]:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        }

        /* 卡片样式 */
        .layui-card {
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }
        .layui-card-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-bottom: 1px solid var(--border-color);
        }
        .layui-card-body {
            background: white;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .product-name {
                max-width: 180px;
            }
            .layui-table-cell {
                padding: 8px 6px !important;
            }
            .layui-tab-title li {
                padding: 10px 15px;
                font-size: 13px;
            }
            .sub-tabs .layui-tab-title li {
                font-size: 12px;
                padding: 6px 12px;
            }
        }

        @media (max-width: 768px) {
            .packer-info {
                padding: 12px 15px;
                margin: 10px 0;
            }
            .packer-info h4 {
                font-size: 14px;
            }
            .packer-info p {
                font-size: 12px;
            }
            .layui-form-item .layui-inline {
                display: block;
                margin-bottom: 10px;
            }
            .layui-tab-title li {
                padding: 8px 12px;
                font-size: 12px;
            }
            .order-count-badge {
                font-size: 10px;
                padding: 1px 4px;
            }
            .sub-tabs .layui-tab-title li {
                font-size: 11px;
                padding: 4px 8px;
                margin-right: 5px;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
            transition: var(--transition);
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .layui-table tbody tr {
            animation: fadeIn 0.3s ease-in-out;
        }

        /* 特殊状态颜色 */
        #count-pending { background-color: #f59e0b; }
        #count-awaiting_deliver { background-color: #06b6d4; }
        #count-awaiting_deliver2 { background-color: #06b6d4; }
        #count-delivering { background-color: #2563eb; }
        #count-delivered { background-color: #059669; }
        #count-cancelled { background-color: #dc2626; }
        #count-all { background-color: #2563eb; }
        
        /* 二级筛选计数标签颜色 */
        #count-outbound_all { background-color: #06b6d4; }
        #count-outbound_yes { background-color: #10b981; }
        #count-outbound_no { background-color: #ef4444; }

        /* 模态框样式 */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px 20px 10px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-color);
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            font-size: 24px;
            color: #999;
            cursor: pointer;
            line-height: 1;
            transition: color 0.2s ease;
        }

        .modal-close:hover {
            color: #333;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 10px 20px 20px 20px;
            text-align: right;
            border-top: 1px solid #f0f0f0;
        }

        .modal-footer .layui-btn {
            margin-left: 10px;
        }

        /* 物料费选择样式 */
        .material-cost-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .material-option {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            transition: all 0.2s ease;
            position: relative;
        }

        .material-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(37, 99, 235, 0.05);
        }

        .material-option input[type="checkbox"] {
            display: none;
        }

        .material-option input[type="checkbox"]:checked + .checkmark {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .material-option input[type="checkbox"]:checked + .checkmark::after {
            display: block;
        }

        .checkmark {
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            margin-right: 12px;
            position: relative;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .checkmark::after {
            content: "";
            position: absolute;
            display: none;
            left: 5px;
            top: 1px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        /* 表格工具栏按钮 */
        .table-toolbar {
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .batch-action-btn {
            margin-right: 10px;
            border-radius: 6px;
            font-size: 13px;
            padding: 8px 16px;
        }

        /* 异常类型下拉菜单 */
        .exception-dropdown {
            position: relative;
            display: inline-block;
        }

        .exception-dropdown-content {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: white;
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 6px;
            border: 1px solid var(--border-color);
            z-index: 1001;
        }

        .exception-dropdown-content a {
            color: var(--text-color);
            padding: 10px 16px;
            text-decoration: none;
            display: block;
            font-size: 13px;
            transition: background-color 0.2s ease;
        }

        .exception-dropdown-content a:hover {
            background-color: #f8f9fa;
        }

        .exception-dropdown:hover .exception-dropdown-content {
            display: block;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <div class="packer-info" id="packerInfo">
                <div class="loading-msg">正在加载打包员信息...</div>
            </div>
        </div>
        
        <div class="layui-card-body">
            <!-- 搜索表单 -->
            <form class="layui-form layui-form-pane" lay-filter="orderForm">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <input type="text" name="text" placeholder="SKU/货件编号/运号/采购单号/快递单号/标题(双语)" class="layui-input"
                            autocomplete="off" style="width: 380px;">
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 80px;">价格区间</label>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="text" name="money" placeholder="最低价" class="layui-input" autocomplete="off">
                        </div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="text" name="moneys" placeholder="最高价" class="layui-input" autocomplete="off">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 80px;">店铺</label>
                        <div class="layui-input-inline" style="width: 150px;">
                            <select name="storeid" lay-search id="sellerSelect">
                                <option value="">所有店铺</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 80px;">用户UID</label>
                        <div class="layui-input-inline" style="width: 120px;">
                            <input type="text" name="uid" placeholder="用户UID" class="layui-input" autocomplete="off">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 85px;">日期选择</label>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="text" class="layui-input" name="date1" id="date1" placeholder="yyyy-MM-dd">
                        </div>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="text" class="layui-input" name="date2" id="date2" placeholder="yyyy-MM-dd">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                        <button type="reset" class="layui-btn layui-btn-primary" lay-filter="reset">重置</button>
                    </div>
                </div>
            </form>

            <!-- 订单状态标签 -->
            <div class="layui-tab layui-tab-brief" lay-filter="orderTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">
                        待处理
                        <span class="order-count-badge" id="count-pending">0</span>
                    </li>
                    <li>
                        等待发货
                        <span class="order-count-badge" id="count-awaiting_deliver">0</span>
                    </li>
                    <li>
                        交运平台
                        <span class="order-count-badge" id="count-awaiting_deliver2">0</span>
                    </li>
                    <li>
                        运输中
                        <span class="order-count-badge" id="count-delivering">0</span>
                    </li>
                    <li>
                        已送达
                        <span class="order-count-badge" id="count-delivered">0</span>
                    </li>
                    <li>
                        已取消
                        <span class="order-count-badge" id="count-cancelled">0</span>
                    </li>
                    <li>
                        全部订单
                        <span class="order-count-badge" id="count-all">0</span>
                    </li>
                </ul>
            </div>

            <!-- 等待发货的二级筛选标签 -->
            <div class="sub-tabs" id="awaitingDeliverSubTabs">
                <div class="layui-tab layui-tab-brief" lay-filter="outboundTab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">
                            全部
                            <span class="order-count-badge" id="count-outbound_all">0</span>
                        </li>
                        <li>
                            已调用库存
                            <span class="order-count-badge" id="count-outbound_yes">0</span>
                        </li>
                        <li>
                            未调用库存
                            <span class="order-count-badge" id="count-outbound_no">0</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 表格工具栏 -->
            <div class="table-toolbar" style="display: none;" id="tableToolbar">
                <button class="layui-btn layui-btn-sm layui-btn-normal batch-action-btn" onclick="batchPrintLabels()">
                    <i class="layui-icon layui-icon-print"></i> 打印标签
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-warm batch-action-btn" onclick="batchMarkShipped()">
                    <i class="layui-icon layui-icon-ok"></i> 标记发货
                </button>
                <div class="exception-dropdown">
                    <button class="layui-btn layui-btn-sm layui-btn-danger batch-action-btn">
                        <i class="layui-icon layui-icon-close"></i> 异常问题 <i class="layui-icon layui-icon-down"></i>
                    </button>
                    <div class="exception-dropdown-content">
                        <a href="#" onclick="openExceptionModal('query_exception')">查询不到物流单号</a>
                        <a href="#" onclick="openExceptionModal('tracking_number_error')">国际单号有问题</a>
                        <a href="#" onclick="openExceptionModal('other_exception')">其他异常</a>
                    </div>
                </div>
            </div>

            <!-- 订单表格 -->
            <table id="orderTable" lay-filter="orderTable"></table>
        </div>
    </div>

    <!-- 物料费选择模态框 -->
    <div id="materialCostModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择打包物料费</h3>
                <span class="modal-close" onclick="closeMaterialCostModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="material-cost-options">
                    <label class="material-option">
                        <input type="checkbox" class="material-cost-checkbox" value="0" checked>
                        <span class="checkmark"></span>
                        物料费: 0元
                    </label>
                    <label class="material-option">
                        <input type="checkbox" class="material-cost-checkbox" value="1">
                        <span class="checkmark"></span>
                        物料费: 1元
                    </label>
                    <label class="material-option">
                        <input type="checkbox" class="material-cost-checkbox" value="2">
                        <span class="checkmark"></span>
                        物料费: 2元
                    </label>
                    <label class="material-option">
                        <input type="checkbox" class="material-cost-checkbox" value="3">
                        <span class="checkmark"></span>
                        物料费: 3元
                    </label>
                    <label class="material-option">
                        <input type="checkbox" class="material-cost-checkbox" value="5">
                        <span class="checkmark"></span>
                        物料费: 5元
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="layui-btn layui-btn-primary" onclick="closeMaterialCostModal()">取消</button>
                <button type="button" class="layui-btn layui-btn-normal" onclick="confirmMaterialCost()">确认标记发货</button>
            </div>
        </div>
    </div>

    <!-- 异常问题模态框 -->
    <div id="exceptionModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>标记异常</h3>
                <span class="modal-close" onclick="closeExceptionModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="exceptionForm">
                    <input type="hidden" id="exceptionType" name="exceptionType" value="">
                    <div class="layui-form-item">
                        <label class="layui-form-label">异常类型：</label>
                        <div class="layui-input-block">
                            <span id="exceptionTypeText" style="color: #666;"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">异常原因：</label>
                        <div class="layui-input-block">
                            <textarea id="exceptionReason" name="exceptionReason" class="layui-textarea" placeholder="请输入异常原因..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="layui-btn layui-btn-primary" onclick="closeExceptionModal()">取消</button>
                <button type="button" class="layui-btn layui-btn-danger" onclick="submitException()">提交异常</button>
            </div>
        </div>
    </div>

    <script src="../assets/component/layui/layui.js"></script>
    <script src="../assets/component/pear/pear.js"></script>
    <script src="https://label-open.kuaimai.com/cloudPrinterSDK/KM_SDK.umd.js"></script>
    <script>
        layui.use(['table', 'form', 'laydate', 'element', 'layer'], function() {
            var table = layui.table,
                form = layui.form,
                laydate = layui.laydate,
                element = layui.element,
                layer = layui.layer,
                $ = layui.$;

            var currentStatus = 'pending';
            var currentOutboundFilter = 'all'; // 新增：调用库存筛选状态
            var packerInfo = null;

            // 初始化日期选择器
            laydate.render({
                elem: '#date1'
            });
            laydate.render({
                elem: '#date2'
            });

            // 获取打包员信息
            function loadPackerInfo() {
                $.get('ajax.php?act=get_packer_info', function(res) {
                    if (res.code === 0) {
                        packerInfo = res.data;
                        $('#packerInfo').html(
                            '<h4>打包员：' + packerInfo.username + '</h4>' +
                            '<p>关联用户ID：' + packerInfo.user_uid + '</p>'
                        );
                        // 加载其他数据
                        loadStores();
                        loadOrderCounts();
                        initTable();
                    } else {
                        $('#packerInfo').html('<div class="error-msg">' + res.msg + '</div>');
                        if (res.code === 401) {
                            layer.msg('未登录，请先登录', {icon: 0});
                            setTimeout(function() {
                                window.location.href = 'login.html';
                            }, 2000);
                        }
                    }
                }).fail(function() {
                    $('#packerInfo').html('<div class="error-msg">加载打包员信息失败</div>');
                });
            }

            // 初始化表格
            function initTable() {
                var cols = [[
                    {type: 'checkbox', width: 50},
                    {field: 'primary_image', title: '图片', width: 120, templet: function(d) {
                        if (d.has_multiple_products && d.products_info && d.products_info.length > 1) {
                            var html = '<div class="multi-images" style="display: flex; flex-direction: column; gap: 4px; align-items: center;">';
                            var maxDisplay = Math.min(d.products_info.length, 3); // 最多显示3个图片
                            for (var i = 0; i < maxDisplay; i++) {
                                var product = d.products_info[i];
                                var imgSrc = product.image || '../assets/img/syncing.png';
                                html += '<img src="' + imgSrc + '" class="product-image thumbnail" style="width: 50px; height: 50px; border-radius: 4px; object-fit: cover;" alt="商品' + (i + 1) + '" data-rel="' + imgSrc + '" title="商品' + (i + 1) + ': ' + (product.name || 'SKU: ' + product.sku) + '">';
                            }
                            if (d.products_info.length > 3) {
                                html += '<div style="width: 50px; height: 18px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; font-size: 11px; border-radius: 3px; color: #666; font-weight: 500;">+' + (d.products_info.length - 3) + '</div>';
                            }
                            html += '</div>';
                            return html;
                        } else {
                            if (d.primary_image) {
                                return '<img src="' + d.primary_image + '" class="product-image thumbnail" alt="商品图片" data-rel="' + d.primary_image + '">';
                            }
                            return '<div class="no-image">无图</div>';
                        }
                    }},
                    {field: 'posting_number', title: '订单号', width: 150, templet: function(d) {
                        return '<div class="text-break order-number">' + (d.posting_number || '') + '</div>';
                    }},
                    {field: 'sku', title: 'SKU', width: 150, templet: function(d) {
                        if (d.has_multiple_products && d.products_info && d.products_info.length > 1) {
                            var html = '<div class="multi-skus">';
                            for (var i = 0; i < d.products_info.length; i++) {
                                var product = d.products_info[i];
                                var productSku = product.sku || '';
                                html += '<div style="font-size: 11px; margin-bottom: 2px; color: #666;">' + (i + 1) + '. ' + productSku + '</div>';
                            }
                            html += '</div>';
                            return html;
                        } else {
                            return '<div class="text-break">' + (d.sku || '') + '</div>';
                        }
                    }},
                    {field: 'order_name', title: '商品名称', width: 300, templet: function(d) {
                        if (d.has_multiple_products && d.products_info && d.products_info.length > 1) {
                            var html = '<div class="multi-products">';
                            html += '<div style="color: #FF5722; font-weight: bold; margin-bottom: 5px;">该订单包含 ' + d.products_info.length + ' 个商品:</div>';
                            for (var i = 0; i < d.products_info.length; i++) {
                                var product = d.products_info[i];
                                var productName = product.name || '';
                                var productSku = product.sku || '';
                                html += '<div class="product-item" style="margin-bottom: 8px; padding: 5px; background: #f9f9f9; border-radius: 3px;">';
                                if (productSku) {
                                    html += '<a href="https://www.ozon.ru/product/' + productSku + '/" target="_blank" style="text-decoration: none;">';
                                    html += '<div class="product-name" style="color: #1E9FFF; font-size: 12px; margin-bottom: 2px;">' + (i + 1) + '. ' + productName + '</div>';
                                    html += '</a>';
                                } else {
                                    html += '<div class="product-name" style="font-size: 12px; margin-bottom: 2px;">' + (i + 1) + '. ' + productName + '</div>';
                                }
                                html += '<div style="color: #666; font-size: 11px;">SKU: ' + productSku + ' | 数量: ' + (product.quantity || 1) + '</div>';
                                html += '</div>';
                            }
                            html += '</div>';
                            return html;
                        } else {
                            var name = d.order_name || d.name2 || '';
                            var sku = d.sku || '';
                            if (sku) {
                                return '<a href="https://www.ozon.ru/product/' + sku + '/" target="_blank" style="text-decoration: none;"><div class="product-name" title="' + name + '" style="color: #1E9FFF; cursor: pointer;">' + name + '</div></a>';
                            }
                            return '<div class="product-name" title="' + name + '">' + name + '</div>';
                        }
                    }},
                    {field: 'quantity', title: '数量', width: 80, align: 'center', templet: function(d) {
                        if (d.has_multiple_products && d.calculated_total_quantity) {
                            return '<span style="font-weight: 600; color: #FF5722;" title="多商品订单，共' + d.products_info.length + '种商品">' + d.calculated_total_quantity + '</span>';
                        } else {
                            return '<span style="font-weight: 600;">' + (d.quantity || 0) + '</span>';
                        }
                    }},
                    {field: 'price', title: '总价(¥)', width: 100, align: 'right', templet: function(d) {
                        if (d.has_multiple_products && d.calculated_total_price) {
                            return '<span class="price-value" title="多商品订单总价，包含' + d.products_info.length + '个商品">¥' + d.calculated_total_price + '</span>';
                        } else {
                            return '<span class="price-value">¥' + (d.price || '0.00') + '</span>';
                        }
                    }},
                    {field: 'cost', title: '成本(¥)', width: 100, align: 'right', templet: function(d) {
                        var cost = d.cost || '';
                        if (cost && cost !== '' && cost !== '0') {
                            return '<span class="cost-value">¥' + cost + '</span>';
                        }
                        return '<span class="cost-empty">未设置</span>';
                    }},
                    {field: 'status', title: '状态', width: 120, align: 'center', templet: function(d) {
                        var statusMap = {
                            'pending': '待处理',
                            'awaiting_deliver': '等待发货',
                            'delivering': '运输中',
                            'delivered': '已送达',
                            'cancelled': '已取消',
                            'awaiting_packaging': '待打包'
                        };
                        var statusText = statusMap[d.status] || d.status;
                        var statusClass = 'status-' + d.status;
                        return '<span class="status-badge ' + statusClass + '">' + statusText + '</span>';
                    }},
                    {field: 'Outbound_data', title: '调用库存', width: 120, align: 'center', templet: function(d) {
                        if (d.Outbound_data && d.Outbound_data !== '' && d.Outbound_data !== 'null' && d.Outbound_data !== '0000-00-00 00:00:00') {
                            return '<span class="status-badge status-delivered">已调用</span>';
                        }
                        return '<span class="status-badge status-cancelled">未调用</span>';
                    }},
                    {field: 'in_process_at', title: '下单时间', width: 120, templet: function(d) {
                        if (d.in_process_at) {
                            var date = d.in_process_at.replace(/^\d{4}-/, '');
                            return '<div class="text-small">' + date + '</div>';
                        }
                        return '<span class="text-empty">无</span>';
                    }},
                    {field: 'courierNumber', title: '快递单号', width: 150, templet: function(d) {
                        if (d.courierNumber) {
                            return '<div class="text-break text-small">' + d.courierNumber + '</div>';
                        }
                        return '<span class="text-empty">无</span>';
                    }},
                    {field: 'tracking_number', title: '国际单号', width: 150, templet: function(d) {
                        if (d.tracking_number) {
                            return '<div class="text-break text-small">' + d.tracking_number + '</div>';
                        }
                        return '<span class="text-empty">无</span>';
                    }},
                    {field: 'storename', title: '店铺', width: 120, templet: function(d) {
                        return '<div class="store-name" title="' + (d.storename || '') + '">' + (d.storename || '') + '</div>';
                    }}
                ]];

                table.render({
                    elem: '#orderTable',
                    url: 'ajax.php?act=get_orders',
                    method: 'GET',
                    cols: cols,
                    page: true,
                    height: 'full-280', // 增加高度以适应二级标签
                    limit: 20,
                    limits: [10, 20, 50, 100],
                    where: {
                        status: currentStatus,
                        outbound_filter: currentOutboundFilter
                    },
                    parseData: function(res) {
                        return {
                            "code": res.code,
                            "msg": res.msg,
                            "count": res.count,
                            "data": res.data
                        };
                    },
                    done: function(res, curr, count) {
                        if (res.code !== 0) {
                            if (res.code === 401) {
                                layer.msg('登录已过期，请重新登录', {icon: 0});
                                setTimeout(function() {
                                    window.location.href = 'login.html';
                                }, 2000);
                            } else {
                                layer.msg(res.msg || '获取订单数据失败', {icon: 2});
                            }
                        }
                        
                        // 调试：检查返回的数据
                        if (res.data && res.data.length > 0) {
                            var multiProductCount = 0;
                            res.data.forEach(function(item) {
                                if (item.products && item.products.length > 0) {
                                    console.log('Order ' + item.posting_number + ' has products field with length:', item.products.length);
                                }
                                if (item.has_multiple_products) {
                                    multiProductCount++;
                                    console.log('Multi-product order found:', item.posting_number, item.products_info);
                                }
                            });
                            console.log('Total multi-product orders in this page:', multiProductCount);
                        }
                        

                        

                        
                        // 前端库存筛选（仅在等待发货状态下生效）
                        if (currentStatus === 'awaiting_deliver' && currentOutboundFilter !== 'all') {
                            applyOutboundFilter();
                        }
                        
                        // 绑定图片点击放大事件
                        $('.thumbnail').off('click').on('click', function () {
                            const $thumbnail = $(this);
                            const imgSrc = $thumbnail.attr('data-rel') || $thumbnail.attr('src');
                            layer.photos({
                                photos: {
                                    data: [{ src: imgSrc }]      // 使用Layer组件展示大图
                                },
                                anim: 5                     // 展开动画
                            });
                        });
                    }
                });
            }

            // 主状态标签切换
            element.on('tab(orderTab)', function(data) {
                var statusMap = ['pending', 'awaiting_deliver', 'awaiting_deliver2', 'delivering', 'delivered', 'cancelled', 'all'];
                currentStatus = statusMap[data.index];
                
                // 显示或隐藏二级筛选标签
                if (currentStatus === 'awaiting_deliver') {
                    $('#awaitingDeliverSubTabs').addClass('show');
                    currentOutboundFilter = 'all'; // 重置为全部
                    // 重置二级标签的选中状态
                    setTimeout(function() {
                        $('#awaitingDeliverSubTabs .layui-tab-title li').removeClass('layui-this');
                        $('#awaitingDeliverSubTabs .layui-tab-title li:first-child').addClass('layui-this');
                    }, 100);
                } else {
                    $('#awaitingDeliverSubTabs').removeClass('show');
                    currentOutboundFilter = 'all';
                }
                

                
                reloadTable();
            });

            // 二级筛选标签切换（调用库存筛选）
            element.on('tab(outboundTab)', function(data) {
                var outboundMap = ['all', 'yes', 'no'];
                currentOutboundFilter = outboundMap[data.index];
                if (currentStatus === 'awaiting_deliver') {
                    applyOutboundFilter();
                }
            });
            
            // 手动绑定二级标签点击事件（主要方案）
            $(document).on('click', '#awaitingDeliverSubTabs .layui-tab-title li', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var index = $(this).index();
                var outboundMap = ['all', 'yes', 'no'];
                currentOutboundFilter = outboundMap[index];
                

                
                // 手动切换选中状态
                $('#awaitingDeliverSubTabs .layui-tab-title li').removeClass('layui-this');
                $(this).addClass('layui-this');
                
                // 只有在"等待发货"状态下才执行筛选
                if (currentStatus === 'awaiting_deliver') {
                    // 使用前端筛选而不是重新加载数据
                    applyOutboundFilter();
                }
            });

            // 搜索功能
            form.on('submit(search)', function(data) {
                reloadTable(data.field);
                return false;
            });

            // 重置功能
            form.on('submit(reset)', function() {
                $('#orderTable').next('.layui-laypage').find('.layui-laypage-skip').find('input').val(1);
                reloadTable();
                return false;
            });

            // 重新加载表格
            function reloadTable(searchData) {
                var whereData = {
                    status: currentStatus
                };
                if (searchData) {
                    $.extend(whereData, searchData);
                }
                
                table.reload('orderTable', {
                    where: whereData,
                    page: {curr: 1}
                });
            }

            // 加载店铺选择器
            function loadStores() {
                $.get('ajax.php?act=get_stores', function(res) {
                    if (res.code === 0) {
                        var html = '<option value="">所有店铺</option>';
                        $.each(res.data, function(i, item) {
                            html += '<option value="' + item.id + '">' + item.storename + '</option>';
                        });
                        $('#sellerSelect').html(html);
                        form.render('select');
                    }
                });
            }

            // 获取订单状态统计
            function loadOrderCounts() {
                $.get('ajax.php?act=order_status_counts', function(res) {
                    if (res.code === 0) {
                        updateOrderCounts(res.data);
                    }
                });
            }

            // 更新订单计数
            function updateOrderCounts(counts) {
                $('#count-pending').text(counts.pending || 0);
                $('#count-awaiting_deliver').text(counts.awaiting_deliver || 0);
                $('#count-awaiting_deliver2').text(counts.awaiting_deliver2 || 0);
                $('#count-delivering').text(counts.delivering || 0);
                $('#count-delivered').text(counts.delivered || 0);
                $('#count-cancelled').text(counts.cancelled || 0);
                $('#count-all').text(counts.all || 0);
                
                // 更新二级筛选计数
                $('#count-outbound_all').text(counts.outbound_all || 0);
                $('#count-outbound_yes').text(counts.outbound_yes || 0);
                $('#count-outbound_no').text(counts.outbound_no || 0);
            }



            // 前端库存筛选函数
            function applyOutboundFilter() {
                var $rows = $('.layui-table-body tbody tr');
                var visibleCount = 0;
                
                $rows.each(function() {
                    var $row = $(this);
                    var $outboundCell = $row.find('td').eq(9); // 调用库存列（第10列，索引为9）
                    var outboundText = $outboundCell.text().trim();
                    
                    var shouldShow = false;
                    
                    if (currentOutboundFilter === 'yes') {
                        // 显示已调用库存的行
                        shouldShow = outboundText.includes('已调用');
                    } else if (currentOutboundFilter === 'no') {
                        // 显示未调用库存的行
                        shouldShow = outboundText.includes('未调用');
                    } else {
                        // 全部显示
                        shouldShow = true;
                    }
                    
                    if (shouldShow) {
                        $row.show();
                        visibleCount++;
                    } else {
                        $row.hide();
                    }
                });
                
                // 更新分页信息
                if (visibleCount === 0) {
                    if ($('.layui-table-body .layui-none').length === 0) {
                        $('.layui-table-body tbody').append(
                            '<tr class="layui-none"><td colspan="100%" style="text-align: center; color: #999; padding: 30px;">暂无符合条件的数据</td></tr>'
                        );
                    }
                } else {
                    $('.layui-table-body .layui-none').remove();
                }
            }

            // 监听表格复选框事件，显示/隐藏工具栏
            table.on('checkbox(orderTable)', function(obj) {
                updateToolbarVisibility();
            });

            // 更新工具栏显示状态
            function updateToolbarVisibility() {
                var checkStatus = table.checkStatus('orderTable');
                var hasSelected = checkStatus.data.length > 0;
                
                if (hasSelected) {
                    $('#tableToolbar').show();
                } else {
                    $('#tableToolbar').hide();
                }
            }

            // 清除所有选择状态
            function clearAllSelections() {
                // 清除表格中所有选中状态
                $('input[type="checkbox"][lay-filter="orderTable"]').prop('checked', false);
                // 隐藏工具栏
                $('#tableToolbar').hide();
                // 触发layui表格的选择状态更新
                layui.table.checkStatus('orderTable');
            }

            // 获取选中的订单数据
            function getSelectedOrders() {
                var checkStatus = table.checkStatus('orderTable');
                return checkStatus.data;
            }

            // 批量打印标签
            window.batchPrintLabels = function() {
                var selectedOrders = getSelectedOrders();
                if (selectedOrders.length === 0) {
                    layer.msg('请勾选要打印标签的订单', {icon: 0});
                    return;
                }

                // 去重订单号 - 一个订单可能有多个商品
                var postingNumbers = [];
                var uniquePostingNumbers = new Set();
                selectedOrders.forEach(function(order) {
                    if (!uniquePostingNumbers.has(order.posting_number)) {
                        uniquePostingNumbers.add(order.posting_number);
                        postingNumbers.push(order.posting_number);
                    }
                });

                var orderCount = postingNumbers.length;
                var itemCount = selectedOrders.length;
                var message = '确定要打印 ' + orderCount + ' 个订单的标签吗？';
                if (orderCount !== itemCount) {
                    message += '<br><small style="color:#999;">已选择 ' + itemCount + ' 个商品，涉及 ' + orderCount + ' 个唯一订单</small>';
                }
                message += '<br><small style="color:#999;">首次生成面单可能需要较长时间，请耐心等待</small>';

                layer.confirm(message, {
                    icon: 3,
                    title: '确认打印'
                }, function(index) {
                    layer.close(index);
                    handlePrintLabels(postingNumbers);
                });
            };

            // 处理打印标签
            async function handlePrintLabels(postingNumbers) {
                // 显示进度提示
                var loadingIndex = layer.msg('正在获取打印机配置...', {
                    icon: 16,
                    shade: [0.3, '#000'],
                    time: 0
                });
                
                try {
                    // 1. 获取打印机配置
                    const configRes = await $.get('ajax.php?act=get_printer_config');
                    if (configRes.code !== 0) {
                        throw new Error(configRes.msg || '获取打印机配置失败');
                    }
                    const config = configRes.data;
                    
                    // 2. 获取标签文件
                    layer.close(loadingIndex);
                    loadingIndex = layer.msg('正在生成标签文件，请耐心等待...', {
                        icon: 16,
                        shade: [0.3, '#000'],
                        time: 0
                    });
                    
                    const printRes = await $.ajax({
                        url: 'ajax.php?act=print_label',
                        method: 'POST',
                        data: JSON.stringify({postingNumbers: postingNumbers}),
                        contentType: 'application/json',
                        timeout: 90000  // 90秒超时
                    });
                    
                    if (printRes.code !== 0 || !printRes.data || printRes.data.length === 0) {
                        throw new Error(printRes.msg || '获取标签文件失败');
                    }
                    
                    // 3. 将base64转换为File对象数组
                    layer.close(loadingIndex);
                    loadingIndex = layer.msg('正在准备打印文件...', {
                        icon: 16,
                        shade: [0.3, '#000'],
                        time: 0
                    });
                    
                    const files = printRes.data.map(item => {
                        const byteCharacters = atob(item.content);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        return new File([byteArray], item.name, { type: 'application/pdf' });
                    });
                    
                    // 4. 使用打印机SDK打印
                    const KMSDK = new KM_SDK();
                    const params = {
                        appId: config.appId,
                        secret: config.secret,
                        sn: config.sn,
                        file: files,
                        isPDF: true,
                        kmPrintTimes: 1,
                        extra: "0",
                        printPreview: true,
                        previewWidth: config.previewWidth || 80,
                        previewHeight: config.previewHeight || 120,
                        callback: res => {
                            if (res.code === 0) {
                                layer.msg('打印任务已提交', {icon: 1});
                            } else {
                                layer.msg('打印失败: ' + (res.msg || '未知错误'), {icon: 2});
                            }
                        }
                    };
                    
                    KMSDK.printFile(params);
                    
                    layer.close(loadingIndex);
                    
                    // 清除选择状态，隐藏工具栏
                    clearAllSelections();
                    
                } catch (error) {
                    layer.close(loadingIndex);
                    if (error.message && error.message.includes('timeout')) {
                        layer.msg('请求超时，面单生成可能需要更长时间，请稍后重试', {icon: 0, time: 5000});
                    } else {
                        layer.msg(error.message || '打印失败', {icon: 2});
                    }
                }
            }

            // 批量标记发货
            window.batchMarkShipped = function() {
                var selectedOrders = getSelectedOrders();
                if (selectedOrders.length === 0) {
                    layer.msg('请勾选要标记发货的订单', {icon: 0});
                    return;
                }

                $('#materialCostModal').show();
            };

            // 物料费模态框相关函数
            window.closeMaterialCostModal = function() {
                $('#materialCostModal').hide();
            };

            window.confirmMaterialCost = function() {
                var selectedOrders = getSelectedOrders();
                if (selectedOrders.length === 0) {
                    layer.msg('请勾选要标记发货的订单', {icon: 0});
                    return;
                }

                // 计算物料费
                var materialCost = 0;
                $('.material-cost-checkbox:checked').each(function() {
                    materialCost += parseFloat($(this).val());
                });

                // 准备订单数据 - 去重订单号
                var orders = [];
                var uniquePostingNumbers = new Set();
                selectedOrders.forEach(function(order) {
                    if (!uniquePostingNumbers.has(order.posting_number)) {
                        uniquePostingNumbers.add(order.posting_number);
                        orders.push({
                            postingNumber: order.posting_number,
                            trackingNumber: order.courierNumber || '',
                            courierNumber: order.courierNumber || ''
                        });
                    }
                });

                closeMaterialCostModal();
                
                layer.load(1, {shade: [0.3, '#000']});
                
                $.ajax({
                    url: 'ajax.php?act=mark_shipped',
                    method: 'POST',
                    data: JSON.stringify({
                        orders: orders,
                        materialCost: materialCost
                    }),
                    contentType: 'application/json',
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.msg('已成功标记发货', {icon: 1});
                            // 清除选择状态，隐藏工具栏
                            clearAllSelections();
                        } else {
                            layer.msg(res.msg || '标记发货失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.msg('网络错误，请重试', {icon: 2});
                    }
                });
            };

            // 异常问题相关函数
            window.openExceptionModal = function(exceptionType) {
                var selectedOrders = getSelectedOrders();
                if (selectedOrders.length === 0) {
                    layer.msg('请勾选要标记异常的订单', {icon: 0});
                    return;
                }

                var exceptionTypeMap = {
                    'query_exception': '查询不到物流单号',
                    'tracking_number_error': '国际单号有问题',
                    'other_exception': '其他异常'
                };

                $('#exceptionType').val(exceptionType);
                $('#exceptionTypeText').text(exceptionTypeMap[exceptionType]);
                $('#exceptionReason').val('');
                $('#exceptionModal').show();
            };

            window.closeExceptionModal = function() {
                $('#exceptionModal').hide();
            };

            window.submitException = function() {
                var selectedOrders = getSelectedOrders();
                if (selectedOrders.length === 0) {
                    layer.msg('请勾选要标记异常的订单', {icon: 0});
                    return;
                }

                var exceptionType = $('#exceptionType').val();
                var exceptionReason = $('#exceptionReason').val().trim();

                if (!exceptionReason) {
                    layer.msg('请输入异常原因', {icon: 0});
                    return;
                }

                closeExceptionModal();
                
                layer.load(1, {shade: [0.3, '#000']});

                // 批量标记异常
                var trackingNumbers = selectedOrders.map(function(order) {
                    return order.courierNumber || '';
                }).filter(function(num) {
                    return num !== '';
                });

                if (trackingNumbers.length === 0) {
                    layer.closeAll('loading');
                    layer.msg('所选订单缺少快递单号', {icon: 0});
                    return;
                }

                $.ajax({
                    url: 'ajax.php?act=mark_exception',
                    method: 'POST',
                    data: JSON.stringify({
                        trackingNumbers: trackingNumbers,
                        exceptionType: exceptionType,
                        exceptionReason: exceptionReason
                    }),
                    contentType: 'application/json',
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                        } else {
                            layer.msg(res.msg || '标记异常失败', {icon: 2});
                        }
                        
                        // 清除选择状态，隐藏工具栏
                        clearAllSelections();
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.msg('网络错误，请重试', {icon: 2});
                    }
                });
            };

            // 物料费选择框互斥逻辑
            $(document).on('change', '.material-cost-checkbox', function() {
                if ($(this).prop('checked')) {
                    $('.material-cost-checkbox').not(this).prop('checked', false);
                }
            });

            // 初始化页面
            loadPackerInfo();
        });
    </script>
</body>
</html> 