<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>产品库管理</title>
    
    <style>
        .layui-card-header h3 {
            margin: 0;
            font-size: 18px;
            color: #1E9FFF;
            font-weight: 600;
        }
        .main-container {
            display: flex;
            gap: 20px;
            min-height: 800px;
            padding: 10px 0;
        }
        .left-panel {
            flex: 2.5;
            min-width: 900px;
            background: #fff;
            border: 1px solid #e2e2e2;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .right-panel {
            flex: 1;
            min-width: 400px;
            max-width: 500px;
            background: #fff;
            border: 1px solid #e2e2e2;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            max-height: 800px;
            overflow-y: auto;
        }
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #ddd;
            display: block;
            margin: 0 auto;
        }
        .sku-match {
            color: #1E9FFF;
            cursor: pointer;
            font-weight: 500;
            font-size: 12px;
            display: inline-block;
            margin: 2px 0;
        }
        .stock-detail {
            color: #009688;
            cursor: pointer;
            font-weight: 500;
        }
        .layui-form-label {
            width: 130px;
            font-weight: 600;
        }
        .layui-input-block {
            margin-left: 150px;
        }
        .form-section {
            margin-bottom: 25px;
        }
        .form-section h3 {
            border-bottom: 2px solid #1E9FFF;
            padding-bottom: 10px;
            margin-bottom: 20px;
            color: #1E9FFF;
            font-weight: 700;
            font-size: 18px;
        }
        .spec-table th, .spec-table td {
            text-align: center;
            font-size: 14px;
        }
        #imagePreview img {
            max-width: 100%;
            max-height: 160px;
            border-radius: 6px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }
        .button-group .layui-btn {
            flex: 1;
            font-weight: 600;
            font-size: 15px;
        }
        .filter-container {
            margin-bottom: 15px;
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }
        .filter-container input.layui-input {
            border-radius: 4px;
            border: 1px solid #ccc;
            padding: 6px 10px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        .filter-container input.layui-input:focus {
            border-color: #1E9FFF;
            box-shadow: 0 0 5px rgba(30, 159, 255, 0.5);
        }
        .filter-container button.layui-btn {
            min-width: 80px;
            font-weight: 600;
            font-size: 14px;
        }
        .platform-sku-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .platform-sku-container input {
            flex: 1;
        }
        .platform-sku-tag {
            display: inline-block;
            background-color: #1E9FFF;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .platform-sku-tag .close {
            margin-left: 5px;
            cursor: pointer;
        }
        #platformSkuTags {
            margin-top: 5px;
        }
        
        /* 表格容器宽度设置 */
        .layui-table-view {
            overflow-x: auto;
        }
        
        /* 响应式设计 */
        @media (max-width: 1600px) {
            .main-container {
                flex-direction: column;
                gap: 15px;
            }
            .left-panel {
                min-width: auto;
                max-width: none;
                width: 100%;
                order: 2;
            }
            .right-panel {
                min-width: auto;
                max-width: none;
                width: 100%;
                max-height: none;
                order: 1;
            }
        }
        
        /* 表格容器滚动条优化 */
        .left-panel::-webkit-scrollbar {
            height: 8px;
        }
        .left-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .left-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        .left-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
<div class="layui-fluid" style="margin-top: 20px; padding: 0 20px;">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2>产品库管理</h2>
        </div>
        <div class="layui-card-body">
            <div class="main-container">
                <!-- 左侧面板 - 产品列表 -->
                <div class="left-panel">
                    <div style="margin-bottom: 10px; display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="filterProductIdList" placeholder="产品ID" class="layui-input" style="width: 100px;">
                        <input type="text" id="filterSkuList" placeholder="SKU号" class="layui-input" style="width: 120px;">
                        <input type="text" id="filterTitleList" placeholder="商品名称" class="layui-input" style="width: 150px;">
                        <button class="layui-btn layui-btn-sm" id="btnFilterProductList">筛选</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" id="btnResetProductList">重置</button>
                    </div>
                    <table class="layui-hide" id="productTable" lay-filter="productTable"></table>
                </div>
                <!-- 右侧面板 - 新增/编辑商品表单 -->
                <div class="right-panel">
                    <div class="layui-card">
                        <div class="layui-card-header"><h3>新增/编辑商品</h3></div>
                        <div class="layui-card-body">
                            <form class="layui-form" lay-filter="productForm" id="productForm">
                                <input type="hidden" name="id" />
                                <div class="form-section">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商品类型</label>
                                        <div class="layui-input-block">
                                            <input type="radio" name="product_type" value="single" title="单品" checked>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商品标题</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="title" required lay-verify="required" placeholder="请输入商品标题" autocomplete="off" class="layui-input" maxlength="200">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商品图片链接</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="image_url" placeholder="请输入图片链接" autocomplete="off" class="layui-input" maxlength="255" />
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">尺寸 (长*宽*高 cm)</label>
                                        <div class="layui-input-block" style="display:flex; gap:10px;">
                                            <input type="number" name="size_length" placeholder="长" min="0" step="0.01" class="layui-input" style="width: 100px;">
                                            <input type="number" name="size_width" placeholder="宽" min="0" step="0.01" class="layui-input" style="width: 100px;">
                                            <input type="number" name="size_height" placeholder="高" min="0" step="0.01" class="layui-input" style="width: 100px;">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">重量 (kg)</label>
                                        <div class="layui-input-block" style="width: 150px;">
                                            <input type="number" name="weight" placeholder="重量" min="0" step="0.001" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商品状态</label>
                                        <div class="layui-input-block">
                                            <select name="status" lay-verify="required">
                                                <option value="onsale">在售</option>
                                                <option value="instock">已入仓</option>
                                                <option value="outofstock">缺货</option>
                                                <option value="stop">停售</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商品SKU号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="sku" required lay-verify="required" placeholder="请输入商品SKU" autocomplete="off" class="layui-input" maxlength="150">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">平台产品匹配</label>
                                        <div class="layui-input-block">
                                            <div class="platform-sku-container">
                                                <input type="text" id="platformSkuInput" placeholder="输入平台SKU后按回车" autocomplete="off" class="layui-input">
                                                <button type="button" class="layui-btn layui-btn-sm" id="btnAddPlatformSku">添加</button>
                                            </div>
                                            <div id="platformSkuTags"></div>
                                            <input type="hidden" name="platform_sku" id="platformSkuHidden">
                                            <span id="matchResult" style="margin-left:10px;color:#1E9FFF;"></span>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商品单价 (元)</label>
                                        <div class="layui-input-block" style="width: 150px;">
                                            <input type="number" name="price" required lay-verify="required" placeholder="请输入商品单价" min="0" step="0.0001" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item layui-form-text">
                                        <label class="layui-form-label">商品备注</label>
                                        <div class="layui-input-block">
                                            <textarea name="remark" placeholder="请输入备注" class="layui-textarea" maxlength="500"></textarea>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">仓库位置</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="warehouse" placeholder="请输入仓库位置" autocomplete="off" class="layui-input" maxlength="150" />
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">仓库库存数量</label>
                                        <div class="layui-input-block" style="width: 150px;">
                                            <input type="number" name="warehouse_stock" placeholder="仓库库存数量" min="0" step="1" class="layui-input" disabled />
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item" style="text-align:center; margin-top: 20px;">
                                    <button class="layui-btn" lay-submit lay-filter="submitProduct">保存</button>
                                    <button type="button" class="layui-btn layui-btn-primary" id="btnCancel">取消</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-tab layui-tab-brief" lay-filter="inventoryLogTab" style="margin-top: 20px;">
                <ul class="layui-tab-title">
                    <li class="layui-this">库存日志</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div style="margin-bottom: 10px; display: flex; gap: 10px; align-items: center;">
                            <input type="text" id="filterProductId" placeholder="商品ID" class="layui-input" style="width: 120px;">
                            <input type="text" id="filterSku" placeholder="SKU" class="layui-input" style="width: 150px;">
                            <button class="layui-btn layui-btn-sm" id="btnFilterLogs">筛选</button>
                            <button class="layui-btn layui-btn-sm layui-btn-primary" id="btnResetFilter">重置</button>
                        </div>
                        <table class="layui-hide" id="inventoryLogTable" lay-filter="inventoryLogTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 平台SKU管理
    var platformSkus = [];
    var isSubmitting = false; // 防止重复提交标志
    
    // 添加平台SKU
    function addPlatformSku(sku) {
        if (!sku.trim()) return;
        if (platformSkus.includes(sku.trim())) {
            layer.msg('该SKU已存在', {icon: 0});
            return;
        }
        platformSkus.push(sku.trim());
        updatePlatformSkuDisplay();
        updatePlatformSkuHiddenField();
    }
    
    // 移除平台SKU
    function removePlatformSku(index) {
        platformSkus.splice(index, 1);
        updatePlatformSkuDisplay();
        updatePlatformSkuHiddenField();
    }
    
    // 更新显示
    function updatePlatformSkuDisplay() {
        var html = '';
        platformSkus.forEach(function(sku, index) {
            html += '<span class="platform-sku-tag">' + sku + 
                   '<span class="close" data-index="' + index + '">&times;</span></span>';
        });
        $('#platformSkuTags').html(html);
        
        // 绑定删除事件 - 使用事件委托
        $('#platformSkuTags').off('click', '.close').on('click', '.close', function() {
            var index = parseInt($(this).data('index'));
            removePlatformSku(index);
        });
    }
    
    // 更新隐藏字段
    function updatePlatformSkuHiddenField() {
        $('#platformSkuHidden').val(JSON.stringify(platformSkus));
    }
    
    // 从隐藏字段加载
    function loadPlatformSkusFromHidden() {
        var skusJson = $('#platformSkuHidden').val();
        if (skusJson) {
            try {
                platformSkus = JSON.parse(skusJson);
                updatePlatformSkuDisplay();
            } catch (e) {
                console.error('解析平台SKU失败', e);
            }
        }
    }
    
    // 添加按钮事件 - 防止重复绑定
    $('#btnAddPlatformSku').off('click').on('click', function() {
        var sku = $('#platformSkuInput').val();
        addPlatformSku(sku);
        $('#platformSkuInput').val('').focus();
    });
    
    // 回车键添加 - 防止重复绑定
    $('#platformSkuInput').off('keypress').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            var sku = $(this).val();
            addPlatformSku(sku);
            $(this).val('').focus();
            return false;
        }
    });

    // 初始化产品表格
    var productTable = table.render({
        elem: '#productTable',
        url: '/user/ajax.php?act=list',
        method: 'GET',
        page: true,
        limit: 15,
        cols: [[
            {type: 'checkbox', fixed: 'left', width: 50},
            {field: 'id', title: 'ID', width: 70, fixed: 'left'},
            {field: 'sku', title: 'SKU号', width: 120, fixed: 'left'},
            {field: 'image_url', title: '图片', width: 80, align: 'center', templet: function(d){
                if(d.image_url){
                    return '<img src="'+d.image_url+'" alt="产品图片" class="product-image">';
                } else {
                    return '-';
                }
            }},
            {field: 'title', title: '商品名称', width: 200},
            {field: 'remark', title: '描述', width: 180},
            {field: 'price', title: '单价', width: 80},
            {field: 'warehouse_stock', title: '库存', width: 80},
            {field: 'platform_sku', title: '平台匹配', width: 160, templet: function(d){
                if (!d.platform_sku) return '-';
                try {
                    var skus = JSON.parse(d.platform_sku);
                    return skus.map(function(sku) {
                        return '<span class="sku-match">'+sku+'</span>';
                    }).join('<br>');
                } catch (e) {
                    return '<span class="sku-match">'+d.platform_sku+'</span>';
                }
            }},
            {field: 'created_at', title: '创建时间', width: 140},
            {field: 'updated_at', title: '更新时间', width: 140},
            {title: '操作', fixed: 'right', width: 180, align: 'center', toolbar: '#operationTpl'}
        ]],
        done: function(res, curr, count){
            console.log('表格加载完成:', res);
        }
    });

    // 产品列表筛选事件 - 防止重复绑定
    $('#btnFilterProductList').off('click').on('click', function(){
        var filterProductId = $('#filterProductIdList').val().trim();
        var filterSku = $('#filterSkuList').val().trim();
        var filterTitle = $('#filterTitleList').val().trim();
        var url = '/user/ajax.php?act=list';
        var params = [];
        if(filterProductId){
            params.push('id=' + encodeURIComponent(filterProductId));
        }
        if(filterSku){
            params.push('sku=' + encodeURIComponent(filterSku));
        }
        if(filterTitle){
            params.push('title=' + encodeURIComponent(filterTitle));
        }
        if(params.length > 0){
            url += '&' + params.join('&');
        }
        productTable.reload({
            url: url,
            page: {curr: 1}
        });
    });

    // 产品列表重置筛选 - 防止重复绑定
    $('#btnResetProductList').off('click').on('click', function(){
        $('#filterProductIdList').val('');
        $('#filterSkuList').val('');
        $('#filterTitleList').val('');
        productTable.reload({
            url: '/user/ajax.php?act=list',
            page: {curr: 1}
        });
    });

    // 初始化库存日志表格
    var currentProductId = null;
    var inventoryLogTable = table.render({
        elem: '#inventoryLogTable',
        url: '/user/ajax.php?act=warehouse_stock_log_list',
        method: 'GET',
        page: true,
        limit: 15,
        cols: [[
            {field: 'id', title: '日志ID', width: 80},
            {field: 'product_id', title: '产品ID', width: 100},
            {field: 'change_quantity', title: '变动数量', width: 100},
            {field: 'change_type', title: '变动类型', width: 100, templet: function(d){
                return d.change_type === 'in' ? '入库' : '出库';
            }},
            {field: 'remark', title: '备注', minWidth: 200},
            {field: 'created_at', title: '创建时间', width: 160},
            {title: '操作', width: 100, align: 'center', toolbar: '#logOperationTpl'}
        ]],
        done: function(res, curr, count){
            console.log('库存日志表格加载完成:', res);
        }
    });

    // 筛选库存日志事件 - 防止重复绑定
    $('#btnFilterLogs').off('click').on('click', function(){
        var filterProductId = $('#filterProductId').val().trim();
        var filterSku = $('#filterSku').val().trim();
        var url = '/user/ajax.php?act=warehouse_stock_log_list';
        if(filterProductId){
            url += '&product_id=' + encodeURIComponent(filterProductId);
        } else if(filterSku){
            url += '&sku=' + encodeURIComponent(filterSku);
        }
        inventoryLogTable.reload({
            url: url,
            page: {curr: 1}
        });
    });

    // 重置筛选 - 防止重复绑定
    $('#btnResetFilter').off('click').on('click', function(){
        $('#filterProductId').val('');
        $('#filterSku').val('');
        inventoryLogTable.reload({
            url: '/user/ajax.php?act=warehouse_stock_log_list',
            page: {curr: 1}
        });
    });

    // 监听产品表格编辑事件
    table.on('tool(productTable)', function(obj){
        var data = obj.data;
        
        if(obj.event === 'stocklog'){
            if(!data.id){
                layer.msg('无效的产品ID', {icon: 0});
                return;
            }
            currentProductId = data.id;
            layer.open({
                type: 1,
                title: '出入库',
                area: ['450px', '360px'],
                content: `<form class="layui-form" id="addLogForm" style="padding: 25px 30px; background: #fff; border-radius: 8px;">
                    <input type="hidden" name="product_id" value="` + currentProductId + `" />
                    <div class="layui-form-item" style="margin-bottom: 18px;">
                        <label class="layui-form-label" style="width: 110px;">变动数量</label>
                        <div class="layui-input-block" style="margin-left: 120px;">
                            <input type="number" name="change_quantity" required lay-verify="required|number" min="1" class="layui-input" style="padding: 8px 12px; font-size: 15px;" />
                        </div>
                    </div>
                    <div class="layui-form-item" style="margin-bottom: 18px;">
                        <label class="layui-form-label" style="width: 110px;">变动类型</label>
                        <div class="layui-input-block" style="margin-left: 120px;">
                            <select name="change_type" lay-verify="required" style="height: 38px; font-size: 15px;">
                                <option value="in">入库</option>
                                <option value="out">出库</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text" style="margin-bottom: 20px;">
                        <label class="layui-form-label" style="width: 110px;">备注</label>
                        <div class="layui-input-block" style="margin-left: 120px;">
                            <textarea name="remark" placeholder="请输入备注" class="layui-textarea" style="font-size: 14px; padding: 8px 12px;"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item" style="text-align: center; margin-top: 10px;">
                        <button class="layui-btn" lay-submit lay-filter="submitAddLog" style="font-size: 16px; padding: 8px 30px;">提交</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="btnCancelAddLog" style="font-size: 16px; padding: 8px 30px; margin-left: 15px;">取消</button>
                    </div>
                </form>`
            });
            form.render();

            // 取消按钮关闭弹窗 - 防止重复绑定
            $('#btnCancelAddLog').off('click').on('click', function(){
                layer.closeAll('page');
            });

            // 表单提交 - 防止重复绑定
            form.on('submit(submitAddLog)', function(data){
                $.post('/user/ajax.php?act=warehouse_stock_log_add', data.field, function(res){
                    if(res.code === 0){
                        layer.msg('添加成功', {icon: 1});
                        layer.closeAll('page');
                        table.reload('inventoryLogTable', {
                            url: '/user/ajax.php?act=warehouse_stock_log_list&product_id=' + currentProductId,
                            page: {curr: 1}
                        });
                    } else {
                        layer.msg('添加失败: ' + (res.msg || '未知错误'), {icon: 2});
                    }
                }, 'json');
                return false;
            });
        } else if(obj.event === 'edit'){
            // 重置平台SKU数组
            platformSkus = [];
            
            // 解析平台SKU
            if (data.platform_sku) {
                try {
                    var parsed = JSON.parse(data.platform_sku);
                    if (Array.isArray(parsed)) {
                        platformSkus = parsed;
                    } else if (typeof parsed === 'string') {
                        // 兼容旧格式（逗号分隔）
                        platformSkus = parsed.split(',').map(s => s.trim()).filter(s => s);
                    }
                } catch (e) {
                    console.error('解析平台SKU失败', e);
                    // 尝试直接分割
                    platformSkus = data.platform_sku.split(',').map(s => s.trim()).filter(s => s);
                }
            }
            
            // 更新显示
            updatePlatformSkuDisplay();
            updatePlatformSkuHiddenField();
            
            // 填充表单
            form.val('productForm', {
                id: data.id,
                product_type: data.product_type || 'single',
                title: data.title,
                image_url: data.image_url || '',
                size_length: data.size_length,
                size_width: data.size_width,
                size_height: data.size_height,
                weight: data.weight,
                status: data.status,
                sku: data.sku,
                price: data.price,
                remark: data.remark,
                warehouse: data.warehouse,
                warehouse_stock: data.warehouse_stock
            });
            
            // 滚动到表单
            $('html, body').animate({scrollTop: $('.right-panel').offset().top}, 300);

            // 加载库存日志
            currentProductId = data.id;
            table.reload('inventoryLogTable', {
                url: '/user/ajax.php?act=warehouse_stock_log_list&product_id=' + currentProductId,
                page: {curr: 1}
            });
        } else if(obj.event === 'delete'){
            layer.confirm('确定删除该商品吗？', function(index){
                $.post('/user/ajax.php?act=delete', {id: data.id}, function(res){
                    if(res.code === 0){
                        layer.msg('删除成功', {icon: 1});
                        table.reload('productTable');
                    } else {
                        layer.msg('删除失败: ' + (res.msg || '未知错误'), {icon: 2});
                    }
                }, 'json');
                layer.close(index);
            });
        } else if(obj.event === 'match'){
            // 匹配操作 - 查询平台匹配记录
            $.get('/user/ajax.php?act=query_platform_sku&sku_list=' + data.sku, function(res){
                if(res.code === 0 && res.data.length > 0){
                    var matchSkus = res.data.map(item => item.platform_sku).join(', ');
                    layer.msg('匹配成功: ' + matchSkus, {icon: 1, time: 3000});
                    
                    // 自动填充匹配结果到编辑表单
                    if (data.id) {
                        // 如果当前产品在编辑状态，自动填充
                        res.data.forEach(item => {
                            if (item.platform_sku && !platformSkus.includes(item.platform_sku)) {
                                platformSkus.push(item.platform_sku);
                            }
                        });
                        
                        // 更新显示
                        updatePlatformSkuDisplay();
                        updatePlatformSkuHiddenField();
                    }
                } else {
                    layer.msg('未找到匹配记录', {icon: 2});
                }
            }, 'json');
        }
    });

    // 保存产品表单 - 防止重复提交
    form.on('submit(submitProduct)', function(data){
        // 防止重复提交
        if (isSubmitting) {
            layer.msg('正在处理中，请稍候...', {icon: 16, shade: 0.01, time: 1000});
            return false;
        }
        
        isSubmitting = true;
        
        // 显示加载中提示
        var loadIndex = layer.load(2, {shade: 0.1});
        
        // 确保platform_sku是JSON数组格式
        data.field.platform_sku = $('#platformSkuHidden').val();
        
        $.ajax({
            url: '/user/ajax.php?act=save',
            type: 'POST',
            data: data.field,
            dataType: 'json',
            success: function(res) {
                layer.close(loadIndex);
                isSubmitting = false;
                
                if(res.code === 0){
                    layer.msg('保存成功', {icon: 1, time: 1500});
                    table.reload('productTable');
                    
                    // 清空表单
                    form.val('productForm', {
                        id: '',
                        product_type: 'single',
                        title: '',
                        image_url: '',
                        size_length: '',
                        size_width: '',
                        size_height: '',
                        weight: '',
                        status: 'onsale',
                        sku: '',
                        price: '',
                        remark: '',
                        warehouse: '',
                        warehouse_stock: ''
                    });
                    
                    platformSkus = [];
                    updatePlatformSkuDisplay();
                    updatePlatformSkuHiddenField();
                } else {
                    layer.msg('保存失败: ' + (res.msg || '未知错误'), {icon: 2, time: 3000});
                }
            },
            error: function() {
                layer.close(loadIndex);
                isSubmitting = false;
                layer.msg('请求失败，请重试', {icon: 2, time: 2000});
            }
        });
        
        return false;
    });

    // 取消按钮事件 - 防止重复绑定
    $('#btnCancel').off('click').on('click', function(){
        form.val('productForm', {
            id: '',
            product_type: 'single',
            title: '',
            image_url: '',
            size_length: '',
            size_width: '',
            size_height: '',
            weight: '',
            status: 'onsale',
            sku: '',
            price: '',
            remark: '',
            warehouse: '',
            warehouse_stock: ''
        });
        platformSkus = [];
        updatePlatformSkuDisplay();
        updatePlatformSkuHiddenField();
    });

    // 页面加载完成后初始化
    $(document).ready(function() {
        loadPlatformSkusFromHidden();
    });
});
</script>

<script type="text/html" id="operationTpl">
    <a class="layui-btn layui-btn-xs" lay-event="stocklog">出入库</a>
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    <a class="layui-btn layui-btn-xs" lay-event="match">匹配</a>
</script>

<script type="text/html" id="logOperationTpl">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
</script>

</body>
</html>
