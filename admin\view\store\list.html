<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>店铺管理</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>店铺管理</span>
        </div>
        <div class="layui-card-body">
            <!-- 搜索栏 -->
            <div class="layui-form layui-row layui-col-space15">
                <div class="layui-col-md2">
                    <input type="text" id="search-storename" placeholder="店铺名称" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <input type="text" id="search-username" placeholder="用户名" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <input type="text" id="search-uid" placeholder="用户ID" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <input type="text" id="search-phone" placeholder="手机号" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <select id="search-status" class="layui-select">
                        <option value="">所有状态</option>
                        <option value="1">API正常</option>
                        <option value="2">API失效</option>
                        <option value="3">店铺封锁</option>
                        <option value="0">API禁用</option>
                    </select>
                </div>
                <div class="layui-col-md2">
                    <button class="layui-btn" id="search-btn">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button class="layui-btn layui-btn-normal" id="refresh-btn">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
            
            <!-- 数据表格 -->
            <table class="layui-hide" id="store-table" lay-filter="storeTable"></table>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <!-- 表格操作按钮模板 -->
    <script type="text/html" id="toolbar">
        <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
    </script>
    
    <!-- 状态显示模板 -->
    <script type="text/html" id="status-tpl">
        {{# if(d.apistatus == 1) { }}
            <span class="layui-badge layui-bg-green">API正常</span>
        {{# } else if(d.apistatus == 2) { }}
            <span class="layui-badge layui-bg-orange">API失效</span>
        {{# } else if(d.apistatus == 3) { }}
            <span class="layui-badge layui-bg-red">店铺封锁</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray">API禁用</span>
        {{# } }}
    </script>

    <script>
        layui.use(['table', 'layer', 'jquery', 'form'], function() {
            var table = layui.table;
            var layer = layui.layer;
            var $ = layui.jquery;
            var form = layui.form;
            
            // 渲染表格
            var tableIns = table.render({
                elem: '#store-table',
                url: 'ajax.php?act=store_list',
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'storename', title: '店铺名称', width: 180},
                    {field: 'username', title: '所属用户', width: 120},
                    {field: 'uid', title: '用户ID', width: 80},
                    {field: 'phone', title: '手机号', width: 120},
                    {field: 'ClientId', title: 'Client ID', width: 120, templet: function(d) {
                        return d.ClientId ? d.ClientId.toString().substring(0, 8) + '...' : '-';
                    }},
                    {field: 'key', title: 'API Key', width: 120, templet: function(d) {
                        return d.key ? d.key.substring(0, 8) + '...' : '-';
                    }},
                    {field: 'currency_code', title: '货币', width: 80},
                    {field: 'addtime', title: '添加时间', width: 160},
                    {field: 'apistatus', title: '状态', width: 100, templet: '#status-tpl'},
                    {title: '操作', width: 180, toolbar: '#toolbar', align: 'center'}
                ]],
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                loading: true,
                even: true
            });
            
            // 搜索功能
            $('#search-btn').on('click', function() {
                var storename = $('#search-storename').val().trim();
                var username = $('#search-username').val().trim();
                var uid = $('#search-uid').val().trim();
                var phone = $('#search-phone').val().trim();
                var status = $('#search-status').val();
                
                tableIns.reload({
                    where: {
                        storename: storename,
                        username: username,
                        uid: uid,
                        phone: phone,
                        status: status
                    },
                    page: {
                        curr: 1
                    }
                });
            });
            
            // 回车搜索
            $('#search-storename, #search-username, #search-uid, #search-phone').on('keydown', function(e) {
                if (e.keyCode === 13) {
                    $('#search-btn').click();
                }
            });
            
            // 刷新按钮
            $('#refresh-btn').on('click', function() {
                $('#search-storename').val('');
                $('#search-username').val('');
                $('#search-uid').val('');
                $('#search-phone').val('');
                $('#search-status').val('');
                form.render('select');
                tableIns.reload({
                    where: {},
                    page: {
                        curr: 1
                    }
                });
            });
            
            // 监听表格工具条
            table.on('tool(storeTable)', function(obj) {
                var data = obj.data;
                
                if (obj.event === 'view') {
                    var statusText = '';
                    switch(data.apistatus) {
                        case 1: statusText = 'API正常'; break;
                        case 2: statusText = 'API失效'; break;
                        case 3: statusText = '店铺封锁'; break;
                        default: statusText = 'API禁用'; break;
                    }
                    
                    var content = `
                        <div style="padding: 20px;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">店铺ID:</label>
                                <div class="layui-input-inline">${data.id}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">店铺名称:</label>
                                <div class="layui-input-inline">${data.storename || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">所属用户:</label>
                                <div class="layui-input-inline">${data.username || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">用户ID:</label>
                                <div class="layui-input-inline">${data.uid || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">手机号:</label>
                                <div class="layui-input-inline">${data.phone || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Client ID:</label>
                                <div class="layui-input-inline" style="max-width: 300px; word-break: break-all;">${data.ClientId || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">API Key:</label>
                                <div class="layui-input-inline" style="max-width: 300px; word-break: break-all;">${data.key || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">货币:</label>
                                <div class="layui-input-inline">${data.currency_code || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">状态:</label>
                                <div class="layui-input-inline">${statusText}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">添加时间:</label>
                                <div class="layui-input-inline">${data.addtime || '-'}</div>
                            </div>
                        </div>
                    `;
                    
                    layer.open({
                        type: 1,
                        title: '店铺详情 - ' + data.storename,
                        content: content,
                        area: ['600px', '500px'],
                        shadeClose: true
                    });
                } else if (obj.event === 'edit') {
                    // 编辑店铺
                    var editContent = `
                        <form class="layui-form" lay-filter="editForm" style="padding: 20px;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">店铺名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="storename" value="${data.storename || ''}" required lay-verify="required" placeholder="请输入店铺名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Client ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="ClientId" value="${data.ClientId || ''}" required lay-verify="required" placeholder="请输入Client ID" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">API Key</label>
                                <div class="layui-input-block">
                                    <input type="text" name="key" value="${data.key || ''}" required lay-verify="required" placeholder="请输入API Key" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">货币</label>
                                <div class="layui-input-block">
                                    <select name="currency_code" lay-verify="required">
                                        <option value="">请选择货币</option>
                                        <option value="RUB" ${data.currency_code === 'RUB' ? 'selected' : ''}>RUB 卢布</option>
                                        <option value="USD" ${data.currency_code === 'USD' ? 'selected' : ''}>USD 美元</option>
                                        <option value="CNY" ${data.currency_code === 'CNY' ? 'selected' : ''}>CNY 人民币</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">API状态</label>
                                <div class="layui-input-block">
                                    <select name="apistatus" lay-verify="required">
                                        <option value="0" ${data.apistatus == 0 ? 'selected' : ''}>API禁用</option>
                                        <option value="1" ${data.apistatus == 1 ? 'selected' : ''}>API正常</option>
                                        <option value="2" ${data.apistatus == 2 ? 'selected' : ''}>API失效</option>
                                        <option value="3" ${data.apistatus == 3 ? 'selected' : ''}>店铺封锁</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="editSubmit">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    `;
                    
                    layer.open({
                        type: 1,
                        title: '编辑店铺 - ' + data.storename,
                        content: editContent,
                        area: ['500px', '450px'],
                        shadeClose: false,
                        success: function(layero, index) {
                            form.render();
                            
                            // 监听提交
                            form.on('submit(editSubmit)', function(formData) {
                                var loadIndex = layer.load(2, {shade: [0.3, '#fff']});
                                
                                $.ajax({
                                    url: 'ajax.php?act=store_edit',
                                    type: 'POST',
                                    data: $.extend({id: data.id}, formData.field),
                                    dataType: 'json',
                                    success: function(res) {
                                        layer.close(loadIndex);
                                        if (res.code === 0) {
                                            layer.msg('编辑成功', {icon: 1});
                                            layer.close(index);
                                            tableIns.reload();
                                        } else {
                                            layer.msg(res.msg || '编辑失败', {icon: 2});
                                        }
                                    },
                                    error: function() {
                                        layer.close(loadIndex);
                                        layer.msg('网络错误', {icon: 2});
                                    }
                                });
                                return false;
                            });
                        }
                    });
                } else if (obj.event === 'delete') {
                    // 删除店铺
                    layer.confirm('确定要删除店铺 "' + data.storename + '" 吗？删除后无法恢复！', {
                        icon: 3,
                        title: '删除确认'
                    }, function(index) {
                        var loadIndex = layer.load(2, {shade: [0.3, '#fff']});
                        
                        $.ajax({
                            url: 'ajax.php?act=store_delete',
                            type: 'POST',
                            data: {id: data.id},
                            dataType: 'json',
                            success: function(res) {
                                layer.close(loadIndex);
                                if (res.code === 0) {
                                    layer.msg('删除成功', {icon: 1});
                                    layer.close(index);
                                    tableIns.reload();
                                } else {
                                    layer.msg(res.msg || '删除失败', {icon: 2});
                                }
                            },
                            error: function() {
                                layer.close(loadIndex);
                                layer.msg('网络错误', {icon: 2});
                            }
                        });
                    });
                }
            });
        });
    </script>
</body>
</html> 