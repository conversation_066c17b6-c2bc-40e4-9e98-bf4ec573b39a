<?php
if (function_exists("set_time_limit")) {
    @set_time_limit(0);
}
if (function_exists("ignore_user_abort")) {
    @ignore_user_abort(true);
}
$proxies = [
    [
        'ip' => '************',
        'port' => 3128,
        'type' => 'HTTP',
        'anonymity' => 'Transparent',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '**************',
        'port' => 4145,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '**************',
        'port' => 1080,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '************',
        'port' => 3629,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '***************',
        'port' => 9999,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '*************',
        'port' => 4153,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '*************',
        'port' => 3128,
        'type' => 'HTTPS',
        'anonymity' => 'Elite',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '************',
        'port' => 3629,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '*************',
        'port' => 4145,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '***********',
        'port' => 3629,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '**************',
        'port' => 32231,
        'type' => 'HTTP',
        'anonymity' => 'Elite',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '**************',
        'port' => 4145,
        'type' => 'SOCKS4',
        'anonymity' => 'Anonymous',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ],
    [
        'ip' => '*************',
        'port' => 3128,
        'type' => 'HTTP',
        'anonymity' => 'Transparent',
        'country' => 'Россия',
        'unknown' => 'Unknown'
    ]
];

function check_proxy($proxy) {
    $ch = curl_init();
    
    // 代理类型映射
    $proxy_type = CURLPROXY_HTTP;
    $tunnel = false;
    switch ($proxy['type']) {
        case 'HTTPS':
            $tunnel = true;
            break;
        case 'SOCKS4':
            $proxy_type = CURLPROXY_SOCKS4;
            break;
        case 'SOCKS5':
            $proxy_type = CURLPROXY_SOCKS5;
            break;
    }

    curl_setopt_array($ch, [
        CURLOPT_URL => 'https://httpbin.org/ip',
        CURLOPT_PROXY => $proxy['ip'],
        CURLOPT_PROXYPORT => $proxy['port'],
        CURLOPT_PROXYTYPE => $proxy_type,
        CURLOPT_HTTPPROXYTUNNEL => $tunnel,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_CONNECTTIMEOUT => 8,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0
    ]);

    $response = curl_exec($ch);
    $errno = curl_errno($ch);
    $error = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    curl_close($ch);

    // 严格验证（检查返回内容是否包含代理IP）
    $isValid = ($errno === 0) && 
              ($httpCode === 200) && 
              (strpos($response, $proxy['ip']) !== false);

    return [
        'status' => $isValid,
        'code' => $errno ? "cURL $errno: $error" : "HTTP $httpCode"
    ];
}

$results = [];
$total = count($proxies);
$current = 0;

foreach ($proxies as $proxy) {
    $current++;
    echo "检测进度：{$current}/{$total} - {$proxy['ip']}:{$proxy['port']}...";
    
    $result = check_proxy($proxy);
    
    if ($result['status']) {
        echo "可用 [HTTP {$result['code']}]</br>";
        $results[] = "{$proxy['ip']}:{$proxy['port']}";
    } else {
        echo "不可用 [错误码: {$result['code']}]</br>";
    }
}

// 输出结果
echo "\n===== 可用代理列表 =====\n";
echo implode("</br>", $results);