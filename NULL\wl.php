<?php
include("../includes/common.php");
// 使用示例
$criteria = [
    'provider' => 'GUOO',
    'method' => 'Extra Small',
    'speed' => 'Economy'
];
//$result = ;

//exit(json_encode($criteria));
// 使用示例
$shippingOption = [
    'basePrice' => 36,
    'perGramPrice' => 0.017
];
$weight = 2480; // 3000克

$cost = calculateShippingCost(findShippingMethod($criteria), $weight);

if ($cost !== null) {
    echo "運費計算結果: " . number_format($cost, 2) . " RUB";
} else {
    echo "無法計算運費，缺少必要參數";
}
exit(json_encode($result));
$list = $DB->getAll("SELECT * FROM ozon_order WHERE tracking_number!='' ORDER BY in_process_at DESC limit 1000");
foreach ($list as $itme){
    echo "</br>";
    echo $itme['tpl_provider']."</br>";
    echo "物流单号：{$itme['tracking_number']}</br>";
    $exp = explode(" ",$itme['tpl_provider']);
    echo "物流服务商：{$exp[0]}</br>";
    if($exp[1]=='Express'){
        $speed = "空运";
    }elseif ($exp[1]=='Standard') {
        $speed = "陆空";
    }elseif ($exp[1]=='Economy') {
        $speed = "陆运";
    }
    echo "运输方式：{$speed}</br>";
}