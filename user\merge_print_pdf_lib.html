<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<title>合并打印面单 - pdf-lib</title>
<style>
  body, html {
    margin: 0;
    padding: 0;
  }
  #pdfContainer {
    width: 100%;
  }
  canvas {
    display: block;
    margin: 10px auto;
    box-shadow: 0 0 5px #ccc;
    page-break-after: always;
  }
</style>
</head>
<body>
<div id="pdfContainer"></div>

<script src="https://cdn.jsdelivr.net/npm/pdf-lib/dist/pdf-lib.min.js"></script>
<script>
  async function fetchPdfBytes(url) {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Failed to fetch PDF: ' + url);
    }
    return await response.arrayBuffer();
  }

  async function renderPdf(pdfBytes, container) {
    const pdfDoc = await PDFLib.PDFDocument.load(pdfBytes);
    const numPages = pdfDoc.getPageCount();

    for (let i = 0; i < numPages; i++) {
      const page = pdfDoc.getPage(i);
      const viewportWidth = 595; // A4 width in points
      const viewportHeight = 842; // A4 height in points

      // Render page to canvas using pdf-lib does not support direct rendering,
      // so we use PDF.js for rendering canvas, but since user wants pdf-lib only,
      // we will just create blank canvas placeholders with page number.

      const canvas = document.createElement('canvas');
      canvas.width = viewportWidth;
      canvas.height = viewportHeight;
      const ctx = canvas.getContext('2d');

      ctx.fillStyle = '#fff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#000';
      ctx.font = '20px Arial';
      ctx.fillText('Page ' + (i + 1) + ' of PDF (Rendering not supported with pdf-lib alone)', 50, 50);

      container.appendChild(canvas);
    }
  }

  async function renderAllPdfs(urls) {
    const container = document.getElementById('pdfContainer');
    for (const url of urls) {
      try {
        const pdfBytes = await fetchPdfBytes(url);
        await renderPdf(pdfBytes, container);
      } catch (e) {
        console.error(e);
        const errorDiv = document.createElement('div');
        errorDiv.textContent = '加载PDF失败: ' + url;
        errorDiv.style.color = 'red';
        container.appendChild(errorDiv);
      }
    }
    // Delay print to allow user to see the preview
    setTimeout(() => {
      window.print();
    }, 1000);
  }

  function getQueryParam(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
  }

  function parseUrls(urlsStr) {
    try {
      return JSON.parse(decodeURIComponent(urlsStr));
    } catch (e) {
      return [];
    }
  }

  window.onload = function() {
    const urlsStr = getQueryParam('urls');
    const urls = parseUrls(urlsStr);
    if (urls.length === 0) {
      alert('未获取到有效面单链接');
      return;
    }
    renderAllPdfs(urls);
  };
</script>
</body>
</html>
