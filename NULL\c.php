<?php
class ImageUploader {
    private $cookieFile;
    private $maxRetry = 3;
    private $retryDelay = 2; // seconds

    public function __construct() {
        $this->cookieFile = tempnam(sys_get_temp_dir(), 'cookies_');
    }

    public function __destruct() {
        if (file_exists($this->cookieFile)) {
            unlink($this->cookieFile);
        }
    }

    /**
     * 获取图片数据
     */
    public function fetchImageData($imgUrl) {
        try {
            $ch = curl_init($imgUrl);
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_HEADER         => false
            ]);
            
            $data = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if ($status !== 200 || curl_errno($ch)) {
                throw new Exception("Failed to fetch image: HTTP $status");
            }
            
            curl_close($ch);
            return [
                'success' => true,
                'data'    => $data
            ];
        } catch (Exception $e) {
            error_log("Image fetch error: " . $e->getMessage());
            return [
                'success' => false,
                'error'   => $e->getMessage()
            ];
        }
    }

    /**
     * 文件上传方法
     */
    public function uploadFile($fileData, $fileName = 'image.jpg') {
        $retryCount = 0;
        $antiContent = $this->getAntiContent(); // 获取 anti-content
        
        while ($retryCount < $this->maxRetry) {
            try {
                $boundary = '----' . md5(microtime());
                $payload = $this->buildMultipartBody([
                    'file' => [
                        'content' => $fileData,
                        'name'    => $fileName
                    ]
                ], $boundary);

                $ch = curl_init('https://pifa.pinduoduo.com/mille/slow/upload/uploadSearchImage');
                curl_setopt_array($ch, [
                    CURLOPT_POST            => true,
                    CURLOPT_RETURNTRANSFER  => true,
                    CURLOPT_HTTPHEADER      => [
                        'Content-Type: multipart/form-data; boundary=' . $boundary,
                        'Anti-Content: ' . $antiContent,
                        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                        'cookies: '.'_nano_fp=XpmYnqXJXpTbnqTxno_VTLbLYXYBeIdzLhhQ2fFB; webp=true; api_uid=ChB6Gmgm7PtbbgBiqL05Ag==; VISITOR_PASS_ID=eXHl-ph4PcOiveeZGtdya4OyvSOLBwEijHJrX8Smp0A9pmt4XEiT8NFc5D-3lO-TuWg4GFhmDJchMf_W58a4Tt2JljtoZt9K2vMKhzgd_ts_102c605de92; rckk=LAcqxFRqCEe2cV5hjM63vReo6cLeweFB; _bee=LAcqxFRqCEe2cV5hjM63vReo6cLeweFB; ru1k=9e7856f1-95e6-4248-a88a-c595ea4e6a05; _f77=9e7856f1-95e6-4248-a88a-c595ea4e6a05; ru2k=a75bb84f-4127-468a-9a7c-9eb0412a9ba6; _a42=a75bb84f-4127-468a-9a7c-9eb0412a9ba6; SUB_PASS_ID=eyJ0IjoiZUc0N2ZBMGl5WXNtOHFTMlFRZTZLUTRsZDRsbUJTNC9BTTBXY0FMZ0VxbkRQaVQ5V1d2aDZDNURPT1kvN3U4cyIsInYiOjEsInMiOjE0LCJtIjo5Nzc1MjY2NDksInUiOjUxMTg4Nzk1fQ',
                    ],
                    CURLOPT_COOKIEFILE      => $this->cookieFile,
                    CURLOPT_COOKIEJAR       => $this->cookieFile,
                    CURLOPT_POSTFIELDS      => $payload
                ]);

                $response = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                
                if ($status === 429) {
                    throw new Exception("Rate limit exceeded");
                }

                if ($status >= 400) {
                    throw new Exception("Upload failed: HTTP $status");
                }

                return [
                    'success' => true,
                    'status'  => $status,
                    'data'    => json_decode($response, true)
                ];
            } catch (Exception $e) {
                $retryCount++;
                error_log("Upload attempt $retryCount failed: " . $e->getMessage());
                sleep($this->retryDelay * $retryCount);
            }
        }

        return [
            'success'    => false,
            'error'      => 'Max retries exceeded',
            'retryCount' => $retryCount
        ];
    }

    /**
     * 构建 multipart 请求体
     */
    private function buildMultipartBody($fields, $boundary) {
        $body = '';
        foreach ($fields as $name => $field) {
            $body .= "--$boundary\r\n";
            $body .= "Content-Disposition: form-data; name=\"$name\"; filename=\"{$field['name']}\"\r\n";
            $body .= "Content-Type: application/octet-stream\r\n\r\n";
            $body .= $field['content'] . "\r\n";
        }
        $body .= "--$boundary--\r\n";
        return $body;
    }

    /**
     * 获取 anti-content (示例实现)
     */
    private function getAntiContent() {
        // 这里需要实现实际的 anti-content 生成逻辑
        $req = file_get_contents("http://*************:3003/api/0ar");
        $json = json_decode($req,true);
        return $json['anti_content'];
    }
}

// 使用示例
$uploader = new ImageUploader();

// 获取图片
$imageResult = $uploader->fetchImageData('https://ir-2.ozonstatic.cn/s3/multimedia-1-o/wc1000/7543410648.jpg');
if (!$imageResult['success']) {
    die("图片获取失败: " . $imageResult['error']);
}

// 上传文件
$uploadResult = $uploader->uploadFile($imageResult['data']);
if ($uploadResult['success']) {
    echo "上传成功: ", print_r($uploadResult['data'], true);
} else {
    echo "上传失败: {$uploadResult['error']} (重试次数: {$uploadResult['retryCount']})";
}
?>
