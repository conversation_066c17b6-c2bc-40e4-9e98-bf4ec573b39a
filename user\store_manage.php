<!-- 添加多选下拉样式 -->
    <style>
    .multiselect-dropdown {
      position: relative;
      display: inline-block;
      width: 100%;
    }
    .multiselect-dropdown-list {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 1000;
      display: none;
      width: 280px;
      max-height: 300px;
      overflow-y: auto;
      background-color: #fff;
      border: 1px solid #d2d2d2;
      border-radius: 3px;
      box-shadow: 0 3px 8px rgba(0,0,0,0.15);
      padding: 5px 0;
      margin-top: 2px;
    }
    .multiselect-dropdown-list.active {
      display: block;
      animation: fadeIn 0.15s ease-in-out;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-5px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .multiselect-option {
      padding: 7px 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
      user-select: none;
      transition: background-color 0.15s;
    }
    .multiselect-option:hover {
      background-color: #f8f8f8;
    }
    .multiselect-option:active {
      background-color: #f2f2f2;
    }
    .multiselect-option input[type="checkbox"] {
      margin-right: 8px;
      cursor: pointer;
    }
    .multiselect-option label {
      cursor: pointer;
      display: block;
      width: 100%;
      font-weight: normal;
    }
    .multiselect-search {
      width: calc(100% - 20px);
      padding: 8px 10px;
      margin: 5px 10px 8px;
      border: 1px solid #e2e2e2;
      border-radius: 3px;
      box-sizing: border-box;
      font-size: 13px;
      transition: border-color 0.15s;
      background-color: #fcfcfc;
    }
    .multiselect-search:focus {
      border-color: #1E9FFF;
      background-color: #fff;
      outline: none;
      box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.08);
    }
    .multiselect-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      margin-top: 5px;
      min-height: 22px;
    }
    .multiselect-tag {
      background-color: #1E9FFF;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      display: inline-flex;
      align-items: center;
      font-size: 12px;
      margin-bottom: 3px;
      line-height: 1.5;
      box-shadow: 0 1px 2px rgba(0,0,0,0.1);
      transition: all 0.15s ease;
    }
    .multiselect-tag:hover {
      background-color: #1a90eb;
    }
    .multiselect-tag.green {
      background-color: #5FB878;
    }
    .multiselect-tag.green:hover {
      background-color: #52a96b;
    }
    .multiselect-tag i {
      margin-left: 5px;
      font-size: 12px;
      cursor: pointer;
      opacity: 0.8;
      transition: all 0.15s ease;
    }
    .multiselect-tag i:hover {
      opacity: 1;
      transform: scale(1.1);
    }
    .multiselect-selected {
      height: 36px;
      line-height: 34px;
      padding: 0 12px;
      border: 1px solid #e2e2e2;
      border-radius: 3px;
      cursor: pointer;
      background-color: #fff;
      width: 100%;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      transition: all 0.15s ease;
      box-sizing: border-box;
      font-size: 13px;
      color: #666;
    }
    .multiselect-selected:hover {
      border-color: #c2c2c2;
    }
    .multiselect-selected:after {
      content: "\e625";
      font-family: layui-icon !important;
      font-size: 12px;
      margin-left: 5px;
      float: right;
      color: #999;
      transition: transform 0.2s ease;
    }
    .multiselect-dropdown-list.active + .multiselect-selected:after {
      transform: rotate(180deg);
    }
    </style>
<div style="padding: 16px;">
  <div class="layui-card">
    <div class="layui-card-header">
      <div class="layui-btn-group">
        <button type="button" class="layui-btn layui-btn-primary" id="btnAddStore">
          <i class="layui-icon layui-icon-add-1"></i> 新增店铺
        </button>
        <button type="button" class="layui-btn layui-btn-primary" id="btnManageGroups">
          <i class="layui-icon layui-icon-form"></i> 管理分组
        </button>
        <button type="button" class="layui-btn layui-btn-primary" id="btnBatchGroup">
          <i class="layui-icon layui-icon-group"></i> 批量分组
        </button>
        <button type="button" class="layui-btn layui-btn-primary" id="btnBatchUpdateWarehouse">
          <i class="layui-icon layui-icon-refresh-3"></i> 批量更新仓库
        </button>
      </div>
    </div>
    
    <!-- 添加筛选区域 -->
    <div class="layui-card-body" style="padding-bottom: 0;">
      <div class="layui-form" id="filterForm">
        <div class="layui-form-item">
          <div class="layui-inline">
            <label class="layui-form-label">店铺筛选：</label>
            <div class="layui-input-inline" style="width: 200px;">
              <div id="storeSelect"></div>
              <div id="selectedStores" class="multiselect-tags" style="display:none;"></div>
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">分组筛选：</label>
            <div class="layui-input-inline" style="width: 200px;">
              <div id="groupSelect"></div>
              <div id="selectedGroups" class="multiselect-tags" style="display:none;"></div>
            </div>
          </div>
          <div class="layui-inline" style="margin-left: 5px;">
            <button type="button" class="layui-btn layui-btn-sm" id="btnFilter"><i class="layui-icon layui-icon-search"></i> 筛选</button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="btnResetFilter"><i class="layui-icon layui-icon-refresh"></i> 重置</button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="layui-card-body">
      <table class="layui-hide" id="storetable" lay-filter="storeTableFilter"></table>
    </div>
  </div>
</div>

<!-- 表格工具栏模板 -->
<script type="text/html" id="toolbarTpl">
  <div class="layui-btn-group">
    <a class="layui-btn layui-btn-xs" lay-event="upwarehouseedit">更新仓库</a>
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
  </div>
</script>

<!-- 不再需要筛选弹窗模板 -->

<!-- 分组管理弹窗模板 - 优化版 -->
<script type="text/html" id="groupManagerTpl">
  <div class="layui-fluid" style="padding: 20px;">
    <div class="layui-row layui-col-space15">
      <!-- 左侧分组列表区域 -->
      <div class="layui-col-md5">
        <div class="layui-card" style="box-shadow: none;">
          <div class="layui-card-header">
            <span><i class="layui-icon layui-icon-group"></i> 分组列表</span>
            <div class="layui-inline" style="float: right;">
              <button class="layui-btn layui-btn-xs layui-btn-normal" id="btnAddGroup"><i class="layui-icon layui-icon-add-1"></i> 新增分组</button>
              <button class="layui-btn layui-btn-xs" id="btnRefreshGroups"><i class="layui-icon layui-icon-refresh"></i></button>
            </div>
          </div>
          <div class="layui-card-body">
            <div class="layui-form-item">
              <div class="layui-input-block" style="margin-left: 0;">
                <input type="text" id="groupSearch" placeholder="搜索分组名称" class="layui-input">
              </div>
            </div>
            
            <!-- 分组表格容器 -->
            <div style="margin-top: 10px; border: 1px solid #eee; border-radius: 2px; padding: 0px; max-height: 480px; overflow: hidden;">
              <table class="layui-hide" id="groupTable" lay-filter="groupTableFilter"></table>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧编辑区域 -->
      <div class="layui-col-md7">
        <div class="layui-card" style="box-shadow: none;">
          <div class="layui-card-header">
            <span><i class="layui-icon layui-icon-edit"></i> 分组详情</span>
            <div class="layui-badge layui-bg-blue" id="selectedCountBadge" style="display: none; margin-left: 10px;"></div>
          </div>
          <div class="layui-card-body">
            <form class="layui-form" id="groupForm">
              <input type="hidden" name="groupId" id="editGroupId">
              <div class="layui-form-item">
                <label class="layui-form-label">分组名称</label>
                <div class="layui-input-block">
                  <input type="text" name="groupName" required lay-verify="required" placeholder="请输入分组名称" autocomplete="off" class="layui-input">
                </div>
              </div>
              
              <div class="layui-form-item">
                <div class="layui-inline" style="margin-bottom: 0;">
                  <label class="layui-form-label">包含店铺</label>
                  <div class="layui-input-inline" style="width: 200px;">
                    <input type="text" id="storeSearch" placeholder="搜索店铺名称或ClientID" class="layui-input">
                  </div>
                  <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm" id="btnSearchStores">搜索</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary" id="btnToggleSelection">反选</button>
                  </div>
                </div>
              </div>
              
              <!-- 店铺表格容器 -->
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <div style="border: 1px solid #eee; border-radius: 2px; max-height: 320px; overflow: hidden;">
                    <table class="layui-hide" id="storeSelectTable"></table>
                  </div>
                </div>
              </div>
              
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <div class="layui-btn-group">
                    <button class="layui-btn" lay-submit lay-filter="groupFormSubmit"><i class="layui-icon layui-icon-ok"></i> 保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="btnCancelEdit"><i class="layui-icon layui-icon-close"></i> 取消</button>
                    <button type="button" class="layui-btn layui-btn-normal" id="btnSetDefault"><i class="layui-icon layui-icon-star"></i> 设为默认</button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</script>

<!-- 分组操作列模板 -->
<script type="text/html" id="groupActionTpl">
  <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="select">编辑</a>
  <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
</script>

<!-- 批量分组弹窗 -->
<script type="text/html" id="batchGroupTpl">
  <div class="layui-form" style="padding: 20px;">
    <div class="layui-form-item">
      <label class="layui-form-label">选择分组</label>
      <div class="layui-input-block">
        <select name="batchGroupId" lay-verify="required" lay-search>
          <option value="">请选择分组</option>
          {{# layui.each(d.groups, function(index, group){ }}
          <option value="{{ group.id }}">{{ group.name }}{{ group.isDefault ? ' (默认)' : '' }}</option>
          {{# }); }}
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <div class="layui-input-block">
        <button class="layui-btn" lay-submit lay-filter="batchGroupSubmit">确认</button>
        <button type="button" class="layui-btn layui-btn-primary" id="btnBatchCancel">取消</button>
      </div>
    </div>
    <button type="button" class="layui-btn layui-btn-danger" id="btnDeleteGroup" style="display: none;"><i class="layui-icon layui-icon-delete"></i> 删除分组</button>
  </div>
</script>

<script>
layui.use(['table', 'form', 'layer', 'dropdown', 'jquery', 'laytpl'], function () {
  var table = layui.table;
  var form = layui.form;
  var layer = layui.layer;
  var $ = layui.jquery;
  var laytpl = layui.laytpl;
  
  // 当前选中的分组ID
  var currentGroupId = null;
  // 所有店铺数据
  var allStores = [];
  // 所有分组数据
  var allGroups = [];
  // 默认分组ID
  var defaultGroupId = null;
  
  // 分组表格实例
  var groupTable = null;
  // 店铺选择表格实例
  var storeSelectTable = null;
  // 当前显示的店铺数据（用于搜索）
  var currentDisplayStores = [];
  
  // 用于存储批量分组时选择的店铺
  var batchSelectedStores = [];
  // 用于存储跨页选择的店铺ID
  var allSelectedStoreIds = [];

  // 全局变量：存储分组管理页面中当前分组选中的店铺ID（解决翻页问题）
  var globalSelectedStoreIds = [];
  
  // 筛选用的变量
  var selectedStoreIds = [];
  var selectedGroupIds = [];
  var shopGroups = [];
  var defaultGroupId = null;

  // 初始化函数
  function init() {
    // 显示加载中
    var loadingIndex = layer.load(2);
    
    // 获取formSelects实例
    var formSelects = layui.formSelects;
    
    // 加载店铺数据
    $.ajax({
      url: 'ajax.php?act=store_List',
      type: 'GET',
      dataType: 'json',
      success: function(res) {
        if (res.code === 0) {
          // 获取店铺列表
          if (res.data && res.data.length > 0) {
            allStores = res.data.map(function(item) {
              return {
                name: item.storename,
                value: item.id
              };
            });
            
            // 加载店铺下拉选项
            formSelects.data('store_select', 'local', {
              arr: allStores
            });
          } else {
            allStores = [];
          }
          
          // 加载分组数据
          $.ajax({
            url: 'ajax.php?act=getShopGroups',
            type: 'GET',
            dataType: 'json',
            success: function(res) {
              if (res.code === 0) {
                // 处理分组数据
                allGroups = res.data || [];
                
                // 转换为下拉选项格式
                var groupOptions = [];
                $.each(allGroups, function(i, group) {
                  groupOptions.push({
                    name: group.name + (group.isDefault ? ' (默认)' : ''),
                    value: group.id
                  });
                  
                  // 记录默认分组
                  if (group.isDefault) {
                    defaultGroupId = group.id;
                  }
                });
                
                // 加载分组下拉选项
                formSelects.data('group_select', 'local', {
                  arr: groupOptions
                });
              } else {
                console.error("加载分组失败:", res);
                allGroups = [];
              }
              
              // 关闭加载中
              layer.close(loadingIndex);
            },
            error: function() {
              console.error("加载分组请求错误");
              allGroups = [];
              layer.close(loadingIndex);
            }
          });
        } else {
          console.error("加载店铺失败:", res);
          allStores = [];
          allGroups = [];
          layer.close(loadingIndex);
        }
      },
      error: function() {
        console.error("加载店铺请求错误");
        allStores = [];
        allGroups = [];
        layer.close(loadingIndex);
      }
    });
  }
  
  // 筛选表格
  function filterTable() {
    // 显示加载中
    var loadingIndex = layer.load(2);
    
    var where = {};
    
    if (selectedStoreIds.length > 0) {
      where.store_ids = selectedStoreIds.join(',');
    }
    
    if (selectedGroupIds.length > 0) {
      where.group_ids = selectedGroupIds.join(',');
    }
    
    // 调试输出
    console.log("筛选条件:", where);
    
    // 重载表格
    table.reload('storeTableId', {
      url: 'ajax.php?act=store_List',
      where: where,
      page: {
        curr: 1 // 重新从第 1 页开始
      },
      done: function(res) {
        // 关闭加载中
        layer.close(loadingIndex);
        
        // 记录返回结果
        console.log("筛选结果:", res);
        
        // 显示筛选结果信息
        if (res.count === 0 || !res.data || res.data.length === 0) {
          if (selectedStoreIds.length > 0 || selectedGroupIds.length > 0) {
            layer.msg('没有找到符合条件的数据', {icon: 0});
          }
        }
      }
    });
  }

  // 初始化表格 - 修复：添加跨页选择支持
  function initTable() {
    table.render({
      elem: '#storetable',
      url: 'ajax.php?act=store_List',
      toolbar: true,
      defaultToolbar: ['filter', 'exports', 'print', {
        title: '提示',
        layEvent: 'LAYTABLE_TIPS',
        icon: 'layui-icon-tips'
      }],
      height: 'full-200', // 调整高度，为筛选区域留出空间
      cellMinWidth: 80,
      page: true,
      limit: 50,  // 默认每页显示50条
      limits: [20, 50, 100, 200],
      id: 'storeTableId', // 设置表格ID用于跨页选择
      cols: [[
        { type: 'checkbox', fixed: 'left' },
        { field: 'ClientId', title: 'ClientId', width: 120, sort: true, fixed: 'left' },
        { field: 'storename', title: '店铺名称', width: 150 },
        
         { 
          field: 'apistatus',
          title: '店铺状态', 
          width: 120,
          templet: function(d) {
            var storeStatus = d.apistatus || d.status || 'unknown';
            var statusClass = '';
            var statusText = '';
            var statusIcon = '';
            
            switch(storeStatus) {
              case 1:
                statusClass = 'layui-bg-green';
                statusText = '正常营业';
                statusIcon = 'layui-icon-ok-circle';
                break;
              case 2:
                statusClass = 'layui-bg-red';
                statusText = 'API异常';
                statusIcon = 'layui-icon-close-fill';
                break;
              case 3:
                statusClass = 'layui-bg-red';
                statusText = '公司已封锁';
                statusIcon = 'layui-icon-close-fill';
                break;
              case 2:
                statusClass = 'layui-bg-orange';
                statusText = '审核中';
                statusIcon = 'layui-icon-time';
                break;
              case 'maintenance':
              case '3':
              case 3:
                statusClass = 'layui-bg-blue';
                statusText = '维护中';
                statusIcon = 'layui-icon-set-fill';
                break;
              default:
                statusClass = 'layui-bg-gray';
                statusText = '未知';
                statusIcon = 'layui-icon-help';
            }
            
            return '<span class="layui-badge ' + statusClass + '" style="padding:4px 8px;font-size:12px;">' +
              '<i class="layui-icon ' + statusIcon + '" style="font-size:12px;margin-right:3px;"></i>' + statusText +
              '</span>';
          }
        },
        
        { field: 'currency_code', title: '币种', width: 80 },
        {
          field: 'daily_create',
          title: '每日限额(上传更新)',
          width: 280,
          templet: function (d) {
            return '总限额: ' + (d.limit || '0') + ' / 添: ' + (d.daily_create || '0') + ' / 改: ' + (d.daily_update || '0');
          }
        },
        {
          field: 'actions',
          title: '自动关闭活动',
          width: 130,
          templet: function (d) {
            return '<input type="checkbox" name="auto_close" lay-skin="switch" ' +
              (d.actions == 1 ? 'checked' : '') +
              ' lay-filter="auto_close_switch" data-id="' + d.id + '">';
          }
        },
        { 
          field: 'Groupname',
          title: '分组', 
          width: 150,
          templet: function(d) {
            return d.Groupname || '<span class="layui-badge layui-bg-gray">未分组</span>';
          }
        },
        {
          field: 'notice',
             title: '通知链接状态',
          width: 200,
          templet: function (d) {
            var apiStatus = d.apistatus || 'unknown';
            var statusClass = '';
            var statusText = '';
            
            switch(apiStatus) {
              case 'active':
              case '1':
              case 1:
                statusClass = 'layui-bg-green';
                statusText = '正常';
                break;
              case 'inactive':
              case 'error':
              case '0':
              case 0:
                statusClass = 'layui-bg-red';
                statusText = '异常';
                break;
              case 'warning':
              case '2':
              case 2:
                statusClass = 'layui-bg-orange';
                statusText = '警告';
                break;
              default:
                statusClass = 'layui-bg-gray';
                statusText = '未知';
            }
            
            return '<div style="display:flex;align-items:center;gap:8px;flex-wrap:wrap;">' +
              '<a class="layui-btn layui-btn-xs copy-notice" style="background-color:#1E9FFF;color:white;flex-shrink:0;" data-url="https://www.100b.cn/api/notice">' +
              '<i class="layui-icon layui-icon-link"></i> 复制</a>' +
              '<span class="layui-badge ' + statusClass + '" style="font-size:11px;padding:3px 8px;flex-shrink:0;">' +
              '<i class="layui-icon layui-icon-circle-dot" style="font-size:10px;margin-right:2px;"></i>' + statusText +
              '</span>' +
              '</div>';
          }
        },
        { field: 'cost', title: '采购总花销', width: 120 },
        { field: 'price', title: '总销售金额', width: 120 },
        {
          field: 'addtime',
          title: '店铺添加时间',
          width: 180,
          templet: function (d) {
            return d.addtime + (d.time ? '<br/>' + d.time : '');
          }
        },
        { fixed: 'right', title: '操作', toolbar: '#toolbarTpl', width: 220 }
      ]],
      done: function(res) {
        // 修复：正确处理后端返回的数据格式
        if (res && res.code === 0 && res.data) {
          allStores = res.data.map(function(item) {
            return {
              id: item.id,
              storename: item.storename
            };
          });
          
          // 更新筛选器显示
          updateSelectedStoresDisplay();
          updateSelectedGroupsDisplay();
        }
        
        // 渲染表单组件
        form.render('checkbox');
        form.render('switch');
        
        // 处理无数据情况
        if (res.count === 0) {
          // 检查是否有筛选条件
          if (selectedStoreIds.length > 0 || selectedGroupIds.length > 0) {
            layer.msg('没有找到符合筛选条件的数据', {icon: 5});
          }
        }
      },
      error: function() {
        layer.msg('加载数据失败，请稍后重试', {icon: 2});
      }
    });
    
    // 修复：监听复选框选择事件，支持跨页选择
    table.on('checkbox(storeTableFilter)', function(obj){
      var checkStatus = table.checkStatus('storeTableId');
      allSelectedStoreIds = checkStatus.data.map(function(item) { return item.id; });
    });
    
    // 监听工具条事件
    table.on('tool(storeTableFilter)', function(obj) {
      var data = obj.data;
      
      switch(obj.event) {
        case 'edit':
          openStoreForm(data);
          break;
          
        case 'upwarehouseedit':
          layer.load(2);
          $.post('ajax.php?act=store_upwarehouseedit', { id: data.id }, function(res) {
            layer.closeAll('loading');
            if (res && res.code == 0) {
              layer.msg(res.msg || '更新成功');
              table.reload('storetable');
            } else {
              layer.msg(res.msg || '更新失败: ' + (res.data ? res.data.error : ''));
            }
          }, 'json');
          break;
          
        case 'delete':
          layer.confirm('确定删除该店铺吗？', function(index) {
            layer.load(2);
            $.post('ajax.php?act=store_delete', { id: data.id }, function(res) {
              layer.closeAll();
              // 统一处理响应格式
              if (res && res.code == 1) {
                layer.msg(res.msg || '删除成功');
                obj.del();
              } else {
                layer.msg(res.msg || '删除失败: ' + (res.data ? res.data.error : ''));
              }
            }, 'json');
          });
          break;
      }
    });
  }

  // 初始化分组管理
  function initGroupManager() {
    // 加载分组数据
    loadGroups();
    
    // 绑定事件
    $('#btnRefreshGroups').on('click', loadGroups);
    $('#btnAddGroup').on('click', function() {
      // 打开一个简单的弹窗输入分组名称
      layer.prompt({
        formType: 0,
        title: '新增分组',
        area: ['350px', '180px'],
        btn: ['保存', '取消'],
        placeholder: '请输入分组名称'
      }, function(value, index, elem){
        // 直接创建新分组
        if (!value.trim()) {
          layer.msg('分组名称不能为空', {icon: 2});
          return false;
        }
        
        // 调用后端接口创建分组
        layer.load(2);
        $.ajax({
          url: 'ajax.php?act=manage_shop_groups',
          type: 'POST',
          data: {
            op: 'create',
            name: value.trim(),
            shopIds: []
          },
          dataType: 'json',
          success: function(res) {
            layer.closeAll('loading');
            if (res && res.code == 1) {
              layer.msg(res.msg || '分组创建成功', {icon: 1});
              layer.close(index);
              // 重新加载分组列表
              loadGroups();
            } else {
              layer.msg(res.msg || '创建失败', {icon: 2});
            }
          },
          error: function() {
            layer.closeAll('loading');
            layer.msg('请求失败，请稍后重试', {icon: 2});
          }
        });
      });
    });
    
    // 分组搜索 - 修复：阻止表单提交
    $('#groupSearch').on('keyup', function(e) {
      if (e.keyCode === 13) { // 阻止回车键提交
        e.preventDefault();
      }
      filterGroups();
    });
    
    // 店铺搜索 - 修复：使用allStores进行搜索并阻止默认行为
    $('#btnSearchStores').on('click', function(e) {
      e.preventDefault(); // 阻止默认提交行为
      
      var keyword = $('#storeSearch').val().trim().toLowerCase();
      
      // 如果没有输入关键字，则显示所有店铺
      if (!keyword) {
        currentDisplayStores = allStores.slice();
        renderStoreSelectTable(currentDisplayStores, globalSelectedStoreIds);
        return;
      }
      
      // 使用全部店铺数据allStores进行过滤
      var filtered = allStores.filter(function(store) {
        return (store.storename && store.storename.toLowerCase().includes(keyword)) || 
               (store.ClientId && store.ClientId.toLowerCase().includes(keyword));
      });
      
      currentDisplayStores = filtered;
      renderStoreSelectTable(currentDisplayStores, globalSelectedStoreIds);
    });
    
    // 反选店铺 - 修复：使用当前显示的店铺数据并阻止默认行为
    $('#btnToggleSelection').on('click', function(e) {
      e.preventDefault(); // 阻止默认提交行为
      
      var checkStatus = table.checkStatus('storeSelectTable');
      var selectedIds = checkStatus.data.map(function(item) { return item.id; });
      
      // 使用当前显示的店铺数据
      var allIds = currentDisplayStores.map(function(store) { return store.id; });
      var newSelectedIds = allIds.filter(function(id) {
        return !selectedIds.includes(id);
      });
      
      // 更新全局选中状态
      // 注意：这里需要同时更新全局选中状态（globalSelectedStoreIds）和当前页的选中状态
      // 对于当前页，反选就是选中当前页未选中的，取消当前页已选中的
      // 全局选中状态的处理：将当前页未选中的加入，当前页选中的移除（但注意全局选中状态还包含其他页的选中）
      // 由于我们使用全局选中状态，这里直接重新计算全局选中状态
      var allSelectedIds = [...globalSelectedStoreIds];
      // 遍历当前页所有店铺ID
      allIds.forEach(id => {
        if (selectedIds.includes(id)) {
          // 当前页原本选中的，现在要反选（即取消选中）
          const index = allSelectedIds.indexOf(id);
          if (index !== -1) {
            allSelectedIds.splice(index, 1);
          }
        } else {
          // 当前页原本未选中的，现在要选中
          if (!allSelectedIds.includes(id)) {
            allSelectedIds.push(id);
          }
        }
      });
      globalSelectedStoreIds = allSelectedIds;
      
      // 重新渲染表格并选中反选后的店铺
      renderStoreSelectTable(currentDisplayStores, globalSelectedStoreIds);
    });
    
    // 表单提交
    form.on('submit(groupFormSubmit)', function(data) {
      saveGroup(data.field);
      return false;
    });
    
    // 取消编辑
    $('#btnCancelEdit').on('click', function() {
      resetGroupForm();
    });
    
    // 删除分组 - 事件已移至分组表格行内
    
    // 设为默认
    $('#btnSetDefault').on('click', function() {
      if (!currentGroupId) return;
      setDefaultGroup(currentGroupId);
    });
  }

  // 加载分组数据 - 修复：正确处理后端返回的数据格式
  function loadGroups() {
    layer.load(2);
    $.post('ajax.php?act=manage_shop_groups', { op: 'get' }, function(res) {
      layer.closeAll('loading');
      
      // 修复：根据后端返回的数据结构调整
      if (res && res.data && res.data.groups) {
        // 后端返回格式：{code:0, msg:"操作失败", data:{groups:[...], defaultGroupId:null}}
        allGroups = res.data.groups || [];
        defaultGroupId = res.data.defaultGroupId || null;
      } else if (res && res.groups) {
        // 备用格式：直接返回分组数据
        allGroups = res.groups || [];
        defaultGroupId = res.defaultGroupId || null;
      } else {
        layer.msg('分组数据格式错误');
        return;
      }
      
      // 为每个分组添加店铺数量属性
      allGroups.forEach(function(group) {
        group.shopCount = group.shopIds ? group.shopIds.length : 0;
      });
      
      renderGroupTable(allGroups);
      resetGroupForm();
    }, 'json');
  }

  // 渲染分组表格
  function renderGroupTable(groups) {
    groupTable = table.render({
      elem: '#groupTable',
      data: groups.map(group => {
        // 确保每个分组都有shopCount属性
        return {
          ...group,
          shopCount: group.shopIds ? group.shopIds.length : 0,
          isDefault: group.id == defaultGroupId
        };
      }),
      height: 'full-150',
      page: true,
      limit: 30,
      limits: [15, 30, 50],
      cols: [[
        {field: 'id', title: 'ID', width: 60, sort: true},
        {field: 'name', title: '分组名称', minWidth: 60, templet: function(d) {
          return d.isDefault ? 
            '<span class="layui-badge layui-bg-blue" style="margin-right:5px;">默认</span>' + d.name : 
            d.name;
        }},
        {field: 'shopCount', title: '店铺数', width: 80, align: 'center'},
        {title: '操作', width: 130, align: 'center', fixed: 'right', toolbar: '#groupActionTpl'}
      ]],
      done: function(res, curr, count) {
        // 默认选中第一行
        if (res.data.length > 0 && !currentGroupId) {
          var firstId = res.data[0].id;
          selectGroup(firstId);
        }
      }
    });
  }

  // 渲染店铺选择表格
  function renderStoreSelectTable(stores, selectedIds) {
    storeSelectTable = table.render({
      elem: '#storeSelectTable',
      data: stores,
      height: 280,
      page: true,
      limit: 10,
      limits: [5, 10, 20],
      text: {none: '暂无店铺数据'},
      skin: 'line',
      cols: [[
        {type: 'checkbox', fixed: 'left'},
        {field: 'id', title: 'ID', width: 60},
        {field: 'storename', title: '店铺名称', minWidth: 150},
        {field: 'ClientId', title: 'Client ID', width: 120},
        {field: 'currency_code', title: '币种', width: 60}
      ]],
      done: function(res) {
        // 设置已选中的店铺
        res.data.forEach(function(store, index) {
          if (selectedIds.includes(store.id)) {
            $('.layui-table-body tr[data-index="'+index+'"]').find('input[type="checkbox"]').prop('checked', true);
          }
        });
        form.render('checkbox');
        updateSelectedCount(selectedIds.length);
      }
    });
  }

  // 更新选中店铺计数
  function updateSelectedCount(count) {
    var $badge = $('#selectedCountBadge');
    $badge.text('已选择: ' + count + ' 家店铺');
    count > 0 ? $badge.show() : $badge.hide();
  }

  // 过滤分组
  function filterGroups() {
    var keyword = $('#groupSearch').val().toLowerCase();
    if (!keyword) {
      renderGroupTable(allGroups);
      return;
    }
    
    var filtered = allGroups.filter(function(group) {
      return group.name.toLowerCase().includes(keyword);
    });
    
    renderGroupTable(filtered);
  }

  // 选择分组
  function selectGroup(groupId) {
    currentGroupId = groupId;
    
    // 移除之前的高亮
    $('.layui-table-body tr').removeClass('layui-table-click');
    
    // 高亮当前行
    $('.layui-table-body tr[data-id="'+groupId+'"]').addClass('layui-table-click');
    
    var group = allGroups.find(g => g.id == groupId);
    if (group) {
      // 启用设为默认按钮
      $('#btnSetDefault').removeClass('layui-btn-disabled').prop('disabled', false);
      
      // 如果是默认分组，高亮"设为默认"按钮
      if (group.id == defaultGroupId) {
        $('#btnSetDefault').addClass('layui-btn-disabled').attr('disabled', true)
          .html('<i class="layui-icon layui-icon-ok-circle"></i> 已是默认');
      } else {
        $('#btnSetDefault').removeClass('layui-btn-disabled').attr('disabled', false)
          .html('<i class="layui-icon layui-icon-star"></i> 设为默认');
      }
      
      $('#editGroupId').val(group.id);
      $('input[name="groupName"]').val(group.name);
      
      // 重置当前显示店铺为全部店铺
      currentDisplayStores = allStores.slice();
      
      // 设置全局选中状态为当前分组的店铺ID
      globalSelectedStoreIds = group.shopIds || [];
      
      // 渲染店铺选择表格
      renderStoreSelectTable(currentDisplayStores, globalSelectedStoreIds);
      
      layer.msg('已选择分组: ' + group.name, {icon: 1, time: 1000});
    }
  }

  // 保存分组 - 修复：确保正确处理更新
  // 在 saveGroup 函数中增加日志输出和错误处理
  function saveGroup(data) {
    if (!data.groupName) {
      layer.msg('请输入分组名称');
      return;
    }
    
    // 获取选中的店铺
    var checkStatus = table.checkStatus('storeSelectTable');
    var selectedIds = checkStatus.data.map(item => item.id);
    
    console.log('保存分组数据:', {
      groupId: data.groupId,
      groupName: data.groupName,
      shopIds: selectedIds
    });
    
    var op = data.groupId ? 'update' : 'create';
    var postData = {
      op: op,
      name: data.groupName,
      shopIds: selectedIds,
      exclusiveMode: true // 添加排他模式参数，确保店铺只属于一个分组
    };
    
    if (op === 'update') {
      postData.groupId = data.groupId;
    }
    
    layer.load(2);
    $.ajax({
      url: 'ajax.php?act=manage_shop_groups',
      type: 'POST',
      data: postData,
      dataType: 'json',
      traditional: true,
      success: function(res) {
        console.log('保存响应:', res);
        layer.closeAll('loading');
        
        if (res && res.code == 1) {
          layer.msg(res.msg || '分组保存成功');
          loadGroups();
          table.reload('storetable');
        } else {
          var errorMsg = res.msg || '保存失败';
          if (res && res.data && res.data.error) {
            errorMsg += ': ' + res.data.error;
          }
          layer.msg(errorMsg);
        }
      },
      error: function(xhr) {
        console.error('保存请求失败:', xhr);
        layer.closeAll('loading');
        layer.msg('请求失败: ' + xhr.status + ' ' + xhr.statusText);
      }
    });
  }

  // 删除分组
  function deleteGroup(groupId) {
    layer.load(2);
    $.post('ajax.php?act=manage_shop_groups', {
      op: 'delete',
      groupId: groupId
    }, function(res) {
      layer.closeAll('loading');
      
      // 修复：统一处理后端响应格式
      if (res && res.code == 1) {
        layer.msg(res.msg || '删除成功');
        loadGroups();
        table.reload('storetable');
        resetGroupForm(); // 删除后重置右侧表单
      } else {
        layer.msg(res.msg || '删除失败');
      }
    }, 'json');
  }

  // 设置默认分组
  function setDefaultGroup(groupId) {
    layer.load(2);
    $.post('ajax.php?act=manage_shop_groups', {
      op: 'set_default',
      groupId: groupId
    }, function(res) {
      layer.closeAll('loading');
      
      // 修复：统一处理后端响应格式
      if (res && res.code == 1) {
        layer.msg(res.msg || '设置成功');
        defaultGroupId = groupId;
        loadGroups();
      } else {
        layer.msg(res.msg || '设置失败');
      }
    }, 'json');
  }

  // 进入新增模式
  function enterAddMode() {
    // 清除选中状态
    $('.layui-table-body tr').removeClass('layui-table-click');
    
    // 重置表单
    currentGroupId = null;
    $('#editGroupId').val('');
    $('input[name="groupName"]').val('').attr('placeholder', '请输入新分组名称');
    
    // 重置店铺选择
    currentDisplayStores = allStores.slice();
    globalSelectedStoreIds = [];
    
    // 渲染店铺选择表格
    renderStoreSelectTable(currentDisplayStores, globalSelectedStoreIds);
    
    // 更新按钮状态
    $('#btnDeleteGroup, #btnSetDefault').addClass('layui-btn-disabled').prop('disabled', true);
    
    // 更新保存按钮文字
    $('#btnSaveGroup').html('<i class="layui-icon layui-icon-add-1"></i> 创建分组');
    
    // 更新卡片标题
    $('.layui-card-header span').eq(1).html('<i class="layui-icon layui-icon-add-1"></i> 新增分组');
    
    // 显示提示
    layer.msg('请输入分组名称并选择要加入的店铺', {icon: 6, time: 2000});
  }

  // 重置分组表单
  function resetGroupForm() {
    currentGroupId = null;
    $('#editGroupId').val('');
    $('input[name="groupName"]').val('');
    $('#storeCheckboxGroup').html('');
    $('.layui-table-body tr').removeClass('layui-table-click');
    $('#selectedCountBadge').hide();
    // 重置全局选中状态
    globalSelectedStoreIds = [];
    // 禁用设为默认按钮
    $('#btnSetDefault').addClass('layui-btn-disabled').prop('disabled', true);
    form.render();
    
    // 提示用户选择或创建分组
    layer.msg('请选择一个分组或创建新分组', {icon: 6});
  }

  // 渲染空的店铺表格（显示提示信息）
  function renderEmptyStoreTable() {
    storeSelectTable = table.render({
      elem: '#storeSelectTable',
      data: [],
      height: 280,
      page: false,
      text: {none: '<div style="padding: 60px 0; text-align: center; color: #999;"><i class="layui-icon layui-icon-tips" style="font-size: 32px; display: block; margin-bottom: 10px;"></i><p>请先选择一个分组或创建新分组</p></div>'},
      skin: 'line',
      cols: [[
        {type: 'checkbox', fixed: 'left'},
        {field: 'id', title: 'ID', width: 60},
        {field: 'storename', title: '店铺名称', minWidth: 150},
        {field: 'ClientId', title: 'Client ID', width: 120},
        {field: 'currency_code', title: '币种', width: 60}
      ]]
    });
  }

  // 打开新增店铺弹窗
  $('#btnAddStore').on('click', function() {
    openStoreForm();
  });

  // 打开分组管理弹窗
  $('#btnManageGroups').on('click', function() {
    layer.open({
      type: 1,
      title: '店铺分组管理',
      area: ['1150px', '680px'],
      content: $('#groupManagerTpl').html(),
      success: function() {
        initGroupManager();
        
        // 绑定分组表格事件
        table.on('tool(groupTableFilter)', function(obj) {
          var data = obj.data;
          if (obj.event === 'select') {
            selectGroup(obj.data.id);
          } else if (obj.event === 'delete') {
            layer.confirm('确定删除分组 ' + '<strong>' + data.name + '</strong>' + ' 吗？<br><span style="color: #999; font-size: 12px;">分组内的店铺不会被删除。</span>', {
              title: '<i class="layui-icon layui-icon-help" style="color: #ff9900;"></i> 确认删除',
              icon: 3,
              btn: ['确认删除', '取消']
            }, function(index) {
                deleteGroup(data.id);
                layer.close(index);
            });
          }
        });
      }
    });
  });

  // 打开批量更新仓库弹窗
  $('#btnBatchUpdateWarehouse').on('click', function() {
    // 获取选中的店铺 - 使用跨页选择的数据
    if (allSelectedStoreIds.length === 0) {
      var checkStatus = table.checkStatus('storeTableId');
      allSelectedStoreIds = checkStatus.data.map(item => item.id);
    }
    
    if (allSelectedStoreIds.length === 0) {
      layer.msg('请至少选择一家店铺');
      return;
    }
    
    // 确认批量更新仓库
    layer.confirm('确定要批量更新选中的 ' + allSelectedStoreIds.length + ' 家店铺的仓库信息吗？<br><span style="color: #999; font-size: 12px;">此操作可能需要较长时间，请耐心等待。</span>', {
      title: '<i class="layui-icon layui-icon-refresh-3" style="color: #1E9FFF;"></i> 确认批量更新仓库',
      icon: 3,
      btn: ['确认更新', '取消'],
      area: ['400px', '180px']
    }, function(index) {
      batchUpdateWarehouse(allSelectedStoreIds);
      layer.close(index);
    });
  });

  // 打开批量分组弹窗 - 修复版本
  $('#btnBatchGroup').on('click', function() {
    // 获取选中的店铺 - 修复：使用跨页选择的数据
    if (allSelectedStoreIds.length === 0) {
      var checkStatus = table.checkStatus('storeTableId');
      allSelectedStoreIds = checkStatus.data.map(item => item.id);
    }
    
    if (allSelectedStoreIds.length === 0) {
      layer.msg('请至少选择一家店铺');
      return;
    }
    
    // 加载分组数据
    layer.load(2);
    $.post('ajax.php?act=manage_shop_groups', { op: 'get' }, function(res) {
      layer.closeAll('loading');
      
      // 修复：统一处理分组数据格式
      var groups = [];
      if (res && res.data && res.data.groups) {
        groups = res.data.groups;
        defaultGroupId = res.data.defaultGroupId;
      } else if (res && res.groups) {
        groups = res.groups;
        defaultGroupId = res.defaultGroupId;
      } else {
        layer.msg('无法加载分组数据');
        return;
      }
      
      // 打开弹窗
      var batchLayerIndex = layer.open({
        type: 1,
        title: '批量设置店铺分组',
        area: ['500px', '300px'],
        content: layui.laytpl($('#batchGroupTpl').html()).render({
          groups: groups.map(g => ({
            ...g,
            isDefault: g.id == defaultGroupId
          }))
        }),
        success: function(layerElem, layerIndex) {
          form.render('select');
          
          // 表单提交
          form.on('submit(batchGroupSubmit)', function(data) {
            if (!data.field.batchGroupId) {
              layer.msg('请选择分组');
              return false;
            }
            
            // 使用全局选中的店铺ID
            batchAssignGroup(allSelectedStoreIds, parseInt(data.field.batchGroupId));
            return false;
          });
          
          // 取消按钮
          $('#btnBatchCancel').on('click', function() {
            layer.close(batchLayerIndex);
          });
        }
      });
    }, 'json');
  });

  // 批量更新仓库
  function batchUpdateWarehouse(shopIds) {
    var updateCount = 0;
    var totalCount = shopIds.length;
    var errors = [];
    
         var progressContent = `
       <div style="padding: 30px; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px;">
         <div style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 12px; backdrop-filter: blur(10px);">
           <div style="margin-bottom: 20px;">
             <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 40px; color: #FFD700;"></i>
           </div>
           <h3 style="margin: 0 0 15px 0; font-weight: normal; font-size: 18px;">批量更新仓库</h3>
           <div id="progressBar" style="background: rgba(255,255,255,0.2); height: 8px; border-radius: 4px; margin: 20px 0; overflow: hidden;">
             <div id="progressFill" style="background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%); height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 4px;"></div>
           </div>
           <p id="progressText" style="margin: 10px 0 0 0; font-size: 14px; opacity: 0.9;">准备开始更新...</p>
           <p id="progressDetail" style="margin: 8px 0 0 0; font-size: 12px; opacity: 0.7;">共 ${totalCount} 家店铺</p>
           <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 6px; border-left: 3px solid #FFD700;">
             <p style="margin: 0; font-size: 13px; opacity: 0.8;"><i class="layui-icon layui-icon-tips" style="margin-right: 5px;"></i>请保持页面开启，更新过程中请勿关闭</p>
           </div>
         </div>
       </div>
     `;
    
    layer.open({
      type: 1,
      title: false,
      closeBtn: 0,
      shade: [0.8, '#000'],
      area: ['480px', 'auto'],
      content: progressContent
    });
    
         function updateNext(index) {
       if (index >= totalCount) {
         var successCount = updateCount;
         var failCount = errors.length;
         
         layer.closeAll();
         
         var resultContent = `
           <div style="padding: 25px; text-align: center;">
             <div style="margin-bottom: 20px;">
               <i class="layui-icon ${failCount === 0 ? 'layui-icon-ok-circle' : 'layui-icon-close-fill'}" 
                  style="font-size: 50px; color: ${failCount === 0 ? '#5FB878' : '#FF5722'};"></i>
             </div>
             <h3 style="margin: 0 0 20px 0; color: #333;">批量更新完成</h3>
             <div style="display: flex; justify-content: space-around; margin: 20px 0;">
               <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; min-width: 80px;">
                 <div style="font-size: 24px; font-weight: bold; color: #5FB878;">${successCount}</div>
                 <div style="font-size: 12px; color: #666; margin-top: 5px;">成功</div>
               </div>
               <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; min-width: 80px;">
                 <div style="font-size: 24px; font-weight: bold; color: #FF5722;">${failCount}</div>
                 <div style="font-size: 12px; color: #666; margin-top: 5px;">失败</div>
               </div>
               <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; min-width: 80px;">
                 <div style="font-size: 24px; font-weight: bold; color: #1E9FFF;">${totalCount}</div>
                 <div style="font-size: 12px; color: #666; margin-top: 5px;">总计</div>
               </div>
             </div>
             ${failCount > 0 ? `
               <div style="margin-top: 20px; padding: 15px; background: #fff2f0; border-left: 3px solid #ff4d4f; border-radius: 4px; text-align: left;">
                 <div style="font-weight: bold; color: #ff4d4f; margin-bottom: 10px;">
                   <i class="layui-icon layui-icon-close-fill"></i> 失败详情 (${Math.min(errors.length, 5)} 条)
                 </div>
                 ${errors.slice(0, 5).map(err => `<div style="font-size: 12px; color: #666; margin: 5px 0;">• ${err}</div>`).join('')}
                 ${errors.length > 5 ? `<div style="font-size: 12px; color: #999; margin-top: 8px;">... 还有 ${errors.length - 5} 条错误</div>` : ''}
               </div>
             ` : ''}
           </div>
         `;
         
         layer.open({
           type: 1,
           title: '更新结果',
           area: ['450px', 'auto'],
           content: resultContent,
           btn: ['确定'],
           yes: function(index) {
             layer.close(index);
           }
         });
         
         // 刷新表格
         table.reload('storetable');
         
         // 重置选中状态
         allSelectedStoreIds = [];
         
         return;
       }
       
       var shopId = shopIds[index];
       var currentProgress = Math.round(((index + 1) / totalCount) * 100);
       
       $('#progressFill').css('width', currentProgress + '%');
       $('#progressText').text('正在更新第 ' + (index + 1) + ' 家店铺...');
       $('#progressDetail').text('成功: ' + updateCount + ' | 失败: ' + errors.length + ' | 总计: ' + totalCount);
       
       $.ajax({
         url: 'ajax.php?act=store_upwarehouseedit',
         type: 'POST',
         data: { id: shopId },
         dataType: 'json',
         timeout: 45000,
         cache: false,
         success: function(res) {
           if (res && res.code == 0) {
             updateCount++;
           } else {
             var errorMsg = '店铺ID ' + shopId;
             if (res) {
               errorMsg += ': ' + (res.msg || '更新失败') + ' (code: ' + res.code + ')';
             } else {
               errorMsg += ': 无响应数据';
             }
             errors.push(errorMsg);
           }
         },
                             error: function(xhr, status, error) {
           var errorMsg = '店铺ID ' + shopId + ': ';
           if (status === 'timeout') {
             errorMsg += '请求超时';
           } else if (status === 'error') {
             errorMsg += '网络错误 (' + xhr.status + ')';
           } else {
             errorMsg += '请求失败 (' + error + ')';
           }
           errors.push(errorMsg);
         },
         complete: function() {
           setTimeout(function() {
             updateNext(index + 1);
           }, 1000);
         }
       });
     }
    
         updateNext(0);
   }

  // 批量分配分组 - 修复版本
  function batchAssignGroup(shopIds, groupId) {
    layer.load(2);
    
    $.ajax({
      url: 'ajax.php?act=manage_shop_groups',
      type: 'POST',
      data: {
        op: 'batch_assign',
        groupId: groupId,
        shopIds: shopIds.join(','), // 修复：将数组转换为逗号分隔的字符串
        exclusiveMode: true // 添加排他模式参数，确保店铺只属于一个分组
      },
      dataType: 'json',
      traditional: true, // 修复数组参数传递问题
      success: function(res) {
        layer.closeAll('loading');
        // 统一处理响应格式
        if (res && res.code == 1) {
          layer.msg(res.msg || '批量分组成功');
          table.reload('storetable');
          // 修复：刷新分组管理数据
          loadGroups();
          layer.closeAll();
          // 重置选中状态
          allSelectedStoreIds = [];
        } else if (res && res.code == 0) {
          layer.msg(res.msg || '批量分组失败: ' + (res.data ? res.data.error : ''));
        } else {
          layer.msg('未知响应格式: ' + JSON.stringify(res));
        }
      },
      error: function(xhr, status, error) {
        layer.closeAll('loading');
        layer.msg('请求失败: ' + error);
      }
    });
  }

  // 打开店铺表单
  function openStoreForm(data) {
    var title = data ? '编辑店铺 - ' + data.storename : '新增店铺';
    
    // 加载分组数据
    function loadGroupsForForm(callback) {
      if (allGroups.length > 0) {
        callback();
        return;
      }
      
      $.post('ajax.php?act=manage_shop_groups', { op: 'get' }, function(res) {
        if (res && res.data && res.data.groups) {
          allGroups = res.data.groups || [];
          defaultGroupId = res.data.defaultGroupId || null;
          callback();
        } else {
          layer.msg('分组数据加载失败');
        }
      }, 'json');
    }
    
    loadGroupsForForm(function() {
      var content = `
        <form class="layui-form" lay-filter="storeForm" style="padding: 20px;">
          ${data ? '<input type="hidden" name="id" value="' + data.id + '">' : ''}
          <div class="layui-form-item">
            <label class="layui-form-label">店铺名称</label>
            <div class="layui-input-block">
              <input type="text" name="name" value="${data ? data.storename : ''}" required lay-verify="required" placeholder="请输入店铺名称" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">ClientId</label>
            <div class="layui-input-block">
              <input type="text" name="ClientId" value="${data ? data.ClientId : ''}" required lay-verify="required" placeholder="请输入ClientId" autocomplete="off" class="layui-input" ${data ? 'readonly' : ''}>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">API密匙</label>
            <div class="layui-input-block">
              <input type="text" name="key" value="${data ? data.key : ''}" required lay-verify="required" placeholder="请输入API密匙" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">Cookies</label>
            <div class="layui-input-block">
              <input type="text" name="cookie" value="${data ? data.cookie : ''}" placeholder="请输入Cookies" autocomplete="off" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">币种</label>
            <div class="layui-input-block">
              <select name="money" required lay-verify="required">
                <option value="">请选择币种</option>
                <option value="1" ${data && data.currency_code === 'CNY' ? 'selected' : ''}>CNY</option>
                <option value="2" ${data && data.currency_code === 'RUB' ? 'selected' : ''}>RUB</option>
                <option value="3" ${data && data.currency_code === 'USD' ? 'selected' : ''}>USD</option>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">分组</label>
            <div class="layui-input-block">
              <select name="group_id" lay-search>
                <option value="">请选择分组</option>
                ${allGroups.map(g => 
                  `<option value="${g.id}" ${data && data.group_id == g.id ? 'selected' : ''}>
                    ${g.name}${g.id == defaultGroupId ? ' (默认)' : ''}
                  </option>`
                ).join('')}
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="storeFormSubmit">保存</button>
          </div>
        </form>
      `;
      
      layer.open({
        type: 1,
        title: title,
        area: '500px',
        content: content,
        success: function() {
          form.render();
          
          // 表单提交
          form.on('submit(storeFormSubmit)', function(formData) {
            var url = data ? 'ajax.php?act=store_edit' : 'ajax.php?act=store_add';
            
            // 过滤空值
            if (formData.field.group_id === '') {
              delete formData.field.group_id;
            }
            if (formData.field.cookie === '') {
              delete formData.field.cookie;
            }
            
            layer.load(2);
            $.post(url, formData.field, function(res) {
              layer.closeAll('loading');
              // 统一处理响应格式
              if (res && res.code == 1) {
                layer.msg(res.msg || '操作成功');
                table.reload('storetable');
                layer.closeAll('page');
              } else {
                layer.msg(res.msg || '操作失败: ' + (res.data ? res.data.error : ''));
              }
            }, 'json');
            return false;
          });
        }
      });
    });
  }

  // 开关切换事件
  form.on('switch(auto_close_switch)', function(obj) {
    var id = $(this).data('id');
    var status = obj.elem.checked ? 1 : 0;
    
    layer.load(2);
    $.post('ajax.php?act=store_switch', { id: id, status: status }, function(res) {
      layer.closeAll('loading');
      // 统一处理响应格式
      if (res && res.code == 0) {
        // 成功不做额外处理
      } else {
        obj.elem.checked = !status;
        form.render('checkbox');
        layer.msg(res ? (res.msg || '操作失败') : '请求失败');
      }
    }, 'json');
  });

  // 复制通知链接
  $(document).on('click', '.copy-notice', function() {
    var url = $(this).data('url');
    copyToClipboard(url, '通知链接已复制');
  });

  // 复制到剪贴板
  function copyToClipboard(text, successMsg) {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text).then(function() {
        layer.msg('<i class="layui-icon layui-icon-ok"></i> ' + (successMsg || '复制成功'), { time: 1000 });
      }).catch(function() {
        fallbackCopy(text, successMsg);
      });
    } else {
      fallbackCopy(text, successMsg);
    }
  }

  // 兼容性复制方法
  function fallbackCopy(text, successMsg) {
    var $temp = $('<textarea>').val(text).css({ 
      position: 'fixed', 
      left: '-9999px', 
      top: '-9999px' 
    }).appendTo('body').select();
    
    try {
      document.execCommand('copy');
      layer.msg('<i class="layui-icon layui-icon-ok"></i> ' + (successMsg || '复制成功'), { time: 1000 });
    } catch (err) {
      layer.msg('<i class="layui-icon layui-icon-close"></i> 请手动复制: ' + text, { time: 3000 });
    } finally {
      $temp.remove();
    }
  }

  // 绑定筛选相关事件
  function bindFilterEvents() {
    
    // 绑定筛选按钮事件
    $('#btnFilter').on('click', function() {
      filterTable();
    });
    
    // 绑定重置按钮事件
    $('#btnResetFilter').on('click', function() {
      // 清空选中的值
      selectedStoreIds = [];
      selectedGroupIds = [];
      
      // 清空下拉框内容
      $('#storeSelect, #groupSelect').empty();
      
      // 重新初始化多选控件
      loadShopsAndGroups();
      
      // 延迟一下再初始化多选框，确保数据已加载
      setTimeout(function() {
        initMultiSelect();
        updateSelectedStoresDisplay();
        updateSelectedGroupsDisplay();
      }, 300);
      
      // 重置表格
      table.reload('storeTableId', {
        url: 'ajax.php?act=store_List',
        where: {}
      });
    });
    
    // 使用事件委托处理删除标签按钮点击事件
    $(document).on('click', '.layui-icon-close[data-type="store"]', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      var id = $(this).data('id');
      console.log('删除店铺标签，ID:', id);
      
      // 确保数据类型一致，转换为字符串进行比较
      var index = -1;
      for (var i = 0; i < selectedStoreIds.length; i++) {
        if (String(selectedStoreIds[i]) === String(id)) {
          index = i;
          break;
        }
      }
      
      if(index !== -1) {
        selectedStoreIds.splice(index, 1);
        updateSelectedStoresDisplay();
        console.log('删除成功，剩余店铺ID:', selectedStoreIds);
      } else {
        console.log('未找到匹配的店铺ID');
      }
    });
    
    // 使用事件委托处理删除分组标签按钮点击事件
    $(document).on('click', '.layui-icon-close[data-type="group"]', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      var id = $(this).data('id');
      console.log('删除分组标签，ID:', id);
      
      // 确保数据类型一致，转换为字符串进行比较
      var index = -1;
      for (var i = 0; i < selectedGroupIds.length; i++) {
        if (String(selectedGroupIds[i]) === String(id)) {
          index = i;
          break;
        }
      }
      
      if(index !== -1) {
        selectedGroupIds.splice(index, 1);
        updateSelectedGroupsDisplay();
        console.log('删除成功，剩余分组ID:', selectedGroupIds);
      } else {
        console.log('未找到匹配的分组ID');
      }
    });
  }
  
  // 文档加载完成后初始化
layui.use(['table', 'layer', 'form', 'laytpl'], function(){
  var table = layui.table;
  var layer = layui.layer;
  var form = layui.form;
  var laytpl = layui.laytpl;
    
    // 初始化表单组件
    form.render();
    
    // 初始化表格
    initTable();
    
    // 不再使用旧的初始化方法
    // init();
    
    // 绑定筛选事件
    bindFilterEvents();
    
    // 加载商店和分组数据
    loadShopsAndGroups();
    
    // 等待数据加载完成后初始化多选控件
    setTimeout(function() {
      // 初始化多选下拉框
      initMultiSelect();
      
      // 初始化选中显示
      updateSelectedStoresDisplay();
      updateSelectedGroupsDisplay();
    }, 500);
    
    // 初始筛选一次以确保数据正确显示
    setTimeout(function() {
      filterTable();
    }, 500);
  });

  // 存储所有店铺和分组数据
  var allStores = [];
  var allGroups = [];
  
  // 获取商店和分组数据
  function loadShopsAndGroups() {
    // 获取分组数据
    $.ajax({
      url: 'ajax.php?act=getShopGroups',
      type: 'GET',
      async: false, // 使用同步请求确保数据先加载完成
      success: function(res) {
        if(res.code === 0) {
          allGroups = res.data || [];
        } else {
          console.error("加载分组失败:", res);
          allGroups = [];
        }
      },
      error: function(xhr, status, error) {
        console.error("加载分组请求错误:", error);
        allGroups = [];
      }
    });
    
    // 获取店铺数据
    $.ajax({
      url: 'ajax.php?act=store_List',
      type: 'GET',
      async: false, // 使用同步请求确保数据先加载完成
      success: function(res) {
        if(res.code === 0) {
          allStores = res.data || [];
        } else {
          console.error("加载店铺失败:", res);
          allStores = [];
        }
      },
      error: function(xhr, status, error) {
        console.error("加载店铺请求错误:", error);
        allStores = [];
      }
    });
    
    console.log("数据加载完成 - 店铺数:", allStores.length, "分组数:", allGroups.length);
  }
  
  // 初始化下拉多选框
  function initMultiSelect() {
    // 清除现有组件
    $('#storeSelect, #groupSelect').empty();
    $('.multiselect-dropdown').remove();
    
    // 初始化店铺多选
    initMultiSelectDropdown('#storeSelect', allStores, 'id', 'storename', selectedStoreIds, updateSelectedStoresDisplay, 'store');
    
    // 初始化分组多选
    initMultiSelectDropdown('#groupSelect', allGroups, 'id', 'name', selectedGroupIds, updateSelectedGroupsDisplay, 'group');
  }
  
  // 创建多选下拉组件
  function initMultiSelectDropdown(selector, data, valueKey, textKey, selectedValues, updateCallback, type) {
    var $select = $(selector);
    var $container = $select.parent();
    
    // 创建多选下拉框界面
    var placeholder = type === 'store' ? '点击选择店铺' : '点击选择分组';
    var $selectedDisplay = $('<div class="multiselect-selected">' + placeholder + '</div>');
    var $dropdown = $('<div class="multiselect-dropdown"></div>');
    var $dropdownList = $('<div class="multiselect-dropdown-list"></div>');
    
    // 创建搜索框
    var $search = $('<input type="text" class="multiselect-search" placeholder="输入关键词搜索...">');
    var $tagsContainer = $('<div class="multiselect-tags"></div>');
    $dropdown.append($tagsContainer);
    $dropdownList.append($search);
    
    // 添加选项
    $.each(data, function(index, item) {
      var value = item[valueKey];
      var text = item[textKey];
      
      // 使用字符串比较检查是否选中
      var isSelected = false;
      for (var i = 0; i < selectedValues.length; i++) {
        if (String(selectedValues[i]) === String(value)) {
          isSelected = true;
          break;
        }
      }
      
      // 创建选项
      var $option = $('<div class="multiselect-option"></div>');
      var $checkbox = $('<input type="checkbox" id="multi_' + type + '_' + value + '">');
      var $label = $('<label>' + text + '</label>'); // 移除for属性，避免重复触发
      
      // 设置选中状态
      if (isSelected) {
        $checkbox.prop('checked', true);
      }
      
      $option.append($checkbox).append($label);
      $dropdownList.append($option);
      
      // 点击选项的任何位置都触发复选框状态改变
      $option.on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        // 切换复选框状态
        $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
      });
      
      // 复选框点击事件，阻止冒泡避免重复触发
      $checkbox.on('click', function(e) {
        e.stopPropagation();
      });
      
      // 复选框改变事件
      $checkbox.on('change', function(e) {
        e.stopPropagation();
        var val = String(value); // 确保值为字符串类型
        var index = -1;
        
        // 使用字符串比较查找索引
        for (var i = 0; i < selectedValues.length; i++) {
          if (String(selectedValues[i]) === val) {
            index = i;
            break;
          }
        }
        
        if ($(this).prop('checked') && index === -1) {
          selectedValues.push(value); // 保持原始数据类型
        } else if (!$(this).prop('checked') && index !== -1) {
          selectedValues.splice(index, 1);
        }
        
        updateCallback();
      });
    });
    
    // 添加搜索功能
    $search.on('keyup', function(e) {
      e.stopPropagation();
      var keyword = $(this).val().toLowerCase();
      $dropdownList.find('.multiselect-option').each(function() {
        var text = $(this).find('label').text().toLowerCase();
        if (text.indexOf(keyword) !== -1) {
          $(this).show();
        } else {
          $(this).hide();
        }
      });
    });
    
    // 点击显示/隐藏下拉列表
    $selectedDisplay.on('click', function(e) {
      e.stopPropagation();
      $('.multiselect-dropdown-list').not($dropdownList).removeClass('active');
      $dropdownList.toggleClass('active');
      
      if ($dropdownList.hasClass('active')) {
        // 聚焦搜索框
        setTimeout(function() {
          $search.focus();
        }, 10);
      }
    });
    
    // 阻止下拉列表内点击事件冒泡
    $dropdownList.on('click', function(e) {
      e.stopPropagation();
    });
    
    // 点击外部区域关闭下拉框
    $(document).on('click', function(e) {
      $('.multiselect-dropdown-list').removeClass('active');
    });
    
    // 组装组件
    $dropdown.append($selectedDisplay).append($dropdownList);
    $container.append($dropdown);
  }
  
  // 更新已选择店铺的显示
  function updateSelectedStoresDisplay() {
    var html = '';
    var tagsHtml = '';
    
    if(selectedStoreIds.length > 0) {
      $.each(selectedStoreIds, function(i, storeId) {
        var storeName = '';
        $.each(allStores, function(j, store) {
          if(store.id == storeId) {
            storeName = store.storename;
            return false;
          }
        });
        if(storeName) {
          var tagHtml = '<span class="multiselect-tag">' + storeName + 
                  ' <i class="layui-icon layui-icon-close" data-id="' + storeId + '" data-type="store"></i></span>';
          html += tagHtml;
          tagsHtml += tagHtml;
        }
      });
      
      // 更新下拉框显示文本和标签
      $('.multiselect-dropdown').eq(0).find('.multiselect-selected').text('已选择 ' + selectedStoreIds.length + ' 个店铺');
      $('.multiselect-dropdown').eq(0).find('.multiselect-tags').html(tagsHtml);
    } else {
      $('.multiselect-dropdown').eq(0).find('.multiselect-selected').text('点击选择店铺');
      $('.multiselect-dropdown').eq(0).find('.multiselect-tags').html('');
    }
    $('#selectedStores').html(html);
    
    // 更新复选框状态
    $('.multiselect-dropdown').eq(0).find('.multiselect-option input').each(function() {
      var id = $(this).attr('id').replace('multi_store_', '');
      var isChecked = false;
      
      // 使用字符串比较检查是否选中
      for (var i = 0; i < selectedStoreIds.length; i++) {
        if (String(selectedStoreIds[i]) === String(id)) {
          isChecked = true;
          break;
        }
      }
      $(this).prop('checked', isChecked);
    });
  }
  
  // 更新已选择分组的显示
  function updateSelectedGroupsDisplay() {
    var html = '';
    var tagsHtml = '';
    
    if(selectedGroupIds.length > 0) {
      $.each(selectedGroupIds, function(i, groupId) {
        var groupName = '';
        $.each(allGroups, function(j, group) {
          if(group.id == groupId) {
            groupName = group.name;
            return false;
          }
        });
        if(groupName) {
          var tagHtml = '<span class="multiselect-tag green">' + groupName + 
                  ' <i class="layui-icon layui-icon-close" data-id="' + groupId + '" data-type="group"></i></span>';
          html += tagHtml;
          tagsHtml += tagHtml;
        }
      });
      
      // 更新下拉框显示文本和标签
      $('.multiselect-dropdown').eq(1).find('.multiselect-selected').text('已选择 ' + selectedGroupIds.length + ' 个分组');
      $('.multiselect-dropdown').eq(1).find('.multiselect-tags').html(tagsHtml);
    } else {
      $('.multiselect-dropdown').eq(1).find('.multiselect-selected').text('点击选择分组');
      $('.multiselect-dropdown').eq(1).find('.multiselect-tags').html('');
    }
    $('#selectedGroups').html(html);
    
    // 更新复选框状态
    $('.multiselect-dropdown').eq(1).find('.multiselect-option input').each(function() {
      var id = $(this).attr('id').replace('multi_group_', '');
      var isChecked = false;
      
      // 使用字符串比较检查是否选中
      for (var i = 0; i < selectedGroupIds.length; i++) {
        if (String(selectedGroupIds[i]) === String(id)) {
          isChecked = true;
          break;
        }
      }
      $(this).prop('checked', isChecked);
    });
  }
});
</script>

</body>
</html>
