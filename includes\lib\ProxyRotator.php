<?php
namespace lib;

class ProxyRotator {
    private $proxies;
    private $currentIndex = 0;
    private $maxRetries = 10;
    private $badProxies = [];

    public function __construct() {
        $proxies = [
            '***************:1080:vip:99999999',
            '***************:1080:vip:99999999',
            '***************:1080:vip:99999999',
            '***************:1080:vip:99999999',
            '***************:1080:vip:99999999',
            '***************:1080:vip:99999999',
            '*************:1080:vip:99999999',
            '***************:1080:vip:99999999',
            '***************:1080:vip:99999999',
            '**************:1080:vip:99999999',
        ];
        $this->proxies = $proxies;
    }

    public function ozoncurl($url, $post = 0, $referer = 0, $cookie = 0, $proxys = true) {
        $attempt = 0;
        
        while ($attempt < $this->maxRetries) {
            // 获取当前代理
            $proxy = $this->getNextProxy();
            

            if (!$proxy) break;

            // 执行请求
            $result = $this->doRequest($proxy, $url, $post, $referer, $cookie, $proxys);
            // 处理结果
            switch ($result['code']) {
                case 403:
                    $this->markBadProxy($proxy); // 标记失效代理
                    $attempt++;
                break;
                case 307:
                    return $result;
                break;
                default:
                    return $result; // 成功或其它错误
            }
        }
        
        return [
            'error' => 'All proxies failed or blocked',
            'code' => 403,
            'proxies_attempted' => $attempt
        ];
    }

    private function getNextProxy() {
        $total = count($this->proxies);
        $attempt = 0;

        while ($attempt < $total) {
            $proxy = $this->proxies[$this->currentIndex % $total];
            $this->currentIndex++;

            if (!in_array($proxy, $this->badProxies)) {
                return $proxy;
            }
            $attempt++;
        }
        return null;
    }

    private function markBadProxy($proxy) {
        if (!in_array($proxy, $this->badProxies)) {
            $this->badProxies[] = $proxy;
        }
    }

    private function doRequest($proxy, $url, $post, $referer, $cookie, $proxys) {
        
        $ch = curl_init();
        if($proxys){
            // 代理配置
            list($ip, $port, $user, $pass) = explode(':', $proxy);
            curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5);
            curl_setopt($ch, CURLOPT_PROXY, $ip);
            curl_setopt($ch, CURLOPT_PROXYPORT, $port);
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, "$user:$pass");
        }
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_HEADER, true);
        if ($referer) {
            curl_setopt($ch, CURLOPT_REFERER, $referer);
        }
        if ($post) {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
        }
        $sc_company_id = getSubstr($cookie, 'sc_company_id=', ';');
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "accept: application/json, text/plain, */*",
            "accept-language: zh-Hans",
            "authority: seller.ozon.ru",
            "content-type: application/json",
            "origin: https://seller.ozon.ru",
            "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36",
            "x-o3-app-name: seller-ui",
            "x-o3-company-id: $sc_company_id",
            "x-o3-language: zh-Hans",
            "x-o3-page-type: products-other"
        ]);
        if ($cookie) {
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }
        // 执行请求
        $response = curl_exec($ch);
        $err = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $originalCookies = [];
        if (!empty($cookie)) {
            $pairs = explode('; ', $cookie);
            foreach ($pairs as $pair) {
                if (strpos($pair, '=') !== false) {
                    list($name, $value) = explode('=', $pair, 2);
                    $originalCookies[trim($name)] = trim($value);
                }
            }
        }
    
        // 解析新Cookie（从响应头）
        $newCookies = [];
        $headerLines = explode("\r\n", substr($response, 0, $headerSize));
        foreach ($headerLines as $line) {
            if (stripos($line, 'Set-Cookie:') === 0) {
                $cookieStr = trim(substr($line, 11)); // 去掉"Set-Cookie:"
                // 分割键值对
                $parts = explode(';', $cookieStr);
                $cookiePair = trim($parts[0]);
                if (strpos($cookiePair, '=') !== false) {
                    list($name, $value) = explode('=', $cookiePair, 2);
                    $newCookies[trim($name)] = trim($value);
                }
            }
        }
    
        // 精准合并（重点处理 __Secure-ETC）
        $mergedCookies = $originalCookies;
        foreach ($newCookies as $key => $value) {
            // 强制更新目标Cookie
            if ($key === '__Secure-ETC') {
                $mergedCookies[$key] = $value;
                error_log("已更新 __Secure-ETC: $value"); // 调试日志
            }
            // 其他Cookie正常合并
            else {
                $mergedCookies[$key] = $value;
            }
        }
        
        // 生成新Cookie字符串
        $mergedCookieStr = implode('; ', array_map(
            function ($k, $v) { return "$k=$v"; }, 
            array_keys($mergedCookies), 
            $mergedCookies
        ));
        if($mergedCookieStr and $sc_company_id){
            file_put_contents($cookieurl, $mergedCookieStr);
        }
        curl_close($ch);

        return [
            'code' => $httpCode,
            'body' => substr($response, $headerSize),
            'error' => $err,
            'proxy' => $proxy, // 返回使用的代理信息
            'old_cookie' => $cookie,
            'new_cookies' => $newCookies,
            'merged_cookies' => $mergedCookieStr,
        ];
    }
}

