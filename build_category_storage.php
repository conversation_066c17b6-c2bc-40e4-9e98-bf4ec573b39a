<?php
define('ROOT', __DIR__ . '/');

// 确保所有必要的目录都存在
$requiredDirs = [
    ROOT . 'assets/ozon/categories/',  // 分类存储目录
    ROOT . 'storage/'                  // 索引存储目录
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "创建目录: {$dir}\n";
    }
}

// 加载原始数据
$sourceFile = ROOT . 'assets/json/俄语类目.json';
if (!file_exists($sourceFile)) {
    die("错误：源文件 {$sourceFile} 不存在");
}

$json = file_get_contents($sourceFile);
$data = json_decode($json, true);
$nodes = $data['result'] ?? [];

if (empty($nodes)) {
    die("错误：未找到有效的分类数据");
}

// 创建分类ID索引
$categoryIndex = [];
$processedCount = 0;
$maxDepth = 0;

// 使用栈实现非递归深度优先遍历（避免递归限制）
$stack = new SplStack();
foreach ($nodes as $node) {
    $stack->push([$node, 0]); // [节点数据, 当前深度]
}

// 处理所有节点
while (!$stack->isEmpty()) {
    list($node, $depth) = $stack->pop();
    $maxDepth = max($maxDepth, $depth);
    
    // 处理当前节点
    if (isset($node['description_category_id'])) {
        $catId = $node['description_category_id'];
        
        // 添加到索引
        $categoryIndex[$catId] = [
            'name' => $node['category_name'] ?? '',
            'path' => $node['path'] ?? '' // 添加分类路径
        ];
        
        // 保存分类文件
        $node['path'] = $categoryIndex[$catId]['path']; // 保存路径信息
        file_put_contents(
            ROOT . "assets/ozon/categories/{$catId}.json",
            json_encode($node, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
        );
        
        $processedCount++;
        
        // 每处理100个分类输出进度
        if ($processedCount % 100 === 0) {
            echo "已处理: {$processedCount} 个分类...\n";
        }
    }
    
    // 添加子节点到栈（保持原始顺序）
    if (!empty($node['children'])) {
        $children = array_reverse($node['children']); // 反转以保持原始顺序
        $parentPath = ($node['category_name'] ?? '') . ($categoryIndex[$catId]['path'] ?? '');
        
        foreach ($children as $child) {
            // 添加路径信息
            $child['path'] = $parentPath ? $parentPath . ' > ' . ($child['category_name'] ?? '') : ($child['category_name'] ?? '');
            $stack->push([$child, $depth + 1]);
        }
    }
}

// 保存分类ID索引
$indexFilePath = ROOT . 'storage/category_index.php';
file_put_contents(
    $indexFilePath,
    "<?php\nreturn " . var_export($categoryIndex, true) . ';'
);

// 最终统计报告
echo "=================================\n";
echo "分类存储构建完成！\n";
echo "总处理分类数: " . $processedCount . " 个\n";
echo "最大嵌套深度: " . $maxDepth . " 层\n";
echo "分类文件位置: " . ROOT . "assets/ozon/categories/\n";
echo "索引文件位置: {$indexFilePath}\n";
echo "=================================\n";

// 生成索引检查脚本
$checkScriptPath = ROOT . 'check_index.php';
file_put_contents($checkScriptPath, <<<'EOT'
<?php
define('ROOT', __DIR__ . '/');

// 确保包含必要的文件
$indexFile = ROOT . 'storage/category_index.php';
if (!file_exists($indexFile)) {
    die("错误：索引文件 {$indexFile} 不存在");
}

$index = include $indexFile;

echo "分类索引检查报告:\n";
echo "总分类数: " . count($index) . "\n\n";

// 显示前10个分类
echo "示例分类:\n";
$counter = 0;
foreach ($index as $id => $data) {
    if ($counter++ >= 10) break;
    echo "[{$id}] " . $data['name'] . "\n";
    echo "路径: " . ($data['path'] ?? '无路径信息') . "\n\n";
}

// 检查文件是否存在
echo "\n文件完整性检查:\n";
$missing = 0;
$storageDir = ROOT . 'assets/ozon/categories/';

if (!is_dir($storageDir)) {
    die("错误：分类目录 {$storageDir} 不存在");
}

foreach ($index as $id => $data) {
    $file = $storageDir . $id . '.json';
    if (!file_exists($file)) {
        echo "缺失文件: {$id}.json\n";
        $missing++;
    }
}

echo "\n检查完成! ";
echo $missing ? "发现 {$missing} 个缺失文件" : "所有文件完整存在";
EOT
);

echo "已创建检查脚本: {$checkScriptPath}\n";
echo "运行命令: php check_index.php\n";