<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物流利润计算器</title>
    <link href="../assets/css/select2.min.css" rel="stylesheet" />
  
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --success-color: #4cc9f0;
            --warning-color: #f8961e;
            --danger-color: #f94144;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: var(--dark-color);
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 20px;
        }
        
        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 30px;
            font-weight: 500;
            font-size: 2.2rem;
            position: relative;
            padding-bottom: 1px;
            grid-column: 1 / -1;
        }
        
        h1:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            border-radius: 3px;
        }
        
        h2 {
            color: var(--secondary-color);
            margin-bottom: 20px;
            font-weight: 500;
            font-size: 1.5rem;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 15px;
            margin-bottom: 30px;
            transition: var(--transition);
            height: 100%;
        }
        
        .card:hover {
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark-color);
            font-size: 14px;
        }
        
        .input-group input, 
        .input-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: var(--transition);
            background-color: var(--light-color);
        }
        
        .input-group input:focus, 
        .input-group select:focus {
            border-color: var(--accent-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }
        
        .dimensions-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .dimensions-group input {
            flex: 1;
            text-align: center;
            padding: 10px 5px;
        }
        
        .dimensions-group span {
            color: var(--secondary-color);
            font-weight: 500;
        }
        
        .btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            display: inline-block;
            text-align: center;
            width: 100%;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
        }
        
        .filter-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 15px;
            margin-bottom: 15px;
            overflow-x: auto;
            padding-bottom: 5px;
        }
        
        .select2-container {
            width: 100% !important;
        }
        
        .select2-container--default .select2-selection--multiple {
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            min-height: 40px;
            padding: 5px;
            background-color: var(--light-color);
        }
        
        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: var(--primary-color);
            border: none;
            border-radius: 4px;
            color: white;
            padding: 2px 6px;
            font-size: 12px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            font-size: 13px;
        }
        
        th {
            background-color: var(--primary-color);
            color: white;
            padding: 10px 12px;
            text-align: left;
            font-weight: 500;
            font-size: 13px;
        }
        
        td {
            padding: 10px 12px;
            border-bottom: 1px solid #eee;
            font-size: 13px;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        tr:hover {
            background-color: #f1f3ff;
        }
        
        .highlight {
            background-color: #e3f2fd !important;
            font-weight: 500;
        }
        
        .summary {
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: var(--border-radius);
            margin-bottom: 15px;
            font-size: 13px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .summary-row {
            display: flex;
            gap: 15px;
            justify-content: space-between;
        }
        
        .summary p {
            margin-bottom: 6px;
        }
        
        .summary strong {
            color: var(--primary-color);
        }
        
        .exchange-rate {
            font-size: 12px;
            color: #6c757d;
            margin-top: 3px;
        }
        
        .input-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .left-column {
            display: flex;
            flex-direction: column;
        }
        
        .right-column {
            display: flex;
            flex-direction: column;
        }
        
        .results-container {
            flex-grow: 1;
            overflow-y: auto;
        }
        
        .weight-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr); /* 修改为2列 */
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .weight-info-item {
            background-color: #f0f8ff;
            padding: 10px;
            border-radius: var(--border-radius);
            text-align: center;
        }
        
        .weight-info-item .label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .weight-info-item .value {
            font-size: 14px;
            font-weight: 500;
            color: var(--primary-color);
        }
        
        .target-profit-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .target-profit-item {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: var(--border-radius);
        }
        
        .target-profit-item label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .target-profit-item input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 14px;
        }
        
        .weight-valid {
            color: #28a745;
            font-weight: bold;
        }
        
        .weight-valid::after {
            content: " ✓";
        }
        
        .weight-invalid {
            color: #dc3545;
            font-weight: bold;
        }
        
        .weight-invalid::after {
            content: " ✗";
        }
        
        .profit-high {
            color: #28a745;
            font-weight: 500;
        }
        
        .profit-low {
            color: #dc3545;
            font-weight: 500;
        }
        
        /* 列选择菜单样式 */
        .column-menu {
            position: absolute;
            background: white;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 10px;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .column-menu label {
            display: block;
            padding: 5px 8px;
            cursor: pointer;
            white-space: nowrap;
        }
        
        .column-menu label:hover {
            background-color: #f8f9fa;
        }
        
        #column-toggle-btn {
            background: none;
            border: none;
            color: white;
            padding: 0;
            font-size: 13px;
            cursor: pointer;
        }
        
        @media (max-width: 576px) {
            .input-row {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 10px;
                overflow-x: auto;
                padding-bottom: 5px;
            }
            
            .input-row::-webkit-scrollbar {
                height: 4px;
            }
            
            .input-row::-webkit-scrollbar-thumb {
                background: #ddd;
                border-radius: 2px;
            }
        }

        @media (max-width: 992px) {
            .input-row {
                grid-template-columns: repeat(2, 1fr) !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>物流利润计算器</h1>
        
        <div class="left-column">
            <div class="card">
                <div class="input-group">
                    <label for="length">产品尺寸 (cm)</label>
                    <div class="dimensions-group">
                        <input type="number" id="length" min="1" step="0.1" placeholder="长" value="20">
                        <span>×</span>
                        <input type="number" id="width" min="1" step="0.1" placeholder="宽" value="15">
                        <span>×</span>
                        <input type="number" id="height" min="1" step="0.1" placeholder="高" value="10">
                    </div>
                </div>
                
                <div class="input-row">
                    <div class="input-group">
                        <label for="volumetric-ratio">泡比系数</label>
                        <input type="number" id="volumetric-ratio" min="1" step="1" value="12000" title="体积重量计算公式: 长×宽×高(cm)/泡比系数">
                        <span class="exchange-rate">默认12000 (长×宽×高/泡比系数)</span>
                    </div>
                    
                    <div class="input-group">
                        <label for="weight">产品重量 (kg)</label>
                        <input type="number" id="weight" min="0.001" step="0.001" value="0.5">
                    </div>
                </div>
                
                <div class="input-row">
                    <div class="input-group">
                        <label for="price">产品售价 (¥)</label>
                        <input type="number" id="price" min="0" step="0.01" value="1000">
                    </div>
                    
                    <div class="input-group">
                        <label for="cost">采购成本 (¥)</label>
                        <input type="number" id="cost" min="0" step="0.01" value="500">
                    </div>
                </div>
                
                <div class="input-row">
                    <div class="input-group">
                        <label for="commission">佣金比例 (%)</label>
                        <input type="number" id="commission" min="0" max="100" step="0.1" value="15">
                    </div>
                    
                    <div class="input-group">
                        <label for="withdrawal-fee">提现手续费 (%)</label>
                        <input type="number" id="withdrawal-fee" min="0" max="100" step="0.1" value="1">
                    </div>
                </div>
                
                <div class="input-row">
                    <div class="input-group">
                        <label for="label-fee">贴单费用 (¥)</label>
                        <input type="number" id="label-fee" min="0" step="0.01" value="5">
                    </div>
                    
                    <div class="input-group">
                        <label for="exchange-rate">卢布兑换人民币汇率</label>
                        <input type="number" id="exchange-rate" min="0" step="0.0001" value="0.0795">
                    </div>
                </div>
                
                <div class="target-profit-row">
                    <div class="target-profit-item">
                        <label for="target-sales-profit">目标销售利润率 (%)</label>
                        <input type="number" id="target-sales-profit" min="0" step="0.1" value="25">
                    </div>
                    <div class="target-profit-item">
                        <label for="target-cost-profit">目标成本利润率 (%)</label>
                        <input type="number" id="target-cost-profit" min="0" step="0.1" value="50">
                    </div>
                </div>
                
                <div class="filter-section">
                    <div class="input-group">
                        <label for="logistics-filter">筛选物流商</label>
                        <select id="logistics-filter" multiple>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label for="speed-filter">筛选物流速度</label>
                        <select id="speed-filter" multiple>
                            <option value="空运Express">空运Express</option>
                            <option value="陆空Standard">陆空Standard</option>
                            <option value="陆运Economy">陆运Economy</option>
                        </select>
                    </div>
                </div>
                
                <button id="calculate-btn" class="btn">计算利润</button>
            </div>
        </div>
        
        <div class="right-column">
            <div class="card">
                <h2>计算结果</h2>
                <div class="summary" id="summary"></div>
                <div class="weight-info" id="weight-info">
                    <div class="weight-info-item">
                        <div class="label">实际重量</div>
                        <div class="value" id="actualWeight">0.500 kg</div>
                    </div>
                    <div class="weight-info-item">
                        <div class="label">体积重量</div>
                        <div class="value" id="volumetricWeight">0.500 kg</div>
                    </div>
                </div>
                <div class="results-container" style="overflow-x: auto;">
                    <table id="results-table">
                        <thead>
                            <tr>
                                <th>物流商</th>
                                <th>物流方式</th>
                                <th>物流速度</th>
                                <th>价格范围 (¥)</th>
                                <th>重量范围 (kg)</th>
                                <th>运费 (¥)</th>
                                <th>佣金 (¥)</th>
                                <th>总成本 (¥)</th>
                                <th>利润 (¥)</th>
                                <th>销售利润率 (%)</th>
                                <th>成本利润率 (%)</th>
                                <th>建议售价一 (¥)</th>
                                <th>建议售价二 (¥)</th>
                                <th><button id="column-toggle-btn">显示列 ▼</button></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Results will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/select2.min.js"></script>
    <script>
        // 物流数据（原始数据为卢布，将在计算时转换为人民币）
         const logisticsData = [
            // GUOO
    { provider: "GUOO", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "空运Express", basePrice: 3, perGramPrice: 0.045 },
    { provider: "GUOO", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 3, perGramPrice: 0.035 },
    { provider: "GUOO", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 2.8, perGramPrice: 0.024 },
    { provider: "GUOO", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 23, perGramPrice: 0.025 },
    { provider: "GUOO", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 23, perGramPrice: 0.017 },
    { provider: "GUOO", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 16, perGramPrice: 0.045 },
    { provider: "GUOO", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 16, perGramPrice: 0.035 },
    { provider: "GUOO", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 16, perGramPrice: 0.025 },
    { provider: "GUOO", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 36, perGramPrice: 0.025 },
    { provider: "GUOO", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆运Economy", basePrice: 36, perGramPrice: 0.017 },
    { provider: "GUOO", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 22, perGramPrice: 0.045 },
    { provider: "GUOO", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 22, perGramPrice: 0.035 },
    { provider: "GUOO", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 22, perGramPrice: 0.025 },
    { provider: "GUOO", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 62, perGramPrice: 0.028 },
    { provider: "GUOO", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆运Economy", basePrice: 62, perGramPrice: 0.023 },

         // Ural
    { provider: "Ural", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "空运Express", basePrice: 2.9, perGramPrice: 0.042 },
    { provider: "Ural", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 2.8, perGramPrice: 0.032 },
    { provider: "Ural", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 3, perGramPrice: 0.025 },
    { provider: "Ural", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "空运Express", basePrice: 23, perGramPrice: 0.033 },
    { provider: "Ural", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 23, perGramPrice: 0.025 },
    { provider: "Ural", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 16, perGramPrice: 0.045 },
    { provider: "Ural", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 16, perGramPrice: 0.035 },
    { provider: "Ural", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 16, perGramPrice: 0.025 },
    { provider: "Ural", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "空运Express", basePrice: 36, perGramPrice: 0.033 },
    { provider: "Ural", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 36, perGramPrice: 0.025 },
    { provider: "Ural", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 22, perGramPrice: 0.045 },
    { provider: "Ural", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 22, perGramPrice: 0.035 },
    { provider: "Ural", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 22, perGramPrice: 0.025 },
    { provider: "Ural", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "空运Express", basePrice: 62, perGramPrice: 0.033 },
    { provider: "Ural", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 62, perGramPrice: 0.028 },
    { provider: "Ural", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆运Economy", basePrice: 62, perGramPrice: 0.023 },

    // UNI
    { provider: "UNI", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "空运Express", basePrice: 3, perGramPrice: 0.045 },
    { provider: "UNI", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 3, perGramPrice: 0.035 },
    { provider: "UNI", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 3, perGramPrice: 0.025 },
    { provider: "UNI", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "空运Express", basePrice: 23, perGramPrice: 0.033 },
    { provider: "UNI", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 23, perGramPrice: 0.025 },
    { provider: "UNI", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 23, perGramPrice: 0.017 },
    { provider: "UNI", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 16, perGramPrice: 0.045 },
    { provider: "UNI", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 16, perGramPrice: 0.035 },
    { provider: "UNI", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 16, perGramPrice: 0.025 },
    { provider: "UNI", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "空运Express", basePrice: 36, perGramPrice: 0.033 },
    { provider: "UNI", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 36, perGramPrice: 0.025 },
    { provider: "UNI", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆运Economy", basePrice: 36, perGramPrice: 0.017 },
    { provider: "UNI", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 22, perGramPrice: 0.035 },
    { provider: "UNI", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 22, perGramPrice: 0.035 },
    { provider: "UNI", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 22, perGramPrice: 0.025 },
    { provider: "UNI", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "空运Express", basePrice: 62, perGramPrice: 0.033 },
    { provider: "UNI", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 62, perGramPrice: 0.028 },
    { provider: "UNI", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆运Economy", basePrice: 62, perGramPrice: 0.023 },

    // CEL
    { provider: "CEL", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "空运Express", basePrice: 3, perGramPrice: 0.045 },
    { provider: "CEL", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 3, perGramPrice: 0.035 },
    { provider: "CEL", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 3, perGramPrice: 0.025 },
    { provider: "CEL", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "空运Express", basePrice: 23, perGramPrice: 0.033 },
    { provider: "CEL", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 23, perGramPrice: 0.025 },
    { provider: "CEL", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 23, perGramPrice: 0.017 },
    { provider: "CEL", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 16, perGramPrice: 0.045 },
    { provider: "CEL", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 16, perGramPrice: 0.025 },
    { provider: "CEL", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 16, perGramPrice: 0.025 },
    { provider: "CEL", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "空运Express", basePrice: 36, perGramPrice: 0.033 },
    { provider: "CEL", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 36, perGramPrice: 0.025 },
    { provider: "CEL", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆运Economy", basePrice: 36, perGramPrice: 0.017 },
    { provider: "CEL", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 22, perGramPrice: 0.045 },
    { provider: "CEL", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 22, perGramPrice: 0.035 },
    { provider: "CEL", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 22, perGramPrice: 0.025 },
    { provider: "CEL", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "空运Express", basePrice: 62, perGramPrice: 0.033 },
    { provider: "CEL", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 62, perGramPrice: 0.028 },
    { provider: "CEL", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆运Economy", basePrice: 62, perGramPrice: 0.023 },


    // OYX
    { provider: "OYX", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 3, perGramPrice: 0.035 },
    { provider: "OYX", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 2.9, perGramPrice: 0.025 },
    { provider: "OYX", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 23, perGramPrice: 0.025 },
    { provider: "OYX", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 15.6, perGramPrice: 0.035 },
    { provider: "OYX", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 15.6, perGramPrice: 0.025 },
    { provider: "OYX", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 35.6, perGramPrice: 0.025 },
    { provider: "OYX", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 21.6, perGramPrice: 0.035 },
    { provider: "OYX", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 21.6, perGramPrice: 0.025 },
    { provider: "OYX", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 62, perGramPrice: 0.028 },

    // RETS
    { provider: "RETS", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 2.8, perGramPrice: 0.335 },
    { provider: "RETS", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 2.85, perGramPrice: 0.0235 },
    { provider: "RETS", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 22.9, perGramPrice: 0.0246 },
    { provider: "RETS", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 22.5, perGramPrice: 0.0168 },
    { provider: "RETS", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 15, perGramPrice: 0.033 },
    { provider: "RETS", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 15.9, perGramPrice: 0.023 },
    { provider: "RETS", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 21.9, perGramPrice: 0.034 },
    { provider: "RETS", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 21.9, perGramPrice: 0.024 },
    { provider: "RETS", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆运Economy", basePrice: 62, perGramPrice: 0.0227 },

    // ATC
    { provider: "ATC", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 3, perGramPrice: 0.035 },
    { provider: "ATC", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 3, perGramPrice: 0.024 },
    { provider: "ATC", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 22, perGramPrice: 0.025 },
    { provider: "ATC", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 22.7, perGramPrice: 0.0168 },
    { provider: "ATC", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 16, perGramPrice: 0.033 },
    { provider: "ATC", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 16, perGramPrice: 0.238 },
    { provider: "ATC", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 36, perGramPrice: 0.025 },
    { provider: "ATC", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆运Economy", basePrice: 36, perGramPrice: 0.017 },
    { provider: "ATC", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 21, perGramPrice: 0.034 },
    { provider: "ATC", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 21.5, perGramPrice: 0.024 },
    { provider: "ATC", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 62, perGramPrice: 0.025 },
    { provider: "ATC", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆运Economy", basePrice: 61.98, perGramPrice: 0.017 },

    // Tanais
    { provider: "Tanais", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "空运Express", basePrice: 3, perGramPrice: 0.045 },
    { provider: "Tanais", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 2.9, perGramPrice: 0.0349 },
    { provider: "Tanais", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 2.8, perGramPrice: 0.0249 },
    { provider: "Tanais", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "空运Express", basePrice: 23, perGramPrice: 0.033 },
    { provider: "Tanais", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 22.9, perGramPrice: 0.0245 },
    { provider: "Tanais", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 22.8, perGramPrice: 0.0165 },
    { provider: "Tanais", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 16, perGramPrice: 0.045 },
    { provider: "Tanais", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 15.5, perGramPrice: 0.0345 },
    { provider: "Tanais", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 15.5, perGramPrice: 0.0245 },
    { provider: "Tanais", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 22, perGramPrice: 0.045 },
    { provider: "Tanais", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 21.9, perGramPrice: 0.0345 },
    { provider: "Tanais", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 21.9, perGramPrice: 0.024 },

    // ABT
    { provider: "ABT", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 2.95, perGramPrice: 0.035 },
    { provider: "ABT", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 2.95, perGramPrice: 0.025 },
    { provider: "ABT", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 22.8, perGramPrice: 0.025 },
    { provider: "ABT", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 15.8, perGramPrice: 0.035 },
    { provider: "ABT", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 15.8, perGramPrice: 0.025 },
    { provider: "ABT", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 35.8, perGramPrice: 0.025 },
    { provider: "ABT", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 21.8, perGramPrice: 0.035 },
    { provider: "ABT", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 21.8, perGramPrice: 0.025 },
    { provider: "ABT", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 61.8, perGramPrice: 0.028 },

    
    // XY
    { provider: "XY", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "空运Express", basePrice: 2.95, perGramPrice: 0.045 },
    { provider: "XY", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 2.95, perGramPrice: 0.035 },
    { provider: "XY", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 3, perGramPrice: 0.025 },
    { provider: "XY", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 23, perGramPrice: 0.025 },
    { provider: "XY", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 16, perGramPrice: 0.035 },
    { provider: "XY", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 16, perGramPrice: 0.025 },
    { provider: "XY", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 36, perGramPrice: 0.025 },
    { provider: "XY", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 22, perGramPrice: 0.035 },
    { provider: "XY", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 22, perGramPrice: 0.025 },
    { provider: "XY", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 62, perGramPrice: 0.028 },

    // Leader
    { provider: "Leader", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 3, perGramPrice: 0.0345 },
    { provider: "Leader", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 3, perGramPrice: 0.0245 },
    { provider: "Leader", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 15.5, perGramPrice: 0.035 },
    { provider: "Leader", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 22, perGramPrice: 0.045 },
    { provider: "Leader", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 21.9, perGramPrice: 0.0345 },
    { provider: "Leader", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 21.5, perGramPrice: 0.025 },

    // ZTO
    { provider: "ZTO", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "空运Express", basePrice: 3, perGramPrice: 0.045 },
    { provider: "ZTO", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 3, perGramPrice: 0.035 },
    { provider: "ZTO", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 3, perGramPrice: 0.025 },
    { provider: "ZTO", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "空运Express", basePrice: 23, perGramPrice: 0.033 },
    { provider: "ZTO", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 23, perGramPrice: 0.025 },
    { provider: "ZTO", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 23, perGramPrice: 0.017 },
    { provider: "ZTO", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 16, perGramPrice: 0.045 },
    { provider: "ZTO", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 16, perGramPrice: 0.035 },
    { provider: "ZTO", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 16, perGramPrice: 0.025 },
    { provider: "ZTO", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 36, perGramPrice: 0.025 },
    { provider: "ZTO", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 22, perGramPrice: 0.045 },
    { provider: "ZTO", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 22, perGramPrice: 0.035 },
    { provider: "ZTO", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 22, perGramPrice: 0.025 },
    { provider: "ZTO", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 62, perGramPrice: 0.028 },
    { provider: "ZTO", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆运Economy", basePrice: 62, perGramPrice: 0.023 },

    // IML
    { provider: "IML", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "空运Express", basePrice: 2.95, perGramPrice: 0.045 },
    { provider: "IML", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆空Standard", basePrice: 2.95, perGramPrice: 0.035 },
    { provider: "IML", method: "Extra Small", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 1, maxWeight: 500, speed: "陆运Economy", basePrice: 2.95, perGramPrice: 0.025 },
    { provider: "IML", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 22.95, perGramPrice: 0.025 },
    { provider: "IML", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 22.95, perGramPrice: 0.017 },
    { provider: "IML", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 15.95, perGramPrice: 0.045 },
    { provider: "IML", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 15.95, perGramPrice: 0.035 },
    { provider: "IML", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 15.95, perGramPrice: 0.025 },
    { provider: "IML", method: "Big", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 2001, maxWeight: 30000, speed: "陆空Standard", basePrice: 35.95, perGramPrice: 0.025 },
    { provider: "IML", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 21.95, perGramPrice: 0.035 },
    { provider: "IML", method: "Premium Big", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 5001, maxWeight: 30000, speed: "陆空Standard", basePrice: 61.9, perGramPrice: 0.028 },

   
    // GBS
    { provider: "GBS", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "空运Express", basePrice: 21.85, perGramPrice: 0.033 },
    { provider: "GBS", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆空Standard", basePrice: 21.85, perGramPrice: 0.025 },
    { provider: "GBS", method: "Budget", minPriceRUB: 0, maxPriceRUB: 1500, minWeight: 501, maxWeight: 30000, speed: "陆运Economy", basePrice: 22.4, perGramPrice: 0.017 },
    { provider: "GBS", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 16, perGramPrice: 0.04275 },
    { provider: "GBS", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆空Standard", basePrice: 15.2, perGramPrice: 0.034 },
    { provider: "GBS", method: "Small", minPriceRUB: 1501, maxPriceRUB: 7000, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 15.2, perGramPrice: 0.02375 },
    { provider: "GBS", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 22, perGramPrice: 0.04275 },
    { provider: "GBS", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆空Standard", basePrice: 21, perGramPrice: 0.034 },
    { provider: "GBS", method: "Premium Small", minPriceRUB: 7001, maxPriceRUB: 250000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 23, perGramPrice: 0.02375 },
    //邮政
    { provider: "China Post", method: "ePacket Economy BelarusOzon白俄EUB陆运", minPriceRUB: 1, maxPriceRUB: 9600, minWeight: 1, maxWeight: 2000, speed: "陆运Economy", basePrice: 17.6, perGramPrice: 0.088 },
    { provider: "China Post", method: "ePacket BelarusOzon白俄EUB空运", minPriceRUB: 1, maxPriceRUB: 9600, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 34.65, perGramPrice: 0.01584 },
    { provider: "China Post", method: "格鲁吉亚", minPriceRUB: 1501, maxPriceRUB: 100000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 15.15, perGramPrice: 0.09025 },
    { provider: "China Post", method: "乌兹别克斯坦", minPriceRUB: 1501, maxPriceRUB: 100000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 25, perGramPrice: 0.049 },
    { provider: "China Post", method: "ARM Armenia亚美尼亚", minPriceRUB: 1501, maxPriceRUB: 100000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 25, perGramPrice: 0.071 },
    { provider: "China Post", method: "ARM Azerbaijan阿塞拜疆", minPriceRUB: 1501, maxPriceRUB: 100000, minWeight: 1, maxWeight: 2000, speed: "空运Express", basePrice: 25, perGramPrice: 0.052 },
    { provider: "China Post", method: "China Post ePacket EconomyTrack Kazakhstan陆运", minPriceRUB: 1, maxPriceRUB: 100000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 10.6, perGramPrice: 0.0247 },
    { provider: "China Post", method: "China Post ePacket Kazakhstan空运", minPriceRUB: 1, maxPriceRUB: 100000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 1.87, perGramPrice: 0.05143 },
    { provider: "China Post", method: "China Post ePacket Economy Track陆运", minPriceRUB: 1, maxPriceRUB: 100000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 1.87, perGramPrice: 0.0374 },{ provider: "China Post", method: "ozon ePacket EconomyTrack 陆运", minPriceRUB: 1, maxPriceRUB: 100000, minWeight: 1, maxWeight: 5001, speed: "陆运Economy", basePrice: 12, perGramPrice: 0.0255 },
    { provider: "China Post", method: "ozon ePacket 空运", minPriceRUB: 1, maxPriceRUB: 100000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 12, perGramPrice: 0.056 },
    { provider: "China Post", method: "ozon ePacket Super Express空运 ", minPriceRUB: 1, maxPriceRUB: 100000, minWeight: 1, maxWeight: 5001, speed: "空运Express", basePrice: 12, perGramPrice: 0.095 }
     
   
     
        ];

        // 列显示控制功能
        let columnVisibility = {};

        function initColumnVisibility() {
            const saved = localStorage.getItem('columnVisibility');
            if (saved) {
                columnVisibility = JSON.parse(saved);
            } else {
                document.querySelectorAll('#results-table thead th').forEach((th, index) => {
                    if (index < 13) { // 排除最后一个操作列
                        columnVisibility[index] = true;
                    }
                });
            }
        }

        function applyColumnVisibility() {
            const ths = document.querySelectorAll('#results-table th');
            const tds = document.querySelectorAll('#results-table td');
            
            ths.forEach((th, index) => {
                if (index >= 13) return;
                th.style.display = columnVisibility[index] ? '' : 'none';
            });
            
            document.querySelectorAll('#results-table tr').forEach(tr => {
                tr.querySelectorAll('td').forEach((td, index) => {
                    if (index >= 13) return;
                    td.style.display = columnVisibility[index] ? '' : 'none';
                });
            });
        }

        function createColumnMenu() {
            const menu = document.createElement('div');
            menu.className = 'column-menu';
            menu.id = 'column-menu';
            
            document.querySelectorAll('#results-table th').forEach((th, index) => {
                if (index >= 13) return;
                
                const label = document.createElement('label');
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = columnVisibility[index];
                checkbox.dataset.index = index;
                
                checkbox.addEventListener('change', function() {
                    columnVisibility[this.dataset.index] = this.checked;
                    localStorage.setItem('columnVisibility', JSON.stringify(columnVisibility));
                    applyColumnVisibility();
                });
                
                label.appendChild(checkbox);
                label.appendChild(document.createTextNode(th.textContent));
                menu.appendChild(label);
            });
            
            return menu;
        }

        document.getElementById('column-toggle-btn').addEventListener('click', function(e) {
            e.stopPropagation();
            const menu = document.getElementById('column-menu') || createColumnMenu();
            menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
            
            const rect = this.getBoundingClientRect();
            menu.style.position = 'absolute';
            menu.style.left = `${rect.left}px`;
            menu.style.top = `${rect.bottom}px`;
            
            if (!document.body.contains(menu)) {
                document.body.appendChild(menu);
            }
        });

        document.addEventListener('click', function(e) {
            const menu = document.getElementById('column-menu');
            if (menu && !menu.contains(e.target) && e.target.id !== 'column-toggle-btn') {
                menu.style.display = 'none';
            }
        });

        // 初始化列显示设置
        initColumnVisibility();
        applyColumnVisibility();

        // 获取所有物流商名称
        const providers = [...new Set(logisticsData.map(item => item.provider))];
        
        // 初始化Select2下拉搜索
        $(document).ready(function() {
            $('#logistics-filter').select2({
                placeholder: "选择物流商",
                allowClear: true
            });
            
            $('#speed-filter').select2({
                placeholder: "选择物流速度",
                allowClear: true
            });
            
            // 填充物流商选项
            providers.forEach(provider => {
                $('#logistics-filter').append(new Option(provider, provider));
            });
            
            // 设置输入框自动计算
            setupAutoCalculation();
            
            // 初始化显示
            calculateWeights();
            calculate();
        });

        // 设置输入框自动计算
        function setupAutoCalculation() {
            const inputs = ['weight', 'length', 'width', 'height', 'volumetric-ratio', 
                           'price', 'cost', 'commission', 'withdrawal-fee', 'label-fee', 'exchange-rate',
                           'target-cost-profit', 'target-sales-profit'];
            
            inputs.forEach(id => {
                document.getElementById(id).addEventListener('input', function() {
                    calculateWeights();
                    calculate();
                });
            });
            
            // 输入框获得焦点时选中内容
            inputs.forEach(id => {
                document.getElementById(id).addEventListener('focus', function() {
                    this.select();
                });
            });
        }

        // 计算各种重量
        function calculateWeights() {
            const length = parseFloat(document.getElementById('length').value) || 0;
            const width = parseFloat(document.getElementById('width').value) || 0;
            const height = parseFloat(document.getElementById('height').value) || 0;
            const ratio = parseFloat(document.getElementById('volumetric-ratio').value) || 12000;
            const actualWeightKg = parseFloat(document.getElementById('weight').value) || 0;
            
            // 计算体积重量
            const volumetricWeightKg = (length * width * height) / ratio;
            
            // 更新显示
            document.getElementById('actualWeight').textContent = actualWeightKg.toFixed(3) + " kg";
            document.getElementById('volumetricWeight').textContent = volumetricWeightKg.toFixed(3) + " kg";
            
            return actualWeightKg * 1000; // 返回实际重量（克）
        }

        // 计算运费 - 修改：Big和Premium Big记泡，取较大重量计费
        function calculateShippingCost(option, actualWeightG) {
            if (!option.basePrice || !option.perGramPrice) return null;
            
            let shippingWeight = actualWeightG;
            let isVolumetric = false;
            
            // Big和Premium Big记泡，将采用实际重量与体积重量取较大一方的重量值计费
            if (option.method === 'Big' || option.method === 'Premium Big') {
                const length = parseFloat(document.getElementById('length').value) || 0;
                const width = parseFloat(document.getElementById('width').value) || 0;
                const height = parseFloat(document.getElementById('height').value) || 0;
                const ratio = parseFloat(document.getElementById('volumetric-ratio').value) || 12000;
                
                // 体积重量计算公式：长x宽x高÷12000
                const volumetricWeightKg = (length * width * height) / ratio;
                const volumetricWeightG = volumetricWeightKg * 1000;
                
                // 取实际重量与体积重量中的较大者
                if (volumetricWeightG > actualWeightG) {
                    shippingWeight = volumetricWeightG;
                    isVolumetric = true;
                } else {
                    shippingWeight = actualWeightG;
                }
            }
            
            const cost = option.basePrice + (shippingWeight * option.perGramPrice);
            
            return {
                cost: cost,
                isVolumetric: isVolumetric
            };
        }

        // 计算利润
        function calculateProfit(price, cost, shippingCost, commissionRate, withdrawalFeeRate, labelFee) {
            const commission = price * (commissionRate / 100);
            const withdrawalFee = price * (withdrawalFeeRate / 100);
            const totalCost = cost + shippingCost + commission + withdrawalFee + labelFee;
            const jcCost = cost + withdrawalFee + labelFee;
             
            const profit = price - totalCost;
            const salesProfitMargin = (profit / price) * 100;  // 销售利润率
            const costProfitMargin = (profit / jcCost) * 100;  // 成本利润率
            
            return {
                commission: commission,
                withdrawalFee: withdrawalFee,
                labelFee: labelFee,
                totalCost: totalCost,
                profit: profit,
                salesProfitMargin: salesProfitMargin,
                costProfitMargin: costProfitMargin
            };
        }

        // 计算建议售价
        function calculateSuggestedPrice(cost, shippingCost, labelFee, targetRate, isSalesProfit) {
            if (isSalesProfit) {
                const commissionRate = parseFloat(document.getElementById('commission').value) / 100;
                const withdrawalFeeRate = parseFloat(document.getElementById('withdrawal-fee').value) / 100;
                const denominator = 1 - (targetRate / 100) - commissionRate - withdrawalFeeRate;
                
                if (denominator <= 0) return Infinity; // 避免除以零或负数
                
                return (cost + shippingCost + labelFee) / denominator;
            } else {
                const commissionRate = parseFloat(document.getElementById('commission').value) / 100;
                const withdrawalFeeRate = parseFloat(document.getElementById('withdrawal-fee').value) / 100;
                const denominator = 1 - commissionRate - withdrawalFeeRate;
                
                if (denominator <= 0) return Infinity; // 避免除以零或负数
                
                const totalCostWithoutFees = cost + shippingCost + labelFee;
                const numerator = totalCostWithoutFees * (1 + targetRate / 100);
                return numerator / denominator;
            }
        }

        // 主计算函数
        function calculate() {
            const actualWeightG = calculateWeights(); // 获取实际重量（克）
            const price = parseFloat(document.getElementById('price').value);
            const cost = parseFloat(document.getElementById('cost').value);
            const commissionRate = parseFloat(document.getElementById('commission').value);
            const withdrawalFeeRate = parseFloat(document.getElementById('withdrawal-fee').value);
            const labelFee = parseFloat(document.getElementById('label-fee').value);
            const exchangeRate = parseFloat(document.getElementById('exchange-rate').value);
            const targetCostProfit = parseFloat(document.getElementById('target-cost-profit').value);
            const targetSalesProfit = parseFloat(document.getElementById('target-sales-profit').value);
            
            // 获取筛选条件
            const selectedProviders = $('#logistics-filter').val() || [];
            const selectedSpeeds = $('#speed-filter').val() || [];
            
            // 筛选符合条件的物流选项
            let filteredOptions = logisticsData.filter(option => {
                // 将卢布价格限制转换为人民币
                const minPriceCNY = option.minPriceRUB * exchangeRate;
                const maxPriceCNY = option.maxPriceRUB * exchangeRate;
                
                // 计算用于验证的重量（Big和Premium Big需要考虑体积重量）
                let checkWeight = actualWeightG;
                if (option.method === 'Big' || option.method === 'Premium Big') {
                    const length = parseFloat(document.getElementById('length').value) || 0;
                    const width = parseFloat(document.getElementById('width').value) || 0;
                    const height = parseFloat(document.getElementById('height').value) || 0;
                    const ratio = parseFloat(document.getElementById('volumetric-ratio').value) || 12000;
                    const volumetricWeightKg = (length * width * height) / ratio;
                    const volumetricWeightG = volumetricWeightKg * 1000;
                    checkWeight = Math.max(actualWeightG, volumetricWeightG);
                }
                
                return (selectedProviders.length === 0 || selectedProviders.includes(option.provider)) &&
                       (selectedSpeeds.length === 0 || selectedSpeeds.includes(option.speed)) &&
                       checkWeight >= option.minWeight && checkWeight <= option.maxWeight &&
                       price >= minPriceCNY && price <= maxPriceCNY;
            });
            
            // 计算每种选项的利润
            const results = filteredOptions.map(option => {
                const shippingResult = calculateShippingCost(option, actualWeightG);
                if (shippingResult === null) return null;
                
                const profitData = calculateProfit(price, cost, shippingResult.cost, commissionRate, withdrawalFeeRate, labelFee);
                const minPriceCNY = option.minPriceRUB * exchangeRate;
                const maxPriceCNY = option.maxPriceRUB * exchangeRate;
                
                // 计算建议售价
                const suggestedPriceBySales = calculateSuggestedPrice(cost, shippingResult.cost, labelFee, targetSalesProfit, true);
                const suggestedPriceByCost = calculateSuggestedPrice(cost, shippingResult.cost, labelFee, targetCostProfit, false);
                
                // 检查利润率是否达到目标
                const meetsCostTarget = profitData.costProfitMargin >= targetCostProfit;
                const meetsSalesTarget = profitData.salesProfitMargin >= targetSalesProfit;
                
                return {
                    provider: option.provider,
                    method: option.method,
                    speed: option.speed,
                    minPriceCNY: minPriceCNY,
                    maxPriceCNY: maxPriceCNY,
                    minWeight: option.minWeight / 1000, // 转换为kg
                    maxWeight: option.maxWeight / 1000, // 转换为kg
                    shippingCost: shippingResult.cost,
                    isVolumetric: shippingResult.isVolumetric,
                    commission: profitData.commission,
                    withdrawalFee: profitData.withdrawalFee,
                    labelFee: labelFee,
                    totalCost: profitData.totalCost,
                    profit: profitData.profit,
                    salesProfitMargin: profitData.salesProfitMargin,
                    costProfitMargin: profitData.costProfitMargin,
                    meetsCostTarget: meetsCostTarget,
                    meetsSalesTarget: meetsSalesTarget,
                    suggestedPriceBySales: suggestedPriceBySales,
                    suggestedPriceByCost: suggestedPriceByCost
                };
            }).filter(item => item !== null);
            
            // 按物流商分组排序
            const groupedResults = {};
            results.forEach(result => {
                if (!groupedResults[result.provider]) {
                    groupedResults[result.provider] = [];
                }
                groupedResults[result.provider].push(result);
            });
            
            // 按利润率排序每组内的结果
            Object.keys(groupedResults).forEach(provider => {
                groupedResults[provider].sort((a, b) => b.salesProfitMargin - a.salesProfitMargin);
            });
            
            // 显示结果
            displayResults(groupedResults, actualWeightG / 1000, price, cost, commissionRate, withdrawalFeeRate, labelFee, targetCostProfit, targetSalesProfit);
        }

        // 显示结果
        function displayResults(groupedResults, weightKg, price, cost, commissionRate, withdrawalFeeRate, labelFee, targetCostProfit, targetSalesProfit) {
            const tableBody = document.querySelector('#results-table tbody');
            tableBody.innerHTML = '';
            
            const allResults = Object.values(groupedResults).flat();
            
            if (allResults.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="13" style="text-align: center;">没有找到符合条件的物流选项</td>';
                tableBody.appendChild(row);
                document.getElementById('summary').innerHTML = '<p>没有找到符合条件的物流选项</p>';
                return;
            }
            
            // 修正后的displayResults函数中的摘要部分
            document.getElementById('summary').innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-bottom: 8px;">
                    <div><strong>产品售价:</strong> ¥${price.toFixed(2)}</div>
                    <div><strong>采购成本:</strong> ¥${cost.toFixed(2)}</div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-bottom: 8px;">
                    <div><strong>目标销售利润率:</strong> ${targetSalesProfit}%</div>
                    <div><strong>目标成本利润率:</strong> ${targetCostProfit}%</div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 8px;">
                    <div title="佣金 = 售价 × 佣金比例">
                        <strong>佣金:</strong> 
                        ${commissionRate}% (¥${(price * commissionRate / 100).toFixed(2)})
                    </div>
                    
                    <div title="提现手续费 = 售价 × 手续费比例">
                        <strong>提现费:</strong> 
                        ${withdrawalFeeRate}% (¥${(price * withdrawalFeeRate / 100).toFixed(2)})
                    </div>
                    <div>
                        <strong>贴单费:</strong> 
                        ¥${labelFee.toFixed(2)}
                    </div>
                </div>
                
                <div style="border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;">
                    <strong>找到 ${allResults.length} 个物流选项</strong>
                </div>
            `;
            
            // 填充表格（按物流商分组）
            Object.keys(groupedResults).forEach(provider => {
                groupedResults[provider].forEach((result, index) => {
                    const row = document.createElement('tr');
                    if (index === 0) row.classList.add('highlight'); // 高亮显示每组最优选项
                    
                    // 根据利润率目标设置样式
                    const salesProfitClass = result.salesProfitMargin >= targetSalesProfit ? 'profit-high' : 'profit-low';
                    const costProfitClass = result.costProfitMargin >= targetCostProfit ? 'profit-high' : 'profit-low';
                    
                    // 格式化建议售价
                    const suggestedPriceBySales = isFinite(result.suggestedPriceBySales) ? 
                        result.suggestedPriceBySales.toFixed(2) : "不可行";
                    const suggestedPriceByCost = isFinite(result.suggestedPriceByCost) ? 
                        result.suggestedPriceByCost.toFixed(2) : "不可行";
                    
                    // 根据是否使用体积重量来显示运费文本
                    const shippingCostDisplay = result.isVolumetric ? 
                        `¥${result.shippingCost.toFixed(2)}(体积)` : 
                        `¥${result.shippingCost.toFixed(2)}`;
                    
                    row.innerHTML = `
                        <td>${result.provider}</td>
                        <td>${result.method}</td>
                        <td>${result.speed}</td>
                        <td>¥${result.minPriceCNY.toFixed(2)}-¥${result.maxPriceCNY.toFixed(2)}</td>
                        <td>${result.minWeight.toFixed(3)}-${result.maxWeight.toFixed(3)}</td>
                        <td>${shippingCostDisplay}</td>
                        <td>¥${result.commission.toFixed(2)}</td>
                        <td>¥${result.totalCost.toFixed(2)}</td>
                        <td>¥${result.profit.toFixed(2)}</td>
                        <td class="${salesProfitClass}">${result.salesProfitMargin.toFixed(2)}%</td>
                        <td class="${costProfitClass}">${result.costProfitMargin.toFixed(2)}%</td>
                        <td>¥${suggestedPriceBySales}</td>
                        <td>¥${suggestedPriceByCost}</td>
                    `;
                    
                    tableBody.appendChild(row);
                });
            });
        }

        // 绑定事件
        document.getElementById('calculate-btn').addEventListener('click', calculate);
    </script>
</body>
</html>
