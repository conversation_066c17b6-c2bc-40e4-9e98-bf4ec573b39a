<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>订单管理</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
    <link rel="stylesheet" href="../../../assets/css/order.css">
    <style>
        .order-count-badge {
            background: #ff5722;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 12px;
            margin-left: 5px;
            display: inline-block;
            min-width: 20px;
            text-align: center;
        }
        .order-count-badge.hidden {
            display: none;
        }
        .sub-tabs {
            background: #f8f9fa;
            border-top: 1px solid #e6e6e6;
            padding: 0 15px;
        }
        .sub-tabs .layui-tab-title {
            margin: 0;
            background: transparent;
            border-bottom: 1px solid #ddd;
        }
        .sub-tabs .layui-tab-title li {
            background: transparent;
        }
        .sub-tabs .layui-tab-title li.layui-this {
            background: #fff;
            border-top: 2px solid #1E9FFF;
        }
        .layui-form-pane .layui-form-label {
            background-color: #fafafa;
        }
        .layui-input, .layui-select {
            height: 38px;
        }
    </style>
</head>
<body class="pear-container">
    <!-- 返回顶部按钮 -->
    <button id="layuiBackToTop" class="layui-btn layui-btn-danger layui-btn-radius" style="position: fixed; right: 20px; bottom: 70px; z-index: 99999; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
        <i class="layui-icon layui-icon-top" style="font-size: 18px; color: #fff;"></i>
    </button>

    <div class="order-page">
        <div style="padding: 10px;">
            <div class="layui-card">
                <!-- 筛选表单 -->
                <div class="layui-row" style="margin-top: 15px;">
                    <div class="layui-col-md12">
                        <form class="layui-form layui-form-pane" lay-filter="orderForm">
                            <div class="layui-inline">
                                <input type="text" name="text" placeholder="SKU/货件编号/运号/采购单号/快递单号/标题(双语)" class="layui-input"
                                    autocomplete="off" style="width: 380px;">
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">价格区间</label>
                                <div class="layui-input-inline" style="width: 80px;">
                                    <input type="text" name="money" placeholder="最低价" class="layui-input"
                                        autocomplete="off">
                                </div>
                                <div class="layui-input-inline" style="width: 80px;">
                                    <input type="text" name="moneys" placeholder="最高价" class="layui-input"
                                        autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">店铺</label>
                                <div class="layui-input-inline" style="width: 150px;">
                                    <select name="storeid" lay-search id="sellerSelect">
                                        <option value="">所有店铺</option>
                                    </select>
                                </div>
                            </div>
                            <!-- 用户筛选 -->
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">所属用户</label>
                                <div class="layui-input-inline" style="width: 150px;">
                                    <select name="uid" lay-search id="userSelect">
                                        <option value="">所有用户</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 85px;">日期选择</label>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="text" class="layui-input" name="date1" id="date1" placeholder="yyyy-MM-dd">
                                </div>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="text" class="layui-input" name="date2" id="date2" placeholder="yyyy-MM-dd">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                                <button type="reset" class="layui-btn layui-btn-primary" lay-filter="reset">重置</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 状态导航Tab -->
                <div class="layui-tab layui-tab-brief" lay-filter="orderTab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">
                            待处理
                            <span class="order-count-badge" id="count-pending">0</span>
                        </li>
                        <li>
                            等待发货
                            <span class="order-count-badge" id="count-awaiting_deliver">0</span>
                        </li>
                        <li>
                            交运平台
                            <span class="order-count-badge" id="count-awaiting_deliver2">0</span>
                        </li>
                        <li>
                            运输中
                            <span class="order-count-badge" id="count-delivering">0</span>
                        </li>
                        <li>
                            已送达
                            <span class="order-count-badge" id="count-delivered">0</span>
                        </li>
                        <li>
                            已取消
                            <span class="order-count-badge" id="count-cancelled">0</span>
                        </li>
                        <li>
                            全部订单
                            <span class="order-count-badge" id="count-all">0</span>
                        </li>
                    </ul>

                    <!-- 待处理子标签 -->
                    <div class="sub-tabs" id="pendingSubTabs" style="display: none;">
                        <ul class="layui-tab-title">
                            <li class="layui-this">
                                全部
                            </li>
                            <li>
                                未采购
                                <span class="order-count-badge" id="count-not_purchased">0</span>
                            </li>
                            <li>
                                已采购
                                <span class="order-count-badge" id="count-purchased">0</span>
                            </li>
                            <li>
                                未上传护照
                                <span class="order-count-badge" id="count-awaiting_verification">0</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <div class="layui-col-md12">
                    <table class="layui-hide" id="order-table" lay-filter="orderTable"></table>
                </div>
            </div>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <!-- 表格工具栏模板 -->
    <script type="text/html" id="toolbar">
        <div class="layui-btn-container" style="display: flex; align-items: center;">
            <div class="layui-form" style="display: inline-flex; align-items: center; margin-right: 10px;">
                <input type="checkbox" id="selectAll" lay-skin="primary" title="全选" lay-filter="selectAll">
            </div>
            <button class="layui-btn layui-btn-sm layui-bg-blue" id="syncOrders">同步订单</button>
            <button class="layui-btn layui-btn-sm" lay-event="exportOrders">导出订单</button>
            <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="refresh">刷新</button>
        </div>
    </script>
    
    <!-- 表格操作按钮模板 -->
    <script type="text/html" id="rowToolbar">
        <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="sync">同步</a>
    </script>
    
    <!-- 状态显示模板 (旧，保留以防万一) -->
    <script type="text/html" id="status-tpl">
        {{# if(d.status === 'awaiting_packaging') { }}
            <span class="layui-badge layui-bg-blue">等待包装</span>
        {{# } else if(d.status === 'awaiting_deliver') { }}
            <span class="layui-badge layui-bg-orange">等待发货</span>
        {{# } else if(d.status === 'delivered') { }}
            <span class="layui-badge layui-bg-green">已送达</span>
        {{# } else if(d.status === 'delivering') { }}
            <span class="layui-badge layui-bg-cyan">运输中</span>
        {{# } else if(d.status === 'cancelled') { }}
            <span class="layui-badge layui-bg-red">已取消</span>
        {{# } else if(d.status === 'awaiting_verification') { }}
            <span class="layui-badge layui-bg-gray">未上传护照</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray">{{ d.status || '未知' }}</span>
        {{# } }}
    </script>

    <!-- 新的组合模板 -->
    <script type="text/html" id="productInfo-tpl">
        <div style="display: flex; align-items: center; padding: 5px 0;">
            <img src="{{ d.primary_image || '../assets/img/syncing.png' }}" style="width: 70px; height: 70px; object-fit: cover; border-radius: 4px; flex-shrink: 0;">
            <div style="margin-left: 10px; flex-grow: 1; min-width: 0;">
                <p style="font-weight: 500; word-break: break-all; white-space: normal;">{{ d.order_name || d.name2 || '-' }}</p>
                <p style="color: #555;">SKU: {{ d.sku || '-' }}</p>
            </div>
        </div>
    </script>

    <script type="text/html" id="orderStore-tpl">
        <div>
            <p><strong>单号:</strong> {{ d.posting_number || '-' }}</p>
            <p><strong>店铺:</strong> {{ d.storename || '-' }}</p>
            <p><strong>用户:</strong> {{ d.username || '-' }}</p>
        </div>
    </script>

    <script type="text/html" id="amount-tpl">
        <div>
            <p>售价: <span style="color: #FF5722; font-weight: bold;">￥{{ d.price || '0.00' }}</span></p>
            <p>利润: <span style="font-weight: bold;" class="profit-color-{{ d.id }}">
                {{# if(d.estimated_profit != null) { }}
                    ￥{{ d.estimated_profit }}
                {{# } else { }}
                    N/A
                {{# } }}
            </span></p>
        </div>
    </script>

    <script type="text/html" id="statusTime-tpl">
        <div>
            <span>
                {{# if(d.status === 'awaiting_packaging') { }}
                    <span class="layui-badge layui-bg-blue">等待包装</span>
                {{# } else if(d.status === 'awaiting_deliver') { }}
                    <span class="layui-badge layui-bg-orange">等待发货</span>
                {{# } else if(d.status === 'delivered') { }}
                    <span class="layui-badge layui-bg-green">已送达</span>
                {{# } else if(d.status === 'delivering') { }}
                    <span class="layui-badge layui-bg-cyan">运输中</span>
                {{# } else if(d.status === 'cancelled') { }}
                    <span class="layui-badge layui-bg-red">已取消</span>
                {{# } else if(d.status === 'awaiting_verification') { }}
                    <span class="layui-badge layui-bg-gray">未上传护照</span>
                {{# } else { }}
                    <span class="layui-badge layui-bg-gray">{{ d.status || '未知' }}</span>
                {{# } }}
            </span>
            <p style="color: #999; font-size: 12px; margin-top: 5px;">{{ d.in_process_at || '-' }}</p>
        </div>
    </script>


    <script>
        layui.use(['table', 'layer', 'jquery', 'form', 'laydate', 'element'], function() {
            var table = layui.table;
            var layer = layui.layer;
            var $ = layui.jquery;
            var form = layui.form;
            var laydate = layui.laydate;
            var element = layui.element;
            
                         // 全局变量保存当前状态和搜索条件
             var currentStatus = 'pending';
             var text = '';
             var money = '';
             var moneys = '';
             var storeid = '';
             var date1 = '';
             var date2 = '';
             var uid = '';

            // 新增：根据利润获取颜色的辅助函数
            function getProfitColor(profit) {
                if (profit == null) return '#999';
                return profit > 0 ? '#009688' : '#FF5722';
            }
            
            // 返回顶部按钮
            $('#layuiBackToTop').on('click', function() {
                $('html, body').animate({ scrollTop: 0 }, 'smooth');
                return false;
            });
            
            // 渲染日期选择器
            laydate.render({
                elem: '#date1',
                format: 'yyyy-MM-dd'
            });
            laydate.render({
                elem: '#date2',
                format: 'yyyy-MM-dd'
            });
            
            // 初始化店铺下拉框
            function initShopSelect() {
                $.ajax({
                    url: '/admin/ajax.php?act=getShops',
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            var html = '<option value="">所有店铺</option>';
                            res.data.forEach(shop => {
                                html += `<option value="${shop.id}">${shop.storename}</option>`;
                            });
                            $('#sellerSelect').html(html);
                            form.render('select');
                        }
                    },
                    error: function() {
                        $('#sellerSelect').html('<option value="">加载失败</option>');
                        form.render('select');
                    }
                });
            }
            
                         // 初始化用户下拉框
             function initUserSelect() {
                 $.ajax({
                     url: '/admin/ajax.php?act=getUsers',
                     success: function (res) {
                         if (res.code === 0 && res.data) {
                             var $select = $('#userSelect');
                             $select.empty();
                             $select.append('<option value="">所有用户</option>');
                             res.data.forEach(function (user) {
                                 $select.append('<option value="' + user.uid + '">' + user.username + ' (UID:' + user.uid + ')</option>');
                             });
                             form.render('select');
                         }
                     },
                     error: function() {
                         $('#userSelect').html('<option value="">加载失败</option>');
                         form.render('select');
                     }
                 });
             }
            
            // 初始化订单状态统计
            function initOrderStatusCounts() {
                $.ajax({
                    url: '/admin/ajax.php?act=order_status_counts',
                    type: 'GET',
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            updateOrderStatusCounts(res.data);
                        }
                    },
                    error: function (xhr) {
                        console.log('获取状态统计失败:', xhr.statusText);
                    }
                });
            }
            
            // 更新订单状态统计数字
            function updateOrderStatusCounts(counts) {
                Object.keys(counts).forEach(function(status) {
                    var count = counts[status] || 0;
                    var $badge = $('#count-' + status);
                    if ($badge.length > 0) {
                        $badge.text(count);
                        if (count === 0) {
                            $badge.addClass('hidden');
                        } else {
                            $badge.removeClass('hidden');
                        }
                    }
                });
            }
            
                         // 统一渲染表格
             function renderTable(status, searchText, minPrice, maxPrice, storeId, startDate, endDate, userId) {
                var tableIns = table.render({
                    elem: '#order-table',
                    url: '/admin/ajax.php?act=order_list',
                    toolbar: '#toolbar',
                    defaultToolbar: ['filter', 'print', 'exports'],
                                         where: {
                         status: status,
                         text: searchText || '',
                         money: minPrice || '',
                         moneys: maxPrice || '',
                         storeid: storeId || '',
                         date1: startDate || '',
                         date2: endDate || '',
                         uid: userId || ''
                     },
                    cols: [[
                        {type: 'checkbox', width: 50},
                        {title: '商品信息', minWidth: 350, templet: '#productInfo-tpl'},
                        {title: '订单/店铺信息', width: 220, templet: '#orderStore-tpl'},
                        {title: '金额 (￥)', width: 140, templet: '#amount-tpl', sort: true},
                        {title: '状态与时间', width: 160, templet: '#statusTime-tpl'},
                        {title: '操作', width: 150, toolbar: '#rowToolbar', align: 'center'}
                    ]],
                    page: true,
                    limit: 20,
                    limits: [10, 20, 50, 100],
                    loading: true,
                    even: true,
                    done: function(res, curr, count) {
                        // 重新渲染表单元素
                        form.render();
                        
                        // 新增：渲染完成后，为利润设置颜色
                        if (res.data) {
                            res.data.forEach(function(item) {
                                var color = getProfitColor(item.estimated_profit);
                                $('.profit-color-' + item.id).css('color', color);
                            });
                        }

                        // 更新统计
                        initOrderStatusCounts();
                        
                        // 显示或隐藏子标签
                        if (currentStatus === 'pending') {
                            $('#pendingSubTabs').show();
                        } else {
                            $('#pendingSubTabs').hide();
                        }
                    }
                });
                
                return tableIns;
            }
            
                         // 初始化表格
             var tableIns = renderTable(currentStatus, text, money, moneys, storeid, date1, date2, uid);
            
                         // 搜索功能
             form.on('submit(search)', function (data) {
                 var field = data.field;
                 text = field.text || '';
                 money = field.money || '';
                 moneys = field.moneys || '';
                 storeid = field.storeid || '';
                 uid = field.uid || '';
                 date1 = field.date1 || '';
                 date2 = field.date2 || '';

                 renderTable(currentStatus, text, money, moneys, storeid, date1, date2, uid);
                 return false;
             });
            
                         // 重置功能
             form.on('reset(orderForm)', function () {
                 text = '';
                 money = '';
                 moneys = '';
                 storeid = '';
                 date1 = '';
                 date2 = '';
                 uid = '';
                 
                 renderTable(currentStatus, text, money, moneys, storeid, date1, date2, uid);
             });
            
            // 状态Tab切换
            element.on('tab(orderTab)', function (data) {
                const statusList = ['pending', 'awaiting_deliver', 'awaiting_deliver2', 'delivering', 'delivered', 'cancelled', 'all'];
                currentStatus = statusList[data.index];
                
                var loadingIndex = layer.load(2, {shade: [0.3, '#fff']});
                
                // 显示/隐藏子标签
                if (currentStatus === 'pending') {
                    $('#pendingSubTabs').show();
                } else {
                    $('#pendingSubTabs').hide();
                }
                
                setTimeout(function() {
                    renderTable(currentStatus, text, money, moneys, storeid, date1, date2, uid);
                    layer.close(loadingIndex);
                }, 100);
            });
            
            // 子标签切换事件
            $(document).on('click', '#pendingSubTabs > .layui-tab-title li', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var loadingIndex = layer.load(2, {shade: [0.3, '#fff']});

                $('#pendingSubTabs > .layui-tab-title li').removeClass('layui-this');
                $(this).addClass('layui-this');
                $('#pendingSubTabs').show();

                const subIndex = $(this).index();
                const subStatusList = ['pending', 'not_purchased', 'purchased', 'awaiting_verification'];
                currentStatus = subStatusList[subIndex];

                setTimeout(function() {
                    renderTable(currentStatus, text, money, moneys, storeid, date1, date2, uid);
                    layer.close(loadingIndex);
                }, 100);
            });
            
            // 监听表格工具条
            table.on('tool(orderTable)', function(obj) {
                var data = obj.data;
                
                if (obj.event === 'view') {
                    var imgSrc = data.primary_image || '../assets/img/syncing.png';
                    var content = `
                        <div style="padding: 20px; max-height: 450px; overflow-y: auto;">
                            <div class="layui-form-item" style="text-align: center; margin-bottom: 20px;">
                                <img src="${imgSrc}" style="max-width: 200px; max-height: 200px; object-fit: contain; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">订单ID:</label>
                                <div class="layui-input-inline">${data.id}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">发货单号:</label>
                                <div class="layui-input-inline">${data.posting_number || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">所属用户:</label>
                                <div class="layui-input-inline">${data.username || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">店铺:</label>
                                <div class="layui-input-inline">${data.storename || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">商品名称:</label>
                                <div style="margin-left: 110px; word-break: break-all;">${data.order_name || data.name2 || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">SKU:</label>
                                <div class="layui-input-inline">${data.sku || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">价格:</label>
                                <div class="layui-input-inline">${data.price ? '￥' + data.price : '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">数量:</label>
                                <div class="layui-input-inline">${data.quantity || '1'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">状态:</label>
                                <div class="layui-input-inline">${data.status || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">跟踪号:</label>
                                <div class="layui-input-inline">${data.tracking_number || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">处理时间:</label>
                                <div class="layui-input-inline">${data.in_process_at || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">发货时间:</label>
                                <div class="layui-input-inline">${data.shipment_date || '-'}</div>
                            </div>
                        </div>
                    `;
                    
                    layer.open({
                        type: 1,
                        title: '订单详情 - ' + (data.posting_number || data.id),
                        content: content,
                        area: ['600px', '550px'],
                        shadeClose: true
                    });
                } else if (obj.event === 'sync') {
                    layer.load(2);
                    $.ajax({
                        url: '/admin/ajax.php?act=sync_order',
                        type: 'POST',
                        data: { id: data.id },
                        success: function (res) {
                            layer.closeAll();
                            if (res.code === 0) {
                                layer.msg('同步成功', { icon: 1 });
                                tableIns.reload();
                            } else {
                                layer.msg(res.msg || '同步失败', { icon: 2 });
                            }
                        },
                        error: function (xhr) {
                            layer.closeAll();
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        }
                    });
                }
            });
            
            // 监听表格头部工具栏
            table.on('toolbar(orderTable)', function(obj) {
                if (obj.event === 'exportOrders') {
                    var checkStatus = table.checkStatus('orderTable');
                    var data = checkStatus.data;
                    if (data.length === 0) {
                        layer.msg('请选择要导出的数据', { icon: 2 });
                        return;
                    }
                    layer.msg('导出功能开发中...', { icon: 2 });
                } else if (obj.event === 'refresh') {
                    tableIns.reload();
                    initOrderStatusCounts();
                    layer.msg('刷新成功', { icon: 1 });
                }
            });
            
            // 全选功能
            form.on('checkbox(selectAll)', function(data) {
                var checked = data.elem.checked;
                $('input[type="checkbox"][lay-filter="orderCheckbox"]').prop('checked', checked);
                form.render('checkbox');
            });
            
            // 批量同步订单
            $('#syncOrders').on('click', function() {
                var checkStatus = table.checkStatus('orderTable');
                var data = checkStatus.data;
                
                if (data.length === 0) {
                    layer.msg('请选择要同步的订单', { icon: 2 });
                    return;
                }
                
                layer.confirm('确定要同步选中的 ' + data.length + ' 个订单吗？', {
                    icon: 3,
                    title: '批量同步确认',
                    btn: ['确定', '取消']
                }, function (index) {
                    layer.close(index);
                    
                    var ids = data.map(function(item) {
                        return item.id;
                    });
                    
                    var loadingIndex = layer.load(2, {
                        shade: [0.3, '#fff'],
                        content: '正在同步订单...'
                    });
                    
                    $.ajax({
                        url: '/admin/ajax.php?act=batch_sync_orders',
                        type: 'POST',
                        data: { ids: ids.join(',') },
                        success: function (res) {
                            layer.close(loadingIndex);
                            if (res.code === 0) {
                                layer.msg('批量同步成功！', { icon: 1 });
                                tableIns.reload();
                                initOrderStatusCounts();
                            } else {
                                layer.msg(res.msg || '批量同步失败', { icon: 2 });
                            }
                        },
                        error: function (xhr) {
                            layer.close(loadingIndex);
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        }
                    });
                });
            });
            
                         // 初始化
             initShopSelect();
             initUserSelect();
             initOrderStatusCounts();
        });
    </script>
</body>
</html> 