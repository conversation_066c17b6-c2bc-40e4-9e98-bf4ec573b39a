<?php
namespace lib;

class XiangJiAI
{
    private $apiUrl  = 'http://api.tosoiot.com';
    private $userKey = '';
    private $imgTransKey = '';

    public function __construct($userKey = null)
    {
        if ($userKey) {
            $this->userKey = $userKey;
        }
        return $this;
    }
    
    public function setImgTransKey($imgTransKey = null)
    {
        if ($imgTransKey) {
            $this->imgTransKey = $imgTransKey;
        }
        return $this;
    }

    public function getImageTranslate($originUrl, $sourceLanguage, $targetLanguage)
    {
        if (empty($this->userKey) || empty($this->imgTransKey)) {
            throw new Exception('error: miss UserKey or ImgTransKey');
        }

        $params = [
            'Action'         => 'GetImageTranslate',
            'SourceLanguage' => $sourceLanguage,
            'TargetLanguage' => $targetLanguage,
            'Url'            => $originUrl,
            'ImgTransKey'    => $this->imgTransKey,
        ];

        $signStr = $sourceLanguage . '_' . $targetLanguage . '_' . $originUrl . '_' . $this->userKey . '_' . $this->imgTransKey;
        $sign = md5($signStr);

        $params['Sign'] = $sign;

        $url = $this->apiUrl . '?' . http_build_query($params);
        $response = $this->httpGet($url);
        if ($response === false) {
            throw new Exception('HTTP GET request failed');
        }

        $res = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON decode error: ' . json_last_error_msg());
        }

        return $res;
    }

    private function httpGet($url)
    {
        $options = [
            'http' => [
                'method' => 'GET',
                'timeout' => 30,
            ]
        ];
        $context = stream_context_create($options);
        return @file_get_contents($url, false, $context);
    }
}