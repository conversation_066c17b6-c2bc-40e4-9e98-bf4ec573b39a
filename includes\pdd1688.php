<?php
function pddorder($row){
    global $DB,$userrow;
    /*
    $url = "https://mobile.yangkeduo.com/proxy/api/api/express/split/pack?pdduid=&order_sn=".$row['purchase_orderSn'].'&shipping_id=85&tracking_number=1';
    $response = pddcurl($url);
    exit($response);
    
    $url = "https://mobile.yangkeduo.com/order.html?order_sn=".$row['purchase_orderSn'];
    $headers = [
        'Cookie:PDDAccessToken='.$PDDAccessToken,
    ];
    exit(pddpost($url,$params,$headers));
    */
    $url = "https://mobile.yangkeduo.com/proxy/api/api/aristotle/order_list_v3?is_back=1&type=all&size=50&page=1"; #&key_word=".$row['purchase_orderSn'];
    if($purchase_orderSn){
        $url.="&offset=$purchase_orderSn"; # 刷新第2页第3页第4页.....
    }
    $response = pddcurl($url);
    //($response);
    $json = json_decode($response,true);
    if(empty($json['error_code'])){
        foreach ($json['orders'] as $itme){
            $purchase_orderSn = $itme['order_sn'];
            $data = [];
            // 获取当前订单中的商品数量
            $goods_quantity = $itme['order_goods'][0]['goods_number'];
            $total_price = $itme['order_amount'] / 100; // 总金额
            
            $order = $DB->getAll("SELECT * FROM ozon_order WHERE purchase_orderSn = ?", [$purchase_orderSn]);

            if (empty($order)) {
                continue; // 如果没有关联订单则跳过
            }
            
            foreach ($order as $itmes){
                if($itme['cost'])continue; // 如果有则跳过
                if(empty($itmes['courierNumber']) and empty($order['courierNumber'])){
                    $url = "https://mobile.yangkeduo.com/proxy/api/api/express/split/pack?pdduid=&order_sn=".$purchase_orderSn.'&shipping_id=85&tracking_number=1';
                    $response = pddcurl($url);
                    $json = json_decode($response,true);
                    if($json['result']['count']>=2){
                        $i=0;
                        foreach ($json['result']['list'] as $itmes){
                            if($i>=1){
                                $data['courierNumber'].=','.$itmes['tracking_number'];
                            }else{
                                $data['courierNumber'] = $itmes['tracking_number'];
                            }
                            $i++;
                        }
                    }
                }
                if(count($order)==1){
                    $price = round($total_price, 2);
                }else{
                    $price = ($total_price/$goods_quantity)*$itmes['quantity'];
                    $price = round($price, 2);
                }
                $DB->update('order', ['cost'=>$price], ['order_id'=>$itmes['order_id']]);
            }
            if($itme['order_status_prompt']=='交易已取消' or $itme['order_status_prompt']=='未发货，退款成功'){
                $data['purchase_ok'] = 6;
            }else if($itme['order_status_prompt']=='待发货' or $itme['order_status_prompt']=='拼单成功，待发货' or $itme['order_status_prompt']=='免拼成功，待发货'){
                $data['purchase_ok'] = 1;
            }else if ($itme['order_status_prompt']=='待收货') {
                $data['purchase_ok'] = 2;
                if(empty($data['courierNumber']) and empty($order[0]['courierNumber'])){
                    $data['courierNumber'] = $itme['tracking_number']; #快递单号
                }
                //$data['purchase_kdname'] = $json['express_info']['shipping_name']; #快递公司
                $data['purchase_lus'] = $itme['extra_info']['order_hint']['message'];
            }else if($itme['order_status_prompt']=='待评价'){
                $data['purchase_ok'] = 3;
                $data['purchase_lus'] = $itme['extra_info']['order_hint']['message'];
            }
            
            if($purchase_orderSn and $data){
                $DB->update('order', $data, ['purchase_orderSn'=>$purchase_orderSn]);
                $error = $DB->error();
                if($error){
                    exit($error);
                }
            }
        }
    }else{
        /*
        $pdduid = getSubstr($userrow['pddcookie'], 'pdd_user_id=', ';');
        $url = 'https://mobile.yangkeduo.com/proxy/api/order/'.$row['purchase_orderSn'].'?pdduid='.$pdduid;
        $response = pddcurl($url);
        $json = json_decode($response,true);//等待买家付款
        if($json['error_code'])return false;
        if($json['top_banner']['order_status_prompt']=='交易已取消'){
            $data['purchase_ok'] = 6;
        }else if($json['top_banner']['order_status_prompt']=='待发货'){
            $data['purchase_ok'] = 1;
        }else if ($json['top_banner']['order_status_prompt']=='待收货') {
            $data['purchase_ok'] = 2;
            $data['courierNumber'] = $json['express_info']['tracking_number']; #快递单号
            $data['purchase_kdname'] = $json['express_info']['shipping_name']; #快递公司
            $data['purchase_lus'] = $json['express_info']['traces'][0]['info'];
        }
        $DB->update('order', $data, ['posting_number'=>$row['posting_number']]);
        */
    }
    
    return true;
}
/*
    $url = "https://mobile.yangkeduo.com/proxy/api/api/aristotle/order_list?pdduid=2592804964";
    $PDDAccessToken = getSubstr($userrow['pddcookie'], 'PDDAccessToken=', ';');
    $headers = [
        'Cookie:PDDAccessToken='.$PDDAccessToken,
    ];
     #
     # type
     #
     # all        全部
     # unpaidV2   待付款
     # grouping   待分享
     # unshipping 待发货
     # unreceived 待收货
     # unrated    待评价
     # search     指定搜索 搜索参数key_word=
    $params = [
        'timeout' => 1300,
        'type' => $type,
        'page' => 1,
        'pay_channel_list' => ['9','30','31','35','38','52','-1'],
        'size' => 10,
        //'offset' => '190504-513695417032376'
    ];
    $headers = [
        'AccessToken:' . $PDDAccessToken,
        'Content-Type:application/json;charset=UTF-8',
    ];
    exit(pddpost($url,$params,$headers));
*/
function pddcurl($url){
    global $DB,$userrow;
    $curl = curl_init();
    curl_setopt_array($curl, array(
       CURLOPT_URL => $url,
       CURLOPT_RETURNTRANSFER => true,
       CURLOPT_ENCODING => '',
       CURLOPT_MAXREDIRS => 10,
       CURLOPT_TIMEOUT => 0,
       CURLOPT_FOLLOWLOCATION => true,
       CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
       CURLOPT_CUSTOMREQUEST => 'GET',
       CURLOPT_HTTPHEADER => array(
           'referer: https://mobile.yangkeduo.com/orders.html',
           'Cookie: '.$userrow['pddcookie'],
           'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'
       ),
    ));
    //curl_setopt($curl, CURLOPT_HEADER, true);
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    return $response;
}

function aliorder($row){
    global $DB,$userrow;
    $auth = $DB->find('1688auth', '*', ['uid' => $userrow['uid']]);
    $service = new \lib\AlibabaLogisticsService($auth);
    
    try {
        //$result = $service->getbuyerView('2620397858338665071','alibaba');
        //exit(json_encode($result));
        if($row['purchase_ok']<2){
            $result = $service->getbuyerView($row['purchase_orderSn']);
            //exit(json_encode($result));
            if($result['success']==='true'){
                $statusStr = $result['result']['productItems'][0]['statusStr'];
                if($result and empty($row['cost']) and $result['result']['baseInfo']['totalAmount']){
                    $data['cost'] = $result['result']['baseInfo']['totalAmount'];
                }
                if ($statusStr=='等待卖家发货') {
                    $data['purchase_ok'] = 1;
                }else if($statusStr=='等待买家收货'){
                    $data['purchase_ok'] = 2;
                    $result2 = $service->getLogisticsTraceInfobuyerView($row['purchase_orderSn']);
                    if($result2['errorMessage']!='该订单没有物流跟踪信息。'){
                        if($result2['logisticsTrace']){
                            $i=0;
                            foreach ($result2['logisticsTrace'] as $itmes){
                                $logisticsSteps = array_reverse($itmes['logisticsSteps']);
                                $data['purchase_lus'] = $logisticsSteps[0]['remark'];
                                if($i>1){
                                    $data['courierNumber'].= ','.$itmes['logisticsBillNo'];
                                }else{
                                    $data['courierNumber'] = $itmes['logisticsBillNo'];
                                }
                                $i++;
                            }
                        }
                    }else{
                        $i = 0;
                        foreach ($result['result']['nativeLogistics']['logisticsItems'] as $itmes){
                            if($i>1){
                                $data['courierNumber'].= ','.$itmes['logisticsBillNo'];
                            }else{
                                $data['courierNumber'] = $itmes['logisticsBillNo'];
                            }
                            $i++;
                        }
                    }
                }
            }
        }else if ($row['purchase_ok']==2) {
            $result = $service->getLogisticsTraceInfobuyerView($row['purchase_orderSn']);
            if($result['success']===true){
                $i=0;
                foreach ($result['logisticsTrace'] as $itmes){
                    $logisticsSteps = array_reverse($itmes['logisticsSteps']);
                    $data['purchase_lus'] = $logisticsSteps[0]['remark'];
                    if($i>1){
                        $data['courierNumber'].= ','.$itmes['logisticsBillNo'];
                    }else{
                        $data['courierNumber'] = $itmes['logisticsBillNo'];
                    }
                    $i++;
                }
            }
        }else{
            $result = $service->getbuyerView($row['purchase_orderSn']);
            exit(json_encode($result));
        }
        if($result['error_code']==401){
            $result = $service->upaccess_token();
            if($result['access_token']){
                $DB->update('1688auth', ['access_token'=>$result['access_token'],'json'=>json_encode($result)], ['id'=>$auth['id']]);
            }else{
                exit(json_encode(['code'=>-2,'msg'=>'授权失效，请重新授权尝试。']));
            }
            exit(json_encode(['code'=>-2,'msg'=>'授权失效正在自动更新，请重新尝试。']));
        }
        if($data){
            $DB->update('order', $data, ['posting_number'=>$row['posting_number']]);
        }
        
        /*
        exit(json_encode($result));
        
        $result = $service->getLogisticsInfosbuyerView($row['purchase_orderSn']);
        
        if($result['success']===true){
            foreach ($result['result'] as $itme){
                if($itme['status']=='NOGET'){
                    $data['purchase_ok'] = 2;
                    
                }else if($itme['status']=='SIGN'){
                    $result2 = $service->getLogisticsTraceInfobuyerView($row['purchase_orderSn']);
                    //exit(json_encode($result2));
                    if($result2['logisticsTrace']){
                        foreach ($result2['logisticsTrace'] as $itmes){
                            $logisticsSteps = array_reverse($itmes['logisticsSteps']);
                            $data['purchase_lus'] = $logisticsSteps[0]['remark'];
                        }
                        
                    }
                    //exit(json_encode($result));
                    $data['purchase_ok'] = 3;
                }
                $data['courierNumber'] = $itme['logisticsBillNo']; #快递单号
                $data['purchase_kdname'] = $itme['logisticsCompanyName']; #快递公司
            }
        }else if($result['error_code']==401){
            $result = $service->upaccess_token();
            if($result['access_token']){
                $DB->update('1688auth', ['access_token'=>$result['access_token'],'json'=>json_encode($result)], ['id'=>$auth['id']]);
            }else{
                exit(json_encode(['code'=>-2,'msg'=>'授权失效，请重新授权尝试。']));
            }
            exit(json_encode(['code'=>-2,'msg'=>'授权失效正在自动更新，请重新尝试。']));
        }else if($result['errorCode']=='500_2'){
            $data['purchase_ok'] = 1;
            
        }else{
            exit(json_encode($result));
        }
        */
        
        return true;
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage();
    }
}

function sku_id1688(string $offerid){
    $cookie = 'cookie2=1d6fabc1dd970d9c43b870f27bd9308f; t=53f2b0d587c3154181686f55311b6e26; _tb_token_=3634e1e457bbf; taklid=5cf4e4042dea455eb3bcd4f6dc6ee97f; xlly_s=1; trackId=399e487b5fc14589998995f2a802e632; lid=tb972052812; ali_apache_track=c_mid=b2b-2200539667150491c7|c_lid=tb972052812|c_ms=1; ali_apache_tracktmp=c_w_signed=Y; _m_h5_c=382ac1df67eb6cad80fe28516d00a167_1751801820422%3B16559bfc1f8cb5729b2211493f3b2bcc; inquiry_preference=webIM; is_identity=buyer; callClientScheme=ali1688im%3Asendmsg; _samesite_flag_=true; tracknick=; union={"amug_biz":"oneself","amug_fl_src":"awakeId_982","creative_url":"https%3A%2F%2Fdetail.1688.com%2Foffer%2F922830017589.html%3Ffromkv%3DcbuPcPlugin%3AimageSearchDrawerCard%26spm%3Da2639h.29135425.offerlist.i1%26source%3Daction%2523offerItem%253Borigin%2523www.100b.cn%26amug_biz%3Doneself%26amug_fl_src%3DawakeId_982","creative_time":1751809389747}; leftMenuLastMode=COLLAPSE; leftMenuModeTip=shown; sgcookie=E100jhlwrLoa9xFqmXXjdC418nv66jui%2BQ2Ko%2BiI4fPcnJt%2FdyvOL3XO%2BaTzCh5x9Run%2BBTPA9FDgOuT%2F4xwmRpjRmoGC0qq6V%2FAaYiMUlTrXlauKKuxFlka%2BnPSMKjIjYBE; uc4=id4=0%40U2grEaqHxefPPDr4ewYfRZS8QAufZZka&nk4=0%40FY4HWy3OqrtRTsWCgY3GTmTk%2F0enXA%3D%3D; mtop_partitioned_detect=1; _m_h5_tk=7ef7b4369a6b583b930e7db6df462f45_1751825018602; _m_h5_tk_enc=3190ba967f52f38a41b2745c4b6264cb; plugin_home_downLoad_cookie=%E5%AE%89%E8%A3%85%E6%8F%92%E4%BB%B6; __cn_logon__=false; cna=B+rwIDjoYioCAXW1lg12C5cM; isg=BFFRjnsP3QpD5T7L6P8c9sXxYF3rvsUw3oAQLjPmTZg32nEsew7VAP8pfq48Ul1o; tfstk=g8aE5GtoewQFodkKxPgyQtnCe9gKP4WfEzMSZ7VoOvDhvkiubbGXELVlp8zrIJCKF4ghzuyaB31KF6d9r8kTALM59ukudRnSxTKQ44y_Lyi3O2GrzRH9PbazwUluZ8CKF6I_9W3-rt6jaZNL9XRMYo6sr_ViPblFlfk09W3J6C9lclVpaJSgL20uEVmiwX0oZ02ojFDtwbvnZefN_Ah-ELDnxfbiGbvor42us1l-IX0ox8ViwUgokdlSx_I3jtfOqmiEnWDwzWUZtD-LtA8krPrZYxvrQUYu7XP1xvevugM0Dvij3Rb684PiaRobYt8UU7VYqczyK6w0tJUYqW7Nn2aLtomuoTKu32yrmymwZE3ESA0ZsrfWDAaZd84EjsTqVV4jm2q1X9iS8jyuJJRyr-V_GynTztJiekGxSbzRi3krqgo9efffwzEeE3mn6fkf_1uSu8r2FdIYE3K-vChZh6mH23nn6fkf_1-J2DBt_x1nx; _user_vitals_session_data_={"user_line_track":true,"ul_session_id":"qek0aphstnn","last_page_id":"detail.1688.com%2Feteqnmhpe2p"}';
    $t = (int)(microtime(true) * 1000);
    $token = getSubstr($cookie, '_m_h5_tk=', '_');
    $h = 12574478;
    $data = '{"offerId":"'.$offerid.'"}';
    $sign = md5("$token&$t&$h&$data");
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://h5api.m.1688.com/h5/mtop.1688.pc.plugin.od.sku.query/1.0/?jsv=2.7.2&appKey='.$h.'&t='.$t.'&sign='.$sign.'&dataType=json&api=mtop.1688.pc.plugin.od.sku.query&v=1.0&type=originaljson');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'accept: */*',
        'accept-language: zh-CN,zh;q=0.9',
        'content-type: application/x-www-form-urlencoded;charset=UTF-8',
        'origin: https://air.1688.com',
        'priority: u=1, i',
        'referer: https://air.1688.com/kapp/1688-pc-front/procurement-center/tools/skuExport?from=extension&amug_biz=oneself&amug_fl_src=awakeId_984&offerId='.$offerid,
        'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile: ?0',
        'sec-ch-ua-platform: "Windows"',
        'sec-fetch-dest: empty',
        'sec-fetch-mode: cors',
        'sec-fetch-site: same-site',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'x-accept-language: zh-CN',
    ]);
    curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    curl_setopt($ch, CURLOPT_POSTFIELDS, 'data='.urlencode($data));
    
    $response = curl_exec($ch);
    curl_close($ch);
    $json = json_decode($response,true);
    return $json;
}

function alimd5($time,$data){
    global $DB,$userrow;
    $token = getSubstr($userrow['1688cookie'], '_m_h5_tk=', '_');
    //taklid=2eac0f5f72f74542952214f141d11179;cookie2=1e39b661cd9099a25c70200f4581ea6f;_m_h5_c=a8a07fed3523978cdf3ae79f995bdff3_1748905716891%3Ba421fb01f6bff5e7b08440fccfacd002;_m_h5_tk=e214b56229f6b820701fd0d5d75fcdbd_1748924222798; _m_h5_tk_enc=cbbdd83e339bfa3716d415fba7434e1d
    
    //taklid=2eac0f5f72f74542952214f141d11179;cookie2=1e39b661cd9099a25c70200f4581ea6f;_m_h5_c=a8a07fed3523978cdf3ae79f995bdff3_1748905716891%3Ba421fb01f6bff5e7b08440fccfacd002;_m_h5_tk=e214b56229f6b820701fd0d5d75fcdbd_1748924222798;_m_h5_tk_enc=cbbdd83e339bfa3716d415fba7434e1d
    $h = 12574478;
    return md5("$token&$time&$h&$data");
}

function curl1688($url, $post) {
    global $DB,$userrow;
    
    // 最大重试次数（防止无限循环）
    $maxRetries = 2;
    $retryCount = 0;
    //exit($userrow['1688cookie']);
    while ($retryCount <= $maxRetries) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
           CURLOPT_URL => $url,
           CURLOPT_RETURNTRANSFER => true,
           CURLOPT_ENCODING => '',
           CURLOPT_MAXREDIRS => 10,
           CURLOPT_TIMEOUT => 0,
           CURLOPT_FOLLOWLOCATION => true,
           CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
           CURLOPT_CUSTOMREQUEST => 'POST',
           CURLOPT_POSTFIELDS => $post,
           CURLOPT_HTTPHEADER => array(
              'accept: application/json',
              'accept-language: zh-CN,zh;q=0.9',
              'origin: https://air.1688.com',
              'priority: u=1, i',
              'referer: https://air.1688.com/',
              'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
              'sec-ch-ua-mobile: ?0',
              'sec-ch-ua-platform: "Windows"',
              'sec-fetch-dest: empty',
              'sec-fetch-mode: cors',
              'sec-fetch-site: same-site',
              'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
              'Cookie: '. $userrow['1688cookie']
           ),
        ));
        curl_setopt($curl, CURLOPT_HEADER, true);
        
        $fullResponse = curl_exec($curl);
        $err = curl_error($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
        curl_close($curl);
        $header = substr($fullResponse, 0, $headerSize);  // 头部
        $response = substr($fullResponse, $headerSize);       // 主体
        // 检查是否 token 过期
        if (strpos($response, '令牌过期') || strpos($response, 'TOKEN_EXPIRED')) {
            
            $retryCount++;
            $newCookie = reipdata1688Cookie($header,$userrow['1688cookie']);
            if (!$newCookie) {
                exit(json_encode(['code' => -1, 'msg' => '登录失败，无法更新令牌']));
            }
            $DB->update('user', ['1688cookie'=>$newCookie], ['uid'=>$userrow['uid']]);
            $userrow['1688cookie'] = $newCookie;
            continue;
        }
        
        
        // 成功返回数据
        exit($fullResponse);
    }
    
    // 超过最大重试次数仍失败
    exit(json_encode(['code' => -1, 'msg' => '令牌更新失败，请手动登录']));
}

/**
 * 刷新 1688 Cookie
 */
function reipdata1688Cookie($header, $cookie) {
    // 1. 解析原始Cookie（保护合法空格）
    preg_match_all('/Set-Cookie: (.*?);/i', $header, $matches);
    foreach ($matches[1] as $item){
        $x = explode('=',$item);
        $array[$x[0]] = $x[1];
    }
    return updateCookieParams($cookie,$array);
}

function updateCookieParams($oldCookie, $newParams) {
    //echo $oldCookie.'</br>';
    foreach ($newCookie as $x=>$y){
        $nametext = getSubstr($oldCookie, $x, ';');
        if($nametext){
            $oldCookie = str_replace($nametext,$y,$oldCookie);
        }
    }
    return str_replace(' ','',$oldCookie);
}

function pddpost($url, $params=[], $headers=[], $cookieJar='')
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    //curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_REFERER, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_POST, 0);
    curl_setopt($ch, CURLOPT_TIMEOUT, 1);
    //curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    if ($cookieJar) curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieJar);
    $result = curl_exec($ch);
    curl_close($ch);
    echo $result;exit;
}



function ali1688data($url){          #获取1688商品数据
    $offerid = ali1688getoffer($url);
    if(empty($offerid)){
        return false;
    }
    $curl = curl_init();
    curl_setopt_array($curl, array(
       CURLOPT_URL => 'https://overseaplugin.1688.com/offer/getOverseasOfferDetail',
       CURLOPT_RETURNTRANSFER => true,
       CURLOPT_ENCODING => '',
       CURLOPT_MAXREDIRS => 10,
       CURLOPT_TIMEOUT => 0,
       CURLOPT_FOLLOWLOCATION => true,
       CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
       CURLOPT_CUSTOMREQUEST => 'POST',
       CURLOPT_POSTFIELDS =>'{"offerId":"'.$offerid.'","language":"zh_CN","currency":"CNY"}',
       
       CURLOPT_HTTPHEADER => array(
          'priority: u=1, i',
          'sec-fetch-storage-access: active',
          'content-type: application/json'
       ),
    ));
    
    $response = curl_exec($curl);
    exit($response);
    curl_close($curl);
    $json = json_decode($response,true);
    if($json['success']==true){
        $data = $json['result'];
        if(['imageUrlList']){
            $images = $data['imageUrlList'];     # 产品图片集
        }
        $mainImage = null;
        foreach ( $data['skuList'] as $item){
            if($item['skuAttributeList'][0]['imageUrl']){
                if(empty($mainImage)){
                    $mainImage = $item['skuAttributeList'][0]['imageUrl'];  # 设置组图
                }
                $images[] = $item['skuAttributeList'][0]['imageUrl'];
            }
        }
        foreach ($data['offerAttributeList'] as $item){
            $AttributeTxt .=$item['name'].": ".$item['value']."\n";
        }
        $array = ['title'=> $data['title'],'category'=> $data['category'],'description'=> $data['description'], 'mainImage'=>$mainImage,'images'=>implode(";", $images),'AttributeTxt'=>$AttributeTxt];
        if($data['mainVideoUrl'] or $data['subVideoUrl']){
            $array['videos'] = $data['mainVideoUrl']??$data['subVideoUrl'];
        }
        return $array;
    }
    return false;
}

function ali1688getoffer($url){      #获取1688offerId数据
    if (preg_match('/\/offer\/(\d+)\.html/', $url, $matches)) {
        return $matches[1];
    }
    if (preg_match('/offer\/(\d+)/', $url, $matches)) {
        return $matches[1];
    }
    return false;
}

/**
 * 生成1688 API签名
 * @param array $params 所有请求参数（包括系统参数）
 * @param string $client_secret 应用密钥
 * @return string 大写形式的签名
 */
function generate1688Signature($params, $client_secret) {
    // 1. 过滤空值参数
    $filteredParams = array_filter($params, function($v) {
        return $v !== null && $v !== '';
    });
    
    // 2. 参数按键名升序排序
    ksort($filteredParams);
    
    // 3. 拼接键值对
    $stringToSign = '';
    foreach ($filteredParams as $k => $v) {
        $stringToSign .= $k . $v;
    }
    
    // 4. 末尾追加密钥
    $stringToSign .= $client_secret;
    
    // 5. 计算MD5并转大写
    return strtoupper(md5($stringToSign));
}