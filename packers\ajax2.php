<?php
include("../includes/common.php");

$act = isset($_POST['act']) ? $_POST['act'] : (isset($_GET['act']) ? $_GET['act'] : '');

switch ($act) {
    //订单云打印
      case 'print_label':
        if ($packer_id <= 0) {
            exit(json_encode(['code' => 401, 'msg' => '未登录']));
        }
        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);
        $trackingNumbers = isset($data['trackingNumbers']) ? $data['trackingNumbers'] : [];
        if (empty($trackingNumbers)) {
            exit(json_encode(['code' => 1, 'msg' => '请选择要打印标签的快递单号']));
        }
        // 记录打印日志
        foreach ($trackingNumbers as $trackingNumber) {
            $DB->exec("INSERT INTO print_logs (packer_id, tracking_number, print_time) VALUES (:packer_id, :tracking_number, NOW())", [
                ':packer_id' => $packer_id,
                ':tracking_number' => $trackingNumber
            ]);
        }
        exit(json_encode(['code' => 0, 'msg' => '打印请求已提交']));
        break;
    
    case 'config':
        echo file_get_contents('./config/pear.config.json');
        exit;
    case 'get_product_info':
        $json_data = file_get_contents('php://input');
        // 解析 JSON
        $data = json_decode(trim($json_data,':'), true);
        $trackingNumber = isset($data['trackingNumber']) ? trim($data['trackingNumber']) : '';
        if (!$trackingNumber) {
            exit(json_encode(['code' => 1, 'msg' => '快递单号不能为空']));
        }
        $products = $DB->getAll("SELECT * FROM ozon_order WHERE courierNumber=:trackingNumber OR tracking_number=:trackingNumber", [':trackingNumber'=>$trackingNumber]);
        if (empty($products)) {
            exit(json_encode(['code' => 2, 'msg' => '未找到对应商品信息，标记查询异常']));
        }
        // 检查国际单号格式
        foreach ($products as $product) {
            if (!preg_match('/^[A-Z0-9]{10,20}$/', $product['tracking_number'])) {
                exit(json_encode(['code' => 3, 'msg' => '查询到物流单号，但国际单号有问题']));
            }
        }
        exit(json_encode(['code' => 0, 'msg' => 'success', 'data' => $products]));
        break;
    case 'mark_shipped':
        if ($packer_id <= 0) {
            exit(json_encode(['code' => 401, 'msg' => '未登录']));
        }
        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);
        $trackingNumbers = isset($data['trackingNumbers']) ? $data['trackingNumbers'] : [];
        if (empty($trackingNumbers)) {
            exit(json_encode(['code' => 1, 'msg' => '请选择要标记发货的快递单号']));
        }
        foreach ($trackingNumbers as $trackingNumber) {
            $order = $DB->getRow("SELECT posting_number FROM ozon_order WHERE courierNumber = :trackingNumber LIMIT 1", [':trackingNumber' => $trackingNumber]);
            if (!$order) {
                continue;
            }
            $updateRes = $DB->exec("UPDATE ozon_order SET is_shipped = 1 WHERE posting_number = :postingNumber", [':postingNumber' => $order['posting_number']]);
            if ($updateRes === false) {
                exit(json_encode(['code' => 2, 'msg' => '标记发货失败']));
            }
        }
        exit(json_encode(['code' => 0, 'msg' => '标记发货成功']));
        break;
  
    case 'mark_exception':
        if ($packer_id <= 0) {
            exit(json_encode(['code' => 401, 'msg' => '未登录']));
        }
        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);
        $trackingNumbers = isset($data['trackingNumbers']) ? $data['trackingNumbers'] : [];
        $exceptionType = isset($data['exceptionType']) ? $data['exceptionType'] : '';
        $exceptionReason = isset($data['exceptionReason']) ? $data['exceptionReason'] : '';
        
        if (empty($trackingNumbers)) {
            exit(json_encode(['code' => 1, 'msg' => '请选择要标记异常的快递单号']));
        }
        
        if (empty($exceptionType)) {
            exit(json_encode(['code' => 2, 'msg' => '请选择异常类型']));
        }
        
        foreach ($trackingNumbers as $trackingNumber) {
            $order = $DB->getRow("SELECT posting_number FROM ozon_order WHERE courierNumber = :trackingNumber OR tracking_number = :trackingNumber LIMIT 1", [':trackingNumber' => $trackingNumber]);
            if (!$order) {
                continue;
            }
            
            $updateData = [
                ':postingNumber' => $order['posting_number'],
                ':isException' => 1,
                ':exceptionType' => $exceptionType,
                ':exceptionReason' => $exceptionReason,
                ':exceptionTime' => date('Y-m-d H:i:s')
            ];
            
            $updateRes = $DB->exec("UPDATE ozon_order 
                                    SET is_exception = :isException, 
                                        exception_type = :exceptionType, 
                                        exception_reason = :exceptionReason, 
                                        exception_time = :exceptionTime 
                                    WHERE posting_number = :postingNumber", $updateData);
            
            if ($updateRes === false) {
                exit(json_encode(['code' => 3, 'msg' => '标记异常失败']));
            }
        }
        exit(json_encode(['code' => 0, 'msg' => '标记异常成功']));
    default:
        exit(json_encode(['code' => 404, 'msg' => '无效的操作']));
}
?>    