<?php
namespace lib;

class AlibabaLogisticsService
{
    private $appKey;
    private $appSecret;
    private $accessToken;
    private $refreshToken;
    private $gatewayUrl = 'https://gw.open.1688.com/openapi/';

    public function __construct($data)
    {
        $this->appKey = 7762286;
        $this->appSecret = 'Y14Wuu65O7Z';
        $this->accessToken = $data['access_token'];
        $this->refreshToken = $data['refresh_token'];
        $this->subUserId = $data['aliId'];
    }
    
    /*************************************代发解决方案（服务商版）********************************************/
    
    #获取分销商品详情接口
    public function productInfo($offerId){
        
        return $this->curl(['offerId'=>$offerId], 'param2/1/com.alibaba.fenxiao/alibaba.fenxiao.productInfo.get/');
    }
    
    public function getProductSpecs($productId) {
        $apiParams = [
            'productId' => $productId,
            'webSite' => '1688'
        ];
        
        return $this->curl($apiParams, 'param2/1/com.alibaba.product/alibaba.product.get/');
    }
    
    #买家获取保存的收货地址信息列表
    public function receiveAddress(){
        return $this->curl(null,'param2/1/com.alibaba.trade/alibaba.trade.receiveAddress.get/');
    }
    
    #创建采购订单
    public function fastCreateOrder($itmes){
        $data = $this->receiveAddress();
        $data = $data['result']['receiveAddressItems'][0];
        $ct = explode(' ',$data['addressCodeText']);
        // 应用级参数
        $apiParams = [
            'flow' => 'general', #general（创建大市场订单），fenxiao（创建分销订单）,saleproxy流程将校验分销关系,paired(火拼下单),boutiquefenxiao(精选货源分销价下单，采购量1个使用包邮)， boutiquepifa(精选货源批发价下单，采购量大于2使用).
            //'subUserId' => $this->subUserId,
            'addressParam'=>json_encode([
                    'addressId'=>$data['id'],               #收货地址id
                    'fullName'=>$data['fullName'],          #收货人姓名
                    'mobile'=>$data['mobilePhone'],         #手机
                    'phone'=>$data['mobilePhone'],          #电话
                    'postCode'=>$data['post'],              #邮编
                    'cityText'=>$ct[1],                     #市文本
                    'provinceText'=>$ct[0],                 #省份文本
                    'areaText'=>$data['townName'],          #区文本
                    'townText'=>$data['townName'],          #镇文本
                    'address'=>$data['address'],            #街道地址
                    'districtCode'=>$data['addressCode'],   #地址编码
                ]),
            'cargoParamList'=> json_encode([
                    'offerId'=>$itmes['offerId'],
                    'specId'=>$itmes['sku'],
                    'quantity'=>$itmes['quantity']??1,
                ])
        ];
        return $this->curl($apiParams,'param2/1/com.alibaba.trade/alibaba.trade.fastCreateOrder/');
    }
    
    public function alipayurl(){
        $apiParams = [
            'orderIdList'=>"[2817212665564665071]"
        ];
        return $this->curl($apiParams,'param2/1/com.alibaba.trade/alibaba.alipay.url.get');
    }
    
    #订单详情查看(买家视角)
    public function getbuyerView($orderId, $webSite='1688'){
        // 应用级参数
        $apiParams = [
            'orderId' => $orderId,
            'webSite' => $webSite,
        ];
        
        return $this->curl($apiParams,'param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/');
    }
    
    #获取交易订单的物流信息(买家视角)
    public function getLogisticsInfosbuyerView($orderId, $webSite='1688', $fields=null)
    {
        // 应用级参数
        $apiParams = [
            'orderId' => $orderId,
            'webSite' => $webSite,
        ];
        
        if ($fields !== null) {
            $apiParams['fields'] = $fields;
        }
        return $this->curl($apiParams,'param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsInfos.buyerView/');
    }
    
    #获取交易订单的物流跟踪信息(买家视角)
    public function getLogisticsTraceInfobuyerView($orderId, $webSite='1688', $logisticsId = null)
    {
        // 应用级参数
        $apiParams = [
            'orderId' => $orderId,
            'webSite' => $webSite,
        ];
        
        if ($logisticsId !== null) {
            $apiParams['logisticsId'] = $logisticsId;
        }
        return $this->curl($apiParams,'param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsTraceInfo.buyerView/');
    }
    
    #刷新 access_token
    public function upaccess_token(){
        
        // 请求参数
        $postData = [
            'client_id' => $this->appKey,
            'client_secret' => $this->appSecret,
            'grant_type' => 'refresh_token',
            'refresh_token' => $this->refreshToken,
            'need_refresh_token'=>true
        ];
        
        // 初始化 cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/".$this->appKey);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 仅测试环境使用，生产环境应启用 HTTPS 验证
        
        // 执行请求
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            die('cURL 请求失败: ' . curl_error($ch));
        }
        curl_close($ch);
        
        // 解析响应
        $result = json_decode($response, true);
        if (isset($result['error'])) {
            die("刷新 Token 失败: " . $result['error_description'] ?? '未知错误');
        }
        return $result;
        // 获取新的 Token
        $newAccessToken = $result['access_token'];
        $newRefreshToken = $result['refresh_token'] ?? $this->refreshToken; // 如果没返回新的 refresh_token，继续用旧的
        $expiresIn = $result['expires_in'];
        
        echo "新的 access_token: $newAccessToken\n";
        echo "新的 refresh_token: $newRefreshToken\n";
        echo "过期时间（秒）: $expiresIn\n";
    }

    public function curl($apiParams=null,$url)
    {
        // 系统级参数
        $systemParams = [
            'access_token' => $this->accessToken,
            '_aop_timestamp' => time() * 1000,
        ];

        // 生成签名（关键修改）
        $systemParams['_aop_signature'] = $this->generateSignature($apiParams, $systemParams,$url.$this->appKey);

        // 构建请求URL
        $requestUrl = $this->gatewayUrl.$url.$this->appKey. '?' . http_build_query($systemParams);

        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $requestUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        if($apiParams){
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($apiParams));
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
        ]);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception('CURL Error: ' . curl_error($ch));
        }
        curl_close($ch);

        return json_decode($response, true);
    }
    private function generateSignature2($code_arr=null, $systemParams,$urlPath) {
        $aliParams = array();
        foreach ($code_arr as $key => $val) {
            $aliParams[] = $key . $val;
        }
        sort($aliParams);
        $sign_str = join('', $aliParams);
        $sign_str = $apiInfo . $sign_str;
        
        return strtoupper(bin2hex(hash_hmac("sha1", $sign_str, $this->appSecret, true)));
    }

    private function generateSignature($apiParams=null, $systemParams,$urlPath) {
        if($apiParams){
            $allParams = array_filter(array_merge($apiParams, $systemParams), function($v) {
                return $v !== null && $v !== '';
            });
        }else {
            $allParams = $systemParams;
        }
        
        
        // 2. 按参数名升序排序
        ksort($allParams);
        
        // 3. 构造待签名字符串（关键修改：包含URL路径）
        $paramStr = '';
        foreach ($allParams as $key => $value) {
            $paramStr .= $key . $value;
        }
        $stringToSign = $urlPath . $paramStr;
        return strtoupper(bin2hex(hash_hmac('sha1', $stringToSign, $this->appSecret, true)));
    }
}