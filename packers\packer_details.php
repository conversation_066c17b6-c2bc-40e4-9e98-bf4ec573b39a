<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>打包员数据管理</title>

</head>
<body>
  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-header">
        <h2>打包记录管理</h2>
      </div>
      <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form layui-form-pane" lay-filter="searchForm">
          <div class="layui-form-item">
            <div class="layui-inline">
              <label class="layui-form-label">订单编号</label>
              <div class="layui-input-inline">
                <input type="text" name="posting_number" placeholder="请输入订单编号" class="layui-input">
              </div>
            </div>
            <div class="layui-inline">
              <label class="layui-form-label">快递单号</label>
              <div class="layui-input-inline">
                <input type="text" name="courierNumber" placeholder="请输入快递单号" class="layui-input">
              </div>
            </div>
            <div class="layui-inline">
              <label class="layui-form-label">状态</label>
              <div class="layui-input-inline">
                <select name="packing_status">
                  <option value="">全部状态</option>
                  <option value="0">异常</option>
                  <option value="1">打包完成</option>
                  <option value="2">退回问题件</option>
                </select>
              </div>
            </div>
            <div class="layui-inline">
              <button class="layui-btn" lay-submit lay-filter="searchBtn">搜索</button>
              <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
          </div>
        </form>
        
        <!-- 表格工具栏 -->
        <div class="layui-btn-group">
          <button class="layui-btn layui-btn-normal" id="addBtn"><i class="layui-icon">&#xe608;</i> 添加</button>
          <button class="layui-btn layui-btn-danger" id="batchDelBtn"><i class="layui-icon">&#xe640;</i> 批量删除</button>
          <button class="layui-btn" id="exportBtn"><i class="layui-icon">&#xe67d;</i> 导出Excel</button>
        </div>
        
        <!-- 数据表格 -->
        <table id="dataTable" lay-filter="dataTable"></table>
        
        <!-- 表格操作列模板 -->
        <script type="text/html" id="tableBar">
          <a class="layui-btn layui-btn-xs" lay-event="edit"><i class="layui-icon">&#xe642;</i> 编辑</a>
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i> 删除</a>
        </script>
        
        <!-- 添加/编辑表单 -->
        <div id="formDialog" style="display: none; padding: 20px;">
          <form class="layui-form" lay-filter="dataForm">
            <input type="hidden" name="id">
            <div class="layui-form-item">
              <label class="layui-form-label">订单编号</label>
              <div class="layui-input-block">
                <input type="text" name="posting_number" required lay-verify="required" placeholder="请输入订单编号" class="layui-input">
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">快递单号</label>
              <div class="layui-input-block">
                <input type="text" name="courierNumber" required lay-verify="required" placeholder="请输入快递单号" class="layui-input">
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">打包数量</label>
              <div class="layui-input-block">
                <input type="number" name="packing_quantity" required lay-verify="required|number" placeholder="请输入打包数量" class="layui-input">
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">耗材费用</label>
              <div class="layui-input-block">
                <input type="text" name="material_cost" lay-verify="number" placeholder="请输入耗材费用" class="layui-input">
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">状态</label>
              <div class="layui-input-block">
                <select name="packing_status" lay-verify="required">
                  <option value="0">异常</option>
                  <option value="1">打包完成</option>
                  <option value="2">退回问题件</option>
                </select>
              </div>
            </div>
            <div class="layui-form-item">
              <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formSubmit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script>
  layui.use(['table', 'form', 'layer', 'jquery'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 初始化表格
    table.render({
      elem: '#dataTable',
      url: 'ajax.php?act=get_list',
      page: true,
      toolbar: '#toolbar',
      cols: [[
        {type: 'checkbox'},
        {field: 'id', title: 'ID', width:80, sort: true},
        {field: 'posting_number', title: '订单编号', width:180},
        {field: 'courierNumber', title: '国内快递单号', width:180},
        {field: 'tracking_number', title: '国际快递单号', width:180},
        {field: 'packerId', title: '打包员ID', width:120},
        {field: 'packing_quantity', title: '打包数量', width:100},
        {field: 'material_cost', title: '耗材费用', width:120, templet: function(d){
          return d.material_cost ? '¥'+d.material_cost : '-';
        }},
        {field: 'packing_status', title: '状态', width:120, templet: function(d){
          var status = {'0':'异常', '1':'打包完成', '2':'退回问题件'};
          return status[d.packing_status] || '未知';
        }},
        {field: 'updated_at', title: '更新时间', width:180},
        {field: 'created_at', title: '创建时间', width:180},
        {fixed: 'right', title: '操作', width:150, align:'center', toolbar: '#tableBar'}
      ]]
    });
    
    // 搜索按钮点击事件
    form.on('submit(searchBtn)', function(data){
      table.reload('dataTable', {
        where: data.field,
        page: {curr: 1}
      });
      return false;
    });
    
    // 添加按钮点击事件
    $('#addBtn').click(function(){
      form.val('dataForm', {
        "id": "",
        "posting_number": "",
        "courierNumber": "",
        "packing_quantity": "",
        "material_cost": "",
        "packing_status": "0"
      });
      layer.open({
        type: 1,
        title: '添加打包记录',
        area: ['600px', '450px'],
        content: $('#formDialog')
      });
    });
    
    // 表格工具栏事件
    table.on('tool(dataTable)', function(obj){
      var data = obj.data;
      if(obj.event === 'del'){
        layer.confirm('确定要删除这条记录吗？', function(index){
          $.post('ajax.php?act=delete', {id: data.id}, function(res){
            if(res.code === 0){
              layer.msg('删除成功', {icon: 1});
              table.reload('dataTable');
            }else{
              layer.msg(res.msg || '删除失败', {icon: 2});
            }
          }, 'json');
          layer.close(index);
        });
      }else if(obj.event === 'edit'){
        form.val('dataForm', data);
        layer.open({
          type: 1,
          title: '编辑打包记录',
          area: ['600px', '450px'],
          content: $('#formDialog')
        });
      }
    });
    
    // 批量删除按钮
    $('#batchDelBtn').click(function(){
      var checkStatus = table.checkStatus('dataTable');
      if(checkStatus.data.length === 0){
        layer.msg('请至少选择一条记录', {icon: 2});
        return;
      }
      var ids = checkStatus.data.map(function(item){ return item.id; });
      layer.confirm('确定要删除选中的'+ids.length+'条记录吗？', function(index){
        $.post('ajax.php?act=delete', {ids: ids}, function(res){
          if(res.code === 0){
            layer.msg('删除成功', {icon: 1});
            table.reload('dataTable');
          }else{
            layer.msg(res.msg || '删除失败', {icon: 2});
          }
        }, 'json');
        layer.close(index);
      });
    });
    
    // 表单提交事件
    form.on('submit(formSubmit)', function(data){
      var url = data.field.id ? 'ajax.php?act=update' : 'ajax.php?act=add';
      $.post(url, data.field, function(res){
        if(res.code === 0){
          layer.msg(data.field.id ? '更新成功' : '添加成功', {icon: 1});
          layer.closeAll();
          table.reload('dataTable');
        }else{
          layer.msg(res.msg || '操作失败', {icon: 2});
        }
      }, 'json');
      return false;
    });
    
    // 导出Excel按钮
    $('#exportBtn').click(function(){
      var searchParams = form.val('searchForm');
      var params = [];
      for(var key in searchParams){
        if(searchParams[key]){
          params.push(key + '=' + encodeURIComponent(searchParams[key]));
        }
      }
      window.open('ajax.php?act=export&' + params.join('&'));
    });
  });
  </script>
</body>
</html>