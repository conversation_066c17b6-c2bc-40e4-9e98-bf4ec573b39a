<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>网络连接监控</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
    <style>
        .status-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .status-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-success {
            background: #f0f9ff;
            color: #10b981;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background: #fef2f2;
            color: #ef4444;
            border: 1px solid #fecaca;
        }
        
        .status-warning {
            background: #fffbeb;
            color: #f59e0b;
            border: 1px solid #fed7aa;
        }
        
        .status-details {
            margin-top: 10px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 500;
            color: #666;
        }
        
        .detail-value {
            font-weight: bold;
        }
        
        .check-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .check-button:hover {
            background: #40a9ff;
        }
        
        .check-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        
        .overall-status {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .overall-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .overall-summary {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-label {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>网络连接监控</span>
            <div style="float: right;">
                <button class="layui-btn layui-btn-sm" id="check-btn">
                    <i class="layui-icon layui-icon-refresh"></i> 开始检查
                </button>
                <span id="last-check" style="margin-left: 10px; color: #666; font-size: 12px;"></span>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 整体状态 -->
            <div class="overall-status" id="overall-status" style="display: none;">
                <div class="overall-title">系统网络状态</div>
                <div class="overall-summary">
                    <div class="summary-item">
                        <div class="summary-number" id="total-checks">0</div>
                        <div class="summary-label">总检查项</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number" id="success-checks">0</div>
                        <div class="summary-label">成功</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number" id="error-checks">0</div>
                        <div class="summary-label">失败</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number" id="success-rate">0%</div>
                        <div class="summary-label">成功率</div>
                    </div>
                </div>
            </div>
            
            <!-- 检查结果容器 -->
            <div id="check-container">
                <div class="loading" id="initial-message">
                    点击"开始检查"按钮开始网络连接检查...
                </div>
            </div>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <script>
        layui.use(['jquery', 'layer'], function() {
            var $ = layui.jquery;
            var layer = layui.layer;
            
            var isChecking = false;
            
            function updateTime() {
                var now = new Date();
                var timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0') + ':' + 
                             now.getSeconds().toString().padStart(2, '0');
                $('#last-check').text('最后检查: ' + timeStr);
            }
            
            function startNetworkCheck() {
                if (isChecking) return;
                
                isChecking = true;
                $('#check-btn').prop('disabled', true).html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 检查中...');
                
                $('#check-container').html('<div class="loading">正在检查网络连接，请稍候...</div>');
                $('#overall-status').hide();
                
                $.ajax({
                    url: '../../ajax.php?act=network_check',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 60000, // 60秒超时
                    success: function(res) {
                        if (res.code === 1) {
                            renderResults(res.data);
                            updateTime();
                        } else {
                            $('#check-container').html(`
                                <div class="layui-text" style="text-align: center; padding: 40px; color: #f56c6c;">
                                    检查失败: ${res.msg || '未知错误'}
                                </div>
                            `);
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMsg = '网络检查失败';
                        if (status === 'timeout') {
                            errorMsg = '检查超时，请重试';
                        } else if (xhr.responseText) {
                            try {
                                var res = JSON.parse(xhr.responseText);
                                errorMsg = res.msg || errorMsg;
                            } catch(e) {
                                errorMsg = '服务器响应异常';
                            }
                        }
                        
                        $('#check-container').html(`
                            <div class="layui-text" style="text-align: center; padding: 40px; color: #f56c6c;">
                                ${errorMsg}
                            </div>
                        `);
                    },
                    complete: function() {
                        isChecking = false;
                        $('#check-btn').prop('disabled', false).html('<i class="layui-icon layui-icon-refresh"></i> 开始检查');
                    }
                });
            }
            
            function renderResults(data) {
                if (!data || !data.results) {
                    $('#check-container').html(`
                        <div class="layui-text" style="text-align: center; padding: 40px; color: #909399;">
                            未获取到检查结果
                        </div>
                    `);
                    return;
                }
                
                // 显示整体状态
                var summary = data.summary;
                $('#total-checks').text(summary.total);
                $('#success-checks').text(summary.success);
                $('#error-checks').text(summary.error);
                $('#success-rate').text(summary.success_rate + '%');
                $('#overall-status').show();
                
                // 渲染详细结果
                var html = '';
                var results = data.results;
                
                // 数据库状态
                if (results.database) {
                    html += renderStatusCard('数据库连接', results.database, 'MySQL数据库服务状态');
                }
                
                // Redis状态
                if (results.redis) {
                    html += renderStatusCard('Redis连接', results.redis, 'Redis缓存服务状态');
                }
                
                // RabbitMQ状态
                if (results.rabbitmq) {
                    html += renderStatusCard('RabbitMQ连接', results.rabbitmq, '消息队列服务状态');
                }
                
                // 外部网络连接
                if (results.external) {
                    html += '<div class="status-card">';
                    html += '<div class="status-header">';
                    html += '<div class="status-title">外部网络连接</div>';
                    html += '</div>';
                    html += '<div class="status-details">';
                    
                    for (var site in results.external) {
                        var status = results.external[site];
                        html += '<div class="detail-item">';
                        html += '<div class="detail-label">' + site + '</div>';
                        html += '<div class="detail-value">';
                        html += '<span class="status-indicator status-' + status.status + '">';
                        html += status.status === 'success' ? '✓ 正常' : '✗ 异常';
                        html += '</span>';
                        html += '</div>';
                        html += '</div>';
                    }
                    
                    html += '</div>';
                    html += '</div>';
                }
                
                // 代理服务器状态
                if (results.proxy) {
                    html += '<div class="status-card">';
                    html += '<div class="status-header">';
                    html += '<div class="status-title">代理服务器</div>';
                    html += '</div>';
                    html += '<div class="status-details">';
                    
                    if (Array.isArray(results.proxy) || typeof results.proxy === 'object') {
                        for (var proxy in results.proxy) {
                            var status = results.proxy[proxy];
                            html += '<div class="detail-item">';
                            html += '<div class="detail-label">' + proxy + '</div>';
                            html += '<div class="detail-value">';
                            html += '<span class="status-indicator status-' + status.status + '">';
                            html += status.status === 'success' ? '✓ 正常' : (status.status === 'warning' ? '⚠ 警告' : '✗ 异常');
                            html += '</span>';
                            html += '</div>';
                            html += '</div>';
                        }
                    } else {
                        html += '<div class="detail-item">';
                        html += '<div class="detail-label">代理状态</div>';
                        html += '<div class="detail-value">';
                        html += '<span class="status-indicator status-' + results.proxy.status + '">';
                        html += results.proxy.status === 'success' ? '✓ 正常' : (results.proxy.status === 'warning' ? '⚠ 警告' : '✗ 异常');
                        html += '</span>';
                        html += '</div>';
                        html += '</div>';
                    }
                    
                    html += '</div>';
                    html += '</div>';
                }
                
                // 端口连通性
                if (results.ports) {
                    html += '<div class="status-card">';
                    html += '<div class="status-header">';
                    html += '<div class="status-title">端口连通性</div>';
                    html += '</div>';
                    html += '<div class="status-details">';
                    
                    var portNames = {
                        '3306': 'MySQL',
                        '5672': 'RabbitMQ',
                        '6379': 'Redis',
                        '80': 'HTTP',
                        '443': 'HTTPS'
                    };
                    
                    for (var port in results.ports) {
                        var status = results.ports[port];
                        html += '<div class="detail-item">';
                        html += '<div class="detail-label">端口 ' + port + ' (' + (portNames[port] || '未知') + ')</div>';
                        html += '<div class="detail-value">';
                        html += '<span class="status-indicator status-' + status.status + '">';
                        html += status.status === 'success' ? '✓ 开放' : '✗ 关闭';
                        html += '</span>';
                        html += '</div>';
                        html += '</div>';
                    }
                    
                    html += '</div>';
                    html += '</div>';
                }
                
                $('#check-container').html(html);
            }
            
            function renderStatusCard(title, status, description) {
                var html = '<div class="status-card">';
                html += '<div class="status-header">';
                html += '<div class="status-title">' + title + '</div>';
                html += '<div class="status-indicator status-' + status.status + '">';
                html += status.status === 'success' ? '✓ 正常' : (status.status === 'warning' ? '⚠ 警告' : '✗ 异常');
                html += '</div>';
                html += '</div>';
                html += '<div class="status-details">';
                html += '<div class="detail-item">';
                html += '<div class="detail-label">' + description + '</div>';
                html += '<div class="detail-value">' + status.message + '</div>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                return html;
            }
            
            // 绑定检查按钮
            $('#check-btn').on('click', function() {
                startNetworkCheck();
            });
            
            // 页面加载时自动进行一次检查
            // startNetworkCheck();
        });
    </script>
</body>
</html> 