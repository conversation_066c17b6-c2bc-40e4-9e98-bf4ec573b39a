<?php
header('Content-Type: application/json');
include("../includes/common.php");


/*
$h = ['Authorization: EPYON3VGDYZ95O984TOQ9X5EGS4SLDWG','Content-Type: application/json'];
$res = get_curl('http://127.0.0.1:3000/api/sku-query',json_encode(['sku'=>'853881829','limit'=>10,'language'=>'ru']),0,0,0,0,0,$h);

$json = json_decode($res,true);
// $json['items'][0]['categories'][2]['id'];
$typeId = findTypeIdByCategoryAndName2($json['items'][0]['categories'][2]['id'], $json['items'][0]['attributes'][0]['value']);
echo $typeId;


$client = new \lib\OzonDataHandler;
//$client->collectData(1773212065);
$client = $client->processData(1773212065,0,['type_id'=>$typeId??970783339,'description_category_id'=>77119630]);
*/
/*
//$row = $DB->getRow("SELECT A.*,B.ClientId,B.key,B.currency_code FROM ozon_cron A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.sku='1637787088'");
$client = new \lib\OzonApiClient('2740471','a5cf8fb0-734c-4c01-94f0-512e323c1f22');
//$data = $client->OzonApiskufz(['sku'=>1941749468,'offer_id2'=>'shjoosGUISooHJHD','currency_code'=>'CNY','old_price'=>'140','price'=>'140']);
$a = $client->characteristics(['offer_id2'=>'shjoosGUISooHJHD']);//exit(json_encode($a['attributes']));
$a['price'] = '150';
$a['currency_code'] = 'CNY';
$a['title'] = 'Вентилятор01';
$a = $client->productimports($a);


exit(json_encode($data));
*/


//echo get_ozoncurl('https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url=/product/1744626946',0,0,$cookie);exit;
//http://localhost:3000/api/sku-query
$act = $_GET['act'];
if($act=='sku'){
    echo get_curl('http://127.0.0.1:3000/api/sku',json_encode(['sku'=>'2132511969','limit'=>1]),0,0,0,0,0,$h);
}elseif ($act=='v2') {
    $h = ['Authorization: EPYON3VGDYZ95O984TOQ9X5EGS4SLDWG','Content-Type: application/json'];
    // http://*************:5000/api/product?sku=2081062018
    $res = get_curl('http://**************:5000/api/product?sku=2132511969');
    //exit($res);
    $json = json_decode($res,true);
    //echo $json['widgetStates']['webProductHeading-3385933-default-1'];
    $json = json_decode($json['widgetStates']['webProductHeading-3385933-default-1'],true);
    echo $json['title'];exit;
}else{
    $h = ['Authorization: EPYON3VGDYZ95O984TOQ9X5EGS4SLDWG','Content-Type: application/json'];
    echo get_curl('http://127.0.0.1:3000/api/sku-query',json_encode(['sku'=>strval(1744626946),'limit'=>10,'language'=>'ru']),0,0,0,0,0,$h);
}