<div style="padding: 16px;">
  <div class="layui-card">
    <div class="layui-card-header">
      <div class="layui-btn-group">
        <button type="button" class="layui-btn layui-btn-primary" id="btnAddStore">
          <span class="layui-badge-dot"></span> 新增店铺
        </button>
        <button type="button" class="layui-btn layui-btn-primary" id="btnAddGroup">
          <span class="layui-badge-dot"></span> 新增分组
        </button>
      </div>
    </div>
    <div class="layui-card-body">
      <table class="layui-hide" id="storetable" lay-filter="storeTableFilter"></table>
    </div>
  </div>
</div>

<script type="text/html" id="toolbarTpl">
  <div class="layui-btn-group">
    <a class="layui-btn layui-btn-xs" lay-event="upwarehouseedit">更新仓库</a>
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs" lay-event="more">
      更多 <i class="layui-icon layui-icon-down"></i>
    </a>
  </div>
</script>

<script>
layui.use(['table', 'form', 'layer', 'dropdown', 'jquery'], function () {
  var table = layui.table;
  var form = layui.form;
  var layer = layui.layer;
  var dropdown = layui.dropdown;
  var $ = layui.jquery;

  // Render the store table
  table.render({
    elem: '#storetable',
    url: 'ajax.php?act=store_List',
    toolbar: true,
    defaultToolbar: ['filter', 'exports', 'print', {
      title: '提示',
      layEvent: 'LAYTABLE_TIPS',
      icon: 'layui-icon-tips'
    }],
    height: 'full-150',
    cellMinWidth: 80,
    page: true,
    cols: [[
      { type: 'checkbox', fixed: 'left' },
      { field: 'ClientId', title: 'ClientId', width: 120, sort: true, fixed: 'left' },
      { field: 'storename', title: '店铺名称', width: 150 },
      { field: 'currency_code', title: '币种', width: 80 },
      {
        field: 'daily_create',
        title: '每日限额(上传更新)',
        width: 280,
        templet: function (d) {
          return '总限额: ' + d.limit + ' / 添: ' + d.daily_create + ' / 改: ' + d.daily_update;
        }
      },
      {
        field: 'actions',
        title: '自动关闭活动',
        width: 130,
        templet: function (d) {
          return '<input type="checkbox" name="auto_close" lay-skin="switch" ' +
            (d.actions == 1 ? 'checked' : '') +
            ' lay-filter="auto_close_switch" data-id="' + d.id + '">';
        }
      },
      { field: 'Groupname', title: '分组名称', width: 120 },
      {
        field: 'notice',
        title: '通知链接',
        width: 120,
        templet: function () {
          return '<a class="layui-btn layui-btn-xs copy-notice" style="background-color:#1E9FFF;color:white" data-url="https://www.100b.cn/api/notice"><i class="layui-icon layui-icon-link"></i> 复制</a>';
        }
      },
      { field: 'cost', title: '采购总花销', width: 120 },
      { field: 'price', title: '总销售金额', width: 120 },
      {
        field: 'addtime',
        title: '店铺添加时间',
        width: 180,
        templet: function (d) {
          return d.addtime + '<br/>' + (d.time || '');
        }
      },
      { fixed: 'right', title: '操作', toolbar: '#toolbarTpl', width: 220 }
    ]],
    done: function () {
      form.render('checkbox');
    }
  });

  // Add store button click
  $('#btnAddStore').on('click', function () {
    openFormPopup('新增店铺', getAddStoreFormHtml(), 'ajax.php?act=store_add');
  });

  // Add group button click
  $('#btnAddGroup').on('click', function () {
    openFormPopup('新增分组', getAddGroupFormHtml(), 'ajax.php?act=addUserGroup');
  });

  // Open form popup helper
  function openFormPopup(title, content, url) {
    var index = layer.open({
      type: 1,
      area: '400px',
      title: title,
      content: content,
      success: function () {
        form.render();
        form.on('submit(formSubmit)', function (data) {
          $.ajax({
            url: url,
            type: 'POST',
            dataType: 'json',
            data: data.field,
            success: function (res) {
              layer.msg(res.msg);
              if (res.code == 1) {
                layer.close(index);
                table.reload('storetable');
              }
            },
            error: function () {
              layer.msg('请求失败，请检查网络');
            }
          });
          return false;
        });
      }
    });
  }

  // Add store form HTML with group select
  function getAddStoreFormHtml() {
    return `
      <form class="layui-form" lay-filter="addStoreForm" style="padding: 20px;">
        <div class="layui-form-item">
          <label class="layui-form-label">店铺名称</label>
          <div class="layui-input-block">
            <input type="text" name="name" required lay-verify="required" placeholder="请输入店铺名称" autocomplete="off" class="layui-input" />
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">ClientId</label>
          <div class="layui-input-block">
            <input type="text" name="ClientId" required lay-verify="required" placeholder="请输入ClientId" autocomplete="off" class="layui-input" />
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">API密匙</label>
          <div class="layui-input-block">
            <input type="text" name="key" required lay-verify="required" placeholder="请输入API密匙" autocomplete="off" class="layui-input" />
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">Cookies</label>
          <div class="layui-input-block">
            <input type="text" name="cookie" required lay-verify="required" placeholder="请输入Cookies" autocomplete="off" class="layui-input" />
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">币种</label>
          <div class="layui-input-block">
            <select name="money" required lay-verify="required">
              <option value="">请选择币种</option>
              <option value="1">CNY</option>
              <option value="2">RUB</option>
              <option value="3">USD</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">分组</label>
          <div class="layui-input-block">
            <select name="group_id" lay-verify="required" lay-search id="groupSelect">
              <option value="">请选择分组</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="formSubmit">保存</button>
        </div>
      </form>
    `;
  }

  // Edit store form HTML with group select
  function getEditStoreFormHtml(data) {
    return `
      <form class="layui-form" lay-filter="editStoreForm" style="padding: 20px;">
        <input type="hidden" name="id" value="${data.id}" />
        <div class="layui-form-item">
          <label class="layui-form-label">店铺名称</label>
          <div class="layui-input-block">
            <input type="text" name="name" value="${data.storename}" required lay-verify="required" placeholder="请输入店铺名称" autocomplete="off" class="layui-input" />
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">ClientId</label>
          <div class="layui-input-block">
            <input type="text" name="ClientId" value="${data.ClientId}" required lay-verify="required" placeholder="请输入ClientId" autocomplete="off" class="layui-input" readonly />
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">API密匙</label>
          <div class="layui-input-block">
            <input type="text" name="key" value="${data.key}" required lay-verify="required" placeholder="请输入API密匙" autocomplete="off" class="layui-input" />
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">Cookies</label>
          <div class="layui-input-block">
            <input type="text" name="cookie" placeholder="请输入Cookies" autocomplete="off" class="layui-input" />
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">币种</label>
          <div class="layui-input-block">
            <select name="money" required lay-verify="required">
              <option value="">请选择币种</option>
              <option value="1" ${data.currency_code === 'CNY' ? 'selected' : ''}>CNY</option>
              <option value="2" ${data.currency_code === 'RUB' ? 'selected' : ''}>RUB</option>
              <option value="3" ${data.currency_code === 'USD' ? 'selected' : ''}>USD</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">分组</label>
          <div class="layui-input-block">
            <select name="group_id" lay-verify="required" lay-search id="editGroupSelect">
              <option value="">请选择分组</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="formSubmit">保存</button>
        </div>
      </form>
    `;
  }

  // Listen to form submit events for add/edit store and add group
  form.on('submit(formSubmit)', function (data) {
    var url = '';
    if (data.form && data.form.id) {
      url = 'ajax.php?act=store_edit';
    } else if (data.form && data.form.group_name !== undefined) {
      url = 'ajax.php?act=addUserGroup';
    } else {
      url = 'ajax.php?act=store_add';
    }
    $.ajax({
      url: url,
      type: 'POST',
      dataType: 'json',
      data: data.field,
      success: function (res) {
        layer.msg(res.msg);
        if (res.code == 1) {
          table.reload('storetable');
          layer.closeAll('page');
        }
      },
      error: function () {
        layer.msg('请求失败，请检查网络');
      }
    });
    return false;
  });

  // Listen to auto_close switch toggle
  form.on('switch(auto_close_switch)', function (obj) {
    var id = $(this).data('id');
    var status = obj.elem.checked ? 1 : 0;
    layer.load(2);
    $.ajax({
      url: 'ajax.php?act=store_switch',
      type: 'POST',
      data: { id: id, status: status },
      success: function (res) {
        layer.closeAll('loading');
        if (res.code != 0) {
          layer.msg(res.msg || '操作失败');
          obj.elem.checked = !status;
          form.render('checkbox');
        }
      },
      error: function () {
        layer.closeAll('loading');
        layer.msg('请求失败，请检查网络');
        obj.elem.checked = !status;
        form.render('checkbox');
      }
    });
  });

  // Copy notice link
  $(document).on('click', '.copy-notice', function () {
    var url = $(this).data('url');
    if (navigator.clipboard) {
      navigator.clipboard.writeText(url).then(function () {
        layer.msg('<span style="color:#5FB878">✓</span> 复制成功', { time: 1000, isHtml: true });
      }).catch(function () {
        legacyCopy(url);
      });
    } else {
      legacyCopy(url);
    }

    function legacyCopy(text) {
      var $temp = $('<textarea>').val(text).css({ position: 'fixed', left: '-9999px', top: '-9999px' }).appendTo('body').select();
      try {
        document.execCommand('copy');
        layer.msg('<span style="color:#5FB878">✓</span> 复制成功', { time: 1000, isHtml: true });
      } catch (err) {
        layer.msg('<span style="color:#FF5722">✗</span> 请手动复制：' + text, { time: 3000, isHtml: true });
      } finally {
        $temp.remove();
      }
    }
  });
  // 页面加载完成后，异步加载分组数据并渲染到新增和编辑表单的分组下拉框
  layui.use(['form', 'jquery'], function () {
    var form = layui.form;
    var $ = layui.jquery;

    function loadGroups() {
      $.ajax({
        url: 'ajax.php?act=getShopGroups',
        type: 'GET',
        dataType: 'json',
        success: function (res) {
          if (res.code === 0) {
            var groups = res.data;
            var $addSelect = $('#groupSelect');
            var $editSelect = $('#editGroupSelect');
            $addSelect.empty().append('<option value="">请选择分组</option>');
            $editSelect.empty().append('<option value="">请选择分组</option>');
            groups.forEach(function (group) {
              var $option = $('<option>').val(group.id).text(group.name);
              $addSelect.append($option);
              $editSelect.append($option.clone());
            });
            form.render('select');
          } else {
            layer.msg('获取分组失败');
          }
        },
        error: function () {
          layer.msg('请求分组失败，请检查网络');
        }
      });
    }

    loadGroups();

    // 编辑表单打开时，设置选中的分组
    var originalOpenFormPopup = window.openFormPopup;
    window.openFormPopup = function (title, content, url) {
      originalOpenFormPopup(title, content, url);
      if (title.startsWith('编辑店铺')) {
        var interval = setInterval(function () {
          var $select = $('#editGroupSelect');
          if ($select.length && $select.find('option').length > 1) {
            // 取出当前编辑店铺的分组ID
            var groupId = null;
            try {
              var data = JSON.parse(content);
              groupId = data.group_id;
            } catch (e) {
              // 解析失败，忽略
            }
            if (groupId !== null) {
              $select.val(groupId);
              form.render('select');
            }
            clearInterval(interval);
          }
        }, 100);
      }
    };
  });
});
</create_file>
