<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商品管理</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
    <style>
        .product-count-badge {
            background: #ff5722;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 12px;
            margin-left: 5px;
            display: inline-block;
            min-width: 20px;
            text-align: center;
        }
        .product-count-badge.hidden {
            display: none;
        }
        .layui-form-pane .layui-form-label {
            background-color: #fafafa;
        }
        .layui-input, .layui-select {
            height: 38px;
        }
        
        /* 表格行样式 */
        .layui-table-body tr {
            height: 140px;
        }
        
        .layui-table-cell {
            height: auto !important;
            overflow: visible !important;
        }

        .layui-table-body td {
            padding: 10px !important;
            vertical-align: top !important;
        }
    </style>
</head>
<body class="pear-container">
    <!-- 返回顶部按钮 -->
    <button id="layuiBackToTop" class="layui-btn layui-btn-danger layui-btn-radius" style="position: fixed; right: 20px; bottom: 70px; z-index: 99999; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
        <i class="layui-icon layui-icon-top" style="font-size: 18px; color: #fff;"></i>
    </button>

    <div class="product-page">
        <div style="padding: 10px;">
            <div class="layui-card">
                <!-- 筛选表单 -->
                <div class="layui-row" style="margin-top: 15px;">
                    <div class="layui-col-md12">
                        <form class="layui-form layui-form-pane" lay-filter="productForm">
                            <div class="layui-inline">
                                <input type="text" name="text" placeholder="商品名/SKU/Offer ID/标题" class="layui-input"
                                    autocomplete="off" style="width: 300px;">
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">价格区间</label>
                                <div class="layui-input-inline" style="width: 80px;">
                                    <input type="text" name="price_min" placeholder="最低价" class="layui-input"
                                        autocomplete="off">
                                </div>
                                <div class="layui-input-inline" style="width: 80px;">
                                    <input type="text" name="price_max" placeholder="最高价" class="layui-input"
                                        autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">库存区间</label>
                                <div class="layui-input-inline" style="width: 80px;">
                                    <input type="text" name="stock_min" placeholder="最低库存" class="layui-input"
                                        autocomplete="off">
                                </div>
                                <div class="layui-input-inline" style="width: 80px;">
                                    <input type="text" name="stock_max" placeholder="最高库存" class="layui-input"
                                        autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">店铺</label>
                                <div class="layui-input-inline" style="width: 150px;">
                                    <select name="storeid" lay-search id="storeSelect">
                                        <option value="">所有店铺</option>
                                    </select>
                                </div>
                            </div>
                            <!-- 用户筛选 -->
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">所属用户</label>
                                <div class="layui-input-inline" style="width: 150px;">
                                    <select name="uid" lay-search id="userSelect">
                                        <option value="">所有用户</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 85px;">创建日期</label>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="text" class="layui-input" name="date1" id="date1" placeholder="yyyy-MM-dd">
                                </div>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="text" class="layui-input" name="date2" id="date2" placeholder="yyyy-MM-dd">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                                <button type="reset" class="layui-btn layui-btn-primary" lay-filter="reset">重置</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 状态导航Tab -->
                <div class="layui-tab layui-tab-brief" lay-filter="productTab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">
                            全部商品
                            <span class="product-count-badge" id="count-all">0</span>
                        </li>
                        <li>
                            正常销售
                            <span class="product-count-badge" id="count-published">0</span>
                        </li>
                        <li>
                            处理中
                            <span class="product-count-badge" id="count-processing">0</span>
                        </li>
                        <li>
                            已归档
                            <span class="product-count-badge" id="count-archived">0</span>
                        </li>
                        <li>
                            审核中
                            <span class="product-count-badge" id="count-moderating">0</span>
                        </li>
                        <li>
                            已下架
                            <span class="product-count-badge" id="count-removed">0</span>
                        </li>
                        <li>
                            库存不足
                            <span class="product-count-badge" id="count-low_stock">0</span>
                        </li>
                    </ul>
                </div>
                
                <!-- 数据表格 -->
                <div class="layui-col-md12">
                    <table class="layui-hide" id="product-table" lay-filter="productTable"></table>
                </div>
            </div>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <!-- 表格工具栏模板 -->
    <script type="text/html" id="toolbar">
        <div class="layui-btn-container" style="display: flex; align-items: center;">
            <div class="layui-form" style="display: inline-flex; align-items: center; margin-right: 10px;">
                <input type="checkbox" id="selectAll" lay-skin="primary" title="全选" lay-filter="selectAll">
            </div>
            <button class="layui-btn layui-btn-sm" lay-event="exportProducts">导出商品</button>
            <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="batchArchive">批量归档</button>
            <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batchDelete">批量删除</button>
            <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="refresh">刷新</button>
        </div>
    </script>
    
    <!-- 表格操作按钮模板 -->
    <script type="text/html" id="rowToolbar">
        <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="sync">同步</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
    </script>
    
    <!-- 商品信息模板 -->
    <script type="text/html" id="productInfo-tpl">
        <div style="display: flex; align-items: flex-start; padding: 5px 0;">
            <div style="width: 120px; height: 120px; flex-shrink: 0; display: flex; align-items: center; justify-content: center; border: 1px solid #eee; border-radius: 4px; overflow: hidden;">
                <img src="{{ d.primary_image || '../assets/img/syncing.png' }}" style="max-width: 100%; max-height: 100%; object-fit: contain;">
            </div>
            <div style="margin-left: 15px; flex-grow: 1; min-width: 0;">
                <p style="font-weight: 500; word-break: break-all; white-space: normal; margin-bottom: 8px; line-height: 1.4;">{{ d.display_name || d.name || '-' }}</p>
                <p style="color: #666; font-size: 12px; margin-bottom: 4px;">ID: {{ d.id }}</p>
                <p style="color: #666; font-size: 12px;">SKU: {{ d.sku || '-' }}</p>
            </div>
        </div>
    </script>

    <!-- 商品标识模板 -->
    <script type="text/html" id="productIdentity-tpl">
        <div>
            <p style="margin-bottom: 8px;"><strong>Offer ID:</strong> {{ d.offer_id || '-' }}</p>
            <p style="margin-bottom: 8px;"><strong>用户:</strong> {{ d.username || '-' }}</p>
            <p><strong>店铺:</strong> {{ d.storename || '-' }}</p>
        </div>
    </script>

    <!-- 价格库存模板 -->
    <script type="text/html" id="priceStock-tpl">
        <div>
            <p style="margin-bottom: 8px;">价格: <span style="color: #FF5722; font-weight: bold;">{{ d.price ? '₽' + d.price : '-' }}</span></p>
            <p>库存: <span style="font-weight: bold;">{{ d.stock_count || '-' }}</span></p>
        </div>
    </script>

    <!-- 状态时间模板 -->
    <script type="text/html" id="statusTime-tpl">
        <div>
            <span>{{ d.status_html }}</span>
            <p style="color: #999; font-size: 12px; margin-top: 5px;">{{ d.created_at || '-' }}</p>
        </div>
    </script>

    <script>
        layui.use(['table', 'layer', 'jquery', 'form', 'laydate', 'element'], function() {
            var table = layui.table;
            var layer = layui.layer;
            var $ = layui.jquery;
            var form = layui.form;
            var laydate = layui.laydate;
            var element = layui.element;
            
            // 全局变量保存当前状态和搜索条件
            var currentStatus = 'all';
            var text = '';
            var price_min = '';
            var price_max = '';
            var stock_min = '';
            var stock_max = '';
            var storeid = '';
            var uid = '';
            var date1 = '';
            var date2 = '';
            
            // 返回顶部按钮
            $('#layuiBackToTop').on('click', function() {
                $('html, body').animate({ scrollTop: 0 }, 'smooth');
                return false;
            });
            
            // 渲染日期选择器
            laydate.render({
                elem: '#date1',
                format: 'yyyy-MM-dd'
            });
            laydate.render({
                elem: '#date2',
                format: 'yyyy-MM-dd'
            });
            
            // 初始化店铺下拉框
            function initStoreSelect() {
                $.ajax({
                    url: '/admin/ajax.php?act=getShops',
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            var html = '<option value="">所有店铺</option>';
                            res.data.forEach(shop => {
                                html += `<option value="${shop.id}">${shop.storename}</option>`;
                            });
                            $('#storeSelect').html(html);
                            form.render('select');
                        }
                    },
                    error: function() {
                        $('#storeSelect').html('<option value="">加载失败</option>');
                        form.render('select');
                    }
                });
            }
            
            // 初始化用户下拉框
            function initUserSelect() {
                $.ajax({
                    url: '/admin/ajax.php?act=getUsers',
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            var $select = $('#userSelect');
                            $select.empty();
                            $select.append('<option value="">所有用户</option>');
                            res.data.forEach(function (user) {
                                $select.append('<option value="' + user.uid + '">' + user.username + ' (UID:' + user.uid + ')</option>');
                            });
                            form.render('select');
                        }
                    },
                    error: function() {
                        $('#userSelect').html('<option value="">加载失败</option>');
                        form.render('select');
                    }
                });
            }
            
            // 初始化商品状态统计
            function initProductStatusCounts() {
                $.ajax({
                    url: '/admin/ajax.php?act=product_status_counts',
                    type: 'GET',
                    success: function (res) {
                        if (res.code === 0 && res.data) {
                            updateProductStatusCounts(res.data);
                        }
                    },
                    error: function (xhr) {
                        console.log('获取状态统计失败:', xhr.statusText);
                    }
                });
            }
            
            // 更新商品状态统计数字
            function updateProductStatusCounts(counts) {
                Object.keys(counts).forEach(function(status) {
                    var count = counts[status] || 0;
                    var $badge = $('#count-' + status);
                    if ($badge.length > 0) {
                        $badge.text(count);
                        if (count === 0) {
                            $badge.addClass('hidden');
                        } else {
                            $badge.removeClass('hidden');
                        }
                    }
                });
            }
            
            // 统一渲染表格
            function renderTable(status, searchText, priceMin, priceMax, stockMin, stockMax, storeId, userId, startDate, endDate) {
                var tableIns = table.render({
                    elem: '#product-table',
                    url: '/admin/ajax.php?act=product_list',
                    toolbar: '#toolbar',
                    defaultToolbar: ['filter', 'print', 'exports'],
                    where: {
                        status: status,
                        text: searchText || '',
                        price_min: priceMin || '',
                        price_max: priceMax || '',
                        stock_min: stockMin || '',
                        stock_max: stockMax || '',
                        storeid: storeId || '',
                        uid: userId || '',
                        date1: startDate || '',
                        date2: endDate || ''
                    },
                    cols: [[
                        {type: 'checkbox', width: 50},
                        {title: '商品信息', width: 480, templet: '#productInfo-tpl'},
                        {title: '商品标识', width: 200, templet: '#productIdentity-tpl'},
                        {title: '价格库存', width: 140, templet: '#priceStock-tpl'},
                        {title: '状态时间', width: 160, templet: '#statusTime-tpl'},
                        {title: '操作', width: 180, toolbar: '#rowToolbar', align: 'center'}
                    ]],
                    page: true,
                    limit: 20,
                    limits: [10, 20, 50, 100],
                    loading: true,
                    even: true,
                    done: function(res, curr, count) {
                        // 预处理数据
                        if (res.data) {
                            res.data.forEach(function(item) {
                                // 处理库存数据
                                if (item.stocks) {
                                    try {
                                        var stocks = JSON.parse(item.stocks);
                                        item.stock_count = stocks.stocks && stocks.stocks[0] ? stocks.stocks[0].present : '-';
                                    } catch(e) {
                                        item.stock_count = '-';
                                    }
                                } else {
                                    item.stock_count = '-';
                                }
                                
                                // 处理商品名称截取
                                if (item.name && item.name.length > 40) {
                                    item.display_name = item.name.substring(0, 40) + '...';
                                } else {
                                    item.display_name = item.name;
                                }
                                
                                // 处理状态HTML
                                if (item.status_info && item.status_info.indexOf('Продается') > -1) {
                                    item.status_html = '<span class="layui-badge layui-bg-green">正常销售</span>';
                                } else if (item.is_archived == 1 || item.is_autoarchived == 1) {
                                    item.status_html = '<span class="layui-badge layui-bg-gray">已归档</span>';
                                } else if (item.status_description && item.status_description.indexOf('На модерации') > -1) {
                                    item.status_html = '<span class="layui-badge layui-bg-blue">审核中</span>';
                                } else if (item.status_description && item.status_description.indexOf('Убран из продажи') > -1) {
                                    item.status_html = '<span class="layui-badge layui-bg-red">已下架</span>';
                                } else if (item.status_info && item.status_info.indexOf('Готов к продаже') > -1) {
                                    item.status_html = '<span class="layui-badge layui-bg-orange">库存不足</span>';
                                } else {
                                    item.status_html = '<span class="layui-badge layui-bg-cyan">处理中</span>';
                                }
                            });
                        }
                        
                        // 重新渲染表单元素
                        form.render();
                        
                        // 更新统计
                        initProductStatusCounts();
                    }
                });
                
                return tableIns;
            }
            
            // 初始化表格
            var tableIns = renderTable(currentStatus, text, price_min, price_max, stock_min, stock_max, storeid, uid, date1, date2);
            
            // 搜索功能
            form.on('submit(search)', function (data) {
                var field = data.field;
                text = field.text || '';
                price_min = field.price_min || '';
                price_max = field.price_max || '';
                stock_min = field.stock_min || '';
                stock_max = field.stock_max || '';
                storeid = field.storeid || '';
                uid = field.uid || '';
                date1 = field.date1 || '';
                date2 = field.date2 || '';

                renderTable(currentStatus, text, price_min, price_max, stock_min, stock_max, storeid, uid, date1, date2);
                return false;
            });
            
            // 重置功能
            form.on('reset(productForm)', function () {
                text = '';
                price_min = '';
                price_max = '';
                stock_min = '';
                stock_max = '';
                storeid = '';
                uid = '';
                date1 = '';
                date2 = '';
                
                renderTable(currentStatus, text, price_min, price_max, stock_min, stock_max, storeid, uid, date1, date2);
            });
            
            // 状态Tab切换
            element.on('tab(productTab)', function (data) {
                const statusList = ['all', 'published', 'processing', 'archived', 'moderating', 'removed', 'low_stock'];
                currentStatus = statusList[data.index];
                
                var loadingIndex = layer.load(2, {shade: [0.3, '#fff']});
                
                setTimeout(function() {
                    renderTable(currentStatus, text, price_min, price_max, stock_min, stock_max, storeid, uid, date1, date2);
                    layer.close(loadingIndex);
                }, 100);
            });
            
            // 监听表格工具条
            table.on('tool(productTable)', function(obj) {
                var data = obj.data;
                
                if (obj.event === 'view') {
                    var imgSrc = data.primary_image || '../assets/img/syncing.png';
                    var stockInfo = data.stock_count || '-';
                    
                    var content = `
                        <div style="padding: 20px; max-height: 450px; overflow-y: auto;">
                            <div class="layui-form-item" style="text-align: center; margin-bottom: 20px;">
                                <img src="${imgSrc}" style="max-width: 300px; max-height: 300px; object-fit: contain; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">商品ID:</label>
                                <div class="layui-input-inline">${data.id}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">商品名称:</label>
                                <div style="margin-left: 110px; word-break: break-all;">${data.name || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Offer ID:</label>
                                <div class="layui-input-inline">${data.offer_id || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">SKU:</label>
                                <div class="layui-input-inline">${data.sku || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">所属用户:</label>
                                <div class="layui-input-inline">${data.username || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">店铺:</label>
                                <div class="layui-input-inline">${data.storename || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">价格:</label>
                                <div class="layui-input-inline">${data.price ? '₽' + data.price : '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">库存:</label>
                                <div class="layui-input-inline">${stockInfo}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">状态描述:</label>
                                <div class="layui-input-inline">${data.status_description || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">创建时间:</label>
                                <div class="layui-input-inline">${data.created_at || '-'}</div>
                            </div>
                        </div>
                    `;
                    
                    layer.open({
                        type: 1,
                        title: '商品详情 - ' + (data.name || data.offer_id),
                        content: content,
                        area: ['600px', '550px'],
                        shadeClose: true
                    });
                } else if (obj.event === 'sync') {
                    layer.load(2);
                    $.ajax({
                        url: '/admin/ajax.php?act=sync_product',
                        type: 'POST',
                        data: { id: data.id },
                        success: function (res) {
                            layer.closeAll();
                            if (res.code === 0) {
                                layer.msg('同步成功', { icon: 1 });
                                tableIns.reload();
                            } else {
                                layer.msg(res.msg || '同步失败', { icon: 2 });
                            }
                        },
                        error: function (xhr) {
                            layer.closeAll();
                            layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                        }
                    });
                } else if (obj.event === 'delete') {
                    layer.confirm('确定要删除这个商品吗？', {icon: 3, title: '提示'}, function(index) {
                        $.ajax({
                            url: '/admin/ajax.php?act=product_delete',
                            type: 'POST',
                            data: {id: data.id},
                            dataType: 'json',
                            success: function(res) {
                                if (res.code === 1) {
                                    layer.msg('删除成功', {icon: 1});
                                    tableIns.reload();
                                    initProductStatusCounts();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('网络异常', {icon: 2});
                            }
                        });
                        layer.close(index);
                    });
                }
            });
            
            // 监听表格头部工具栏
            table.on('toolbar(productTable)', function(obj) {
                if (obj.event === 'exportProducts') {
                    var checkStatus = table.checkStatus('productTable');
                    var data = checkStatus.data;
                    if (data.length === 0) {
                        layer.msg('请选择要导出的数据', { icon: 2 });
                        return;
                    }
                    layer.msg('导出功能开发中...', { icon: 2 });
                } else if (obj.event === 'batchArchive') {
                    var checkStatus = table.checkStatus('productTable');
                    var data = checkStatus.data;
                    if (data.length === 0) {
                        layer.msg('请选择要归档的商品', { icon: 2 });
                        return;
                    }
                    layer.msg('批量归档功能开发中...', { icon: 2 });
                } else if (obj.event === 'batchDelete') {
                    var checkStatus = table.checkStatus('productTable');
                    var data = checkStatus.data;
                    if (data.length === 0) {
                        layer.msg('请选择要删除的商品', { icon: 2 });
                        return;
                    }
                    
                    layer.confirm('确定要删除选中的 ' + data.length + ' 个商品吗？', {
                        icon: 3,
                        title: '批量删除确认',
                        btn: ['确定', '取消']
                    }, function (index) {
                        layer.close(index);
                        
                        var ids = data.map(function(item) {
                            return item.id;
                        });
                        
                        var loadingIndex = layer.load(2, {
                            shade: [0.3, '#fff'],
                            content: '正在删除商品...'
                        });
                        
                        $.ajax({
                            url: '/admin/ajax.php?act=batch_delete_products',
                            type: 'POST',
                            data: { ids: ids.join(',') },
                            success: function (res) {
                                layer.close(loadingIndex);
                                if (res.code === 0) {
                                    layer.msg('批量删除成功！', { icon: 1 });
                                    tableIns.reload();
                                    initProductStatusCounts();
                                } else {
                                    layer.msg(res.msg || '批量删除失败', { icon: 2 });
                                }
                            },
                            error: function (xhr) {
                                layer.close(loadingIndex);
                                layer.msg('请求失败: ' + xhr.statusText, { icon: 2 });
                            }
                        });
                    });
                } else if (obj.event === 'refresh') {
                    tableIns.reload();
                    initProductStatusCounts();
                    layer.msg('刷新成功', { icon: 1 });
                }
            });
            
            // 全选功能
            form.on('checkbox(selectAll)', function(data) {
                var checked = data.elem.checked;
                $('input[type="checkbox"][lay-filter="productCheckbox"]').prop('checked', checked);
                form.render('checkbox');
            });
            
            // 初始化
            initStoreSelect();
            initUserSelect();
            initProductStatusCounts();
        });
    </script>
</body>
</html> 