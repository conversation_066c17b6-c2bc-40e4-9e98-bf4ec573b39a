<?php
define('ROOT', __DIR__ . '/');

// 确保包含必要的文件
$indexFile = ROOT . 'storage/category_index.php';
if (!file_exists($indexFile)) {
    die("错误：索引文件 {$indexFile} 不存在");
}

$index = include $indexFile;

echo "分类索引检查报告:\n";
echo "总分类数: " . count($index) . "\n\n";

// 显示前10个分类
echo "示例分类:\n";
$counter = 0;
foreach ($index as $id => $data) {
    if ($counter++ >= 10) break;
    echo "[{$id}] " . $data['name'] . "\n";
    echo "路径: " . ($data['path'] ?? '无路径信息') . "\n\n";
}

// 检查文件是否存在
echo "\n文件完整性检查:\n";
$missing = 0;
$storageDir = ROOT . 'assets/ozon/categories/';

if (!is_dir($storageDir)) {
    die("错误：分类目录 {$storageDir} 不存在");
}

foreach ($index as $id => $data) {
    $file = $storageDir . $id . '.json';
    if (!file_exists($file)) {
        echo "缺失文件: {$id}.json\n";
        $missing++;
    }
}

echo "\n检查完成! ";
echo $missing ? "发现 {$missing} 个缺失文件" : "所有文件完整存在";