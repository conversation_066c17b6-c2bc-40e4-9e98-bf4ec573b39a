<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
include("../includes/common.php");
use setasign\Fpdi\Tcpdf\Fpdi;

require_once('../includes/vendor/tecnickcom/tcpdf/tcpdf.php');
$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;

if (!checkRefererHost())
    exit('{"code":403}');
if ($act != 'login' and $act != 'reg') {
    if ($islogin2 == 1) {
    } else
        exit('{"code":-3,"msg":"No Login"}');
}
@header('Content-Type: application/json; charset=UTF-8');


switch ($act) {
        case 'order_status_counts':
    // 获取各个订单状态的统计数量
    $counts = [];

    // 待处理 (所有未完成的订单：未采购+已采购但未发货)
    $counts['pending'] =  $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver2', 'awaiting_deliver', 'cancelled_from_split_pending', 'awaiting_registration')", [$uid]);

    // 未采购 (成本为空且未取消、未送达、未运输、未发货)
    $counts['not_purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND (cost IS NULL OR cost = '') AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver2', 'cancelled_from_split_pending', 'awaiting_verification')", [$uid]);

    // 已采购 (有成本但还未进入发货流程的订单)
    $counts['purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND cost IS NOT NULL AND cost != ''  AND status NOT IN ('cancelled', 'delivered', 'delivering',  'awaiting_deliver2','awaiting_deliver' ,'cancelled_from_split_pending', 'awaiting_registration')", [$uid]);
    
    // 未上传护照
    $counts['awaiting_verification'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'awaiting_verification'", [$uid]);
    
   /* $counts['purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND cost IS NOT NULL AND cost != '' AND status = 'awaiting_packaging' AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2', 'awaiting_registration')", [$uid]);*/
    
    
/*
    // 移交给快递
    $counts['awaiting_registration'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'awaiting_registration'", [$uid]);

    // 未发货 (已采购但还未申请备货的订单，即awaiting_packaging状态)
    $counts['not_shipped'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND (status = 'awaiting_packaging' OR status = 'awaiting_registration')", [$uid]);
*/
    // 等待发货
    $counts['awaiting_deliver'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'awaiting_deliver' AND packing_status = 0 AND cost!= 'NULL'", [$uid]);

    // 交运平台
    $counts['awaiting_deliver2'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'awaiting_deliver' AND packing_status = 1", [$uid]);

    // 运输中
    $counts['delivering'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'delivering'", [$uid]);

    // 已送达
    $counts['delivered'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'delivered'", [$uid]);

    // 已取消
    $counts['cancelled'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'cancelled'", [$uid]);

    // 全部订单 (除已取消)
    $counts['all'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND (status!='cancelled' OR status!='cancelled_from_split_pending')", [$uid]);

    exit(json_encode(['code' => 0, 'data' => $counts]));
break;

case 'orders_all':
    $groupid = isset($_GET['groupid']) ? intval($_GET['groupid']) : 0;
    $status = trim($_GET['status']);
    $text = trim($_GET['text']);
    $title = trim($_GET['title']);
    $money = trim($_GET['money']);
    $moneys = trim($_GET['moneys']);
    $storeid = trim($_GET['storeid']);
    $date1 = trim($_GET['date1']);
    $date2 = trim($_GET['date2']);
    $groupid = trim($_GET['groupid']); // 获取分组ID
    $sql = " A.uid={$uid}";
    // 分组筛选逻辑
    if ($groupid && $groupid !== '') {
        $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
        $shopIdsStr = '0'; // 默认无匹配店铺
        if ($user && $user['shop_groups']) {
            $shopGroups = json_decode($user['shop_groups'], true);
            $groupShopIds = [];
            foreach ($shopGroups['groups'] as $group) {
                if ($group['id'] == $groupid) {
                    $groupShopIds = $group['shopIds'];
                    break;
                }
            }
            if (!empty($groupShopIds)) {
                $shopIdsStr = implode(',', $groupShopIds);
            }
        }
        $sql .= " AND A.storeid IN ({$shopIdsStr})";
    }
    if ($status and !$posting_number) {
        if ($status == 'pending') {
            // 待处理：所有未完成的订单
           $sql .= " AND A.status NOT IN ('cancelled', 'delivered', 'delivering',  'awaiting_deliver2', 'cancelled_from_split_pending')";
        } else if ($status == 'not_purchased') {
            // 未采购：成本为空且未完成
            $sql .= " AND (A.cost IS NULL OR A.cost = '') AND A.status NOT IN ('cancelled', 'delivered', 'delivering',  'awaiting_deliver2', 'cancelled_from_split_pending', 'awaiting_verification')";
        } else if ($status == 'purchased') {
            // 已采购：有成本但还未进入发货流程的订单
            $sql .= " AND A.cost IS NOT NULL AND A.cost != ''AND A.status NOT IN ('cancelled', 'delivered', 'delivering',  'awaiting_deliver2', 'cancelled_from_split_pending','awaiting_deliver','awaiting_registration')";
        } else if ($status == 'not_shipped') {
            // 未发货：已采购但还未申请备货的订单
            $sql .= " AND A.status = 'awaiting_packaging'";
        } else if ($status == 'all') {
            $sql .= " AND (A.status!='cancelled' OR A.status!='cancelled_from_split_pending')";
        } else if ($status == 'awaiting_deliver') {
            //等待发货状态：必须是awaiting_deliver状态、打包状态为0(未交运)且已有采购成本(已完成采购)
            //$sql .= " AND (A.status='awaiting_deliver' OR A.status='awaiting_registration') AND A.packing_status=0";
             $sql .= " AND (A.status='awaiting_deliver' OR A.status='awaiting_registration') AND A.packing_status=0 AND A.cost IS NOT NULL AND A.cost != ''";
        } else if ($status == 'awaiting_deliver2') {
            $sql .= " AND A.status='awaiting_deliver' AND A.packing_status=1";
        } else if($status == 'cancelled'){
            $sql .= " AND (A.status='cancelled' OR A.status='cancelled_from_split_pending')";
        } else {
            $sql .= " AND A.status='{$status}'";
        }
    }
    if ($text) {
        $sql .= " AND (A.order_name LIKE '%{$text}%' OR A.name2 LIKE '%{$text}%' OR A.posting_number='{$text}' OR A.sku='{$text}' OR A.purchase_orderSn='{$text}' OR A.courierNumber LIKE '%{$text}%' OR A.tracking_number='{$text}')";
    }
    if ($storeid) {
        $sql .= " AND A.storeid='{$storeid}'";
    }
    if ($money || $moneys) {
        if ($money === '')
            $money = 0;
        if ($moneys === '')
            $moneys = 999999999;
        $sql .= " AND A.price >= '{$money}' AND A.price <= '{$moneys}'";
    }
    if ($date1 and $date2) {
        $sql .= " AND A.date>='{$date1}' AND A.date<= '{$date2}'";
    }
    $page = intval($_GET['page']);
    $limit = intval($_GET['limit']);
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $offset = $limit * ($page - 1);
    
    // 先获取总记录数
    $total = $DB->getColumn("SELECT count(*) from ozon_order A WHERE{$sql}");
    
    $sort_field = isset($_GET['sort_field']) ? $_GET['sort_field'] : 'in_process_at';
    $sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';
    $orderBy = " ORDER BY A.{$sort_field} {$sort_order} ";
    
    // 使用查询优化函数处理待处理子标签的查询
   // $finalSql = "SELECT A.*,B.storename,B.ClientId,C.stocks,C.commissions FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id LEFT JOIN ozon_products C ON A.sku=C.sku WHERE{$sql}";
     $finalSql = "SELECT A.*, B.storename, B.ClientId, C.stocks, C.commissions, T.time_value as print_time 
                 FROM ozon_order A 
                 LEFT JOIN ozon_store B ON A.storeid=B.id 
                 LEFT JOIN ozon_products C ON A.sku=C.sku 
                 LEFT JOIN (
                    SELECT posting_number, MAX(time_value) as time_value 
                    FROM order_times 
                    WHERE time_type = 'print' 
                    GROUP BY posting_number
                 ) T ON A.posting_number = T.posting_number 
                 WHERE{$sql}";
    if (in_array($status, ['pending', 'not_purchased', 'purchased'])) {
        // 对于待处理相关的标签，使用优化函数
        $finalSql = optimize_pending_query($finalSql, $status);
    } else {
        // 其他标签使用常规排序
        $finalSql .= $orderBy;
    }
    
    $finalSql .= " limit $offset,$limit";
    $list = $DB->getAll($finalSql);
    
    $list2 = [];
    foreach ($list as $row) {
        // 检查是否有多个商品
        $products = [];
        if (!empty($row['products'])) {
            $products = json_decode($row['products'], true);
        }
        
        $row['money'] = $row['price'];
        if(empty($row['commission_percent'])){
            $stocks = json_decode($row['stocks'], true);
            $commissions = json_decode($row['commissions'], true);
            switch ($stocks['stocks'][0]['source']) {
                case 'fbo':
                    $i = 0;
                    break;
                case 'fbs':
                    $i = 1;
                    break;
                case 'rfbs':
                    $i = 2;
                    break;
                case 'fbp':
                    $i = 3;
                    break;
            }
            $row['percent'] = $commissions[$i]['percent'];
            if ($row['percent']) {
                $percent = $row['percent'] / 100;
            } else {
                $percent = 0.18;
            }
            $row['commissions'] = round($commissions[$i]['value']*$row['quantity'],2)." ￥";
        }else{
            $row['percent'] = $row['commission_percent'];
            $row['commissions'] = round($row['money']*($row['commission_percent']/100),2)." ￥";
        }
        
        $row['in_process_at'] = preg_replace('/^\d{4}-/', '', $row['in_process_at']);
        $row['shipment_date'] = preg_replace('/^\d{4}-/', '', $row['shipment_date']);
        $row['print_time'] = $row['print_time'] ? substr(str_replace(['T', 'Z'], ' ', $row['print_time']), 5, -4) : '';
        if (!$row['order_name']) {
            $row['order_name'] = Transmart($row['name2']);
            $DB->update('order', ['order_name' => $row['order_name']], ['order_id' => $row['order_id']]);
        }
           
          // 检查是否有多个商品
        $has_multiple_products = !empty($row['products']) && json_decode($row['products'], true) && count(json_decode($row['products'], true)) > 1;
       if ($has_multiple_products) {
            // 多商品订单：price字段已经是总价，quantity为1，直接使用
            // $row['price'] 保持不变，已经是总价
        } else {
            // 单商品订单：price字段是单价，需要乘以数量得到总价
            $row['price'] = round($row['price'] * $row['quantity'], 2);
        }
        
    
        $row['weight'] = round(($row['weight'] / 1000) * $row['quantity'], 2);
        $exp = explode(" ", $row['tpl_provider']);
        $row['wl'] = $exp[0];
        if ($exp[1] == 'Express') {
            $speed = "空运";
            $row['speedtx'] = "Express";
        } elseif ($exp[1] == 'Standard') {
            $speed = "陆空";
            $row['speedtx'] = "Standard";
        } else {
            $speed = "陆运";
            $row['speedtx'] = "Economy";
        }
        $row['provider'] = ozonmethodtype($row['tpl_provider']);
        $row['speed'] = $speed;
        
        if ($row['out_weight']) {
            $weight_type = 0;
            $row['out_weight'] = round($row['out_weight'] / 1000, 2);
            $weight = $row['out_weight'];
        } else {
            $weight_type = 1;
            $row['out_weight'] = "未出库";
            $weight = $row['weight'];
        }
        if(empty($row['delivery']))$row['delivery'] = calculateShippingCost(findShippingMethod(['provider' => $row['wl'],'method' => $row['provider'],'speed' => $row['speedtx']]), $weight);
        if(empty($row['profit']))$row['profit'] = calculateResult($row['price'], $weight, $percent, $row['delivery'], $row['cost']);
        if($row['cost']){
            $zmoney = round($row['cost']-$row['delivery']-$row['commissions']*100,2);
            $row['costprofit'] = round(($row['profit']/$row['cost'])*100,2);
            $szmoney = $row['cost']+$row['delivery']+$row['commissions'];
            $mmoney = $row['price']-$szmoney;
            $row['grossmargin'] = round(($mmoney/$row['price'])*100,2);
        }
        if ($weight_type == 0) {
            $DB->update('order', ['delivery' => $row['delivery'], 'profit' => $row['profit']], ['order_id' => $row['order_id']]);
        }
        
        if ($row['color']) {
            $row['pj'] = ' 颜色: ' . $row['color'];
        }
        if ($row['dimensions']) {
            $row['pj'] .= ' 尺寸mm: ' . $row['dimensions'];
        }
        if ($row['material']) {
            $row['pj'] .= ' 材质: ' . $row['material'];
        }
        if ($row['num']){
            $row['pj'] .= '  配套: ' . $row['num'];
        }
        if ($row['customer']) {
            $customer = json_decode($row['customer'], true);
            $row['customer_name'] = $customer['name'];
            $row['region'] = $customer['address']['region'] . ($customer['address']['zip_code'] ? " / " . $customer['address']['zip_code'] : '');
            if ($customer['address']['country'] == 'Rossiya') {
                $row['country'] = "俄罗斯";
            }
        }
        
        // 添加多商品信息
        if (!empty($products) && is_array($products) && count($products) > 1) {
            $row['has_multiple_products'] = true;
            $row['products_info'] = $products;
            
            // 为每个商品添加图片信息
            foreach ($row['products_info'] as $key => $product) {
                // 尝试从产品表中获取图片
                $product_info = $DB->getRow("SELECT primary_image FROM ozon_products WHERE sku = ?", [$product['sku']]);
                if ($product_info && !empty($product_info['primary_image'])) {
                    $row['products_info'][$key]['image'] = $product_info['primary_image'];
                    
                    // 如果是第一个商品且主图为空，则使用该商品的图片作为主图
                    if ($key === 0 && empty($row['primary_image'])) {
                        $row['primary_image'] = $product_info['primary_image'];
                    }
                } else {
                    // 如果产品表中没有找到图片，尝试从其他来源获取
                    $other_product = $DB->getRow("SELECT primary_image FROM ozon_products WHERE offer_id = ?", [$product['offer_id']]);
                    if ($other_product && !empty($other_product['primary_image'])) {
                        $row['products_info'][$key]['image'] = $other_product['primary_image'];
                    } else {
                        // 尝试从商品API获取图片
                        $api_product = getProductImageFromAPI($product['sku'], $row['ClientId'], $row['key']);
                        if ($api_product && !empty($api_product['image'])) {
                            $row['products_info'][$key]['image'] = $api_product['image'];
                            // 保存到数据库以便下次使用
                            $DB->update('products', ['primary_image' => $api_product['image']], ['sku' => $product['sku']]);
                        } else {
                            // 如果没有找到图片，使用默认图片
                            $row['products_info'][$key]['image'] = '../assets/img/syncing.png';
                        }
                    }
                }
            }
        } else {
            $row['has_multiple_products'] = false;
        }
        $row['time'] = date("Y-m-d H:i:s",$row['time']);
        $list2[] = $row;
    }
    exit(json_encode(['code' => 0, 'count' => $total, 'data' => $list2]));
break;
case 'batch_ship': #批量移入交运平台
    $posting_numbers = isset($_POST['posting_numbers']) ? $_POST['posting_numbers'] : [];
    if (!is_array($posting_numbers) || empty($posting_numbers)) {
        exit(json_encode(['code' => -1, 'msg' => '请选择要交运的订单']));
    }
    $uid = $islogin2 ? $userrow['uid'] : 0;
    if (empty($uid)) {
        exit(json_encode(['code' => -2, 'msg' => '未登录']));
    }
    $success_count = 0;
    $fail_count = 0;
    $fail_orders = [];
    foreach ($posting_numbers as $posting_number) {
        $posting_number = trim($posting_number);
        if ($posting_number === '')
            continue;
        $row = $DB->getRow("SELECT * FROM ozon_order WHERE uid=:uid AND status='awaiting_deliver' AND posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
        if (!$row) {
            $fail_count++;
            $fail_orders[] = $posting_number;
            continue;
        }
        $sql = "UPDATE ozon_order SET packing_status = 1 WHERE uid = :uid AND posting_number = :posting_number";
        $params = [':uid' => $uid, ':posting_number' => $posting_number];
        $update_result = $DB->exec($sql, $params);
        if ($update_result !== false) {
            $success_count++;
        } else {
            $fail_count++;
            $fail_orders[] = $posting_number;
        }
    }
    $msg = "批量交运完成，成功: $success_count 条";
    if ($fail_count > 0) {
        $msg .= "，失败: $fail_count 条，订单号: " . implode(', ', $fail_orders);
    }
    exit(json_encode(['code' => 0, 'msg' => $msg, 'success' => $success_count, 'fail' => $fail_count, 'fail_orders' => $fail_orders]));
break;
case 'orders_updata': #单个同步订单，或者批量更新同步
    $posting_number = daddslashes($_POST['posting_number']);
    if ($posting_number) {
        $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
        //exit(json_encode($row));
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $json = $client->postingfbsget($posting_number);
        //exit(json_encode($json));
        $importer = new \lib\JsonImporter($DB);
        //exit(json_encode($importer->importorder($json, false, $row)));
        if ($importer->importorder($json, false, $row)) {
            $code = 0;
        } else {
            $code = -1;
        }
        exit(json_encode(['code' => $code]));
    }else{
        if (function_exists("set_time_limit")) {
            @set_time_limit(0);
        }
        $id = intval($_POST['shop_ids']);
        $importer = new \lib\JsonImporter($DB);
        $row = $DB->find('store', '*', ['id' => $id]);
        if($row){
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            $filter = [
                'status' => 'cancelled',
                'since' => date('Y-m-d', strtotime('-6 days')),
                'to' => date('Y-m-d')
            ];
            $data = $client->getFbsPostingListV3($filter);
            foreach ($data['result']['postings'] as $items) {
                $result = $importer->importorder($items, false, $row);
                if($result['errors']){
                    exit(json_encode($result));
                }
            }
            $data = $client->fbsunfulfilledlist(['cutoff_from' => date('Y-m-d H:i:s', strtotime('-6 days')), 'cutoff_to' => date('Y-m-d H:i:s', strtotime('+9 days'))]);
            foreach ($data['result']['postings'] as $item) {
                $result = $importer->importorder($item, false, $row);
                if($result['errors']){
                    exit(json_encode($result));
                }
            }
            $result = ['code'=>0];
        }else{
            $result = ['code'=>-1,'msg'=>'店铺数据不存在'];
        }
        exit(json_encode($result));
    }
break;
case 'update_order': #更新采购信息
    $order_id = intval($_POST['order_id']);
    $cost = htmlspecialchars(strip_tags(trim($_POST['cost'])));
    $purchase_orderSn = htmlspecialchars(strip_tags(trim($_POST['purchase_orderSn'])));
    $courierNumber = isset($_POST['courierNumber']) ? htmlspecialchars(strip_tags(trim($_POST['courierNumber']))) : '';
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.order_id=:order_id LIMIT 1", [':uid' => $uid, ':order_id' => $order_id]);
    if ($row) {
        if($cost)$updata['cost'] = $cost;
        if($purchase_orderSn){
            $updata['purchase_orderSn'] = $purchase_orderSn;
            if(strpos($purchase_orderSn, '-') === false){
                $updata['purchase_type'] = '1688';
            }else{
                $updata['purchase_type'] = 'pdd';
            }
        }
        if($courierNumber)$updata['courierNumber'] = $courierNumber;
        
        $updata['Purchase_date'] = $date;
        if ($DB->update('order', $updata, ['uid' => $uid, 'order_id' => $order_id])) {
            if ($userrow['package'] == 1) {
                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                $data = $client->fbsshippackage($row);
                if ($data) {
                    $result['code'] = 0;
                } else {
                    $result['code'] = -3;
                    $result['msg'] = '订单备货失败，采购成功';
                }
            } else {
                $result['code'] = 0;
            }
        } else {
            $result['code'] = -1;
        }
    } else {
        $result = ['code' => -2, 'msg' => '不存在此订单'];
    }
    exit(json_encode($result));
    break;
    
    
case 'cancel_purchase_relation': #强制取消关联订单
    $posting_number = daddslashes($_POST['posting_number']);
    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '订单号不能为空']));
    }
    
    // 检查订单是否存在且属于当前用户
    $row = $DB->getRow("SELECT order_id FROM ozon_order WHERE uid = ? AND posting_number = ? LIMIT 1", [$uid, $posting_number]);
    if (!$row) {
        exit(json_encode(['code' => -2, 'msg' => '订单不存在或没有权限']));
    }
    
    // 清空采购相关字段
    $updateData = [
        'cost' => null,
        'purchase_orderSn' => null,
        'purchase_type' => null,
        'purchase_ok' => null,
        'courierNumber' => null,
        'purchase_lus' => null,
        'Purchase_date' => null
    ];
    
    if ($DB->update('order', $updateData, ['uid' => $uid, 'order_id' => $row['order_id']])) {
        $result = ['code' => 0, 'msg' => '采购关联已取消'];
    } else {
        $result = ['code' => -3, 'msg' => '取消关联失败：' . $DB->error()];
    }
    
    exit(json_encode($result));
    break;
case 'order_package': #备货
    $posting_number = daddslashes($_POST['posting_number']);
    if (empty($posting_number))
        exit(json_encode(['code' => -2, 'msg' => 'posting_number No!']));
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if ($row) {
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $data = $client->fbsshippackage($row);
        if ($data) {
            $result = ['code' => 0];
        } else {
            $result = ['code' => -3, 'msg' => json_encode($data)];
        }
    } else {
        $result = ['code' => -1, 'msg' => '订单不存在'];
    }
    exit(json_encode($result));
    break;
case 'batch_order_package': #批量备货
    $posting_numbers = isset($_POST['posting_numbers']) ? $_POST['posting_numbers'] : [];
    if (!is_array($posting_numbers) || empty($posting_numbers)) {
        exit(json_encode(['code' => -1, 'msg' => '请选择要备货的订单']));
    }
    $uid = $islogin2 ? $userrow['uid'] : 0;
    if (empty($uid)) {
        exit(json_encode(['code' => -2, 'msg' => '未登录']));
    }
    $success_count = 0;
    $fail_count = 0;
    $fail_orders = [];
    foreach ($posting_numbers as $posting_number) {
        $posting_number = trim($posting_number);
        if ($posting_number === '')
            continue;
        $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
        if (!$row) {
            $fail_count++;
            $fail_orders[] = $posting_number;
            continue;
        }
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $data = $client->fbsshippackage($row);
        if ($data) {
            $success_count++;
        } else {
            $fail_count++;
            $fail_orders[] = $posting_number;
        }
    }
    $msg = "批量备货完成，成功: $success_count 条";
    if ($fail_count > 0) {
        $msg .= "，失败: $fail_count 条，订单号: " . implode(', ', $fail_orders);
    }
    exit(json_encode(['code' => 0, 'msg' => $msg, 'success' => $success_count, 'fail' => $fail_count, 'fail_orders' => $fail_orders]));
    break;
case 'user_auto_switch': #切换备货模式
    $id = intval($_POST['id']);
    $status = intval($_POST['status']);
    if ($DB->update('user', ['package' => $status], ['uid' => $uid])) {
        $result = ['code' => 0];
    } else {
        $result = ['code' => -1];
    }
    exit(json_encode($result));
    break;

case 'order_preview': #面单下载
    $posting_number = daddslashes($_POST['posting_number']);
    if ($posting_number) {
        $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
        if ($row) {
            $file_path = targetDateTime($row);
            if (file_exists($file_path[0])) {
                $result = ['code' => 0, 'msg' => '成功获取面单', 'url' => $file_path[1]];
            } else {
                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                $url = $client->packagelabel($row);
                if ($url != false) {
                    $result = ['code' => 0, 'msg' => '成功获取面单', 'url' => $url];
                } else {
                    $result = ['code' => -3, 'msg' => '下载订单面单失败'];
                }
            }
        } else {
            $result = ['code' => -2, 'msg' => '不存在此订单'];
        }

    } else {
        $result = ['code' => -1, 'msg' => '未提交货物单号'];
    }
    exit(json_encode($result));
    break;
case 'getShops': #获取店铺
    $list = $DB->getAll("SELECT id, storename, ClientId, `key`, warehouses FROM ozon_store WHERE uid = ?", [$uid]);
    $list2 = [];
    foreach ($list as $row) {
        // 触发异步更新仓库（如果缺失）
        if (empty($row['warehouses']) || $row['warehouses'] === 'null') {
            async_update_warehouse($row['id'], $row['ClientId'], $row['key']);
            $row['warehouses'] = []; // 前端展示空数组
        } else {
            $row['warehouses'] = json_decode($row['warehouses'], true);
        }
        // 仅返回必要字段
        $list2[] = [
            'id' => $row['id'],
            'storename' => $row['storename']
        ];
    }
    exit(json_encode(['code' => 0, 'data' => $list2]));
    break;
case '1688': #获取1688同款货源
    $posting_number = daddslashes($_POST['posting_number']);
    $data = $DB->find('order', '*', ['posting_number' => $posting_number, 'uid' => $uid]);
    $url = "https://overseaplugin.1688.com/image/upload";
    $post = json_encode(['imageBase64' => urlToBase64($data['primary_image']), 'source' => 'www.100b.cn', 'terminalId' => 'Chrome_136.0.0.0', 'version' => '0.0.18']);
    $res = get_curl($url, $post, 0, 0, 0, 0, 0, ['Content-Type: application/json']);
    $json = json_decode($res, true);
    if ($json['success']) {
        $url = "https://overseaplugin.1688.com/recommend/sameOfferRecommend";
        $post = json_encode(['beginPage' => 1, 'currency' => 'CNY', 'imageId' => $json['result'], 'keyword' => '', 'language' => 'zh_CN', 'pageSize' => 8, 'priceEnd' => '', 'priceStart' => '', 'region' => 'HK', 'source' => 'www.100b.cn', 'terminalId' => 'Chrome_136.0.0.0', 'version' => '0.0.18']);
        $res = get_curl($url, $post, 0, 0, 0, 0, 0, ['Content-Type: application/json']);
        $json = json_decode($res, true);
    }
    exit($res);
    break;
case 'save_links':
    $json_data = file_get_contents('php://input');
    // 解析 JSON
    $data = json_decode(trim($json_data), true);
    $sku = intval($data['sku']);
    $posting_number = daddslashes($data['posting_number']);
    $links = $data['links'];
    if ($sku and $posting_number) {
        $data = $DB->find('management', '*', ['sku' => $sku]);
        if ($data) {
            if ($DB->update('management', ['purchase_url' => json_encode($links)], ['id' => $data['id'], 'uid' => $uid, 'sku' => $sku])) {
                $result = ['code' => 0];
            } else {
                $result = ['code' => -2, 'msg' => '更新数据失败' . $DB->error()];
            }
        } else {
            if ($DB->insert('management', ['uid' => $uid, 'sku' => $sku, 'purchase_url' => json_encode($links)])) {
                $result = ['code' => 0];
            } else {
                $result = ['code' => -3, 'msg' => '保存数据失败' . $DB->error()];
            }
        }
    } else {
        $result = ['code' => -1, 'msg' => '数据不齐全'];
    }
    exit(json_encode($result));
    break;
case 'get_urljh':
    $url = daddslashes($_POST['url']);
    $posting_number = daddslashes($_POST['posting_number']);
    $data = $DB->find('order', '*', ['posting_number' => $posting_number, 'uid' => $uid]);
    $host = $_SERVER['HTTP_HOST'];
    $domainParts = explode('.', $host);
    
    if (count($domainParts) === 1 || in_array(end($domainParts), ['localhost', 'test', 'local'])) {
        $mainDomain = $host;
    } else {
        $mainDomain = implode('.', array_slice($domainParts, -2, 2));
    }
    if (strpos($url, '1688.com') !== false) {
        $type = "1688";
    } else if (strpos($url, 'pinduoduo.com') !== false || strpos($url, 'yangkeduo.com') !== false) {
        $type = "pdd";
    }
    if($data['purchase_id']){
        $id = $data['purchase_id'];
        $result = ['code' => 0, 'url' => $url, 'posting_number' => $posting_number, 'id' => $id];
    }else if ($data) {
        $id = uniqid() . rand(11111, 99999);
        if ($DB->update('order', ['purchase_id' => $id], ['posting_number' => $posting_number])) {
            $result = ['code' => 0, 'url' => $url, 'posting_number' => $posting_number, 'id' => $id];
        } else {
            $result = ['code' => -1, 'msg' => '采购数据保存失败' . $DB->error()];
        }
    } else {
        $result = ['code' => -1, 'msg' => '数据不存在'];
    }
    if($id)setcookie("hterporderid", $id, time() + 300, "/", ".{$mainDomain}", isset($_SERVER['HTTPS']), true);
    if($type)setcookie("type", $type, time() + 300, "/", ".{$mainDomain}", isset($_SERVER['HTTPS']), true);
    exit(json_encode($result));
break;

case 'batch_print_order_tcpdf':
    try {
        // 解析输入的订单号列表
        $input = json_decode(file_get_contents('php://input'), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("无效的JSON输入");
        }
        
        $posting_numbers = array_filter(array_map('trim', $input['posting_numbers'] ?? []));
        if (empty($posting_numbers)) {
            exit(json_encode(['code' => -1, 'msg' => '请选择要打印的订单']));
        }
        
        // 设置输出目录
        $output_dir = ROOT . 'user/temp_prints/';
        if (!is_dir($output_dir) && !mkdir($output_dir, 0755, true)) {
            throw new Exception("无法创建输出目录");
        }
        
        // 初始化TCPDF
        require_once ROOT . 'includes/vendor/tecnickcom/tcpdf/tcpdf.php';
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        $pdf->SetPrintHeader(false);
        $pdf->SetPrintFooter(false);
        $pdf->SetAutoPageBreak(false, 0);
        
        // 检查Imagick扩展
        if (!extension_loaded('imagick')) {
            exit(json_encode(['code' => -500, 'msg' => '系统缺少Imagick扩展，请联系技术支持']));
        }
        
        // 处理每个订单面单
        $success_count = 0;
        $failed_orders = [];
        
        foreach ($posting_numbers as $posting_number) {
            // 获取订单信息
            $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
            
            if (!$row) {
                $failed_orders[] = $posting_number;
                continue;
            }
            
            // 获取面单文件路径
            $file_path = targetDateTime($row);
            $pdf_file = $file_path[0];
            
            // 检查面单是否存在，不存在则下载
            if (!file_exists($pdf_file)) {
                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                $url = $client->packagelabel($row);
                
                if ($url === false) {
                    $failed_orders[] = $posting_number;
                    continue;
                }
                
                // 等待面单生成完成
                sleep(1);
                if (!file_exists($pdf_file)) {
                    $failed_orders[] = $posting_number;
                    continue;
                }
            }
            
            // 使用Imagick处理PDF
            try {
                $imagick = new Imagick();
                $imagick->setResolution(300, 300); // 设置较高分辨率以保持质量
                $imagick->readImage($pdf_file);
                $imagick->setImageFormat('png');
                
                $num_pages = $imagick->getNumberImages();
                
                for ($i = 0; $i < $num_pages; $i++) {
                    $imagick->setIteratorIndex($i);
                    
                    // 保存临时图像
                    $temp_image = tempnam(sys_get_temp_dir(), 'pdfimg') . '.png';
                    file_put_contents($temp_image, $imagick);
                    
                    // 获取图像尺寸
                    $size = getimagesize($temp_image);
                    $width = $size[0];
                    $height = $size[1];
                    $orientation = ($width > $height) ? 'L' : 'P';
                    
                    // 添加到PDF
                    $pdf->AddPage($orientation, [$width, $height]);
                    $pdf->Image($temp_image, 0, 0, $width, $height, 'PNG', '', '', true, 300);
                    
                    // 删除临时文件
                    unlink($temp_image);
                }
                
                $success_count++;
            } catch (Exception $e) {
                $failed_orders[] = $posting_number;
                error_log("处理面单 {$posting_number} 出错: " . $e->getMessage());
            } finally {
                if (isset($imagick)) {
                    $imagick->clear();
                    $imagick->destroy();
                }
            }
        }
        
        // 如果没有成功处理任何面单
        if ($success_count === 0) {
            exit(json_encode([
                'code' => -2,
                'msg' => '没有找到可打印的订单面单',
                'failed_orders' => $failed_orders
            ]));
        }
        
        // 保存合并的PDF
        $output_file = 'merged_' . date('Ymd_His') . '.pdf';
        $output_path = $output_dir . $output_file;
        $pdf->Output($output_path, 'F');
          // 记录打印时间
        $operator = $uid; // 使用当前用户UID作为操作员
        foreach ($posting_numbers as $posting_number) {
            $order = $DB->getRow("SELECT order_id FROM ozon_order WHERE posting_number=?", [$posting_number]);
            if ($order) {
                $DB->exec("INSERT INTO order_times (order_id, posting_number, time_type, time_value, operator) VALUES (?, ?, 'print', NOW(), ?)", [
                    $order['order_id'],
                    $posting_number,
                    $operator
                ]);
            }
        }
        
        // 返回结果
        $result = [
            'code' => 0,
            'msg' => "成功合并 {$success_count} 个面单",
            'url' => '/user/temp_prints/' . $output_file,
            'success_count' => $success_count
        ];
        
        if (!empty($failed_orders)) {
            $result['failed_count'] = count($failed_orders);
            $result['failed_orders'] = $failed_orders;
        }
        
        exit(json_encode($result));
        
    } catch (Exception $e) {
        exit(json_encode([
            'code' => -500,
            'msg' => '系统错误: ' . $e->getMessage()
        ]));
    }
    break;

case 'updata_kd': #更新物流
    $posting_number = daddslashes($_POST['posting_number']);
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if($row['purchase_type']==='pdd'){
        if (pddorder($row)) {
            $result = ['code' => 0];
        } else {
            $result = ['code' => 1, 'msg' => '暂不明原因无法获取数据'];
        }
    }else if($row['purchase_type']==='1688'){
        if (aliorder($row)) {
            $result = ['code' => 0];
        } else {
            $result = ['code' => 1, 'msg' => '暂不明原因无法获取数据'];
        }
    }
    
    exit(json_encode($result));
break;
case 'get_order_notes': #获取订单备注
    $posting_number = daddslashes($_POST['posting_number']);
    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '订单号不能为空']));
    }

    $row = $DB->getRow("SELECT OrderNotes FROM ozon_order WHERE uid=:uid AND posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if ($row) {
        $result = ['code' => 0, 'data' => ['notes' => $row['OrderNotes'] ?? '']];
    } else {
        $result = ['code' => -2, 'msg' => '订单不存在'];
    }
    exit(json_encode($result));
break;

case 'get_single_order': #获取单个订单数据
    $posting_number = daddslashes($_POST['posting_number']);
    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '订单号不能为空']));
    }

    // 使用与orders_all相同的查询逻辑
    $sql = "SELECT A.*, B.storename, B.ClientId, C.stocks, C.commissions, T.time_value as print_time 
            FROM ozon_order A 
            LEFT JOIN ozon_store B ON A.storeid=B.id 
            LEFT JOIN ozon_products C ON A.sku=C.sku 
            LEFT JOIN (
               SELECT posting_number, MAX(time_value) as time_value 
               FROM order_times 
               WHERE time_type = 'print' 
               GROUP BY posting_number
            ) T ON A.posting_number = T.posting_number 
            WHERE A.uid = ? AND A.posting_number = ?";
    
    $row = $DB->getRow($sql, [$uid, $posting_number]);
    
    if ($row) {
        // 处理订单数据，使用与orders_all相同的处理逻辑
        $row['money'] = $row['price'];
        if(empty($row['commission_percent'])){
            $stocks = json_decode($row['stocks'], true);
            $commissions = json_decode($row['commissions'], true);
            switch ($stocks['stocks'][0]['source']) {
                case 'fbo':
                    $i = 0;
                    break;
                case 'fbs':
                    $i = 1;
                    break;
                case 'rfbs':
                    $i = 2;
                    break;
                case 'fbp':
                    $i = 3;
                    break;
                default:
                    $i = 0;
            }
            $row['percent'] = $commissions['commissions'][$i]['percent'];
            $row['delivery'] = $stocks['stocks'][$i]['delivery'];
            $row['commissions'] = $commissions['commissions'][$i]['commissions'];
        } else {
            $row['percent'] = $row['commission_percent'];
            $row['delivery'] = $row['commission_delivery'];
            $row['commissions'] = $row['commission_commissions'];
        }
        
        // 处理多商品信息
        $products = [];
        if (!empty($row['products'])) {
            $products = json_decode($row['products'], true);
        }
        
        if (!empty($products)) {
            $row['has_multiple_products'] = true;
            $row['products_info'] = $products;
        } else {
            $row['has_multiple_products'] = false;
            $row['products_info'] = [];
        }
        
        // 计算利润
        $cost = floatval($row['cost'] ?? 0);
        $price = floatval($row['price'] ?? 0);
        $delivery = floatval($row['delivery'] ?? 0);
        $commissions = floatval($row['commissions'] ?? 0);
        
        $profit = $price - $cost - $delivery - $commissions;
        $row['profit'] = number_format($profit, 2);
        
        if ($cost > 0) {
            $costprofit = ($profit / $cost) * 100;
            $grossmargin = ($profit / $price) * 100;
            $row['costprofit'] = number_format($costprofit, 2);
            $row['grossmargin'] = number_format($grossmargin, 2);
        } else {
            $row['costprofit'] = '0.00';
            $row['grossmargin'] = '0.00';
        }
        
        $result = ['code' => 0, 'data' => $row];
    } else {
        $result = ['code' => -2, 'msg' => '订单不存在'];
    }
    exit(json_encode($result));
break;
case 'update_order_notes': #更新订单备注
    $posting_number = daddslashes($_POST['posting_number']);
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';

    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '订单号不能为空']));
    }

    // 验证订单是否存在且属于当前用户
    $row = $DB->getRow("SELECT posting_number FROM ozon_order WHERE uid=:uid AND posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if (!$row) {
        exit(json_encode(['code' => -2, 'msg' => '订单不存在']));
    }

    // 更新备注
    if ($DB->update('ozon_order', ['OrderNotes' => $notes], ['uid' => $uid, 'posting_number' => $posting_number])) {
        $result = ['code' => 0, 'msg' => '备注更新成功'];
    } else {
        $result = ['code' => -3, 'msg' => '备注更新失败'];
    }
    exit(json_encode($result));
break;
case 'export_orders':
    // 1. Get parameters
    $posting_numbers_str = isset($_POST['posting_numbers']) ? $_POST['posting_numbers'] : '';
    $export_fields_str = isset($_POST['export_fields']) ? $_POST['export_fields'] : '';

    $posting_numbers = [];
    if (!empty($posting_numbers_str)) {
        $posting_numbers = array_filter(explode(',', $posting_numbers_str));
    }
    
    if (empty($export_fields_str)) {
        exit(json_encode(['code' => -1, 'msg' => '请选择要导出的字段']));
    }
    $export_fields = explode(',', $export_fields_str);

    // 2. Build SQL query based on filters using parameterized queries for security
    $sql_conditions = "A.uid = ?";
    $query_params = [$uid];
    
    if (!empty($posting_numbers)) {
        // If specific orders are selected, use their posting_numbers.
        $placeholders = implode(',', array_fill(0, count($posting_numbers), '?'));
        $sql_conditions .= " AND A.posting_number IN ({$placeholders})";
        $query_params = array_merge($query_params, $posting_numbers);
    } else {
        // If no orders are selected, use the filters from the main page.
        $groupid = isset($_POST['groupid']) ? intval($_POST['groupid']) : 0;
        $status = isset($_POST['status']) ? trim($_POST['status']) : '';
        $text = isset($_POST['text']) ? trim($_POST['text']) : '';
        $money = isset($_POST['money']) ? trim($_POST['money']) : '';
        $moneys = isset($_POST['moneys']) ? trim($_POST['moneys']) : '';
        $storeid = isset($_POST['storeid']) ? trim($_POST['storeid']) : '';
        $date1 = isset($_POST['date1']) ? trim($_POST['date1']) : '';
        $date2 = isset($_POST['date2']) ? trim($_POST['date2']) : '';
        
        if ($groupid && $groupid !== '') {
            $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
            $shopIdsStr = '0';
            if ($user && $user['shop_groups']) {
                $shopGroups = json_decode($user['shop_groups'], true);
                foreach ($shopGroups['groups'] as $group) {
                    if ($group['id'] == $groupid) {
                        if (!empty($group['shopIds'])) {
                           $shopIdsStr = implode(',', array_map('intval', $group['shopIds']));
                        }
                        break;
                    }
                }
            }
            $sql_conditions .= " AND A.storeid IN ({$shopIdsStr})";
        }
        if ($status) {
             if ($status == 'pending') {
                $sql_conditions .= " AND A.status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2')";
            } else if ($status == 'not_purchased') {
                $sql_conditions .= " AND (A.cost IS NULL OR A.cost = '') AND A.status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2')";
            } else if ($status == 'purchased') {
                $sql_conditions .= " AND A.cost IS NOT NULL AND A.cost != '' AND A.cost != '0' AND A.status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2', 'awaiting_registration')";
            } else if ($status == 'not_shipped') {
                $sql_conditions .= " AND A.status = 'awaiting_packaging'";
            } else if ($status == 'all') {
                $sql_conditions .= " AND A.status != 'cancelled'";
            } else if ($status == 'awaiting_deliver') {
                 $sql_conditions .= " AND (A.status='awaiting_deliver' OR A.status='awaiting_registration') AND A.packing_status=0 AND A.cost IS NOT NULL AND A.cost != ''";
            } else if ($status == 'awaiting_deliver2') {
                $sql_conditions .= " AND A.status='awaiting_deliver' AND A.packing_status=1";
            } else {
                $sql_conditions .= " AND A.status = ?";
                $query_params[] = $status;
            }
        }
        if ($text) {
            $sql_conditions .= " AND (A.order_name LIKE ? OR A.name2 LIKE ? OR A.posting_number = ? OR A.sku = ? OR A.purchase_orderSn = ? OR A.courierNumber = ?)";
            $like_text = "%{$text}%";
            $query_params = array_merge($query_params, [$like_text, $like_text, $text, $text, $text, $text]);
        }
        if ($storeid) {
            $sql_conditions .= " AND A.storeid = ?";
            $query_params[] = $storeid;
        }
        if ($money || $moneys) {
            if ($money === '') $money = 0;
            if ($moneys === '') $moneys = 999999999;
            $sql_conditions .= " AND A.price >= ? AND A.price <= ?";
            $query_params[] = $money;
            $query_params[] = $moneys;
        }
        if ($date1 and $date2) {
            $sql_conditions .= " AND A.date >= ? AND A.date <= ?";
            $query_params[] = $date1;
            $query_params[] = $date2;
        }
    }

    // 3. Fetch data (all matching rows, no pagination)
    $sort_field = isset($_POST['sort_field']) ? $_POST['sort_field'] : 'in_process_at';
    $sort_order = isset($_POST['sort_order']) ? $_POST['sort_order'] : 'desc';
    $orderBy = " ORDER BY A.{$sort_field} {$sort_order} ";
    $list = $DB->getAll("SELECT A.*,B.storename,B.ClientId,C.stocks,C.commissions FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id LEFT JOIN ozon_products C ON A.sku=C.sku WHERE {$sql_conditions} {$orderBy}", $query_params);
    
    // 4. Generate CSV on server
    $output_dir = ROOT . 'user/temp_exports/';
    if (!is_dir($output_dir) && !mkdir($output_dir, 0755, true)) {
        exit(json_encode(['code' => -1, 'msg' => '无法创建导出目录']));
    }

    $filename = "orders_" . date("Ymd_His") . '_' . uniqid() . ".csv";
    $output_path = $output_dir . $filename;
    
    $output = fopen($output_path, 'w');
    
    // Add BOM to fix UTF-8 in Excel
    fputs($output, $bom =( chr(0xEF) . chr(0xBB) . chr(0xBF) ));
    
    // Create header row from selected fields (with mapping to Chinese names)
    $field_map = [
        'posting_number' => '订单号',
        'in_process_at' => '下单时间',
        'shipment_date' => '发货截止时间',
        'primary_image' => '产品图片',
        'sku' => 'SKU',
        'offer_id' => 'Offer ID',
        'order_name' => '商品中文名',
        'name2' => '商品俄文名',
        'pj' => '规格',
        'quantity' => '数量',
        'customer_name' => '客户姓名',
        'country' => '国家',
        'region' => '地区/邮编',
        'tracking_number' => 'Ozon运单号',
        'tpl_provider' => '物流方式',
        'wl' => '物流服务商',
        'speed' => '运输方式',
        'warehouse' => '发货仓库',
        'purchase_orderSn' => '采购单号',
        'purchase_type' => '采购平台',
        'purchase_ok' => '采购状态',
        'courierNumber' => '快递单号',
        'purchase_lus' => '快递状态',
        'money' => '单价(RMB)',
        'price' => '总价(RMB)',
        'cost' => '采购成本(RMB)',
        'delivery' => '预估运费(RMB)',
        'commissions' => '平台佣金(RMB)',
        'percent' => '佣金比例(%)',
        'profit' => '预估利润(RMB)',
    ];
    $header = [];
    foreach($export_fields as $field){
        $header[] = $field_map[$field] ?? $field;
    }
    fputcsv($output, $header);
    
    // Process and write data rows
    foreach ($list as $row) {
        $processed_row = process_order_row($row, $DB);
        $data_row = [];
        foreach($export_fields as $field) {
            $value = $processed_row[$field] ?? '';
            
            // Format image for Excel
            if ($field === 'primary_image' && !empty($value)) {
                $absolute_url = $value;
                if (strpos($absolute_url, 'http') !== 0) {
                    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
                    $host = $_SERVER['HTTP_HOST'];
                    $cleaned_path = ltrim(str_replace('../', '', $value), '/');
                    $absolute_url = $protocol . $host . '/' . $cleaned_path;
                }
                $value = '=IMAGE("' . $absolute_url . '")';
            }

            // Human-readable values for specific fields
            if ($field === 'purchase_ok' && isset($processed_row[$field])) {
                switch($value) {
                    case 0: $value = '等待付款'; break;
                    case 1: $value = '待发货'; break;
                    case 2: $value = '待收货'; break;
                    case 3: $value = '已签收'; break;
                    case 6: $value = '交易已取消'; break;
                    default: $value = '未知'; break;
                }
            }
            if ($field === 'purchase_type' && !empty($value)) {
                 $value = ($value === 'pdd') ? '拼多多' : '1688';
            }

            $data_row[] = $value;
        }
        fputcsv($output, $data_row);
    }
    
    fclose($output);
    
    // 5. Return JSON with file URL
    exit(json_encode(['code' => 0, 'msg' => '文件生成成功', 'url' => '/user/temp_exports/' . $filename]));

break;
}

function process_order_row($row, $DB) {
        // 检查是否有多个商品
        $products = [];
        if (!empty($row['products'])) {
            $products = json_decode($row['products'], true);
        }
        
        $stocks = json_decode($row['stocks'], true);
        $commissions = json_decode($row['commissions'], true);
        switch ($stocks['stocks'][0]['source']) {
            case 'fbo':
                $i = 0;
                break;
            case 'fbs':
                $i = 1;
                break;
            case 'rfbs':
                $i = 2;
                break;
            case 'fbp':
                $i = 3;
                break;
        }
        $row['percent'] = $commissions[$i]['percent'];
        $row['commissions'] = round($commissions[$i]['value']*$row['quantity'],2)." ￥";
        $row['in_process_at'] = preg_replace('/^\d{4}-/', '', $row['in_process_at']);
        $row['shipment_date'] = preg_replace('/^\d{4}-/', '', $row['shipment_date']);
        $row['time'] = substr(str_replace(['T', 'Z'], ' ', $row['time']), 5, -4);
        $row['print_time'] = $row['print_time'] ? substr(str_replace(['T', 'Z'], ' ', $row['print_time']), 5, -4) : '';
        if (!$row['order_name']) {
            $row['order_name'] = Transmart($row['name2']);
            $DB->update('order', ['order_name' => $row['order_name']], ['order_id' => $row['order_id']]);
        }
        $row['money'] = $row['price'];
        
           
     
             // 检查是否有多个商品
        $has_multiple_products = !empty($row['products']) && json_decode($row['products'], true) && count(json_decode($row['products'], true)) > 1;
        
        if ($has_multiple_products) {
            // 多商品订单：price字段已经是总价，quantity为1，直接使用
            // $row['price'] 保持不变，已经是总价
        } else {
            // 单商品订单：price字段是单价，需要乘以数量得到总价
            $row['price'] = round($row['price'] * $row['quantity'], 2);
        }
        
        
        $row['weight'] = round(($row['weight'] / 1000) * $row['quantity'], 2);
        $exp = explode(" ", $row['tpl_provider']);
        $row['wl'] = $exp[0];
        if ($exp[1] == 'Express') {
            $speed = "空运";
            $speedtx = "Express";
        } elseif ($exp[1] == 'Standard') {
            $speed = "陆空";
            $speedtx = "Standard";
        } else {
            $speed = "陆运";
            $speedtx = "Economy";
        }
        $provider = ozonmethodtype($row['tpl_provider']);
        $row['speed'] = $speed;
        if ($row['out_weight']) {
            $weight_type = 0;
            $row['out_weight'] = round($row['out_weight'] / 1000, 2);
            $weight = $row['out_weight'];
        } else {
            $weight_type = 1;
            $row['out_weight'] = "未出库";
            $weight = $row['weight'];
        }
        if(empty($row['delivery']))$row['delivery'] = calculateShippingCost(findShippingMethod(['provider' => $row['wl'],'method' => $provider,'speed' => $speedtx]), $weight);
        if(empty($row['profit']))$row['profit'] = calculateResult($row['price'], $weight, $percent, $row['delivery'], $row['cost']);
        if ($weight_type == 0) {
            $DB->update('order', ['delivery' => $row['delivery'], 'profit' => $row['profit']], ['order_id' => $row['order_id']]);
        }
        //产品颜色: 灰色,颜色名称: 灰色,尺寸,毫米: 1500*1200
        if ($row['color']) {
            $row['pj'] = ' 颜色: ' . $row['color'];
        }
        if ($row['dimensions']) {
            $row['pj'] .= ' 尺寸mm: ' . $row['dimensions'];
        }
        if ($row['material']) {
            $row['pj'] .= ' 材质: ' . $row['material'];
        }
        if ($row['customer']) {
            $customer = json_decode($row['customer'], true);
            $row['customer_name'] = $customer['name'];
            $row['region'] = $customer['address']['region'] . ($customer['address']['zip_code'] ? " / " . $customer['address']['zip_code'] : '');
            if ($customer['address']['country'] == 'Rossiya') {
                $row['country'] = "俄罗斯";
            }
        }
        
        // 添加多商品信息
        if (!empty($products) && is_array($products) && count($products) > 1) {
            $row['has_multiple_products'] = true;
            $row['products_info'] = $products;
            
            // 为每个商品添加图片信息
            foreach ($row['products_info'] as $key => $product) {
                // 尝试从产品表中获取图片
                $product_info = $DB->getRow("SELECT primary_image FROM ozon_products WHERE sku = ?", [$product['sku']]);
                if ($product_info && !empty($product_info['primary_image'])) {
                    $row['products_info'][$key]['image'] = $product_info['primary_image'];
                    
                    // 如果是第一个商品且主图为空，则使用该商品的图片作为主图
                    if ($key === 0 && empty($row['primary_image'])) {
                        $row['primary_image'] = $product_info['primary_image'];
                    }
                } else {
                    // 如果产品表中没有找到图片，尝试从其他来源获取
                    $other_product = $DB->getRow("SELECT primary_image FROM ozon_products WHERE offer_id = ?", [$product['offer_id']]);
                    if ($other_product && !empty($other_product['primary_image'])) {
                        $row['products_info'][$key]['image'] = $other_product['primary_image'];
                    } else {
                        // 尝试从商品API获取图片
                        $api_product = getProductImageFromAPI($product['sku'], $row['ClientId'], $row['key']);
                        if ($api_product && !empty($api_product['image'])) {
                            $row['products_info'][$key]['image'] = $api_product['image'];
                            // 保存到数据库以便下次使用
                            $DB->update('products', ['primary_image' => $api_product['image']], ['sku' => $product['sku']]);
                        } else {
                            // 如果没有找到图片，使用默认图片
                            $row['products_info'][$key]['image'] = '../assets/img/syncing.png';
                        }
                    }
                }
            }
        } else {
            $row['has_multiple_products'] = false;
        }
    return $row;
}

function getProductImageFromAPI($sku, $clientId, $key) {
    try {
        if (empty($sku) || empty($clientId) || empty($key)) {
            return null;
        }
        
        $client = new \lib\OzonApiClient($clientId, $key);
        $product_info = $client->getProductInfo($sku);
        
        if ($product_info && isset($product_info['result']) && isset($product_info['result']['primary_image'])) {
            return ['image' => $product_info['result']['primary_image']];
        } else if ($product_info && isset($product_info['result']) && isset($product_info['result']['images']) && !empty($product_info['result']['images'])) {
            return ['image' => $product_info['result']['images'][0]];
        }
    } catch (Exception $e) {
        error_log("Error getting product image from API: " . $e->getMessage());
    }
    
    return null;
}

// 添加优化函数，用于处理待处理子标签的查询
function optimize_pending_query($sql, $status) {
    global $DB;
    
    // 针对不同的子标签状态优化SQL查询
    if ($status == 'pending') {
        // 对于"全部"标签，使用索引优化查询
        $sql = str_replace("SELECT", "SELECT /*+ INDEX(orders idx_status) */", $sql);
        $sql .= " ORDER BY in_process_at DESC";
    } else if ($status == 'not_purchased') {
        // 对于"未采购"标签，添加索引提示
        $sql = str_replace("SELECT", "SELECT /*+ INDEX(orders idx_cost_status) */", $sql);
        $sql .= " ORDER BY in_process_at DESC";
    } else if ($status == 'purchased') {
        // 对于"已采购"标签，添加索引提示
        $sql = str_replace("SELECT", "SELECT /*+ INDEX(orders idx_status_cost) */", $sql);
        $sql .= " ORDER BY in_process_at DESC";
    }
    
    return $sql;
}