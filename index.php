<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海豚OZON店铺管理系统 - 专业跨境电商数据服ERP务</title>
    <link rel="stylesheet" href="/assets/fontawesome/css/all.min.css">
    <style>
        .btn-group.vertical {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-width: 300px;
            margin-top: 30px;
        }

        .btn-group.vertical .btn {
            width: 100%;
            justify-content: center;
            padding: 18px 25px;
            font-size: 1.1em;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-group.vertical .btn i {
            font-size: 1.2em;
            margin-right: 12px;
        }

        @media (min-width: 768px) {
            .btn-group.vertical {
                flex-direction: row;
                max-width: none;
                gap: 25px;
            }

            .btn-group.vertical .btn {
                width: auto;
                min-width: 220px;
                padding: 18px 35px;
            }
        }

        @media (max-width: 480px) {
            .btn-group.vertical .btn {
                font-size: 1em;
                padding: 16px 20px;
            }
        }

        :root {
            --primary-color: #3a7bd5;
            --secondary-color: #00d2ff;
            --accent-color: #4CAF50;
            --light-bg: #f8fafc;
            --white: #ffffff;
            --light-gray: #f1f5f9;
            --dark-gray: #334155;
            --text-color: #475569;
            --border-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-bg);
        }

        /* 价格表部分新增样式 */
        .pricing-section {
            padding: 100px 8%;
            background: var(--white);
            display: none;
            /* 默认隐藏 */
        }

        .pricing-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .pricing-plans {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .pricing-card {
            background: var(--white);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .pricing-card.free {
            border-top: 4px solid var(--accent-color);
        }

        .pricing-card.monthly {
            border-top: 4px solid var(--primary-color);
        }

        .pricing-card.quarterly {
            border-top: 4px solid #ff9800;
        }

        .pricing-card.yearly {
            border-top: 4px solid #9c27b0;
        }

        .pricing-card.recommended::before {
            content: '推荐';
            position: absolute;
            top: -10px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .pricing-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .pricing-title {
            font-size: 1.5em;
            color: var(--dark-gray);
            margin-bottom: 10px;
        }

        .pricing-price {
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .pricing-duration {
            color: #64748b;
            font-size: 0.9em;
        }

        .pricing-features {
            margin: 25px 0;
        }

        .pricing-feature {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.95em;
        }

        .pricing-feature i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .pricing-button {
            display: block;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .pricing-button.free {
            background: var(--accent-color);
            color: white;
        }

        .pricing-button.monthly {
            background: var(--primary-color);
            color: white;
        }

        .pricing-button.quarterly {
            background: #ff9800;
            color: white;
        }

        .pricing-button.yearly {
            background: #9c27b0;
            color: white;
        }

        .pricing-button:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }

        /* 原有样式保持不变... */
        /* 导航栏样式 */
        .navbar {
            background: var(--white);
            padding: 20px 8%;
            color: var(--dark-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            color: var(--primary-color);
        }

        .logo i {
            margin-right: 10px;
            color: var(--secondary-color);
        }

        .nav-items {
            display: flex;
            gap: 40px;
        }

        .nav-items span {
            cursor: pointer;
            font-weight: 500;
            padding: 5px 0;
            position: relative;
            transition: all 0.3s ease;
            color: var(--dark-gray);
        }

        .nav-items span:hover {
            color: var(--primary-color);
        }

        .nav-items span::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--secondary-color);
            transition: width 0.3s ease;
        }

        .nav-items span:hover::after {
            width: 100%;
        }

        /* 顶部展示区 */
        .hero-section {
            background: linear-gradient(135deg, var(--white) 0%, #f0f9ff 100%);
            padding: 100px 8% 120px;
            display: flex;
            gap: 80px;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 40%;
            height: 100%;
            background: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center/cover;
            clip-path: polygon(25% 0%, 100% 0%, 100% 100%, 0% 100%);
            z-index: 1;
            opacity: 0.9;
        }

        .left-panel {
            flex: 1;
            position: relative;
            z-index: 2;
        }

        .left-panel h1 {
            font-size: 3.2em;
            margin-bottom: 25px;
            color: var(--primary-color);
            line-height: 1.2;
            font-weight: 700;
        }

        .left-panel p {
            font-size: 1.2em;
            margin-bottom: 15px;
            max-width: 600px;
            color: var(--text-color);
        }

        /* 功能模块区 */
        .features {
            padding: 100px 8%;
            background: var(--white);
            text-align: center;
        }

        .section-title {
            font-size: 2.5em;
            color: var(--primary-color);
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: var(--secondary-color);
        }

        .section-subtitle {
            font-size: 1.2em;
            margin-bottom: 60px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            color: var(--text-color);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .feature-card {
            border-radius: 12px;
            padding: 40px 30px;
            background: var(--white);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.03);
            transition: all 0.3s ease;
            text-align: left;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
            border-color: var(--secondary-color);
        }

        .feature-card i {
            font-size: 2.5em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .feature-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .feature-card p {
            font-size: 1em;
            line-height: 1.8;
            color: var(--text-color);
        }

        /* 统计数据区 */
        .stats {
            padding: 80px 8%;
            background: linear-gradient(135deg, var(--primary-color) 0%, #00d2ff 100%);
            color: var(--white);
            text-align: center;
        }


        .stat-item {
            flex: 1;
            /* 等分剩余空间 */
            min-width: 200px;
            /* 最小宽度防止内容挤压 */
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            /* 半透明背景 */
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--white);
        }

        .stat-label {
            font-size: 1.2em;
            opacity: 0.9;
        }

        /* 合作伙伴区 */
        .partners {
            padding: 100px 8%;
            text-align: center;
            background: var(--light-bg);
        }

        .partner-logos {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-top: 50px;
            flex-wrap: wrap;
            align-items: center;
        }

        .partner-logo {
            height: 50px;
            opacity: 0.7;
            transition: all 0.3s ease;
            filter: grayscale(100%);
        }

        .partner-logo:hover {
            opacity: 1;
            filter: grayscale(0%);
        }

        /* 客户评价区 */
        .testimonials {
            padding: 100px 8%;
            background: var(--white);
            text-align: center;
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .testimonial-card {
            background: var(--light-bg);
            padding: 40px 30px;
            border-radius: 12px;
            text-align: left;
            position: relative;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: 20px;
            left: 30px;
            font-size: 5em;
            color: rgba(0, 0, 0, 0.05);
            font-family: serif;
            line-height: 1;
        }

        .testimonial-content {
            margin-bottom: 20px;
            font-style: italic;
            position: relative;
            z-index: 1;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
        }

        .author-info h4 {
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .author-info p {
            font-size: 0.9em;
            opacity: 0.8;
        }

        /* 底部CTA */
        .cta-section {
            padding: 100px 8%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white);
            text-align: center;
        }

        .cta-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta-content h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }

        .cta-content p {
            font-size: 1.2em;
            margin-bottom: 40px;
        }

        /* 页脚 */
        .footer {
            background: var(--dark-gray);
            color: var(--white);
            padding: 60px 8% 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin-bottom: 50px;
        }

        .footer-column h3 {
            color: var(--white);
            margin-bottom: 25px;
            font-size: 1.2em;
            position: relative;
            padding-bottom: 10px;
        }

        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background: var(--secondary-color);
        }

        .footer-column ul {
            list-style: none;
        }

        .footer-column ul li {
            margin-bottom: 15px;
        }

        .footer-column ul li a {
            color: #bbbbbb;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-column ul li a:hover {
            color: var(--secondary-color);
            padding-left: 5px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9em;
            color: #bbbbbb;
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .social-links a {
            color: var(--white);
            background: rgba(255, 255, 255, 0.1);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background: var(--secondary-color);
            transform: translateY(-3px);
        }

        /* 按钮样式 */
        .btn {
            padding: 15px 35px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn i {
            margin-right: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
            margin-right: 15px;
            box-shadow: 0 5px 15px rgba(58, 123, 213, 0.3);
        }

        .btn-primary:hover {
            background: #2c6bc7;
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(58, 123, 213, 0.4);
        }

        .btn-secondary {
            background: var(--accent-color);
            color: var(--white);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .btn-secondary:hover {
            background: #3d8b40;
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(76, 175, 80, 0.4);
        }

        .btn-outline {
            background: transparent;
            color: var(--white);
            border: 2px solid var(--white);
        }

        .btn-outline:hover {
            background: var(--white);
            color: var(--primary-color);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .hero-section::before {
                width: 50%;
            }
        }

        @media (max-width: 992px) {
            .hero-section {
                flex-direction: column;
                text-align: center;
                padding: 80px 5% 100px;
            }

            .hero-section::before {
                display: none;
            }

            .left-panel {
                text-align: center;
                margin-bottom: 50px;
            }

            .left-panel p {
                margin-left: auto;
                margin-right: auto;
            }

            .stats-container {
                display: flex;
                /* 启用弹性盒子布局 */
                justify-content: space-between;
                /* 两端对齐 */
                gap: 30px;
                /* 项间距 */
                flex-wrap: wrap;
                /* 允许换行 */
            }
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 15px 5%;
                flex-direction: column;
            }

            .nav-items {
                margin-top: 20px;
                gap: 20px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .feature-grid,
            .pricing-plans {
                grid-template-columns: 1fr;
            }

            .btn {
                display: block;
                width: 100%;
                margin-bottom: 15px;
            }

            .btn-primary {
                margin-right: 0;
            }
        }

        @media (max-width: 576px) {
            .stat-item {
                flex: 0 1 100%;
            }

            .section-title {
                font-size: 2em;
            }

            .left-panel h1 {
                font-size: 2.5em;
            }
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <i class="fas fa-dolphin"></i>
            海豚OZON数据
        </div>
        <div class="nav-items">
            <span onclick="showSection('home')">首页</span>
            <span onclick="showSection('pricing')">价格</span>
            <span><a href="./hterp1.7.zip">插件下载 </a></span>
            <span>CDK兑换</span>
            <span>帮助中心</span>
            <span>联系我们</span>
        </div>
    </nav>

    <!-- 首页内容 -->
    <div id="home-content">
        <!-- 顶部展示区 -->
        <section class="hero-section">
            <div class="left-panel">
                <h1>专业OZON数据服务提供商</h1>
                <p>系统满足企业级运营需求，提供全方位选品、工资单、订单管理等服务<br>
                    持续创新与维护，助力您成为最优秀的OZON跨境电商</p>
                <div class="btn-group vertical">
                    <a class="btn btn-primary" href="./user/login.php">
                        <i class="fas fa-tachometer-alt"></i>进入控制台
                    </a>
                    <a class="btn btn-secondary" href="./user/login.php">
                        <i class="fas fa-user-plus"></i>注册免费使用
                    </a>
                </div>
            </div>
        </section>

        <!-- 功能模块区 -->
        <section class="features">
            <h2 class="section-title">高效，便捷的OZON店铺管理系统</h2>
            <p class="section-subtitle">为新手OZON卖家提供低门槛选品上货工具，降低跨境学习成本，缩短出单空窗期，让您的跨境电商之路更加顺畅</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>选品分析</h3>
                    <p>使用多维销售参数筛选热门商品，精准把握市场趋势<br>
                        选品插件一键展示OZON平台热销商品数据<br>
                        24小时实时数据更新，确保决策依据最新市场动态</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-boxes"></i>
                    <h3>商品管理</h3>
                    <p>批量上货功能，极大提升工作效率<br>
                        智能定价计算器，自动计算最优价格区间<br>
                        多店铺矩阵运营支持，统一管理多个店铺</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>订单管理</h3>
                    <p>全流程物流状态跟踪提醒，随时掌握订单动态<br>
                        智能备货管理系统，自动计算补货需求<br>
                        专业扫码设备对接，快速处理大批量订单</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-truck"></i>
                    <h3>物流管理</h3>
                    <p>完整货代服务对接，一站式解决物流难题<br>
                        精确物流账单信息，清晰掌握每笔费用<br>
                        专业仓储贴标验货服务，确保商品符合平台要求</p>
                </div>
            </div>
        </section>

        <!-- 统计数据区 -->
        <section class="stats">
            <h2 class="section-title">我们的成就</h2>
            <p class="section-subtitle">海豚OZON数据服务已帮助数千家跨境电商企业实现业绩增长</p>


            <span class="stat-number">5,000+</span>
            <span class="stat-label">注册商家 </span>

            <span class="stat-number">95%</span>
            <span class="stat-label">客户满意度 </span>

            <span class="stat-number">24/7</span>
            <span class="stat-label">技术支持 </span>


            <span class="stat-number">30+</span>
            <span class="stat-label">合作物流商 </span>


        </section>

        <!-- 合作伙伴区 -->
        <section class="partners">
            <h2 class="section-title">合作伙伴</h2>
            <p class="section-subtitle">我们与行业领先企业建立战略合作，为您提供更全面的服务</p>

            <div class="partner-logos">
                <img src="https://via.placeholder.com/150x60?text=OZON" class="partner-logo" alt="OZON">
                <img src="https://via.placeholder.com/150x60?text=国欧物流" class="partner-logo" alt="国欧物流">
                <img src="https://via.placeholder.com/150x60?text=连连国际" class="partner-logo" alt="连连国际">
                <img src="https://via.placeholder.com/150x60?text=邮宝" class="partner-logo" alt="邮宝">
                <img src="https://via.placeholder.com/150x60?text=Global+E-Pay" class="partner-logo"
                    alt="Global E-Payment">
            </div>
        </section>

        <!-- 客户评价区 -->
        <section class="testimonials">
            <h2 class="section-title">客户评价</h2>
            <p class="section-subtitle">听听我们的客户怎么说</p>

            <div class="testimonial-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "使用海豚OZON系统后，我们的选品效率提升了300%，系统提供的数据非常精准，帮助我们避免了多个选品错误。"
                    </div>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" class="author-avatar" alt="客户头像">
                        <div class="author-info">
                            <h4>XX</h4>
                            <p>X电商 运营总</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "物流管理模块大大简化了我们的工作流程，现在可以实时跟踪所有订单状态，再也不用担心物流延误问题了。"
                    </div>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" class="author-avatar" alt="客户头像">
                        <div class="author-info">
                            <h4>XXX</h4>
                            <p>某在职宝妈</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "作为一个刚进入OZON平台的新手，海豚系统的培训资料和客服支持让我快速上手，现在店铺月销售额已突破5万美元。"
                    </div>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/men/75.jpg" class="author-avatar" alt="客户头像">
                        <div class="author-info">
                            <h4>XXX</h4>
                            <p>XXX贸易 创始人</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 底部CTA -->
        <section class="cta-section">
            <div class="cta-content">
                <h2>立即开始您的OZON跨境电商之旅</h2>
                <p>注册即可获得7天免费试用，体验专业数据服务带来的改变</p>
                <button class="btn btn-outline"><i class="fas fa-play"></i>观看演示视频</button>
                <button class="btn btn-secondary"><i class="fas fa-rocket"></i>立即注册</button>
            </div>
        </section>
    </div>

    <!-- 价格表部分 -->
    <section id="pricing-section" class="pricing-section">
        <div class="pricing-container">
            <h2 class="section-title">会员价格表</h2>
            <p class="section-subtitle">选择适合您的会员套餐，享受不同级别的服务特权</p>

            <div class="pricing-plans">
                <!-- 免费版 -->
                <div class="pricing-card free">
                    <div class="pricing-header">
                        <h3 class="pricing-title">免费</h3>
                        <div class="pricing-price">￥0</div>
                        <div class="pricing-duration">每天有免费的额度供您使用</div>
                    </div>
                    <div class="pricing-features">
                        <div class="pricing-feature"><i class="fas fa-mobile-alt"></i> 手机端浏览</div>
                        <div class="pricing-feature"><i class="fas fa-tags"></i> 跟卖数量 1/天</div>
                        <div class="pricing-feature"><i class="fas fa-store"></i> 店铺上限10个</div>
                        <div class="pricing-feature"><i class="fas fa-plug"></i> 插件批量采集</div>
                        <div class="pricing-feature"><i class="fas fa-shield-alt"></i> 反跟卖数量 1</div>
                        <div class="pricing-feature"><i class="fas fa-chart-bar"></i> 销量插件</div>
                    </div>
                    <button class="pricing-button free">立即使用</button>
                </div>

                <!-- 月度会员 -->
                <div class="pricing-card monthly recommended">
                    <div class="pricing-header">
                        <h3 class="pricing-title">月度会员</h3>
                        <div class="pricing-price">￥200</div>
                        <div class="pricing-duration">/30天</div>
                    </div>
                    <div class="pricing-features">
                        <div class="pricing-feature"><i class="fas fa-mobile-alt"></i> 手机端浏览</div>
                        <div class="pricing-feature"><i class="fas fa-tags"></i> 跟卖数量 无限</div>
                        <div class="pricing-feature"><i class="fas fa-store"></i> 店铺上限100个</div>
                        <div class="pricing-feature"><i class="fas fa-plug"></i> 插件批量采集</div>
                        <div class="pricing-feature"><i class="fas fa-shield-alt"></i> 反跟卖数量 1</div>
                        <div class="pricing-feature"><i class="fas fa-chart-bar"></i> 销量插件</div>
                    </div>
                    <button class="pricing-button monthly">立即购买</button>
                </div>

                <!-- 季度会员 -->
                <div class="pricing-card quarterly">
                    <div class="pricing-header">
                        <h3 class="pricing-title">季度会员</h3>
                        <div class="pricing-price">￥988</div>
                        <div class="pricing-duration">/90天</div>
                    </div>
                    <div class="pricing-features">
                        <div class="pricing-feature"><i class="fas fa-mobile-alt"></i> 手机端浏览</div>
                        <div class="pricing-feature"><i class="fas fa-tags"></i> 跟卖数量 无限</div>
                        <div class="pricing-feature"><i class="fas fa-store"></i> 店铺上限100个</div>
                        <div class="pricing-feature"><i class="fas fa-plug"></i> 插件批量采集</div>
                        <div class="pricing-feature"><i class="fas fa-shield-alt"></i> 反跟卖数量 1</div>
                        <div class="pricing-feature"><i class="fas fa-chart-bar"></i> 销量插件</div>
                    </div>
                    <button class="pricing-button quarterly">立即购买</button>
                </div>

                <!-- 年度会员 -->
                <div class="pricing-card yearly">
                    <div class="pricing-header">
                        <h3 class="pricing-title">年度会员</h3>
                        <div class="pricing-price">￥6800</div>
                        <div class="pricing-duration">/365天</div>
                    </div>
                    <div class="pricing-features">
                        <div class="pricing-feature"><i class="fas fa-mobile-alt"></i> 手机端浏览</div>
                        <div class="pricing-feature"><i class="fas fa-tags"></i> 跟卖数量 无限</div>
                        <div class="pricing-feature"><i class="fas fa-store"></i> 店铺上限100个</div>
                        <div class="pricing-feature"><i class="fas fa-plug"></i> 插件批量采集</div>
                        <div class="pricing-feature"><i class="fas fa-shield-alt"></i> 反跟卖数量 1</div>
                        <div class="pricing-feature"><i class="fas fa-chart-bar"></i> 销量插件</div>
                    </div>
                    <button class="pricing-button yearly">立即购买</button>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <button class="btn btn-primary" onclick="showSection('home')"><i class="fas fa-arrow-left"></i>
                    返回首页</button>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="footer-column">
                <h3>关于我们</h3>
                <ul>
                    <li><a href="#">公司简介</a></li>
                    <li><a href="#">发展历程</a></li>
                    <li><a href="#">团队介绍</a></li>
                    <li><a href="#">加入我们</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>产品服务</h3>
                <ul>
                    <li><a href="#">选品分析</a></li>
                    <li><a href="#">商品管理</a></li>
                    <li><a href="#">订单管理</a></li>
                    <li><a href="#">物流服务</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>帮助中心</h3>
                <ul>
                    <li><a href="#">常见问题</a></li>
                    <li><a href="#">使用教程</a></li>
                    <li><a href="#">API文档</a></li>
                    <li><a href="#">联系我们</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h3>联系我们</h3>
                <ul>
                    <li><i class="fas fa-phone-alt"></i> ************</li>
                    <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    <li><i class="fas fa-map-marker-alt"></i> 广东东莞</li>
                </ul>
                <div class="social-links">
                    <a href="#"><i class="fab fa-weixin"></i></a>
                    <a href="#"><i class="fab fa-weibo"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p><a href="https://beian.miit.gov.cn/">© 2025 极耀海豚ERP 版权所有 | 粤ICP备2025399873号-1</a> </p>
        </div>
    </footer>

    <script>
        // 默认显示首页内容
        document.addEventListener('DOMContentLoaded', function () {
            showSection('home');
        });

        // 显示不同部分的函数
        function showSection(sectionId) {
            // 隐藏所有主要内容区域
            document.getElementById('home-content').style.display = 'none';
            document.getElementById('pricing-section').style.display = 'none';

            // 显示选中的部分
            if (sectionId === 'home') {
                document.getElementById('home-content').style.display = 'block';
                window.scrollTo(0, 0);
            } else if (sectionId === 'pricing') {
                document.getElementById('pricing-section').style.display = 'block';
                // 滚动到价格表部分
                document.getElementById('pricing-section').scrollIntoView({ behavior: 'smooth' });
            }
        }
    </script>
</body