<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL); // 显示所有错误
include("../includes/common.php");

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

try {
    // 1. 添加详细错误报告
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
    
    $client = new Client([
        // 2. 禁用 SSL 验证（仅限测试环境）
        'verify' => false,
        // 3. 设置超时参数
        'timeout'  => 30.0,
        'connect_timeout' => 15.0
    ]);

    // 4. 分块处理长 cookie 值
    $cookie = '__Secure-ext_xcid=27b159ef24002b3fa90be52c644fa187; __Secure-ab-group=2; bacntid=5308574; __Secure-user-id=*********; skipOldBrowserPage=yes; __Secure-ETC=a6a9afd09bc914d78400c62dffe6c9ba; abt_data=7.e8iW9kfLzj-f2Myh7l81EAtAhHJIGmYMju5RqoWV89tlnSN277nVG-piimPBg1o4I6ZWLQ0ztg65n8rR2qtN0aYVtmFUBiNWlisoHt-9g3mFIedHHgojMoJPaTRfPFGo8b67vzJSB7yVZOCB4txR1Ec_lveEdR8KxWKTpjZNDnMLYQVWfI0pk0UY0w5LaMW8WpOADIU3_BcEfM_wakAoxzoKObFYOKeYaPgvXaKNLeUUdi1W5yB2ESS6xEslcrdJPR1-dgiAqQS83p4POZeEMT0ZzSrzMRx3Akc7wLKooywI-P1huS3GgEAyeVxocqVq0044mAbJoTv5HvtqnrULCuw0GNdzkM1Hzx2qpZKszV36qh8dkdeS8rmcgCPBQjMEsk9JLxBi-aDf46dcpjXWDIMk3q14_-vEO5Sei9fRyv8EMZXre1gmhuZTpC9jWW5MFx0LJisfIfksCBs3akzOPcUxHnIt2FZE5nj-5LsXzqQP52Frkn6Kx7ye5NxrlfBNUWKsgvRHTiVq7qM0j9zsvtM7KENaw5SSgzimHjRVKv2HSw6vhWa-uKzYgFQ5eAMBBb7skMenDkiYw-QN5-ywCSfTCTYrEStpNKFKnCkM21c; sc_company_id=2740471; __Secure-access-token=7.*********.yFHqruHwT-2RtQXjoVT4AA.2.AaOgNW2sS3PIXgiT1A3SebUtlqq53m2wa73_tt42DGta6oOd1DkA-gpj0QeVatDD2gOn3N5KUoy1F337Y39tnzELI9mJAGTkf_6uhHaJbJbf.20250312092657.20250512230335.znFQOqhxWChoHqs1_dbneCeXCyqgNDNKSM-cjBi1b_o.12be1b81afc9ede09; __Secure-refresh-token=7.*********.yFHqruHwT-2RtQXjoVT4AA.2.AaOgNW2sS3PIXgiT1A3SebUtlqq53m2wa73_tt42DGta6oOd1DkA-gpj0QeVatDD2gOn3N5KUoy1F337Y39tnzELI9mJAGTkf_6uhHaJbJbf.20250312092657.20250512230335.WsDT6nPSfqDkVmy4mYX3928tO11AiyegEmEAklIPNnc.18ed7ada9b5b2eab4';

    $response = $client->request('GET', 'https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url=/product/2081062018', [
        
        'headers' => [
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Accept-Language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'Cache-Control' => 'no-cache',
                'Pragma' => 'no-cache',
                'Referer' => 'https://www.google.com/',
                'Upgrade-Insecure-Requests' => '1',
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Sec-Fetch-Dest' => 'document',
                'Sec-Fetch-Mode' => 'navigate',
                'Sec-Fetch-Site' => 'cross-site',
                'Sec-Fetch-User' => '?1',
                'Cookie' => $cookie
        ],
        'proxy' => 'socks5://vip:99999999@***************:1080',
        // 5. 添加调试选项
        'debug' => true,
        // 6. 处理重定向
        'allow_redirects' => [
            'max' => 0,
            'strict' => true,
            'referer' => true,
            'track_redirects' => true
        ]
    ]);

    echo 'API 响应: ' . $response->getBody();

} catch (RequestException $e) {
    // 7. 详细错误处理
    echo "请求异常:\n";
    echo $e->getMessage() . "\n";
    if ($e->hasResponse()) {
        echo "响应状态码: " . $e->getResponse()->getStatusCode() . "\n";
        echo "响应内容: " . $e->getResponse()->getBody()->getContents() . "\n";
    }
} catch (Exception $e) {
    // 8. 捕获其他异常
    echo "通用异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
function generateFingerprint() {
        // 生成动态指纹参数
        return [
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/'.rand(100,115).'.0.0.0 Safari/537.36',
            'viewport' => 'width='.rand(1200,1920).', height='.rand(800,1080),
            'accept_language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'client_hints' => [
                'Sec-CH-UA' => '"Google Chrome";v="'.rand(110,115).'", "Not:A-Brand";v="8", "Chromium";v="'.rand(110,115).'"',
                'Sec-CH-UA-Platform' => '"Windows"',
                'Sec-CH-UA-Mobile' => '?0'
            ],
            'timezone' => 'Asia/Shanghai',
            'hardware_concurrency' => rand(4,8),
            'device_memory' => rand(4,8)
        ];
    }
?>