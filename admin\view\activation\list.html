<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>激活码管理</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>激活码管理</span>
        </div>
        <div class="layui-card-body">
            <!-- 搜索栏 -->
            <div class="layui-form layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <input type="text" id="search-input" placeholder="搜索激活码或用户名" class="layui-input">
                </div>
                <div class="layui-col-md3">
                    <select id="status-filter" class="layui-input">
                        <option value="">全部状态</option>
                        <option value="0">未使用</option>
                        <option value="1">已使用</option>
                    </select>
                </div>
                <div class="layui-col-md6">
                    <button class="layui-btn" id="search-btn">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button class="layui-btn layui-btn-normal" id="refresh-btn">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                    <button class="layui-btn layui-btn-primary" id="generate-btn">
                        <i class="layui-icon layui-icon-add-1"></i> 生成激活码
                    </button>
                </div>
            </div>
            
            <!-- 数据表格 -->
            <script type="text/html" id="tableToolbar">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batchDelete"><i class="layui-icon layui-icon-delete"></i> 批量删除</button>
                    <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="batchExport"><i class="layui-icon layui-icon-download-circle"></i> 批量导出</button>
                </div>
            </script>
            <table class="layui-hide" id="activation-table" lay-filter="activationTable"></table>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <!-- 表格操作按钮模板 -->
    <script type="text/html" id="toolbar">
        {{# if(d.status == 0) { }}
            <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
        {{# } }}
        <a class="layui-btn layui-btn-xs" lay-event="copy">复制</a>
    </script>
    
    <!-- 状态显示模板 -->
    <script type="text/html" id="status-tpl">
        {{# if(d.status == 0) { }}
            <span class="layui-badge layui-bg-blue">未使用</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-green">已使用</span>
        {{# } }}
    </script>

    <!-- 用户等级模板 -->
    <script type="text/html" id="level-tpl">
        {{# if(d.user_level == 1) { }}
            <span class="layui-badge">普通用户</span>
        {{# } else if(d.user_level == 2) { }}
            <span class="layui-badge layui-bg-blue">VIP用户</span>
        {{# } else if(d.user_level == 3) { }}
            <span class="layui-badge layui-bg-orange">SVIP用户</span>
        {{# } else if(d.user_level == 4) { }}
            <span class="layui-badge layui-bg-red">企业用户</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray">未知</span>
        {{# } }}
    </script>

    <script>
        layui.use(['table', 'layer', 'jquery'], function() {
            var table = layui.table;
            var layer = layui.layer;
            var $ = layui.jquery;
            
            // 渲染表格
            var tableIns = table.render({
                elem: '#activation-table',
                url: '/admin/ajax.php?act=activation_code_list',
                toolbar: '#tableToolbar',
                cols: [[
                    {type: 'checkbox', fixed: 'left'},
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'agent_uid', title: '代理UID', width: 120, templet: function(d) {
                        return d.agent_uid || '-';
                    }},
                    {field: 'code', title: '激活码', width: 200},
                    {field: 'user_level', title: '会员等级', width: 120, templet: '#level-tpl'},
                    {field: 'days', title: '有效天数', width: 100, align: 'center'},
                    {field: 'status', title: '状态', width: 100, templet: '#status-tpl'},
                    {field: 'used_by_username', title: '使用者', width: 120, templet: function(d) {
                        return d.used_by_username || '-';
                    }},
                    {field: 'used_at', title: '使用时间', width: 160, templet: function(d) {
                        return d.used_at || '-';
                    }},
                    {field: 'created_at', title: '创建时间', width: 160},
                    {title: '操作', width: 150, toolbar: '#toolbar', align: 'center'}
                ]],
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                loading: true,
                even: true
            });
            
            // 搜索功能
            $('#search-btn').on('click', function() {
                var keyword = $('#search-input').val().trim();
                var status = $('#status-filter').val();
                tableIns.reload({
                    where: {
                        search: keyword,
                        status: status
                    },
                    page: {
                        curr: 1
                    }
                });
            });
            
            // 回车搜索
            $('#search-input').on('keydown', function(e) {
                if (e.keyCode === 13) {
                    $('#search-btn').click();
                }
            });
            
            // 刷新按钮
            $('#refresh-btn').on('click', function() {
                $('#search-input').val('');
                $('#status-filter').val('');
                tableIns.reload({
                    where: {},
                    page: {
                        curr: 1
                    }
                });
            });
            
            // 生成激活码按钮
            $('#generate-btn').on('click', function() {
                if (window.parent.PearAdmin) {
                    window.parent.PearAdmin.changePage({
                        id: 'activation-generate',
                        title: '生成激活码',
                        url: 'view/activation/generate.html'
                    });
                } else {
                    layer.alert('父框架 PearAdmin 对象不存在，无法打开新页面。');
                }
            });

            // 监听表格头部工具栏事件
            table.on('toolbar(activationTable)', function(obj) {
                var checkStatus = table.checkStatus(obj.config.id);
                var data = checkStatus.data;
                
                if (data.length === 0) {
                    layer.msg('请先选择要操作的数据', {icon: 2});
                    return;
                }

                var ids = data.map(function(item) {
                    return item.id;
                });

                if (obj.event === 'batchDelete') {
                    layer.confirm('确定要删除选中的 ' + data.length + ' 个激活码吗？', {icon: 3, title: '提示'}, function(index) {
                        $.ajax({
                            url: '/admin/ajax.php?act=batch_delete_activation_codes',
                            type: 'POST',
                            data: {ids: ids},
                            dataType: 'json',
                            success: function(res) {
                                if (res.code === 1) {
                                    layer.msg('成功删除 ' + res.data.count + ' 个激活码', {icon: 1});
                                    tableIns.reload();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('网络异常', {icon: 2});
                            }
                        });
                        layer.close(index);
                    });
                } else if (obj.event === 'batchExport') {
                    var url = '/admin/ajax.php?act=export_activation_codes&ids=' + ids.join(',');
                    window.location.href = url;
                    layer.msg('正在为您导出数据...', {icon: 1});
                }
            });

            // 监听表格工具条
            table.on('tool(activationTable)', function(obj) {
                var data = obj.data;
                
                if (obj.event === 'delete') {
                    layer.confirm('确定要删除这个激活码吗？', {icon: 3, title: '提示'}, function(index) {
                        $.ajax({
                            url: '/admin/ajax.php?act=delete_activation_code',
                            type: 'POST',
                            data: {id: data.id},
                            dataType: 'json',
                            success: function(res) {
                                if (res.code === 1) {
                                    layer.msg('删除成功', {icon: 1});
                                    tableIns.reload();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('网络异常', {icon: 2});
                            }
                        });
                        layer.close(index);
                    });
                } else if (obj.event === 'copy') {
                    // 复制激活码到剪贴板
                    var textarea = document.createElement('textarea');
                    textarea.value = data.code;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    layer.msg('激活码已复制到剪贴板', {icon: 1});
                }
            });
        });
    </script>
</body>
</html> 