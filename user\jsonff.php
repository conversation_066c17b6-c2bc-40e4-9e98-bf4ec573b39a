<?php
include("../includes/common.php");
$id = intval($_GET['id']);
$row=$DB->getRow("SELECT * FROM ozon_production WHERE id=:id AND uid=:uid limit 1", [':id'=>$id,':uid'=>$uid]);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/assets/component/layui/layui.js"></script>
    <script src="/assets/js/jquery-3.6.0.min.js"></script>
    <!-- 美图设计室图片编辑器SDK -->
    <script src="https://public.static.meitudata.com/xiuxiu-pc/image-editor-sdk/3.0.0/dist/index.min.js"></script>
    <title>商品详情展示系统</title>
    <style>
        /* 基础样式 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        .debug-bar {
            position: fixed;
            top: 0;
            right: 0;
            background: #333;
            padding: 10px;
            color: white;
            z-index: 1000;
        }

        .gallery-container {
            max-width: 1200px;
            margin: 60px auto 0;
            padding: 20px;
        }

        .image-card {
            position: relative;
            margin: 20px 0;
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s;
        }

        .responsive-image {
            width: 100%;
            height: auto;
            display: block;
        }

        .delete-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ff4757;
            color: white;
            border: none;
            padding: 8px 15px;
            cursor: pointer;
            border-radius: 4px;
            opacity: 0.9;
        }
        
        /* 编辑按钮样式 */
        .edit-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.85;
            transition: all 0.3s;
            z-index: 3;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .edit-btn:hover {
            background: #2980b9;
            opacity: 1;
            transform: scale(1.1);
        }
        
        .edit-btn::before {
            content: "✏";
            font-size: 16px;
            font-weight: bold;
        }

        /* 移动端适配 */
        @media (max-width: 767px) {
            .gallery-container {
                padding: 10px;
            }
            
            .image-card {
                margin: 15px -10px;
                border-radius: 0;
            }
        }

        /* 调试模式 */
        .mobile-preview .image-card {
            max-width: 375px;
            margin: 15px auto;
        }
        /* 新增按钮样式 */
        .translate-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #2ecc71;
            color: white;
            border: none;
            padding: 8px 15px;
            cursor: pointer;
            border-radius: 4px;
            opacity: 0.9;
            z-index: 2;
        }
        
        /* 翻译状态指示 */
        .translating::after {
            content: "翻译中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
        }
        
        /* 优化后的调试工具栏样式 */
        .debug-bar {
            position: fixed;
            top: 0;
            right: 0;
            background: #333;
            padding: 10px 20px;
            color: white;
            z-index: 1000;
            display: flex;
            gap: 15px;
            border-radius: 0 0 0 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .debug-bar button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            background: #444;
            color: #fff;
            font-size: 14px;
        }
        
        .debug-bar button:hover {
            background: #555;
            transform: translateY(-1px);
        }
        
        /* 为不同功能按钮添加颜色区分 */
        .debug-bar button:nth-child(1) { background: #3498db; } /* 设备模式 */
        .debug-bar button:nth-child(2) { background: #2ecc71; } /* 翻译按钮 */
        .debug-bar button:nth-child(3) { background: #e67e22; } /* 保存按钮 */
        
        /* 添加加载状态指示 */
        .image-card:not(.loaded) {
            background: #f5f6fa;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 0.5; }
            100% { opacity: 0.8; }
        }


    </style>
</head>
<body>
    <!-- 调试工具栏 -->
    <div class="debug-bar">
        <button onclick="toggleDeviceMode()">切换设备模式</button>
        <button onclick="translateAllImages()">全部图片翻译</button>
        <button onclick="saveAllChanges()">保存所有修改</button>
    </div>

    <!-- 图片容器 -->
    <div class="gallery-container" id="gallery"></div>

    <script>
        // 美图SDK初始化
        let isEditorInitialized = false;
        
        function initMeiTuEditor() {
            if (isEditorInitialized) return;
            
            try {
                MTImageEditor.init({
                    moduleName: 'image-editor-sdk',
                    accessKey: 'test_app_key_demo',
                    title: '美图设计室Web版',
                    fullscreen: true,
                    resizeAble: true
                });
                isEditorInitialized = true;
                console.log('美图SDK初始化成功');
            } catch (error) {
                console.error('美图SDK初始化失败:', error);
            }
        }
        
        // 等待SDK加载完成
        window.addEventListener('load', () => {
            setTimeout(initMeiTuEditor, 500);
        });
        
        // 打开美图编辑器
        function openImageEditor(imageUrl, index) {
            if (!isEditorInitialized) {
                layer.msg('美图编辑器初始化中，请稍候...', {icon: 16});
                initMeiTuEditor();
                setTimeout(() => openImageEditor(imageUrl, index), 1000);
                return;
            }
            
            try {
                MTImageEditor.open({
                    imgSrc: imageUrl,
                    onOk: function(result) {
                        saveEditedImage(result.data, index);
                    },
                    onError: function(error) {
                        layer.msg('编辑器打开失败: ' + error.message, {icon: 2});
                    },
                    onClose: function() {
                        console.log('编辑器已关闭');
                    }
                });
            } catch (error) {
                layer.msg('打开编辑器失败: ' + error.message, {icon: 2});
            }
        }
        
        // 保存编辑后的图片
        async function saveEditedImage(base64Data, index) {
            const loading = layer.msg('保存编辑后的图片...', {icon: 16, time: 0});
            
            try {
                const response = await fetch('./ajax.php?act=save_edited_image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image_data: base64Data,
                        product_id: '<?=$id?>'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 更新图片数据
                    const block = productData.content[0].blocks[index];
                    block.img.src = result.image_url;
                    block.img.srcMobile = result.image_url;
                    
                    // 重新渲染画廊
                    await initGallery();
                    
                    layer.msg('图片编辑并保存成功！', {icon: 1});
                } else {
                    throw new Error(result.message || '保存失败');
                }
            } catch (error) {
                layer.msg('保存失败: ' + error.message, {icon: 2});
            } finally {
                layer.close(loading);
            }
        }

        // 原始JSON数据
        const productData = <?=$row['json']?>;

        // 初始化函数
        async function initGallery() {
            const gallery = document.getElementById('gallery');
            gallery.innerHTML = '';
        
            // 按顺序创建容器占位
            const placeholderFragment = document.createDocumentFragment();
            productData.content[0].blocks.forEach((block, index) => {
                const card = document.createElement('div');
                card.className = 'image-card';
                card.dataset.index = index;
                card.style.minHeight = '100px'; // 预占位高度
                placeholderFragment.appendChild(card);
            });
            gallery.appendChild(placeholderFragment);
        
            // 严格按顺序加载
            for (const [index, block] of productData.content[0].blocks.entries()) {
                try {
                    const card = gallery.querySelector(`[data-index="${index}"]`);
                    
                    // 创建图片元素
                    const img = document.createElement('img');
                    img.className = 'responsive-image';
                    
                    // 设置初始源
                    img.src = block.img.src;
        
                    // 添加翻译按钮
                    const translateBtn = document.createElement('button');
                    translateBtn.className = 'translate-btn';
                    translateBtn.innerHTML = '翻译';
                    translateBtn.onclick = () => translateImage(index);
        
                    // 删除按钮
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'delete-btn';
                    deleteBtn.innerHTML = '删除';
                    deleteBtn.onclick = () => deleteImage(index);
                    
                    // 编辑按钮
                    const editBtn = document.createElement('button');
                    editBtn.className = 'edit-btn';
                    editBtn.onclick = () => openImageEditor(block.img.src, index);
        
                    // 同步加载图片
                    await new Promise(resolve => {
                        img.onload = () => {
                            // 自动获取尺寸
                            if (!block.img.width || !block.img.height) {
                                block.img.width = img.naturalWidth;
                                block.img.height = img.naturalHeight;
                            }
                            resolve();
                        };
                        img.onerror = resolve; // 即使加载失败也继续
                    });
        
                    // 填充卡片内容
                    card.innerHTML = '';
                    card.style.minHeight = '';
                    card.appendChild(translateBtn);
                    card.appendChild(img);
                    card.appendChild(editBtn);
                    card.appendChild(deleteBtn);
        
                    // 添加尺寸标记
                    card.dataset.size = `${block.img.width}x${block.img.height}`;
        
                    // 添加加载完成标记
                    card.classList.add('loaded');
        
                } catch (e) {
                    console.error(`图片${index}加载失败:`, e);
                }
            }
        }

        
        // 删除图片
        function deleteImage(index) {
            // 直接从数据源中移除，并重新渲染画廊
            productData.content[0].blocks.splice(index, 1);
            initGallery(); 
        }

        // 切换设备模式
        function toggleDeviceMode() {
            document.body.classList.toggle('mobile-preview');
        }

        // 初始化加载
        window.addEventListener('DOMContentLoaded', initGallery);
        window.addEventListener('resize', initGallery);
        
        // 翻译功能实现
        async function translateImage(index) {
            const block = productData.content[0].blocks[index];
            const originalImg = block.img;
            try {
                const res = await fetch('./ajax.php?act=json_translate', {
                    method: 'POST',
                    body: JSON.stringify({
                        url: isMobile() ? block.img.srcMobile : block.img.src
                    })
                });
                
                const { translatedUrl } = await res.json();
                
                productData.content[0].blocks[index].img = {
                    ...originalImg, // 保留所有原始属性
                    src: translatedUrl,
                    srcMobile: translatedUrl,
                    // 自动获取新图片尺寸（示例）
                    width: await getImageSize(translatedUrl).width,
                    height: await getImageSize(translatedUrl).height
                };
                
                initGallery(); // 重新渲染
            } catch (error) {
                console.error('翻译失败:', error);
            }
        }
        
        // 全部翻译功能框架
        async function translateAllImages() {
            const load = layer.msg('开始翻译全部图片...', {icon: 16, time:0});
            
            try {
                const blocks = productData.content[0].blocks;
                for(let i=0; i<blocks.length; i++) {
                    await translateImage(i); // 复用单图翻译逻辑
                }
                layer.msg(`成功翻译${blocks.length}张图片`, {icon:1});
            } catch(e) {
                layer.msg('翻译失败: ' + e.message, {icon:2});
            } finally {
                layer.close(load);
            }
        }
        
        // 新增图片尺寸获取函数
        function getImageSize(url) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = function() {
                    resolve({
                        width: this.width,
                        height: this.height
                    });
                };
                img.src = url;
            });
        }

        // 设备检测
        function isMobile() {
            return window.innerWidth <= 767;
        }

        // 加强版保存函数
        async function saveAllChanges() {
            const loading = layer.msg('正在保存数据...', { icon: 16, time: 0 });

            try {
                // 直接使用当前最新的 productData 对象，因为它已通过 deleteImage 函数保持了同步
                const payload = productData;

                const res = await fetch('./ajax.php?act=save_json', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: '<?=$id?>', data: payload })
                });

                if (!res.ok) {
                    const errorText = await res.text();
                    throw new Error(`服务器错误: ${res.status} ${errorText}`);
                }

                const result = await res.json();
                if (result.code === 0) {
                    layer.msg('保存成功！', { icon: 1 });
                } else {
                    layer.msg(result.msg || '保存失败', { icon: 2 });
                }

            } catch (error) {
                console.error('保存操作失败:', error);
                layer.msg(`保存失败: ${error.message}`, { icon: 2 });
            } finally {
                layer.close(loading);
            }
        }
    </script>
</body>
</html>