<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>智能物流管理系统</title>
    <style>
        :root {
            --primary-color: #2196F3;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
        }

        body {
            margin: 0;
            padding: 10px;
            background: #f0f0f0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-y: auto;
            padding-bottom: 80px;
        }

        #camera-container {
            position: relative;
            width: 100%;
            height: 30vh;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        #camera-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .scan-overlay {
            position: absolute;
            top: 25%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 100px;
            border: 3px solid #FF5722;
            border-radius: 8px;
            animation: pulse 2s infinite;
        }

        .scan-tip {
            color: #FF5722;
            text-align: center;
            position: absolute;
            width: 100%;
            top: 110%;
            font-size: 14px;
        }

        .input-group {
            margin: 15px 0;
            display: flex;
            gap: 10px;
        }

        .form-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }

        .product-info {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            max-height: 50vh;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .product-image {
            width: 100%;
            height: 150px;
            object-fit: contain;
            margin: 10px 0;
        }

        .button-group {
            display: flex;
            gap: 8px;
            justify-content: center;
            position: fixed;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: white;
            padding: 10px;
            border-radius: 12px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 100;
        }

        .action-button {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            min-width: 120px;
        }

        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 0.4; }
            100% { opacity: 0.8; }
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            display: none;
            z-index: 200;
        }

        .product-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .product-item.selected {
            background: #e0f7fa; /* 勾选后卡片颜色 */
        }

        @media screen and (max-width: 600px) {
            #camera-container { height: 25vh; }
            .form-input { font-size: 14px; padding: 10px; }
            .input-group { flex-direction: column; }
            .action-button { font-size: 14px; padding: 10px; }
            .product-image { height: 120px; }
            .button-group { bottom: 5px; padding: 8px; }
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            bottom: 100%;
            margin-bottom: 5px;
            border-radius: 8px;
        }

        .dropdown-content a {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            border-radius: 8px;
        }

        .dropdown-content a:hover {
            background-color: #f1f1f1;
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }

        .show {
            display: block;
        }

        .exception-modal {
            display: none;
            position: fixed;
            z-index: 300;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        .exception-modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 10px;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .exception-form label {
            display: block;
            margin: 10px 0 5px;
            font-weight: bold;
        }

        .exception-form select,
        .exception-form textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }

        .exception-form button {
            margin-top: 15px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            color: white;
            cursor: pointer;
        }

        .btn-submit {
            background-color: var(--success-color);
        }

        .btn-cancel {
            background-color: #f44336;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div id="camera-container">
        <video id="camera-preview" playsinline autoplay></video>
        <div class="scan-overlay">
            <div class="scan-tip">请将条码置于橙色框内扫描</div>
        </div>
    </div>

    <div class="input-group">
        <input type="text" class="form-input" placeholder="输入物流单号" id="trackingNumber">
        <button class="action-button" style="background: var(--success-color)" 
                onclick="handleManualInput()">提交</button>
    </div>

    <div class="product-info" id="productInfo">
        <div id="product-list"></div>
    </div>

    <div class="button-group">
        <button class="action-button" style="background: var(--primary-color)" 
                onclick="handlePrint()">打印标签</button>
        <button class="action-button" style="background: var(--success-color)" 
                onclick="markAsShipped()">标记发货</button>
        <div class="dropdown">
            <button class="action-button" style="background: var(--warning-color)" 
                    onclick="toggleDropdown()">异常问题</button>
            <div id="dropdown-content" class="dropdown-content">
                <a href="#" onclick="openExceptionModal('query_exception')">查询不到物流单号</a>
                <a href="#" onclick="openExceptionModal('tracking_number_error')">国际单号有问题</a>
                <a href="#" onclick="openExceptionModal('other_exception')">其他异常</a>
            </div>
        </div>
    </div>

    <div id="exceptionModal" class="exception-modal">
        <div class="exception-modal-content">
            <span class="close" onclick="closeExceptionModal()">&times;</span>
            <h2>标记异常</h2>
            <form id="exceptionForm" class="exception-form">
                <input type="hidden" id="exceptionType" name="exceptionType" value="">
                <label for="exceptionReason">异常原因：</label>
                <textarea id="exceptionReason" name="exceptionReason" rows="4" placeholder="请输入异常原因..."></textarea>
                <div class="button-group" style="position: static; margin-top: 20px;">
                    <button type="button" class="btn-cancel" onclick="closeExceptionModal()">取消</button>
                    <button type="button" class="btn-submit" onclick="submitException()">提交</button>
                </div>
            </form>
        </div>
    </div>

    <div class="loading" id="loading">加载中...</div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js"></script>
    <script>
        let currentStream = null;
        let currentTrackingNumber = '';
        let currentExceptionType = '';
        const API_ENDPOINT = './ajax.php';

        // 系统初始化
        async function initSystem() {
            await initCamera();
            initBarcodeScanner();
            setupViewportAdjust();
        }

        // 摄像头初始化
        async function initCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'environment',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    }
                });

                const video = document.getElementById('camera-preview');
                video.srcObject = stream;
                currentStream = stream;

                video.play().catch(() => {
                    showMessage('请允许摄像头访问权限');
                });

            } catch (error) {
                showMessage('摄像头初始化失败: ' + error.message);
            }
        }

        // 条码扫描初始化
        function initBarcodeScanner() {
            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.getElementById('camera-preview'),
                    constraints: {
                        facingMode: 'environment'
                    },
                    area: {
                        top: "0%",
                        right: "0%",
                        left: "0%",
                        bottom: "60%"
                    }
                },
                decoder: {
                    readers: ['code_128_reader']
                },
                locate: true
            }, err => {
                if (err) {
                    console.error('扫码器初始化失败:', err);
                    showMessage('扫码功能初始化失败');
                    return;
                }
                Quagga.start();
            });

            Quagga.onDetected(result => {
                const code = result.codeResult.code;
                if (/^[A-Z0-9]{10,20}$/.test(code)) {
                    handleScanResult(code);
                }
            });
        }

        // 处理扫描结果
        function handleScanResult(code) {
            currentTrackingNumber = code;
            document.getElementById('trackingNumber').value = code;
            fetchProductInfo(code);
            scrollToProductInfo();
        }

        // 手动输入处理
        async function handleManualInput() {
            const input = document.getElementById('trackingNumber').value.trim();
            if (!/^[A-Z0-9]{10,20}$/.test(input)) {
                return showMessage('单号格式错误（10-20位字母数字组合）');
            }
            currentTrackingNumber = input;
            fetchProductInfo(input);
            scrollToProductInfo();
        }

        // 获取商品信息
        async function fetchProductInfo(trackingNumber) {
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=get_product_info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ trackingNumber })
                });

                const res = await response.json();
                if (res.code === 2) {
                    // 未找到对应商品信息，直接标记查询异常
                    await markException(trackingNumber, 'query_exception', '查询不到物流单号');
                    throw new Error(res.msg);
                } else if (res.code === 3) {
                    // 查询到物流单号，但国际单号有问题
                    await markException(trackingNumber, 'tracking_number_error', '国际单号格式不正确');
                    throw new Error(res.msg);
                } else if (res.code !== 0) {
                    throw new Error(res.msg || '获取信息失败');
                }
                
                updateProductInfo(res.data || []);

            } catch (error) {
                showMessage(error.message);
                document.getElementById('productInfo').style.display = 'none';
            } finally {
                showLoading(false);
            }
        }

        // 更新产品信息显示
        function updateProductInfo(data) {
            const productList = document.getElementById('product-list');
            productList.innerHTML = ''; // 清空之前的内容

            if (data.length === 0) {
                document.getElementById('productInfo').style.display = 'none';
                return;
            }

            data.forEach(product => {
                const productItem = document.createElement('div');
                productItem.classList.add('product-item');
                productItem.setAttribute('data-tracking-number', product.courierNumber || product.tracking_number);
                
                // 如果订单已标记为异常，显示异常信息
                if (product.is_exception) {
                    productItem.classList.add('exception');
                    productItem.style.border = '2px solid red';
                }

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        productItem.classList.add('selected');
                    } else {
                        productItem.classList.remove('selected');
                    }
                });
                productItem.appendChild(checkbox);

                const productImage = document.createElement('img');
                productImage.classList.add('product-image');
                productImage.src = product.primary_image || 'https://picsum.photos/200/300';
                productItem.appendChild(productImage);

                const productModel = document.createElement('h3');
                const infoParts = [
                    product.order_name || '未知型号',
                    product.quantity && `数量: ${product.quantity}`,
                    product.dimensions && `尺寸: ${product.dimensions}`,
                    product.color && `颜色: ${product.color}`,
                    product.tracking_number && `国际单号: ${product.tracking_number}`
                ].filter(Boolean);
                productModel.innerHTML = infoParts.join(' · ');
                productItem.appendChild(productModel);
                
                // 如果订单已标记为异常，显示异常信息
                if (product.is_exception) {
                    const exceptionInfo = document.createElement('div');
                    exceptionInfo.style.color = 'red';
                    exceptionInfo.style.marginTop = '10px';
                    const exceptionTypeName = {
                        'query_exception': '查询异常',
                        'tracking_number_error': '国际单号错误',
                        'other_exception': '其他异常'
                    }[product.exception_type] || '未知异常';
                    exceptionInfo.innerHTML = `异常: ${exceptionTypeName} (${product.exception_reason})`;
                    productItem.appendChild(exceptionInfo);
                }

                productList.appendChild(productItem);
            });

            document.getElementById('productInfo').style.display = 'block';
        }

        // 获取勾选的单号
        function getSelectedTrackingNumbers() {
            const checkboxes = document.querySelectorAll('.product-item input[type="checkbox"]:checked');
            const trackingNumbers = [];
            checkboxes.forEach(checkbox => {
                const productItem = checkbox.closest('.product-item');
                const trackingNumber = productItem.getAttribute('data-tracking-number');
                if (trackingNumber) {
                    trackingNumbers.push(trackingNumber);
                }
            });
            return trackingNumbers;
        }

        // 标记发货
        async function markAsShipped() {
            const trackingNumbers = getSelectedTrackingNumbers();
            if (trackingNumbers.length === 0) {
                return showMessage('请勾选要标记发货的商品');
            }
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=mark_shipped', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ trackingNumbers })
                });
                
                const res = await response.json();
                if (res.code !== 0) throw new Error(res.msg || '标记失败');
                showMessage('已成功标记发货');
                fetchProductInfo(currentTrackingNumber);
                
            } catch (error) {
                showMessage(error.message);
            } finally {
                showLoading(false);
            }
        }

        // 打印标签（包含接口请求）
        async function handlePrint() {
            const trackingNumbers = getSelectedTrackingNumbers();
            if (trackingNumbers.length === 0) {
                return showMessage('请勾选要打印标签的商品');
            }
            showLoading(true);
            try {
                // 发送打印请求
                const printResponse = await fetch(API_ENDPOINT + '?act=print_label', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        trackingNumbers,
                        print_time: new Date().toISOString()
                    })
                });

                const printResult = await printResponse.json();
                if (printResult.code !== 0) {
                    throw new Error(printResult.msg || '打印请求提交失败');
                }

                // 执行实际打印
                const content = document.getElementById('productInfo').cloneNode(true);
                const printWindow = window.open('', '_blank');
                
                printWindow.document.write(`
                    <html><head>
                        <title>打印标签 - ${trackingNumbers.join(', ')} </title>
                        <style>
                            img { max-width: 100%; height: auto; }
                            body { padding: 5mm; font-size: 4mm; }
                            h3 { margin: 2mm 0; font-size: 5mm; }
                        </style>
                    </head>
                    <body>${content.innerHTML}</body>
                `);
                printWindow.document.close();
                printWindow.print();
                setTimeout(() => printWindow.close(), 500);

            } catch (error) {
                showMessage('打印失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 显示/隐藏下拉菜单
        function toggleDropdown() {
            document.getElementById('dropdown-content').classList.toggle('show');
        }

        // 打开异常模态框
        function openExceptionModal(exceptionType) {
            currentExceptionType = exceptionType;
            document.getElementById('exceptionType').value = exceptionType;
            document.getElementById('exceptionModal').style.display = 'block';
            
            // 根据异常类型设置默认原因
            const defaultReasons = {
                'query_exception': '系统中未查询到该物流单号',
                'tracking_number_error': '国际单号格式不正确',
                'other_exception': ''
            };
            document.getElementById('exceptionReason').value = defaultReasons[exceptionType] || '';
            
            // 关闭下拉菜单
            document.getElementById('dropdown-content').classList.remove('show');
        }

        // 关闭异常模态框
        function closeExceptionModal() {
            document.getElementById('exceptionModal').style.display = 'none';
        }

        // 提交异常
        async function submitException() {
            const trackingNumbers = getSelectedTrackingNumbers();
            if (trackingNumbers.length === 0) {
                return showMessage('请勾选要标记异常的商品');
            }
            
            const exceptionType = document.getElementById('exceptionType').value;
            const exceptionReason = document.getElementById('exceptionReason').value.trim();
            
            if (!exceptionReason) {
                return showMessage('请输入异常原因');
            }
            
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=mark_exception', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        trackingNumbers,
                        exceptionType,
                        exceptionReason
                    })
                });

                const res = await response.json();
                if (res.code !== 0) throw new Error(res.msg || '标记异常失败');
                showMessage('已成功标记异常');
                closeExceptionModal();
                fetchProductInfo(currentTrackingNumber);

            } catch (error) {
                showMessage('标记异常失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 直接标记异常（用于系统自动检测到的异常）
        async function markException(trackingNumber, exceptionType, exceptionReason) {
            showLoading(true);
            try {
                const response = await fetch(API_ENDPOINT + '?act=mark_exception', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        trackingNumbers: [trackingNumber],
                        exceptionType,
                        exceptionReason
                    })
                });

                const res = await response.json();
                if (res.code !== 0) throw new Error(res.msg || '标记异常失败');

            } catch (error) {
                console.error('自动标记异常失败:', error);
            } finally {
                showLoading(false);
            }
        }

        // 通用功能
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showMessage(msg) {
            alert(msg);
        }

        // 移动端视口调整
        function setupViewportAdjust() {
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    window.scrollTo(0, 0);
                    document.body.style.height = 'calc(100% - 300px)';
                });
                
                input.addEventListener('blur', () => {
                    document.body.style.height = 'auto';
                });
            });
        }

        function scrollToProductInfo() {
            document.getElementById('productInfo').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // 点击页面其他地方关闭下拉菜单
        window.onclick = function(event) {
            if (!event.target.matches('.action-button')) {
                const dropdowns = document.getElementsByClassName('dropdown-content');
                for (let i = 0; i < dropdowns.length; i++) {
                    const openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains('show')) {
                        openDropdown.classList.remove('show');
                    }
                }
            }
        }

        // 初始化系统
        initSystem();
    </script>
</body>
</html>    