# 用户权限管理系统

## 系统概述

本系统基于现有的 `ozon_user` 表实现用户权限管理，主要功能包括：

1. **页面访问限制** - 根据用户状态控制菜单显示
2. **接口权限限制** - 限制用户的操作权限
3. **店铺数量限制** - 根据数据库中的 `max_shops` 字段限制店铺数量
4. **自动状态同步** - 定时任务自动处理用户到期状态
5. **配置开关控制** - 支持动态开启/关闭各项权限功能

## 数据库字段说明

### ozon_user 表关键字段

- `status`: 用户状态 (0=禁用, 1=正常, 2=待激活)
- `user_level`: 用户等级 (0=普通用户, 1-4=付费用户)
- `max_shops`: 最大店铺数量 (从数据库直接读取)
- `expiry_date`: 会员到期时间 (datetime格式)

## 功能实现

### 1. 菜单权限控制

**文件**: `user/ajax.php` - `menu` 接口

- **正常用户** (`status=1`): 显示完整菜单
- **禁用用户** (`status!=1`): 只显示基础菜单（首页、工作空间）

### 2. 接口权限检查

**文件**: `includes/member.php` - 权限检查函数

```php
// 检查用户权限
$permission_check = checkUserPermission($uid, 'store_add');
if (!$permission_check['allowed']) {
    exit(json_encode(['code' => -1, 'msg' => $permission_check['message']]));
}
```

**支持的操作类型**:
- `store_add`: 添加店铺权限检查
- `product_import`: 商品导入权限检查（可扩展）

### 3. 店铺数量限制

**实现位置**: `user/ajax.php` - `store_add` 接口

- 检查用户当前店铺数量
- 严格按照数据库中的 `max_shops` 字段值进行比较
- 只有当 `max_shops > 0` 时才进行限制检查
- 超出限制时返回错误信息

### 4. 权限信息接口

**接口**: `user/ajax.php?act=get_user_permissions`

**返回数据**:
```json
{
    "code": 0,
    "data": {
        "is_active": true,
        "max_shops": 5,
        "current_shops": 2,
        "user_level": 1,
        "expiry_date": "2024-12-31 23:59:59",
        "can_add_store": true
    }
}
```

## 定时任务

### 用户状态同步脚本

**文件**: `worker/user_status_sync.php`

**功能**:
- 检查已到期的用户 (`expiry_date < NOW()`)
- 自动将到期用户状态更新为禁用 (`status = 0`)
- 记录操作日志到 `worker/logs/user_status_sync.log`
- 显示即将到期的用户提醒（7天内）

**使用方法**:
```bash
# 手动执行
php worker/user_status_sync.php

# 添加到crontab（每天凌晨2点执行）
0 2 * * * /usr/bin/php /path/to/worker/user_status_sync.php
```

## 使用示例

### 1. 在接口中添加权限检查

```php
case 'your_operation':
    // 检查用户状态
    $permission_check = checkUserPermission($uid);
    if (!$permission_check['allowed']) {
        exit(json_encode(['code' => -1, 'msg' => $permission_check['message']]));
    }
    
    // 继续执行业务逻辑
    // ...
break;
```

### 2. 前端获取权限信息

```javascript
$.get('ajax.php?act=get_user_permissions', function(res) {
    if (res.code === 0) {
        const permissions = res.data;
        
        // 根据权限控制界面显示
        if (!permissions.is_active) {
            alert('您的账户已禁用或已到期');
            return;
        }
        
        if (!permissions.can_add_store) {
            $('#addStoreBtn').hide();
        }
    }
});
```

### 3. 测试权限系统

访问 `user/test_permissions.php` 可以查看当前用户的权限状态和配置开关状态。

## 配置说明

### 1. 权限系统配置开关

在 `config.php` 中配置权限系统开关：

```php
/*权限系统配置*/
$permission_config=array(
    'enable_store_limit' => true, // 是否启用店铺数量限制 (true=启用, false=禁用)
    'enable_menu_control' => true, // 是否启用菜单权限控制 (true=启用, false=禁用)
    'enable_status_check' => true, // 是否启用用户状态检查 (true=启用, false=禁用)
);
```

**配置说明**：
- `enable_store_limit`: 控制是否限制用户添加店铺数量（严格按照数据库max_shops字段值）
- `enable_menu_control`: 控制是否根据用户状态限制菜单显示
- `enable_status_check`: 控制是否检查用户状态（status字段）

### 2. 在线配置管理

访问 `admin/permission_config.php` 可以在线修改权限配置，无需手动编辑配置文件。

### 3. 用户权限配置

在数据库中直接修改 `ozon_user` 表：

```sql
-- 设置用户最大店铺数
UPDATE ozon_user SET max_shops = 10 WHERE uid = 123;

-- 设置用户到期时间
UPDATE ozon_user SET expiry_date = '2024-12-31 23:59:59' WHERE uid = 123;

-- 禁用用户
UPDATE ozon_user SET status = 0 WHERE uid = 123;
```

### 4. 定时任务配置

确保 `worker/logs/` 目录存在且可写：

```bash
mkdir -p worker/logs
chmod 755 worker/logs
```

## 注意事项

1. **安全性**: 所有权限检查都在后端进行，前端只做界面控制
2. **性能**: 权限检查函数已优化，减少数据库查询次数
3. **日志**: 重要操作都会记录日志，便于问题排查
4. **扩展性**: 权限系统设计为可扩展，可以轻松添加新的权限类型

## 故障排除

### 常见问题

1. **菜单不显示**: 检查用户 `status` 字段是否为1
2. **无法添加店铺**: 检查当前店铺数量是否达到 `max_shops` 限制
3. **权限检查失败**: 确认用户已登录且 `userrow` 变量存在

### 日志查看

```bash
# 查看用户状态同步日志
tail -f worker/logs/user_status_sync.log

# 查看系统错误日志
tail -f /var/log/apache2/error.log
``` 