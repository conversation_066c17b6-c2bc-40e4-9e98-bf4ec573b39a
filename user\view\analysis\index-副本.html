<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <style>
        /* 整体背景色 */
        body {
            background-color: #f4f7fa;
            font-family: 'Inter', sans-serif;
            padding: 2rem;
        }

        /* 待办事项信息栏样式 */
       .todo-info {
            border: none;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 3rem;
            transition: transform 0.3s ease;
            background-color: #ffffff;
        }

       .todo-info:hover {
            transform: translateY(-8px);
        }

       .todo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #007bff;
            padding: 1.25rem 1.75rem;
            color: white;
        }

       .todo-header button {
            background-color: #ffffff;
            color: #007bff;
            border: none;
            padding: 0.625rem 1.25rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, color 0.3s ease;
            font-weight: 600;
        }

       .todo-header button:hover {
            background-color: #e0e0e0;
        }

       .todo-header a {
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

       .todo-header a:hover {
            color: #e0e0e0;
        }

       .todo-row {
            display: flex;
            border-bottom: 1px solid #e0e6ed;
            padding: 1.5rem 1.75rem;
        }

       .todo-type {
            width: 10%;
            display: flex;
            align-items: center;
            font-weight: 600;
            color: #333;
        }

       .todo-type i {
            font-size: 1.5rem;
            margin-right: 0.75rem;
            color: #007bff;
        }

       .todo-item {
            width: 15%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

       .todo-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }

       .todo-number.red {
            color: #f5222d;
        }

       .todo-text {
            font-size: 0.9rem;
            color: #666;
            text-align: center;
        }

        /* 切换按钮样式 */
       .tab-buttons {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

       .tab-buttons button {
            margin: 0 0.75rem;
            padding: 0.75rem 1.5rem;
            border: none;
            background-color: #e0e6ed;
            cursor: pointer;
            border-radius: 8px;
            transition: background-color 0.3s ease, color 0.3s ease;
            font-weight: 600;
            color: #333;
        }

       .tab-buttons button.active {
            background-color: #007bff;
            color: white;
        }

       .tab-buttons button:hover {
            background-color: #0056b3;
            color: white;
        }

        /* 信息列表样式 */
       .info-list {
            display: none;
            animation: fadeIn 0.3s ease;
        }

       .info-list.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 卡片样式 */
       .layui-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
            background-color: #ffffff;
        }

       .layui-card:hover {
            transform: translateY(-8px);
        }

       .layui-card-body {
            padding: 1.75rem;
        }

       .dynamic-status dd {
            margin-bottom: 1rem;
            border-bottom: 1px solid #e0e6ed;
            padding-bottom: 1rem;
        }

       .dynamic-status dd:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

       .dynamic-status p {
            font-size: 1rem;
            color: #333;
            margin-bottom: 0.25rem;
        }

       .dynamic-status span {
            font-size: 0.875rem;
            color: #666;
        }
    </style>
</head>

<body>
    <div class="pear-container">
        <!-- 待办事项信息栏 -->
        <div class="todo-info">
            <div class="todo-header">
                <button>待办事项</button>
                <a href="#">自定义待办事项</a>
            </div>
            <div class="todo-row">
                <div class="todo-type">
                    <i class="fa-solid fa-box"></i> 产品
                </div>
                <div class="todo-item">
                    <div class="todo-number">683</div>
                    <div class="todo-text">采集箱</div>
                </div>
                 <div class="todo-item">
                    <div class="todo-number">0</div>
                    <div class="todo-text">发布成功</div>
                </div>
                <div class="todo-item">
                    <div class="todo-number">0</div>
                    <div class="todo-text">发布中</div>
                </div>
                <div class="todo-item">
                    <div class="todo-number">0</div>
                    <div class="todo-text">今日发布失败</div>
                </div>
            </div>
            <div class="todo-row">
                <div class="todo-type">
                    <i class="fa-solid fa-shopping-cart"></i> 订单
                </div>
                <div class="todo-item">
                    <div class="todo-number">51</div>
                    <div class="todo-text">剩余发货1天</div>
                </div>
                <div class="todo-item">
                    <div class="todo-number">348</div>
                    <div class="todo-text">待处理</div>
                </div>
                <div class="todo-item">
                    <div class="todo-number">419</div>
                    <div class="todo-text">待打单发货</div>
                </div>
                <div class="todo-item">
                    <div class="todo-number red">0</div>
                    <div class="todo-text">交运失败</div>
                </div>
                <div class="todo-item">
                    <div class="todo-number">0</div>
                    <div class="todo-text">待处理售后订单</div>
                </div>
                <div class="todo-item">
                    <div class="todo-number">0</div>
                    <div class="todo-text">搁置订单</div>
                </div>
            </div>
            <div class="todo-row">
                <div class="todo-type">
                    <i class="fa-solid fa-truck"></i> 采购
                </div>
                <div class="todo-item">
                    <div class="todo-number">1</div>
                    <div class="todo-text">今日采购数量</div>
                </div>
                <div class="todo-item">
                    <div class="todo-number">1110</div>
                    <div class="todo-text">今日采购金额</div>
                </div>
             
            </div>
           
        </div>

        <div class="tab-buttons">
            <button onclick="showTab('plugin-log')" class="active">插件更新日志</button>
            <button onclick="showTab('announcement')">公告消息</button>
            <button onclick="showTab('live-training')">直播/培训</button>
            <button onclick="showTab('ozon-news')">OZON 资讯</button>
        </div>

        <!-- 插件更新日志 -->
        <div class="info-list active" id="plugin-log">
            <div class="layui-card">
                <div class="layui-card-body">
                    <dl class="dynamic-status">
                        <dd>
                            <div>
                                <p>插件 v1.1 版本更新，修复了部分兼容性问题</p>
                                <span>2024-01-01</span>
                            </div>
                        </dd>
                        <dd>
                            <div>
                                <p>插件 v1.0 版本发布，新增了若干功能</p>
                                <span>2023-12-01</span>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- 公告消息 -->
        <div class="info-list" id="announcement">
            <div class="layui-card">
                <div class="layui-card-body">
                    <dl class="dynamic-status">
                        <dd>
                            <div>
                                <p>系统将于今晚 23:00 - 01:00 进行维护，请提前做好准备</p>
                                <span>2024-01-10</span>
                            </div>
                        </dd>
                        <dd>
                            <div>
                                <p>新的活动规则已经发布，请各位用户查看</p>
                                <span>2024-01-05</span>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- 直播/培训 -->
        <div class="info-list" id="live-training">
            <div class="layui-card">
                <div class="layui-card-body">
                    <dl class="dynamic-status">
                        <dd>
                            <div>
                                <p>下周五 14:00 将举办新功能培训直播，欢迎参加</p>
                                <span>2024-01-12</span>
                            </div>
                        </dd>
                        <dd>
                            <div>
                                <p>上周的直播培训回放已上传，可在指定位置查看</p>
                                <span>2024-01-08</span>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- OZON 资讯 -->
        <div class="info-list" id="ozon-news">
            <div class="layui-card">
                <div class="layui-card-body">
                    <dl class="dynamic-status">
                        <dd>
                            <div>
                                <p>OZON 平台推出新的营销工具，助力商家提升销量</p>
                                <span>2024-01-15</span>
                            </div>
                        </dd>
                        <dd>
                            <div>
                                <p>OZON 调整了部分类目佣金政策，请商家关注</p>
                                <span>2024-01-13</span>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabId) {
            const tabs = document.querySelectorAll('.info-list');
            const buttons = document.querySelectorAll('.tab-buttons button');

            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            buttons.forEach(button => {
                button.classList.remove('active');
            });

            document.getElementById(tabId).classList.add('active');
            const activeButton = Array.from(buttons).find(button => button.onclick.toString().includes(tabId));
            activeButton.classList.add('active');
        }
    </script>
</body>

</html>
    