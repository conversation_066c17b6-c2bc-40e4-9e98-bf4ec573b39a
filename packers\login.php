<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>海豚ERP系统登录</title>
	<link rel="stylesheet" href="../assets/component/pear/css/pear.css" />
	<style>
		:root {
			--primary-color: #1890ff;
			--bg-gradient: linear-gradient(145deg, #f0f9ff 0%, #e6f4ff 100%);
		}

		/* 全局容器 */
		.login-container {
			display: flex;
			justify-content: center;
			align-items: center;
			min-height: 100vh;
			background: var(--bg-gradient);
			background-image: url(../assets/admin/images/background.svg);
		}

		/* 登录主卡 */
		.login-card {
			width: 1200px;
			background: #fff;
			border-radius: 12px;
			box-shadow: 0 8px 32px rgba(0,0,0,0.08);
			display: grid;
			grid-template-columns: 1fr 1fr;
		}

		/* 左侧品牌区 */
		.brand-section {
			padding: 50px;
			background: var(--primary-color);
			color: white;
			position: relative;
		}

		.brand-header {
			display: flex;
			align-items: center;
			gap: 15px;
			margin-bottom: 30px;
		}

		.brand-title {
			font-size: 28px;
			font-weight: 600;
		}

		.feature-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 15px;
			margin: 40px 0;
		}

		.feature-item {
			background: rgba(255,255,255,0.1);
			padding: 15px;
			border-radius: 6px;
			text-align: center;
		}

		/* 右侧登录区 */
		.login-section {
			padding: 50px 60px;
		}

		.login-form .layui-input-prefix {
			color: var(--primary-color);
		}

		.captcha-wrapper {
			display: grid;
			grid-template-columns: 1fr 120px;
			gap: 15px;
		}

		.protocol-links {
			position: absolute;
			bottom: 30px;
			left: 50%;
			transform: translateX(-50%);
			white-space: nowrap;
			font-size: 12px;
			color: rgba(255,255,255,0.8);
		}

		/* 新增样式 */
		.login-form .feature-list {
			margin-top: 40px;
			display: flex;
			justify-content: center;
			gap: 20px;
			flex-wrap: wrap;
		}

		.feature-item {
			margin: 5px;
		}

		.terms {
			margin-top: 40px;
			font-size: 12px;
			text-align: center;
			color: #666;
		}
	</style>
</head>

<body>
	<div class="login-container">
		<div class="login-card">
			<!-- 左侧品牌信息 -->
			<div class="brand-section">
				<div class="brand-header">
					<i class="layui-icon layui-icon-component" style="font-size:36px"></i>
					<div>
						<div class="brand-title">海豚ERP系统</div>
						<div style="font-size:16px">好用不贵</div>
					</div>
				</div>

				<div class="feature-grid">
					<div class="feature-item">精确选品</div>
					<div class="feature-item">上货跟卖</div>
						<div class="feature-item">竞品监控</div>
						<div class="feature-item">数据分析</div>
				</div>
			</div>

			<!-- 右侧登录表单 -->
			<div class="login-section">
				<div class="layui-form login-form">
					<div style="text-align:center; margin-bottom:40px">
						<div style="font-size:24px;color:#333;margin-bottom:10px">欢迎登录</div>
						<div style="color:#666">请使用您的平台账号登录</div>
					</div>

					<div class="layui-form-item">
						<div class="layui-input-wrap">
							<div class="layui-input-prefix">
								<i class="layui-icon layui-icon-username"></i>
							</div>
							<input  name="username" type="text" 
								   placeholder="手机号/邮箱/用户名" 
								   class="layui-input" 
								   lay-verify="required">
						</div>
					</div>

					<div class="layui-form-item">
						<div class="layui-input-wrap">
							<div class="layui-input-prefix">
								<i class="layui-icon layui-icon-password"></i>
							</div>
							<input name="password" type="password" 
								   placeholder="请输入密码" 
								   class="layui-input"
								   lay-affix="eye">
						</div>
					</div>

					<div class="layui-form-item">
						<div class="captcha-wrapper">
							<div class="layui-input-wrap">
								<div class="layui-input-prefix">
									<i class="layui-icon layui-icon-vercode"></i>
								</div>
								<input type="text" 
									   placeholder="验证码" 
									   class="layui-input"  value="VBJFQ" >
							</div>
							<img src="../assets/admin/images/captcha.gif" 
								 class="verification-img"
								 style="height:38px;border-radius:4px">
						</div>
					</div>

					<div class="layui-form-item">
						<div class="remember-passsword">
							<div class="remember-cehcked">
								<input type="checkbox" name="like1[write]" lay-skin="primary" title="自动登录">
							</div>
						</div>
					</div>

					<div class="layui-form-item">
						<button class="layui-btn layui-btn-fluid" 
								style="background:var(--primary-color)"
								lay-submit lay-filter="login">立即登录</button>
					</div>

					<div class="layui-form-item" style="text-align:center;margin-top:20px">
						<a href="#" style="color:#666">忘记密码？</a>
						<span style="margin:0 10px">|</span>
						<a href="./reg.php" style="color:var(--primary-color)">注册新账号</a>
					</div>

					<!-- 新增功能展示 -->
				

					<!-- 新增协议提示 -->
					<div class="terms">
						登录即表示同意用户协议与隐私条款
					</div>
				</div>
			</div>
		</div>
	</div>

	<script src="../assets/component/layui/layui.js"></script>
	<script src="../assets/component/pear/pear.js"></script>
	<script>
		layui.use(['form', 'button', 'popup'], function () {
			var form = layui.form;
			var button = layui.button;
			var popup = layui.popup;
			var $ = layui.jquery;

			// 登 录 提 交
			form.on('submit(login)', function (data) {
			    var field = data.field; // 获取表单字段值
				/// 验证
				
				$.ajax({
	             url: 'ajax.php?act=login',
	             type: 'POST',
	             dataType: 'json',
	             data: {
	                 username: field.username,
	                 password: field.password
	             },
	             success: function(res){
	                 if(res.code==1){
	                     popup.success("登录成功", function () {
							location.href = "./index.html";
						});
	                 }else{
	                     layer.msg(res.msg);
	                 }
	             },
	             error: function(xhr, status, error){
	                 console.error('请求失败:', status, error);
	             }
	            });
				return false;
			});
		})
	</script>
</body>
</html>