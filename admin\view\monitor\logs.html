<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统日志</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
    <style>
        .log-container {
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Courier New', Consolas, monospace;
            font-size: 12px;
            padding: 15px;
            border-radius: 4px;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .log-container::-webkit-scrollbar {
            width: 8px;
        }
        
        .log-container::-webkit-scrollbar-track {
            background: #2d2d2d;
        }
        
        .log-container::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }
        
        .log-container::-webkit-scrollbar-thumb:hover {
            background: #777;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>系统日志</span>
        </div>
        <div class="layui-card-body">
            <!-- 控制栏 -->
            <div class="layui-form layui-row layui-col-space15" style="margin-bottom: 15px;">
                <div class="layui-col-md2">
                    <select id="log-type" lay-filter="logType">
                        <option value="error">错误日志</option>
                        <option value="worker">任务日志</option>
                        <option value="import">导入日志</option>
                    </select>
                </div>
                <div class="layui-col-md2">
                    <select id="log-lines">
                        <option value="50">最近50行</option>
                        <option value="100">最近100行</option>
                        <option value="200">最近200行</option>
                        <option value="500">最近500行</option>
                    </select>
                </div>
                <div class="layui-col-md4">
                    <button class="layui-btn layui-btn-sm" id="load-btn">
                        <i class="layui-icon layui-icon-refresh"></i> 加载日志
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" id="auto-refresh-btn" data-auto="false">
                        <i class="layui-icon layui-icon-pause"></i> 开启自动刷新
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-warm" id="clear-btn">
                        <i class="layui-icon layui-icon-delete"></i> 清空显示
                    </button>
                </div>
                <div class="layui-col-md4" style="text-align: right;">
                    <span id="log-status" style="color: #666; font-size: 12px;"></span>
                </div>
            </div>
            
            <!-- 日志显示区域 -->
            <div id="log-display" class="log-container">
                点击"加载日志"按钮查看系统日志内容...
            </div>
        </div>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <script>
        layui.use(['form', 'jquery'], function() {
            var form = layui.form;
            var $ = layui.jquery;
            
            var autoRefreshInterval = null;
            
            function updateStatus(message) {
                var now = new Date();
                var timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0') + ':' + 
                             now.getSeconds().toString().padStart(2, '0');
                $('#log-status').text(message + ' - ' + timeStr);
            }
            
            function loadLogs() {
                var logType = $('#log-type').val();
                var lines = $('#log-lines').val();
                
                updateStatus('正在加载...');
                
                $.ajax({
                    url: '../../ajax.php?act=system_logs',
                    type: 'GET',
                    data: {
                        log_type: logType,
                        lines: lines
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            if (res.data && res.data.trim()) {
                                $('#log-display').text(res.data);
                                // 滚动到底部
                                var container = document.getElementById('log-display');
                                container.scrollTop = container.scrollHeight;
                                updateStatus('加载完成 (' + lines + '行)');
                            } else {
                                $('#log-display').text('日志文件为空或无内容');
                                updateStatus('日志为空');
                            }
                        } else {
                            $('#log-display').text('加载失败: ' + (res.msg || '未知错误'));
                            updateStatus('加载失败');
                        }
                    },
                    error: function() {
                        $('#log-display').text('网络异常，请检查连接');
                        updateStatus('网络异常');
                    }
                });
            }
            
            // 加载日志按钮
            $('#load-btn').on('click', function() {
                loadLogs();
            });
            
            // 清空显示按钮
            $('#clear-btn').on('click', function() {
                $('#log-display').text('日志显示已清空');
                updateStatus('显示已清空');
            });
            
            // 自动刷新按钮
            $('#auto-refresh-btn').on('click', function() {
                var $btn = $(this);
                var isAuto = $btn.data('auto');
                
                if (isAuto) {
                    // 停止自动刷新
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                    $btn.html('<i class="layui-icon layui-icon-pause"></i> 开启自动刷新')
                        .removeClass('layui-btn-danger')
                        .addClass('layui-btn-normal')
                        .data('auto', false);
                    updateStatus('自动刷新已停止');
                } else {
                    // 开启自动刷新
                    loadLogs(); // 立即加载一次
                    autoRefreshInterval = setInterval(function() {
                        loadLogs();
                    }, 10000); // 每10秒刷新一次
                    
                    $btn.html('<i class="layui-icon layui-icon-play"></i> 停止自动刷新')
                        .removeClass('layui-btn-normal')
                        .addClass('layui-btn-danger')
                        .data('auto', true);
                    updateStatus('自动刷新已开启 (10秒间隔)');
                }
            });
            
            // 监听下拉框变化
            form.on('select(logType)', function(data) {
                if (autoRefreshInterval) {
                    loadLogs(); // 如果正在自动刷新，立即加载新类型的日志
                }
            });
            
            // 页面卸载时清理定时器
            $(window).on('beforeunload', function() {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                }
            });
        });
    </script>
</body>
</html> 