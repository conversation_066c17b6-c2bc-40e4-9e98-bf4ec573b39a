<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ozon店铺聊天</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../assets/component/layui/css/layui.css">
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .chat-app {
            height: 100vh;
            display: flex;
            flex-direction: column;
            max-width: 1400px;
            margin: 0 auto;
            background: #fff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .chat-app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chat-app-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-app-actions {
            display: flex;
            gap: 10px;
        }

        .chat-main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .chat-sidebar {
            width: 350px;
            background: #fff;
            border-right: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }

        .chat-sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #f8f9fa;
        }

        .chat-sidebar-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 15px 0;
            color: #2c3e50;
        }

        .chat-filters {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-group .layui-input {
            height: 32px;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
            transition: all 0.2s ease;
        }

        .filter-group .layui-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .filter-stats {
            background: #f8f9fa;
            padding: 12px 20px;
            border-bottom: 1px solid #e1e5e9;
            font-size: 12px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .filter-stats-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .filter-stats-badge {
            background: #667eea;
            color: #fff;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
        }

        .filter-clear {
            color: #667eea;
            cursor: pointer;
            font-size: 11px;
            text-decoration: underline;
        }

        .filter-clear:hover {
            color: #5a6fd8;
        }

        .chat-list-container {
            flex: 1;
            overflow-y: auto;
        }

        .chat-list {
            padding: 0;
        }

        .chat-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            background: #fff;
        }

        .chat-item:hover {
            background: #f8f9fa;
            transform: translateX(2px);
        }

        .chat-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            border-left: 4px solid #fff;
        }

        .chat-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .chat-title {
            font-weight: 600;
            font-size: 14px;
            color: #2c3e50;
            margin: 0;
            line-height: 1.3;
        }

        .chat-item.active .chat-title {
            color: #fff;
        }

        .chat-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .chat-time {
            font-size: 11px;
            color: #8e9aaf;
            white-space: nowrap;
        }

        .chat-item.active .chat-time {
            color: rgba(255,255,255,0.8);
        }

        .unread-count {
            background: #ff4757;
            color: #fff;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 11px;
            font-weight: 600;
            min-width: 18px;
            text-align: center;
            line-height: 1.2;
        }

        .chat-item.active .unread-count {
            background: #fff;
            color: #ff4757;
        }

        .chat-item.has-unread {
            background: #f8f9ff;
            border-left: 3px solid #667eea;
        }

        .chat-item.has-unread .chat-title {
            font-weight: 700;
            color: #333;
        }

        .unread-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #e74c3c;
            color: #fff;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            font-weight: 600;
            min-width: 16px;
            text-align: center;
            line-height: 1;
        }

        .chat-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .chat-store {
            font-size: 12px;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .chat-item.active .chat-store {
            color: rgba(255,255,255,0.9);
        }

        .chat-type-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            background: #e9ecef;
            color: #495057;
            font-weight: 500;
        }

        .chat-type-badge.sendable {
            background: #d1ecf1;
            color: #0c5460;
        }

        .chat-type-badge.readonly {
            background: #f8d7da;
            color: #721c24;
        }

        .chat-item.active .chat-type-badge {
            background: rgba(255,255,255,0.2);
            color: #fff;
        }

        .chat-preview {
            font-size: 13px;
            color: #6c757d;
            line-height: 1.4;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-top: 4px;
        }

        .chat-order-info {
            margin: 6px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            align-items: center;
        }

        .order-tag, .sku-tag {
            display: inline-flex;
            align-items: center;
            gap: 3px;
            padding: 2px 6px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #bbdefb;
        }

        .sku-tag {
            background: #f3e5f5;
            color: #7b1fa2;
            border-color: #ce93d8;
        }

        .product-name-tag {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 3px 8px;
            background: #fff3e0;
            color: #f57c00;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #ffcc02;
            margin-top: 2px;
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }



        .no-order-info {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 3px 8px;
            background: #f5f5f5;
            color: #999;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #ddd;
        }

        .chat-item.active .chat-order-info .order-tag,
        .chat-item.active .chat-order-info .sku-tag,
        .chat-item.active .chat-order-info .product-name-tag {
            background: rgba(255,255,255,0.2);
            color: rgba(255,255,255,0.9);
            border-color: rgba(255,255,255,0.3);
        }

        .chat-item.active .chat-order-info .no-order-info {
            background: rgba(255,255,255,0.2);
            color: rgba(255,255,255,0.7);
            border-color: rgba(255,255,255,0.3);
        }

        .chat-item.active .chat-preview {
            color: rgba(255,255,255,0.9);
        }

        .chat-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fff;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-header-info h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
            color: #2c3e50;
        }

        .chat-header-info small {
            color: #6c757d;
            font-size: 12px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message-item {
            display: flex;
            align-items: flex-end;
            gap: 8px;
            max-width: 70%;
        }

        .message-received {
            align-self: flex-start;
        }

        .message-sent {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message-sent .message-avatar {
            background: #28a745;
        }

        .message-bubble {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .message-content {
            padding: 12px 16px;
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            word-wrap: break-word;
            font-size: 14px;
            line-height: 1.4;
            position: relative;
        }

        .message-sent .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
        }

        .message-time {
            font-size: 11px;
            color: #8e9aaf;
            padding: 0 4px;
        }

        .message-sent .message-time {
            text-align: right;
        }

        .chat-input-container {
            border-top: 1px solid #e1e5e9;
            background: #fff;
            padding: 20px;
        }

        .chat-input-notice {
            margin-bottom: 12px;
            padding: 12px 16px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            color: #856404;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-input-form {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input-textarea {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            border: 1px solid #e1e5e9;
            border-radius: 22px;
            padding: 12px 16px;
            resize: none;
            font-size: 14px;
            line-height: 1.4;
            transition: all 0.2s ease;
        }

        .chat-input-textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .chat-input-actions {
            display: flex;
            gap: 8px;
        }

        .chat-input-btn {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 16px;
        }

        .chat-input-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .chat-input-btn:disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
            font-size: 14px;
        }

        .loading i {
            font-size: 24px;
            margin-bottom: 12px;
            display: block;
        }

        .empty-state {
            text-align: center;
            padding: 80px 40px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 48px;
            color: #dee2e6;
            margin-bottom: 16px;
        }

        .empty-state-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #495057;
        }

        .empty-state-text {
            font-size: 14px;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .chat-app {
                height: 100vh;
            }

            .chat-app-header {
                padding: 12px 16px;
            }

            .chat-app-title {
                font-size: 16px;
            }

            .chat-main {
                position: relative;
            }

            .chat-sidebar {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                z-index: 1000;
                transform: translateX(-100%);
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }

            .chat-sidebar.show {
                transform: translateX(0);
            }

            .chat-sidebar-header {
                padding: 16px;
            }

            .chat-item {
                padding: 12px 16px;
            }

            .chat-header {
                padding: 16px;
            }

            .chat-messages {
                padding: 16px;
            }

            .chat-input-container {
                padding: 16px;
            }

            .message-item {
                max-width: 85%;
            }
        }

        @media (max-width: 480px) {
            .chat-sidebar {
                width: 100%;
            }

            .chat-app-header {
                padding: 10px 12px;
            }

            .chat-messages {
                padding: 12px;
            }

            .chat-input-container {
                padding: 12px;
            }

            .message-item {
                max-width: 90%;
            }
        }

        .mobile-toggle {
            display: none;
        }

        @media (max-width: 768px) {
            .mobile-toggle {
                display: inline-block;
            }
        }

        /* 加载更多按钮样式 */
        #loadMoreContainer {
            background: #f8f9fa;
        }

        #loadMoreBtn {
            transition: all 0.3s ease;
            border: 1px solid #e1e5e9;
            font-size: 13px;
        }

        #loadMoreBtn:hover:not(:disabled) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            border-color: #667eea;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        #loadMoreBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <div class="chat-app">
        <!-- 应用头部 -->
        <div class="chat-app-header">
            <h1 class="chat-app-title">
                <i class="layui-icon layui-icon-dialogue"></i>
                Ozon店铺聊天管理
            </h1>
            <div class="chat-app-actions">
                <button class="layui-btn layui-btn-sm mobile-toggle" id="toggleSidebar">
                    <i class="layui-icon layui-icon-spread-left"></i>
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-primary" id="refreshAll">
                    <i class="layui-icon layui-icon-refresh"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 主要内容区 -->
        <div class="chat-main">
            <!-- 侧边栏 -->
            <div class="chat-sidebar" id="chatSidebar">
                <div class="chat-sidebar-header">
                    <h2 class="chat-sidebar-title">聊天列表</h2>
                    
                    <!-- 筛选器 -->
                    <div class="chat-filters">
                        <div class="filter-group">
                            <label class="filter-label">店铺筛选</label>
                            <div class="layui-form">
                                <select id="chatStoreFilter" lay-filter="chatStoreFilter">
                                    <option value="">全部店铺 (数据库缓存)</option>
                                </select>
                            </div>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="refreshFromApi" style="width: 100%; margin-top: 8px;">
                                <i class="layui-icon layui-icon-refresh"></i> 从API刷新
                            </button>
                        </div>
                        
                        <div class="filter-group">
                            <label class="filter-label">类型筛选</label>
                            <div class="layui-form">
                                <select id="chatTypeFilter" lay-filter="chatTypeFilter">
                                    <option value="">全部类型</option>
                                    <option value="Buyer_Seller" selected>买家咨询</option>
                                    <option value="Seller_Support">客服支持</option>
                                    <option value="Seller_Notification">系统通知</option>
                                    <option value="Seller_Notification_FBS">FBS通知</option>
                                    <option value="Seller_Notification_Major">重要通知</option>
                                    <option value="Seller_Marketplace_Promo">平台推广</option>
                                    <option value="Seller_Notification_Findoc">财务文档</option>
                                    <option value="Seller_Notification_UpdateContent">内容更新</option>
                                    <option value="Seller_Notification_Content">内容通知</option>
                                    <option value="Seller_Paid_Brands">品牌推广</option>
                                    <option value="Seller_Online_Training">在线培训</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="filter-group">
                            <label class="filter-label">状态筛选</label>
                            <div class="layui-form">
                                <select id="chatStatusFilter" lay-filter="chatStatusFilter">
                                    <option value="" selected>全部状态</option>
                                    <option value="unread">未读消息</option>
                                    <option value="read">已读消息</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="filter-group">
                            <label class="filter-label">时间筛选</label>
                            <div class="layui-form">
                                <select id="chatTimeFilter" lay-filter="chatTimeFilter">
                                    <option value="" selected>全部时间</option>
                                    <option value="today">今天</option>
                                    <option value="yesterday">昨天</option>
                                    <option value="week">最近7天</option>
                                    <option value="month">最近30天</option>
                                    <option value="custom">自定义时间</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="filter-group" id="customTimeRange" style="display: none;">
                            <label class="filter-label">自定义时间范围</label>
                            <div class="layui-form">
                                <input type="text" id="startDate" placeholder="开始日期" class="layui-input" style="margin-bottom: 8px;">
                                <input type="text" id="endDate" placeholder="结束日期" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 筛选统计 -->
                <div class="filter-stats" id="filterStats" style="display: none;">
                    <div class="filter-stats-item">
                        <span>显示结果:</span>
                        <span class="filter-stats-badge" id="filteredCount">0</span>
                    </div>
                    <div class="filter-clear" id="clearFilters">清除筛选</div>
                </div>
                
                <!-- 聊天列表容器 -->
                <div class="chat-list-container">
                    <div id="chatList" class="loading">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                        正在加载聊天列表...
                    </div>
                    <!-- 加载更多按钮 -->
                    <div id="loadMoreContainer" style="display: none; padding: 15px 20px; text-align: center; border-top: 1px solid #f1f3f4;">
                        <button id="loadMoreBtn" class="layui-btn layui-btn-sm layui-btn-primary" style="width: 100%;">
                            <i class="layui-icon layui-icon-down"></i> 加载更多聊天
                        </button>
                    </div>
                </div>
            </div>

            <!-- 聊天内容 -->
            <div class="chat-content">
                <!-- 聊天头部 -->
                <div class="chat-header" id="chatHeader" style="display: none;">
                    <div class="chat-header-info">
                        <h3 id="currentChatTitle">客户聊天</h3>
                        <small id="currentChatSubtitle">选择聊天开始对话</small>
                    </div>
                    <div class="chat-header-actions">
                        <button class="layui-btn layui-btn-xs layui-btn-primary" id="markAllRead">
                            <i class="layui-icon layui-icon-ok"></i> 标记已读
                        </button>
                        <button class="layui-btn layui-btn-xs layui-btn-normal" id="debugOrderInfo">
                            <i class="layui-icon layui-icon-search"></i> 调试订单
                        </button>
                        <button class="layui-btn layui-btn-xs layui-btn-warm" id="viewCachedOrders">
                            <i class="layui-icon layui-icon-list"></i> 查看缓存
                        </button>
                    </div>
                </div>

                <!-- 消息显示区 -->
                <div class="chat-messages" id="chatMessages">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="layui-icon layui-icon-dialogue"></i>
                        </div>
                        <h3 class="empty-state-title">欢迎使用聊天管理</h3>
                        <p class="empty-state-text">请从左侧选择一个聊天开始对话<br>您可以查看消息历史并回复买家咨询</p>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="chat-input-container" id="chatInput" style="display: none;">
                    <div id="chatInputNotice" class="chat-input-notice" style="display: none;">
                        <i class="layui-icon layui-icon-tips"></i>
                        <span>此聊天类型不支持回复消息</span>
                    </div>
                    
                    <div class="chat-input-form">
                        <textarea id="messageInput" 
                                  placeholder="输入消息内容，按 Ctrl+Enter 快速发送..." 
                                  class="chat-input-textarea"></textarea>
                        
                        <div class="chat-input-actions">
                            <button type="button" class="chat-input-btn" id="sendFile" title="发送文件">
                                <i class="layui-icon layui-icon-upload"></i>
                            </button>
                            <button type="button" class="chat-input-btn" id="sendMessage" title="发送消息">
                                <i class="layui-icon layui-icon-release"></i>
                            </button>
                        </div>
                    </div>
                    
                    <input type="file" id="fileInput" style="display: none;" accept="image/*,.pdf,.doc,.docx,.txt">
                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/component/layui/layui.js"></script>
    <script>
        layui.use(['layer', 'form'], function(){
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.$;

            var currentChatId = null;
            var currentStoreId = null;
            var allChats = []; // 存储所有聊天数据
            var allStores = []; // 存储所有店铺数据
            var filteredChats = []; // 存储筛选后的聊天数据
            var currentFilters = {}; // 当前筛选条件
            var pagination = { // 分页信息
                limit: 200,
                offset: 0,
                hasMore: false,
                loading: false
            };

            // 页面初始化
            init();

            function init() {
                loadAllChats();
                bindEvents();
                initDatePickers();
                
                // 每30秒检查新消息
                setInterval(checkNewMessages, 30000);
            }

            // 初始化日期选择器
            function initDatePickers() {
                layui.use('laydate', function(){
                    var laydate = layui.laydate;
                    
                    // 开始日期
                    laydate.render({
                        elem: '#startDate',
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        done: function(value) {
                            applyFilters();
                        }
                    });
                    
                    // 结束日期
                    laydate.render({
                        elem: '#endDate',
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        done: function(value) {
                            applyFilters();
                        }
                    });
                });
            }

            // 绑定事件
            function bindEvents() {
                console.log('开始绑定事件...');
                
                // 刷新所有
                $('#refreshAll').on('click', function() {
                    location.reload();
                });

                // 移动端切换侧边栏
                $('#toggleSidebar').on('click', function() {
                    $('#chatSidebar').toggleClass('show');
                });

                // 点击空白区域关闭侧边栏
                $(document).on('click', function(e) {
                    if ($(window).width() <= 768) {
                        if (!$(e.target).closest('#chatSidebar, #toggleSidebar').length) {
                            $('#chatSidebar').removeClass('show');
                        }
                    }
                });

                // 聊天筛选事件 - 确保在DOM和layui初始化后绑定
                setTimeout(function() {
                    console.log('绑定店铺筛选事件...');
                    form.on('select(chatStoreFilter)', function(data) {
                        var storeId = data.value;
                        var chatType = $('#chatTypeFilter').val();
                        console.log('店铺选择事件触发，store_id:', storeId, 'chat_type:', chatType);
                        
                        // 重新应用筛选
                        applyFilters();
                    });

                    form.on('select(chatTypeFilter)', function(data) {
                        console.log('聊天类型筛选事件触发:', data.value);
                        applyFilters();
                    });

                    form.on('select(chatStatusFilter)', function(data) {
                        console.log('状态筛选事件触发:', data.value);
                        applyFilters();
                    });

                    form.on('select(chatTimeFilter)', function(data) {
                        console.log('时间筛选事件触发:', data.value);
                        if (data.value === 'custom') {
                            $('#customTimeRange').show();
                        } else {
                            $('#customTimeRange').hide();
                            $('#startDate').val('');
                            $('#endDate').val('');
                        }
                        applyFilters();
                    });
                }, 500);

                // 清除筛选
                $('#clearFilters').on('click', function() {
                    clearAllFilters();
                });

                // 发送消息
                $('#sendMessage').on('click', sendMessage);
                
                // 快捷键发送
                $('#messageInput').on('keydown', function(e) {
                    if (e.ctrlKey && e.keyCode === 13) {
                        sendMessage();
                    }
                });

                // 发送文件
                $('#sendFile').on('click', function() {
                    $('#fileInput').click();
                });

                $('#fileInput').on('change', function() {
                    var file = this.files[0];
                    if (file) {
                        uploadFile(file);
                    }
                });

                // 标记已读
                $('#markAllRead').on('click', function() {
                    if (currentChatId) {
                        markChatAsRead(currentChatId);
                    }
                });

                // 加载更多聊天
                $('#loadMoreBtn').on('click', function() {
                    loadMoreChats();
                });

                // 调试订单信息
                $('#debugOrderInfo').on('click', function() {
                    debugOrderExtraction();
                });

                // 查看缓存的订单信息
                $('#viewCachedOrders').on('click', function() {
                    viewCachedOrderInfo();
                });

                // 从API刷新
                $('#refreshFromApi').on('click', function() {
                    var storeId = $('#chatStoreFilter').val();
                    if (storeId) {
                        refreshFromApi(storeId);
                    } else {
                        layer.msg('请先选择店铺');
                    }
                });
            }



            // 加载所有聊天 - 默认从数据库加载买家咨询
            function loadAllChats() {
                $.ajax({
                    url: 'chat_ajax.php?act=getChatList&chat_type=Buyer_Seller',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 15000,
                    success: function(res) {
                        if (res.code === 1) {
                            allChats = res.data || [];
                            allStores = res.stores || [];
                            
                            // 更新店铺筛选选项
                            updateStoreFilterOptions();
                            
                            // 显示买家咨询列表或提示信息
                            if (allChats.length > 0) {
                                // 默认筛选并显示
                                applyFilters();
                            } else {
                                // 缓存为空，自动加载第一个店铺的数据
                                if (allStores.length > 0) {
                                    var firstStoreId = allStores[0].store_id;
                                    $('#chatStoreFilter').val(firstStoreId);
                                    form.render('select'); // 更新layui select的显示
                                    layer.msg('缓存为空，自动加载第一个店铺的数据', {icon: 0});
                                    applyFilters(); // applyFilters会看到选中的店铺并调用loadStoreChats
                                } else {
                                     // 提示用户添加店铺
                                    $('#chatList').html('<div class="loading">暂无店铺<br>请在店铺管理中添加店铺</div>');
                                }
                                updateLoadMoreButton();
                            }
                        } else {
                            // 加载失败
                            $('#chatList').html('<div class="loading">加载初始数据失败<br>' + res.msg + '</div>');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log('店铺列表加载错误:', textStatus, errorThrown);
                        $('#chatList').html('<div class="loading">网络错误<br>请检查网络连接后重试<br>错误: ' + textStatus + '</div>');
                    }
                });
            }

            // 更新店铺筛选选项
            function updateStoreFilterOptions() {
                console.log('更新店铺筛选选项，店铺数量:', allStores.length);
                var storeFilterHtml = '<option value="">全部店铺 (数据库缓存)</option>';
                allStores.forEach(function(store) {
                    var statusText = store.api_status || '未知状态';
                    storeFilterHtml += '<option value="' + store.store_id + '">' + 
                                     store.store_title + ' (' + statusText + ')</option>';
                });
                $('#chatStoreFilter').html(storeFilterHtml);
                
                // 强制重新渲染select组件
                setTimeout(function() {
                    form.render('select');
                    console.log('店铺筛选选项已更新');
                }, 100);
            }

            // 从API刷新特定店铺的聊天
            function refreshFromApi(storeId) {
                layer.msg('正在从API获取最新数据...', {time: 0, icon: 16});
                
                $.ajax({
                    url: 'chat_ajax.php?act=getChatList&load_from_api=1&store_id=' + storeId + '&chat_type=Buyer_Seller',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 30000,
                    success: function(res) {
                        layer.closeAll('msg');
                        if (res.code === 1) {
                            layer.msg('API数据刷新成功，共获取 ' + (res.data ? res.data.length : 0) + ' 条买家咨询', {icon: 1});
                            
                            // 合并新数据到现有数据
                            if (res.data && res.data.length > 0) {
                                // 移除该店铺的旧数据
                                allChats = allChats.filter(function(chat) {
                                    return chat.store_id != storeId;
                                });
                                // 添加新数据
                                allChats = allChats.concat(res.data);
                                // 重新筛选和显示
                                filterChats();
                            }
                        } else {
                            layer.msg('API刷新失败: ' + res.msg, {icon: 2});
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        layer.closeAll('msg');
                        layer.msg('API刷新失败: ' + textStatus, {icon: 2});
                    }
                });
            }

            // 加载特定店铺的聊天
            function loadStoreChats(storeId, chatType, append) {
                console.log('Loading chats for store:', storeId, 'with filters:', currentFilters);
                
                // 如果不是追加模式，重置分页
                if (!append) {
                    pagination.offset = 0;
                    allChats = allChats.filter(function(chat) {
                        return chat.store_id != storeId;
                    });
                }
                
                // 构建筛选参数
                var params = {
                    store_id: storeId,
                    chat_type: chatType || currentFilters.type,
                    chat_status: currentFilters.status,
                    time_filter: currentFilters.time,
                    start_date: currentFilters.startDate,
                    end_date: currentFilters.endDate,
                    limit: pagination.limit,
                    offset: pagination.offset,
                    load_from_api: 1  // 默认从API刷新数据
                };
                
                // 移除空值参数
                Object.keys(params).forEach(function(key) {
                    if (!params[key] && params[key] !== 0) delete params[key];
                });
                
                var loadingText = append ? '正在加载更多聊天...' : '正在加载聊天数据...';
                if (!append) {
                    $('#chatList').html('<div class="loading"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i><br>' + loadingText + '</div>');
                } else {
                    $('#loadMoreBtn').html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中...').prop('disabled', true);
                }
                
                pagination.loading = true;
                
                $.ajax({
                    url: 'chat_ajax.php?act=getChatList',
                    type: 'GET',
                    data: params,
                    dataType: 'json',
                    timeout: 15000,
                    success: function(res) {
                        console.log('Store chats response:', res);
                        pagination.loading = false;
                        
                        if (res.code === 1) {
                            // 更新分页信息
                            if (res.pagination) {
                                pagination.hasMore = res.pagination.has_more;
                                pagination.offset += pagination.limit; // 为下次请求准备
                            } else {
                                pagination.hasMore = false;
                            }
                            
                            // 更新该店铺的聊天数据
                            if (res.data && res.data.length > 0) {
                                // 如果是追加模式，添加到现有数据；否则替换
                                if (append) {
                                    allChats = allChats.concat(res.data);
                                } else {
                                    allChats = allChats.concat(res.data);
                                }
                                
                                // 更新店铺状态
                                allStores.forEach(function(store) {
                                    if (store.store_id == storeId && res.stores) {
                                        var updatedStore = res.stores.find(function(s) { return s.store_id == storeId; });
                                        if (updatedStore) {
                                            store.api_status = updatedStore.api_status;
                                            store.chat_count = updatedStore.chat_count;
                                        }
                                    }
                                });
                                
                                updateStoreFilterOptions();
                                
                                // 获取当前店铺的所有聊天进行筛选
                                var storeChats = allChats.filter(function(chat) {
                                    return chat.store_id == storeId;
                                });
                                
                                // 应用筛选条件
                                filteredChats = storeChats.filter(function(chat) {
                                    return applyChatFiltersToChat(chat);
                                });
                                
                                updateFilterStats();
                                renderChatList(filteredChats, append);
                                
                                // 更新加载更多按钮状态
                                updateLoadMoreButton();
                                
                                // 显示成功消息
                                if (!append) {
                                    var filterText = '';
                                    if (currentFilters.type || currentFilters.status || currentFilters.time) {
                                        var filters = [];
                                        if (currentFilters.type) filters.push(getChatTypeText(currentFilters.type));
                                        if (currentFilters.status === 'unread') filters.push('未读消息');
                                        if (currentFilters.status === 'read') filters.push('已读消息');
                                        if (currentFilters.time === 'today') filters.push('今天');
                                        if (currentFilters.time === 'week') filters.push('最近7天');
                                        filterText = filters.length ? '(' + filters.join(', ') + ')' : '';
                                    }
                                    layer.msg('成功加载 ' + res.data.length + ' 个聊天' + filterText, {icon: 1});
                                }
                            } else {
                                if (!append) {
                                    renderChatList([]);
                                    var typeText = chatType ? getChatTypeText(chatType) + '类型' : '';
                                    var message = chatType ? '该店铺暂无' + typeText + '聊天数据' : (res.msg || '该店铺暂无聊天数据');
                                    layer.msg(message, {icon: 0});
                                }
                                updateLoadMoreButton();
                            }
                        } else {
                            if (!append) {
                                $('#chatList').html('<div class="loading">加载失败<br>' + res.msg + '</div>');
                            }
                            $('#loadMoreBtn').html('<i class="layui-icon layui-icon-down"></i> 加载更多聊天').prop('disabled', false);
                            layer.msg(res.msg || '加载店铺聊天失败', {icon: 2});
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log('加载店铺聊天错误:', textStatus, errorThrown);
                        pagination.loading = false;
                        
                        if (!append) {
                            $('#chatList').html('<div class="loading">网络错误<br>请重试或选择其他店铺</div>');
                        } else {
                            $('#loadMoreBtn').html('<i class="layui-icon layui-icon-down"></i> 加载更多聊天').prop('disabled', false);
                        }
                        
                        if (textStatus === 'timeout') {
                            layer.msg('请求超时，该店铺聊天较多，请稍后重试', {icon: 2});
                        } else {
                            layer.msg('网络连接错误', {icon: 2});
                        }
                    }
                });
            }

            // 应用所有筛选条件
            function applyFilters() {
                var storeFilter = $('#chatStoreFilter').val();
                var typeFilter = $('#chatTypeFilter').val();
                var statusFilter = $('#chatStatusFilter').val();
                var timeFilter = $('#chatTimeFilter').val();
                var startDate = $('#startDate').val();
                var endDate = $('#endDate').val();

                // 筛选条件变化时重置分页
                pagination.offset = 0;
                pagination.hasMore = false;

                // 如果没有选择店铺，先加载所有聊天
                if (!storeFilter && allChats.length === 0) {
                    $('#chatList').html('<div class="loading">请先选择店铺查看聊天</div>');
                    updateLoadMoreButton();
                    return;
                }

                // 如果选择了店铺但没有该店铺的聊天数据，先加载
                if (storeFilter) {
                    var storeChats = allChats.filter(function(chat) {
                        return chat.store_id == storeFilter;
                    });
                    
                    if (storeChats.length === 0) {
                        // 保存当前筛选条件后再加载
                        currentFilters = {
                            store: storeFilter,
                            type: typeFilter,
                            status: statusFilter,
                            time: timeFilter,
                            startDate: startDate,
                            endDate: endDate
                        };
                        loadStoreChats(storeFilter);
                        return;
                    }
                }

                // 保存当前筛选条件
                currentFilters = {
                    store: storeFilter,
                    type: typeFilter,
                    status: statusFilter,
                    time: timeFilter,
                    startDate: startDate,
                    endDate: endDate
                };

                // 应用筛选
                filteredChats = allChats.filter(function(chat) {
                    // 店铺筛选
                    if (storeFilter && chat.store_id != storeFilter) {
                        return false;
                    }

                    // 类型筛选
                    if (typeFilter && chat.chat_type !== typeFilter) {
                        return false;
                    }

                    // 状态筛选
                    if (statusFilter) {
                        if (statusFilter === 'unread' && (!chat.unread_count || chat.unread_count === 0)) {
                            return false;
                        }
                        if (statusFilter === 'read' && chat.unread_count > 0) {
                            return false;
                        }
                    }

                    // 时间筛选
                    if (timeFilter && timeFilter !== 'custom') {
                        var chatDate = new Date(chat.last_message_time || chat.created_at);
                        var now = new Date();
                        var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        
                        switch (timeFilter) {
                            case 'today':
                                if (chatDate < today) return false;
                                break;
                            case 'yesterday':
                                var yesterday = new Date(today);
                                yesterday.setDate(yesterday.getDate() - 1);
                                if (chatDate < yesterday || chatDate >= today) return false;
                                break;
                            case 'week':
                                var weekAgo = new Date(today);
                                weekAgo.setDate(weekAgo.getDate() - 7);
                                if (chatDate < weekAgo) return false;
                                break;
                            case 'month':
                                var monthAgo = new Date(today);
                                monthAgo.setDate(monthAgo.getDate() - 30);
                                if (chatDate < monthAgo) return false;
                                break;
                        }
                    }

                    // 自定义时间范围筛选
                    if (timeFilter === 'custom' && (startDate || endDate)) {
                        var chatDate = new Date(chat.last_message_time || chat.created_at);
                        if (startDate) {
                            var start = new Date(startDate);
                            if (chatDate < start) return false;
                        }
                        if (endDate) {
                            var end = new Date(endDate);
                            end.setHours(23, 59, 59, 999); // 包含结束日期的整天
                            if (chatDate > end) return false;
                        }
                    }

                    return true;
                });

                // 显示筛选统计
                updateFilterStats();
                
                // 渲染筛选结果
                renderChatList(filteredChats);
                updateLoadMoreButton();
            }

            // 更新筛选统计信息
            function updateFilterStats() {
                var hasFilters = currentFilters.store || currentFilters.type || 
                               currentFilters.status || currentFilters.time;
                
                if (hasFilters) {
                    $('#filteredCount').text(filteredChats.length);
                    $('#filterStats').show();
                } else {
                    $('#filterStats').hide();
                }
            }

            // 清除所有筛选
            function clearAllFilters() {
                $('#chatStoreFilter').val('');
                $('#chatTypeFilter').val('');
                $('#chatStatusFilter').val('');
                $('#chatTimeFilter').val('');
                $('#startDate').val('');
                $('#endDate').val('');
                $('#customTimeRange').hide();
                
                // 重置分页状态
                pagination.offset = 0;
                pagination.hasMore = false;
                
                // 重新渲染表单
                setTimeout(function() {
                    form.render('select');
                }, 100);
                
                // 清除筛选条件并显示所有聊天
                currentFilters = {};
                filteredChats = allChats;
                $('#filterStats').hide();
                renderChatList(allChats);
                updateLoadMoreButton();
            }

            // 兼容旧的筛选函数
            function filterChats() {
                applyFilters();
            }



            // 刷新聊天列表
            function loadChatList() {
                loadAllChats();
            }

            // 加载更多聊天
            function loadMoreChats() {
                if (pagination.loading || !pagination.hasMore) {
                    return;
                }
                
                var currentStoreId = $('#chatStoreFilter').val();
                var currentChatType = $('#chatTypeFilter').val();
                
                if (!currentStoreId) {
                    layer.msg('请先选择店铺', {icon: 0});
                    return;
                }
                
                loadStoreChats(currentStoreId, currentChatType, true); // append = true
            }

            // 更新加载更多按钮状态
            function updateLoadMoreButton() {
                if (pagination.hasMore && !pagination.loading) {
                    $('#loadMoreContainer').show();
                    $('#loadMoreBtn').html('<i class="layui-icon layui-icon-down"></i> 加载更多聊天').prop('disabled', false);
                } else {
                    $('#loadMoreContainer').hide();
                }
            }

            // 对单个聊天应用筛选条件
            function applyChatFiltersToChat(chatItem) {
                // 类型筛选
                if (currentFilters.type && chatItem.chat_type !== currentFilters.type) {
                    return false;
                }
                
                // 状态筛选（未读/已读）
                if (currentFilters.status) {
                    if (currentFilters.status === 'unread' && chatItem.unread_count <= 0) {
                        return false;
                    }
                    if (currentFilters.status === 'read' && chatItem.unread_count > 0) {
                        return false;
                    }
                }
                
                // 时间筛选
                if (currentFilters.time && currentFilters.time !== 'custom') {
                    var chatDate = new Date(chatItem.last_message_time || chatItem.created_at);
                    var now = new Date();
                    var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    
                    switch (currentFilters.time) {
                        case 'today':
                            if (chatDate < today) return false;
                            break;
                        case 'yesterday':
                            var yesterday = new Date(today);
                            yesterday.setDate(yesterday.getDate() - 1);
                            if (chatDate < yesterday || chatDate >= today) return false;
                            break;
                        case 'week':
                            var weekAgo = new Date(today);
                            weekAgo.setDate(weekAgo.getDate() - 7);
                            if (chatDate < weekAgo) return false;
                            break;
                        case 'month':
                            var monthAgo = new Date(today);
                            monthAgo.setDate(monthAgo.getDate() - 30);
                            if (chatDate < monthAgo) return false;
                            break;
                    }
                }
                
                // 自定义时间范围筛选
                if (currentFilters.time === 'custom' && (currentFilters.startDate || currentFilters.endDate)) {
                    var chatDate = new Date(chatItem.last_message_time || chatItem.created_at);
                    if (currentFilters.startDate) {
                        var start = new Date(currentFilters.startDate);
                        if (chatDate < start) return false;
                    }
                    if (currentFilters.endDate) {
                        var end = new Date(currentFilters.endDate);
                        end.setHours(23, 59, 59, 999); // 包含结束日期的整天
                        if (chatDate > end) return false;
                    }
                }
                
                return true;
            }

            // 渲染聊天列表
            function renderChatList(chats, append) {
                var html = '';
                if (chats && chats.length > 0) {
                    chats.forEach(function(chat) {
                        var sendableTypes = ['Buyer_Seller', 'Seller_Support'];
                        var canSend = sendableTypes.includes(chat.chat_type);
                        var typeClass = canSend ? 'sendable' : 'readonly';
                        
                        var unreadHtml = chat.unread_count > 0 ? 
                            '<span class="unread-count">' + chat.unread_count + '</span>' : '';
                        
                        var chatTime = formatTime(chat.last_message_time || chat.created_at);
                        var hasUnread = chat.unread_count > 0;
                        var unreadClass = hasUnread ? ' has-unread' : '';
                        
                        // 构建订单和商品信息
                        var orderInfoHtml = '';
                        if (chat.chat_type === 'Buyer_Seller') {
                            orderInfoHtml = '<div class="chat-order-info">';
                            
                            if (chat.order_number || chat.sku || chat.product_name) {
                                // 有订单信息时显示具体信息
                                if (chat.order_number) {
                                    orderInfoHtml += '<span class="order-tag" title="订单号: ' + chat.order_number + '"><i class="layui-icon layui-icon-template-1"></i> ' + chat.order_number + '</span>';
                                }
                                if (chat.sku) {
                                    orderInfoHtml += '<span class="sku-tag" title="商品SKU: ' + chat.sku + '"><i class="layui-icon layui-icon-component"></i> ' + chat.sku + '</span>';
                                }
                                if (chat.product_name) {
                                    orderInfoHtml += '<div class="product-name-tag" title="商品名称: ' + chat.product_name + '"><i class="layui-icon layui-icon-star"></i> ' + chat.product_name + '</div>';
                                }
                            } else {
                                // 买家咨询但没有订单信息时显示提示
                                orderInfoHtml += '<span class="no-order-info" title="该聊天暂无关联的订单信息">' +
                                               '<i class="layui-icon layui-icon-close"></i> 暂无订单信息</span>';
                            }
                            
                            orderInfoHtml += '</div>';
                        }
                        
                        html += '<div class="chat-item' + unreadClass + '" data-chat-id="' + chat.chat_id + '" data-store-id="' + chat.store_id + '">' +
                                '  <div class="chat-item-header">' +
                                '    <h4 class="chat-title">' + (chat.title || '客户聊天') + '</h4>' +
                                '    <div class="chat-meta">' +
                                '      <span class="chat-time">' + chatTime + '</span>' +
                                       unreadHtml +
                                '    </div>' +
                                '  </div>' +
                                '  <div class="chat-info">' +
                                '    <div class="chat-store">' +
                                '      <i class="layui-icon layui-icon-home"></i>' +
                                '      <span>' + (chat.store_title || '未知店铺') + '</span>' +
                                '      <span class="chat-type-badge ' + typeClass + '">' + getChatTypeText(chat.chat_type) + '</span>' +
                                '    </div>' +
                                     orderInfoHtml +
                                '    <div class="chat-preview">' + (chat.last_message || '暂无消息') + '</div>' +
                                '  </div>' +
                                '</div>';
                    });
                } else {
                    var filterInfo = '';
                    var storeFilter = $('#chatStoreFilter').val();
                    var typeFilter = $('#chatTypeFilter').val();
                    
                    if (storeFilter || typeFilter) {
                        filterInfo = '<br><small>当前筛选条件下</small>';
                    }
                    
                    html = '<div class="empty-state">' +
                           '  <div class="empty-state-icon"><i class="layui-icon layui-icon-dialogue"></i></div>' +
                           '  <h3 class="empty-state-title">暂无聊天记录</h3>' +
                           '  <p class="empty-state-text">当有客户咨询时会显示在这里' + filterInfo + '</p>' +
                           '</div>';
                }
                
                if (append) {
                    $('#chatList .chat-item').last().after(html);
                } else {
                    $('#chatList').html(html);
                }

                // 绑定聊天项点击事件（只绑定新添加的项目或所有项目）
                var chatItems = append ? $('.chat-item').not('.event-bound') : $('.chat-item');
                chatItems.addClass('event-bound').on('click', function() {
                    var chatId = $(this).data('chat-id');
                    var storeId = $(this).data('store-id');
                    selectChat(chatId, storeId, $(this));
                    
                    // 移动端选择聊天后关闭侧边栏
                    if ($(window).width() <= 768) {
                        $('#chatSidebar').removeClass('show');
                    }
                });
                
                // 更新加载更多按钮状态
                updateLoadMoreButton();
            }

            // 获取聊天类型文本
            function getChatTypeText(chatType) {
                var typeMap = {
                    'Buyer_Seller': '买家咨询',
                    'Seller_Support': '客服支持',
                    'Seller_Notification': '系统通知',
                    'Seller_Notification_FBS': 'FBS通知',
                    'Seller_Notification_Major': '重要通知',
                    'Seller_Marketplace_Promo': '平台推广',
                    'Seller_Notification_Findoc': '财务文档',
                    'Seller_Notification_UpdateContent': '内容更新',
                    'Seller_Notification_Content': '内容通知',
                    'Seller_Paid_Brands': '品牌推广',
                    'Seller_Online_Training': '在线培训',
                    'CustomerService': '客服支持',
                    'Buyer': '买家咨询',
                    'System': '系统通知',
                    'Promotion': '平台推广'
                };
                return typeMap[chatType] || '其他类型';
            }

            // 选择聊天
            function selectChat(chatId, storeId, element) {
                if (currentChatId === chatId) return;

                currentChatId = chatId;
                currentStoreId = storeId;
                
                // 添加调试信息
                console.log('Selected chat:', {
                    chatId: chatId,
                    storeId: storeId,
                    currentChatId: currentChatId,
                    currentStoreId: currentStoreId
                });
                
                // 更新选中状态
                $('.chat-item').removeClass('active');
                element.addClass('active');

                // 显示聊天界面
                $('#chatHeader').show();
                $('#chatInput').show();
                
                var titleText = element.find('.chat-title').text().replace(/\d+$/, '').trim();
                var storeText = element.find('small').text();
                $('#currentChatTitle').html(titleText + '<br><small>' + storeText + '</small>');

                // 检查聊天类型，判断是否可以发送消息
                var chatData = allChats.find(function(chat) {
                    return chat.chat_id === chatId;
                });
                
                var canSendMessage = false;
                if (chatData) {
                    var sendableTypes = ['Buyer_Seller', 'Seller_Support'];
                    canSendMessage = sendableTypes.includes(chatData.chat_type);
                }
                
                // 根据聊天类型启用或禁用发送功能
                if (canSendMessage) {
                    $('#messageInput').prop('disabled', false).attr('placeholder', '输入消息内容，按 Ctrl+Enter 快速发送...');
                    $('#sendMessage').prop('disabled', false).removeClass('disabled');
                    $('#sendFile').prop('disabled', false).removeClass('disabled');
                    $('#chatInputNotice').hide();
                } else {
                    $('#messageInput').prop('disabled', true).attr('placeholder', '此聊天类型不支持发送消息');
                    $('#sendMessage').prop('disabled', true).addClass('disabled');
                    $('#sendFile').prop('disabled', true).addClass('disabled');
                    var chatTypeName = getChatTypeText(chatData ? chatData.chat_type : '');
                    $('#chatInputNotice').html('<i class="layui-icon layui-icon-tips"></i> <span>' + chatTypeName + '类型的聊天不支持回复消息</span>').show();
                }

                // 加载聊天历史
                loadChatHistory(chatId);

                // 移除未读标记
                element.find('.unread-count').remove();
            }

            // 加载聊天历史
            function loadChatHistory(chatId) {
                if (!chatId) {
                    $('#chatMessages').html('<div class="loading">请选择一个聊天</div>');
                    return;
                }

                $('#chatMessages').html('<div class="loading"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i><br>正在加载消息历史...</div>');

                // 使用当前选中聊天的店铺ID
                if (!currentStoreId) {
                    $('#chatMessages').html('<div class="loading">消息加载失败<br>店铺信息缺失</div>');
                    console.log('Error: currentStoreId is', currentStoreId);
                    return;
                }

                console.log('Loading chat history with:', {
                    chat_id: chatId,
                    store_id: currentStoreId
                });

                $.ajax({
                    url: 'chat_ajax.php?act=getChatHistory',
                    type: 'GET',
                    data: { 
                        chat_id: chatId,
                        store_id: currentStoreId
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            renderMessages(res.data);
                            
                            // 如果返回了订单信息，更新聊天列表中的显示
                            if (res.order_info) {
                                updateChatOrderInfo(chatId, res.order_info);
                            }
                        } else {
                            $('#chatMessages').html('<div class="loading">消息加载失败<br>' + res.msg + '</div>');
                        }
                    },
                    error: function() {
                        $('#chatMessages').html('<div class="loading">网络错误<br>无法加载消息历史</div>');
                    }
                });
            }

            // 渲染消息
            function renderMessages(messages) {
                var html = '';
                if (messages && messages.length > 0) {
                    messages.forEach(function(msg) {
                        var isReceived = msg.from_me === false;
                        var messageClass = isReceived ? 'message-received' : 'message-sent';
                        var avatar = isReceived ? '客' : '我';
                        
                        html += '<div class="message-item ' + messageClass + '">' +
                                '  <div class="message-avatar">' + avatar + '</div>' +
                                '  <div class="message-bubble">' +
                                '    <div class="message-content">' + msg.text + '</div>' +
                                '    <div class="message-time">' + formatTime(msg.created_at) + '</div>' +
                                '  </div>' +
                                '</div>';
                    });
                } else {
                    html = '<div class="empty-state">' +
                           '  <div class="empty-state-icon"><i class="layui-icon layui-icon-dialogue"></i></div>' +
                           '  <h3 class="empty-state-title">暂无消息记录</h3>' +
                           '  <p class="empty-state-text">开始对话吧！发送第一条消息</p>' +
                           '</div>';
                }
                
                $('#chatMessages').html(html);
                scrollToBottom();
            }

            // 发送消息
            function sendMessage() {
                if (!currentChatId) {
                    layer.msg('请先选择一个聊天');
                    return;
                }

                if (!currentStoreId) {
                    layer.msg('店铺信息缺失');
                    return;
                }

                var message = $('#messageInput').val().trim();
                if (!message) {
                    layer.msg('请输入消息内容');
                    return;
                }

                // 检查当前聊天是否可以发送消息
                var chatData = allChats.find(function(chat) {
                    return chat.chat_id === currentChatId;
                });
                
                if (chatData) {
                    var sendableTypes = ['Buyer_Seller', 'Seller_Support'];
                    if (!sendableTypes.includes(chatData.chat_type)) {
                        layer.msg('此聊天类型不支持发送消息', {icon: 0});
                        return;
                    }
                }

                var loadIndex = layer.load(2);
                
                $.ajax({
                    url: 'chat_ajax.php?act=sendMessage',
                    type: 'POST',
                    data: {
                        chat_id: currentChatId,
                        store_id: currentStoreId,
                        text: message
                    },
                    dataType: 'json',
                    success: function(res) {
                        layer.close(loadIndex);
                        if (res.code === 1) {
                            $('#messageInput').val('');
                            layer.msg('消息发送成功', {icon: 1});
                            // 重新加载消息历史
                            setTimeout(function() {
                                loadChatHistory(currentChatId);
                            }, 500);
                        } else {
                            // 特殊处理聊天禁用错误
                            if (res.error_type === 'chat_disabled') {
                                layer.msg(res.msg, {icon: 0, time: 3000});
                                // 禁用发送功能
                                $('#messageInput').prop('disabled', true).attr('placeholder', '此聊天类型不支持发送消息');
                                $('#sendMessage').prop('disabled', true).addClass('layui-btn-disabled');
                                $('#chatInputNotice').html('<i class="layui-icon layui-icon-tips"></i> ' + res.msg).show();
                            } else {
                                layer.msg('发送失败: ' + res.msg, {icon: 2});
                            }
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('网络错误，发送失败', {icon: 2});
                    }
                });
            }

            // 文件上传
            function uploadFile(file) {
                if (!currentChatId) {
                    layer.msg('请先选择一个聊天');
                    return;
                }

                if (!currentStoreId) {
                    layer.msg('店铺信息缺失');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    layer.msg('文件大小不能超过10MB');
                    return;
                }

                var formData = new FormData();
                formData.append('file', file);
                formData.append('chat_id', currentChatId);
                formData.append('store_id', currentStoreId);

                var loadIndex = layer.load(2);

                $.ajax({
                    url: 'chat_ajax.php?act=sendFile',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function(res) {
                        layer.close(loadIndex);
                        if (res.code === 1) {
                            layer.msg('文件发送成功', {icon: 1});
                            loadChatHistory(currentChatId);
                        } else {
                            layer.msg('文件发送失败: ' + res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('网络错误，文件发送失败', {icon: 2});
                    }
                });
            }

            // 标记聊天为已读
            function markChatAsRead(chatId) {
                if (!currentStoreId) {
                    return;
                }

                $.ajax({
                    url: 'chat_ajax.php?act=markRead',
                    type: 'POST',
                    data: { 
                        chat_id: chatId,
                        store_id: currentStoreId
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            layer.msg('已标记为已读', {icon: 1});
                        }
                    }
                });
            }

            // 检查新消息
            function checkNewMessages() {
                // 静默检查新消息，不显示加载提示
                $.ajax({
                    url: 'chat_ajax.php?act=getChatList',
                    type: 'GET',
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 1) {
                            // 检查是否有新的未读消息
                            var hasNewMessage = false;
                            res.data.forEach(function(chat) {
                                if (chat.unread_count > 0 && chat.chat_id !== currentChatId) {
                                    hasNewMessage = true;
                                }
                            });
                            
                            if (hasNewMessage) {
                                // 更新聊天列表显示
                                renderChatList(res.data);
                            }
                        }
                    }
                });
            }

            // 滚动到底部
            function scrollToBottom() {
                var chatMessages = document.getElementById('chatMessages');
                setTimeout(function() {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 100);
            }

            // 格式化时间
            function formatTime(timeStr) {
                try {
                    var date = new Date(timeStr);
                    var now = new Date();
                    var diff = now - date;
                    
                    if (diff < 60000) { // 1分钟内
                        return '刚刚';
                    } else if (diff < 3600000) { // 1小时内
                        return Math.floor(diff / 60000) + '分钟前';
                    } else if (diff < 86400000) { // 24小时内
                        return Math.floor(diff / 3600000) + '小时前';
                    } else {
                        return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
                    }
                } catch (e) {
                    return timeStr;
                }
            }

            // 更新聊天项的订单信息显示
            function updateChatOrderInfo(chatId, orderInfo) {
                var chatItem = $('.chat-item[data-chat-id="' + chatId + '"]');
                if (chatItem.length > 0) {
                    var orderInfoContainer = chatItem.find('.chat-order-info');
                    
                    if (orderInfo.order_number || orderInfo.sku || orderInfo.product_name) {
                        var orderHtml = '';
                        
                        if (orderInfo.order_number) {
                            orderHtml += '<span class="order-tag" title="订单号: ' + orderInfo.order_number + '"><i class="layui-icon layui-icon-template-1"></i> ' + orderInfo.order_number + '</span>';
                        }
                        if (orderInfo.sku) {
                            orderHtml += '<span class="sku-tag" title="商品SKU: ' + orderInfo.sku + '"><i class="layui-icon layui-icon-component"></i> ' + orderInfo.sku + '</span>';
                        }
                        if (orderInfo.product_name) {
                            orderHtml += '<div class="product-name-tag" title="商品名称: ' + orderInfo.product_name + '"><i class="layui-icon layui-icon-star"></i> ' + orderInfo.product_name + '</div>';
                        }
                        
                        orderInfoContainer.html(orderHtml);
                    }
                    
                    // 在聊天头部也显示订单信息
                    var currentChatTitle = $('#currentChatTitle');
                    if (currentChatTitle.length > 0 && currentChatId === chatId) {
                        var orderText = [];
                        if (orderInfo.order_number) orderText.push('订单: ' + orderInfo.order_number);
                        if (orderInfo.sku) orderText.push('SKU: ' + orderInfo.sku);
                        if (orderInfo.product_name) orderText.push('商品: ' + orderInfo.product_name);
                        
                        if (orderText.length > 0) {
                            var originalTitle = currentChatTitle.text().split('（')[0]; // 移除之前的订单信息
                            currentChatTitle.text(originalTitle + '（' + orderText.join(', ') + '）');
                        }
                    }
                    
                    console.log('Updated order info for chat', chatId, orderInfo);
                }
            }

            // 查看缓存的订单信息
            function viewCachedOrderInfo() {
                if (!currentChatId || !currentStoreId) {
                    layer.msg('请先选择一个聊天', {icon: 0});
                    return;
                }
                
                var loadIndex = layer.load(2);
                
                $.ajax({
                    url: 'chat_ajax.php?act=getCachedOrderInfo',
                    type: 'GET',
                    data: {
                        chat_id: currentChatId,
                        store_id: currentStoreId
                    },
                    dataType: 'json',
                    success: function(res) {
                        layer.close(loadIndex);
                        if (res.code === 1) {
                            var content = '<div style="max-height: 400px; overflow-y: auto;">';
                            content += '<h4>缓存的订单信息</h4>';
                            
                            // 显示当前聊天的缓存信息
                            content += '<h5>当前聊天（' + currentChatId.substring(0, 8) + '...）</h5>';
                            if (res.data.current_chat.cached_info) {
                                var info = res.data.current_chat.cached_info;
                                content += '<p><strong>订单号：</strong>' + (info.order_number || '无') + '</p>';
                                content += '<p><strong>SKU：</strong>' + (info.sku || '无') + '</p>';
                                content += '<p><strong>商品名称：</strong>' + (info.product_name || '无') + '</p>';
                                content += '<p><strong>提取来源：</strong>' + (info.extraction_source || '未知') + '</p>';
                            } else {
                                content += '<p style="color: #999;">该聊天暂无缓存的订单信息</p>';
                            }
                            
                            // 显示所有缓存的订单信息
                            content += '<h5>该店铺的所有缓存订单（共' + res.data.total_cached + '个）</h5>';
                            if (res.data.all_cached && res.data.all_cached.length > 0) {
                                content += '<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">';
                                content += '<thead><tr style="background: #f5f5f5;"><th style="border: 1px solid #ddd; padding: 5px;">聊天ID</th><th style="border: 1px solid #ddd; padding: 5px;">订单号</th><th style="border: 1px solid #ddd; padding: 5px;">SKU</th><th style="border: 1px solid #ddd; padding: 5px;">商品名称</th><th style="border: 1px solid #ddd; padding: 5px;">更新时间</th></tr></thead>';
                                content += '<tbody>';
                                res.data.all_cached.forEach(function(item) {
                                    content += '<tr>';
                                    content += '<td style="border: 1px solid #ddd; padding: 5px; font-size: 12px;">' + item.chat_id.substring(0, 8) + '...</td>';
                                    content += '<td style="border: 1px solid #ddd; padding: 5px;">' + (item.order_number || '-') + '</td>';
                                    content += '<td style="border: 1px solid #ddd; padding: 5px;">' + (item.sku || '-') + '</td>';
                                    content += '<td style="border: 1px solid #ddd; padding: 5px; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="' + (item.product_name || '') + '">' + (item.product_name || '-') + '</td>';
                                    content += '<td style="border: 1px solid #ddd; padding: 5px; font-size: 12px;">' + (item.updated_at || '-') + '</td>';
                                    content += '</tr>';
                                });
                                content += '</tbody></table>';
                            } else {
                                content += '<p style="color: #999;">该店铺暂无缓存的订单信息</p>';
                            }
                            
                            content += '</div>';
                            
                            layer.open({
                                type: 1,
                                title: '缓存的订单信息',
                                content: content,
                                area: ['700px', '500px'],
                                btn: ['关闭'],
                                yes: function(index) {
                                    layer.close(index);
                                }
                            });
                        } else {
                            layer.msg('获取缓存信息失败: ' + res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('网络错误，无法获取缓存信息', {icon: 2});
                    }
                });
            }

            // 调试订单信息提取
            function debugOrderExtraction() {
                var loadIndex = layer.load(2);
                
                $.ajax({
                    url: 'chat_ajax.php?act=debugOrderInfo',
                    type: 'GET',
                    dataType: 'json',
                    success: function(res) {
                        layer.close(loadIndex);
                        if (res.code === 1) {
                            var content = '<div style="max-height: 400px; overflow-y: auto;">';
                            content += '<h4>订单信息提取调试结果</h4>';
                            content += '<p><strong>总计找到聊天数：</strong>' + res.data.total_found + '</p>';
                            
                            if (res.data.recent_chats && res.data.recent_chats.length > 0) {
                                res.data.recent_chats.forEach(function(chat, index) {
                                    content += '<div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 4px;">';
                                    content += '<h5>聊天 ' + (index + 1) + '：' + chat.chat_id.substring(0, 8) + '...</h5>';
                                    content += '<p><strong>类型：</strong>' + chat.chat_type + '</p>';
                                    content += '<p><strong>标题：</strong>' + (chat.title || '无') + '</p>';
                                    content += '<p><strong>最后消息：</strong>' + (chat.last_message || '无') + '</p>';
                                    content += '<p><strong>原始数据字段：</strong>' + chat.raw_data_keys.join(', ') + '</p>';
                                    
                                    // 显示提取结果
                                    content += '<h6>提取结果：</h6>';
                                    Object.keys(chat.extraction_results).forEach(function(method) {
                                        var result = chat.extraction_results[method];
                                        content += '<div style="margin-left: 20px; padding: 5px; background: #f5f5f5;">';
                                        content += '<strong>' + method + '：</strong><br>';
                                        content += '订单号: ' + (result.order_number || '无') + '<br>';
                                        content += 'SKU: ' + (result.sku || '无') + '<br>';
                                        content += '商品名称: ' + (result.product_name || '无') + '<br>';
                                        content += '调试信息: ' + (result.debug || '无') + '<br>';
                                        content += '</div>';
                                    });
                                    
                                    content += '</div>';
                                });
                            } else {
                                content += '<p>未找到最近的聊天数据。请先加载一些买家咨询类型的聊天。</p>';
                            }
                            
                            content += '</div>';
                            
                            layer.open({
                                type: 1,
                                title: '订单信息提取调试',
                                content: content,
                                area: ['600px', '500px'],
                                btn: ['关闭'],
                                yes: function(index) {
                                    layer.close(index);
                                }
                            });
                        } else {
                            layer.msg('调试信息获取失败: ' + res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('网络错误，无法获取调试信息', {icon: 2});
                    }
                });
            }

            // 获取聊天类型文本
            function getChatTypeText(chatType) {
                switch (chatType) {
                    case 'Buyer_Seller':
                        return '买家咨询';
                    case 'Seller_Support':
                        return '客服支持';
                    case 'Seller_Notification':
                        return '系统通知';
                    case 'Seller_Notification_FBS':
                        return 'FBS通知';
                    case 'Seller_Notification_Major':
                        return '重要通知';
                    case 'Seller_Marketplace_Promo':
                        return '平台推广';
                    case 'Seller_Notification_Findoc':
                        return '财务文档';
                    case 'Seller_Notification_UpdateContent':
                        return '内容更新';
                    case 'Seller_Notification_Content':
                        return '内容通知';
                    case 'Seller_Paid_Brands':
                        return '品牌推广';
                    case 'Seller_Online_Training':
                        return '在线培训';
                    default:
                        return '未知类型';
                }
            }
        });
    </script>

    <!-- 调试信息区域 -->
    <div id="debugInfo" style="display: none; position: fixed; bottom: 10px; left: 10px; right: 10px; background: #f5f5f5; border: 1px solid #ddd; padding: 10px; max-height: 200px; overflow: auto; z-index: 9999; font-size: 12px;">
        <div style="margin-bottom: 10px;">
            <strong>调试信息：</strong>
            <button onclick="$('#debugInfo').hide()" style="float: right; background: #ff5722; color: white; border: none; padding: 2px 8px; cursor: pointer;">关闭</button>
        </div>
        <div id="debugContent"></div>
    </div>
</body>
</html> 