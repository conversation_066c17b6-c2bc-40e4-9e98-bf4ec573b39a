<?php
    include '../includes/common.php';
?>
<!DOCTYPE html>
<html>
<head>
    <title>定时上架管理</title>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../assets/layui/css/layui.css" media="all">
    <style>
        :root {
            --primary-color: #1E9FFF;
            --secondary-color: #5FB878;
            --border-color: #e6e6e6;
            --card-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
        }
        
        body {
            background-color: #f8f8f8;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        
        .layui-fluid {
            padding: 15px;
            max-width: 1600px;
        }
        
        .layui-card {
            border-radius: 4px;
            box-shadow: var(--card-shadow);
            margin-bottom: 15px;
            border: none;
            transition: all 0.3s;
        }
        
        .layui-card-header {
            background-color: #f8f8f8;
            border-bottom: 1px solid var(--border-color);
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: bold;
        }

        /* --- 弹窗内的布局样式 --- */
        .modal-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .modal-content {
            display: flex;
            flex: 1;
            min-height: 0;
            gap: 15px;
            padding: 15px;
        }
        
        .modal-left-panel, .modal-right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 400px;
        }

        /* --- 店铺选择器样式 (借鉴 plsc.php) --- */
        .stores-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 6px;
            padding: 8px;
            overflow-y: auto;
            overflow-x: visible;
            flex: 1;
            align-content: start;
        }
        
        .store-item {
            position: relative;
            border: 2px solid #e6e6e6;
            border-radius: 4px;
            padding: 6px 8px;
            cursor: pointer;
            background: #fff;
            transition: all 0.2s ease;
            user-select: none;
            min-height: 28px;
            overflow: visible;
        }
        
        .store-item:hover:not(.selecting-warehouse) {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow);
            border-color: #d9d9d9;
        }
        
        .store-item.selected {
            border-color: var(--primary-color);
            background-color: #f0f8ff;
        }
        
        .store-item.has-warehouse .selected-mark {
            display: inline-block;
        }
        
        .store-item.new-store {
            border-color: #ff9c6e;
            background-color: #fff7e6;
        }
        
        .store-item.new-store:hover {
            border-color: #ff7f50;
            background-color: #ffe7ba;
        }
        
        .store-item input[type="checkbox"] {
            display: none;
        }
        
        .store-content {
            display: flex;
            flex-direction: column;
            width: 100%;
            overflow: hidden;
        }
        
        .store-name {
            font-size: 12px;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 2px;
            color: #333;
            line-height: 1.2;
        }
        
        .group-tag {
            padding: 1px 4px;
            background-color: #f0f0f0;
            border-radius: 8px;
            font-size: 10px;
            color: #666;
            align-self: flex-start;
            margin-bottom: 2px;
        }
        
        .group-tag.new-tag {
            background-color: #fff7e6;
            color: #ff7f50;
            border: 1px solid #ff9c6e;
            animation: pulse-ungrouped 2s infinite;
        }
        
        @keyframes pulse-ungrouped {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }
        
        .selected-mark {
            display: none;
            color: #52c41a;
            font-size: 12px;
            position: absolute;
            top: 2px;
            right: 3px;
        }
        
        .warehouse-selector {
            position: fixed !important;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 99999 !important;
            display: none;
            max-height: 200px;
            overflow-y: auto;
            min-width: 120px;
        }
        
        .warehouse-item {
            padding: 6px 10px;
            transition: all 0.2s;
            font-size: 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            line-height: 1.2;
        }
        
        .warehouse-item:last-child {
            border-bottom: none;
        }
        
        .warehouse-item:hover {
            background-color: #f5f5f5;
        }
        
        .warehouse-item.selected {
            background-color: #e6f7ff;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .warehouse-item.disabled {
            color: #ccc;
            cursor: not-allowed;
            background: none;
        }
        
        .warehouse-item.disabled:hover {
            background: none;
        }

        .group-filter-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 10px 15px;
            background: #f8f8f8;
            border-bottom: 1px solid var(--border-color);
        }
        
        .group-filter-container select {
            flex: 1;
            min-width: 120px;
        }
        
        .group-filter-container button {
            flex-shrink: 0;
        }
        
        .group-filter-container span {
            flex-shrink: 0;
        }

        .layui-form-item .layui-form-label {
            width: 90px;
        }

        .layui-form-item .layui-input-block {
            margin-left: 120px;
        }

        /* 统计信息样式 */
        .stat-info {
            font-size: 12px;
            color: #999;
            padding: 5px 10px;
            background: #f8f8f8;
            border-radius: 3px;
            margin-top: 10px;
        }

        /* 响应式调整 */
        @media (max-width: 1400px) {
            .stores-container {
                grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            }
        }
        
        @media (max-width: 992px) {
            .modal-content {
                flex-direction: column;
            }
            
            .modal-left-panel, .modal-right-panel {
                min-width: 100%;
            }
            
            .stores-container {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                max-height: 300px;
            }
        }

        /* 加载动画 */
        @keyframes loading-rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-icon {
            animation: loading-rotate 1s linear infinite;
        }

        /* 确保弹窗和容器不会裁剪仓库选择器 */
        .layui-layer-content {
            overflow: visible !important;
        }
        
        .modal-container,
        .modal-content,
        .modal-right-panel,
        .layui-card {
            overflow: visible !important;
        }
        
        /* 仓库选择器的额外样式 */
        .warehouse-selector.show {
            display: block !important;
            animation: fadeIn 0.15s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <div>
                <i class="layui-icon layui-icon-time"></i> 定时上架管理
            </div>
            <div class="layui-btn-group">
                <button class="layui-btn" id="create-task-btn">
                    <i class="layui-icon layui-icon-add-1"></i> 创建上架任务
                </button>
                <button class="layui-btn layui-btn-warm" id="clear-products-btn">
                    <i class="layui-icon layui-icon-delete"></i> 清空全部待上架
                </button>
            </div>
        </div>
        <div class="layui-card-body">
            <table class="layui-hide" id="products-table" lay-filter="products-table"></table>
        </div>
    </div>
</div>

<!-- 表格工具栏 -->
<script type="text/html" id="table-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete_selected">删除选中</button>
        <button class="layui-btn layui-btn-sm" lay-event="refresh_table">刷新列表</button>
    </div>
</script>

<!-- 模板: 商品信息 -->
<script type="text/html" id="productInfoTpl">
    <div style="display: flex; align-items: center;">
        <img src="{{ d.primary_image || '../assets/img/syncing.png' }}" style="width: 50px; height: 50px; object-fit: contain; margin-right: 10px; border-radius: 4px;">
        <div>
            <div><strong>SKU:</strong> <a href="https://www.ozon.ru/product/{{ d.sku }}/" target="_blank" class="layui-blue">{{ d.sku }}</a></div>
            <div style="font-size: 12px; color: #999; margin-top: 2px;"><strong>标题:</strong> {{ d.title || '待获取' }}</div>
        </div>
    </div>
</script>

<!-- 模板: 价格信息 -->
<script type="text/html" id="priceInfoTpl">
    <div style="font-size: 13px;">
        <div><strong>导入价:</strong> ￥{{ d.price || 'N/A' }}</div>
        <div style="color: #ff7043;"><strong>采购价:</strong> ¥{{ d.purchase_price || 'N/A' }}</div>
        {{# if(d.average_price) { }}
        <div style="color: #2196F3;"><strong>平均价:</strong> ￥{{ d.average_price }}</div>
        {{# } }}
        {{# if(d.predicted_price) { }}
        <div style="color: #4CAF50;"><strong>预测价:</strong> ￥{{ d.predicted_price }}</div>
        {{# } }}
    </div>
</script>

<!-- 模板: 任务信息 -->
<script type="text/html" id="taskInfoTpl">
    <div style="font-size: 12px;">
        <div><strong>任务ID:</strong> {{ d.task_id || '未分组' }}</div>
        <div><strong>价格预设:</strong> 
        {{# if(d.price_preset == 0) { }}售价
        {{# } else if(d.price_preset == 1) { }}最高价
        {{# } else if(d.price_preset == 2) { }}预测价
        {{# } else if(d.price_preset == 3) { }}平均价
        {{# } else { }}售价{{# } }}
        </div>
        <div><strong>库存:</strong> {{ d.stock || 'N/A' }}</div>
        {{# if(d.store_info) { }}
        <div><strong>分配店铺:</strong> {{ d.store_info.store_name }}</div>
        <div><strong>分配仓库:</strong> {{ d.store_info.warehouse_name }}</div>
        {{# } }}
        {{# if(d.scheduled_at) { }}
        <div><strong>上架时间:</strong> {{ d.scheduled_at }}</div>
        {{# } else { }}
        <div style="color: #999;"><strong>上架时间:</strong> 系统自动安排</div>
        {{# } }}
        {{# if(d.is_distributed == 1) { }}
        <div style="color: #5FB878;"><i class="layui-icon layui-icon-share"></i> 均分模式</div>
        {{# } }}
    </div>
</script>

<!-- 模板: 状态 -->
<script type="text/html" id="statusTpl">
    {{# if(d.status === 'pending'){ }}
        <span class="layui-badge layui-bg-blue">待执行</span>
    {{# } else if(d.status === 'processing'){ }}
        <span class="layui-badge layui-bg-orange">处理中</span>
    {{# } else if(d.status === 'success'){ }}
        <span class="layui-badge layui-bg-green">已上架</span>
    {{# } else if(d.status === 'failed'){ }}
        <span class="layui-badge layui-bg-red" lay-tips="{{ d.msg || '未知错误' }}">上架失败</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">{{ d.status }}</span>
    {{# } }}
</script>

<!-- 弹窗模板: 创建上架任务 -->
<script type="text/html" id="create-task-modal-tpl">
<div class="modal-container">
    <div class="modal-content">
        <!-- 左侧面板 -->
        <div class="modal-left-panel">
            <div class="layui-card" style="flex: 1; display: flex; flex-direction: column;">
                <div class="layui-card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <i class="layui-icon layui-icon-form"></i> 1. 商品信息和设置
                </div>
                <div class="layui-card-body" style="padding: 20px; overflow-y: auto; flex: 1;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">商品信息</label>
                        <div class="layui-input-block">
                            <textarea name="product_input" class="layui-textarea" rows="8" placeholder="每行一条记录，格式示例：&#10;123456 99.99 50.00 https://1688.com/item/123&#10;789012 129.99 80.00&#10;&#10;说明：SKU 售价 采购价 [采购链接(可选)]"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">价格预设</label>
                        <div class="layui-input-block">
                            <select name="price_preset" lay-filter="price_preset">
                                <option value="0">售价</option>
                                <option value="1">最高价</option>
                                <option value="2">预测价</option>
                                <option value="3">平均价</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">统一库存</label>
                        <div class="layui-input-block">
                            <input type="number" name="stock" value="100" min="1" class="layui-input" placeholder="设置所有商品的库存数量">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">上架时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="scheduled_at" id="scheduled-at-input" class="layui-input" placeholder="留空则由系统自动安排上架时间">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">SKU分配</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="distributeSkus" lay-skin="switch" lay-text="均分|不均分" title="开启后，将所有SKU均匀分配给选中的店铺">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">价格区间</label>
                        <div class="layui-input-block">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <input type="checkbox" id="enablePriceRange" lay-skin="switch" lay-filter="enablePriceRange" lay-text="开启|关闭" title="开启后，每个店铺的价格将在基础价格上增加随机区间值">
                                <input type="number" id="priceRangeMin" class="layui-input" placeholder="如：-10.50" style="width: 100px;" step="0.01" disabled>
                                <span style="color: #999;">~</span>
                                <input type="number" id="priceRangeMax" class="layui-input" placeholder="如：50.99" style="width: 100px;" step="0.01" disabled>
                                <span style="font-size: 12px; color: #999;">￥</span>
                            </div>
                            <div class="layui-form-mid layui-word-aux" style="margin-top: 5px;">
                                最终价格 = 导入价格 + 随机区间值（支持负数，如-10~50）
                            </div>
                        </div>
                    </div>
                    <div class="stat-info">
                        <i class="layui-icon layui-icon-tips"></i> 
                        <span id="product-count">0</span> 条商品待导入
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧面板 -->
        <div class="modal-right-panel">
            <div class="layui-card" style="flex: 1; display: flex; flex-direction: column;">
                <div class="layui-card-header" style="background: linear-gradient(135deg, #ff9a56 0%, #ffad56 100%); color: white;">
                    <i class="layui-icon layui-icon-app"></i> 2. 选择店铺和仓库
                </div>
                <div class="layui-card-body" style="padding: 0; display: flex; flex-direction: column; flex: 1; min-height: 0; overflow: visible;">
                    <div class="group-filter-container">
                        <select id="groupFilter" lay-filter="groupFilter">
                            <option value="all">所有分组</option>
                            <option value="ungrouped">未分组</option>
                        </select>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="refreshStores" title="刷新店铺数据">
                            <i class="layui-icon layui-icon-refresh"></i>
                        </button>
                        <span style="font-size: 12px; color: #999;">
                            已选: <span id="selected-store-count">0</span> 店铺
                        </span>
                    </div>
                    <div class="stores-container" id="storesContainer">
                        <div style="text-align: center; color: #999; padding-top: 50px; grid-column: 1 / -1;">
                            <i class="layui-icon layui-icon-loading loading-icon" style="font-size: 24px;"></i>
                            <p>正在加载店铺信息...</p>
                        </div>
                    </div>
                    <div class="stat-info">
                        <i class="layui-icon layui-icon-tips"></i> 
                        双击店铺卡片可取消选择，点击店铺可选择仓库
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="text-align: right; padding: 15px; border-top: 1px solid #eee; background: #f8f8f8;">
        <button type="button" class="layui-btn layui-btn-lg" id="submit-task-btn">
            <i class="layui-icon layui-icon-ok"></i> 立即创建任务
        </button>
        <button type="button" class="layui-btn layui-btn-primary layui-btn-lg" id="cancel-task-btn">
            <i class="layui-icon layui-icon-close"></i> 取消
        </button>
    </div>
</div>
</script>

<script src="../assets/layui/layui.js"></script>
<script>
layui.use(['element', 'form', 'table', 'layer', 'laydate', 'jquery'], function() {
    var $ = layui.jquery,
        form = layui.form,
        table = layui.table,
        layer = layui.layer,
        laydate = layui.laydate;

    // 渲染商品表格
    table.render({
        elem: '#products-table',
        url: 'ajax.php?act=get_scheduled_products',
        page: true,
        limit: 20,
        limits: [20, 50, 100],
        toolbar: '#table-toolbar',
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'info', title: '商品信息', width: 350, templet: '#productInfoTpl'},
            {field: 'price_info', title: '价格信息', width: 150, templet: '#priceInfoTpl'},
            {field: 'task_info', title: '任务信息', width: 200, templet: '#taskInfoTpl'},
            {field: 'status', title: '状态', width: 100, templet: '#statusTpl', align: 'center'},
            {field: 'addtime', title: '创建时间', width: 170, sort: true}
        ]],
        parseData: function(res){
            return {
                "code": res.code, 
                "msg": res.msg, 
                "count": res.count, 
                "data": res.data
            };
        },
        id: 'products-table'
    });

    // 表格工具栏事件
    table.on('toolbar(products-table)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;
        
        switch(obj.event) {
            case 'delete_selected':
                if(data.length === 0){
                    return layer.msg('请先选择要删除的商品');
                }
                var ids = data.map(item => item.id);
                layer.confirm('确定要删除选中的 ' + ids.length + ' 个商品吗？', {icon: 3}, function(index){
                    var loading = layer.load(2);
                    $.post('ajax.php?act=delete_scheduled_products', {ids: ids}, function(res){
                        layer.close(loading);
                        if(res.code === 0){
                            layer.msg('删除成功', {icon: 1});
                            table.reload('products-table');
                        } else {
                            layer.alert('删除失败: ' + res.msg);
                        }
                    }, 'json').fail(function() {
                        layer.close(loading);
                        layer.alert('删除请求失败，请检查网络连接');
                    });
                    layer.close(index);
                });
                break;
                
            case 'refresh_table':
                table.reload('products-table');
                layer.msg('列表已刷新', {icon: 1, time: 1000});
                break;
        }
    });

    // 清空待上架列表
    $('#clear-products-btn').on('click', function() {
        layer.confirm('此操作将清空所有待上架的商品，确定吗？', {
            icon: 3, 
            title:'危险操作',
            btn: ['确定清空', '取消']
        }, function(index){
            var loading = layer.load(2, {shade: [0.3, '#000']});
            $.post('ajax.php?act=clear_scheduled_products', function(res) {
                layer.close(loading);
                if(res.code === 0){
                    layer.msg('已清空所有待上架商品', {icon: 1});
                    table.reload('products-table');
                } else {
                    layer.alert('清空失败: ' + res.msg);
                }
            }, 'json').fail(function() {
                layer.close(loading);
                layer.alert('清空请求失败，请检查网络连接');
            });
            layer.close(index);
        });
    });

    // "创建上架任务"按钮点击事件
    $('#create-task-btn').on('click', function() {
        var modalIndex = layer.open({
            type: 1,
            title: '<i class="layui-icon layui-icon-add-1"></i> 创建新的定时上架任务',
            area: ['90%', '90%'],
            maxmin: true,
            content: $('#create-task-modal-tpl').html(),
            success: function(layero, index) {
                // 重新渲染表单元素
                form.render();
                
                // 初始化日期时间选择器
                laydate.render({
                    elem: '#scheduled-at-input',
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss',
                    min: 0 // 不能选择过去的时间
                });
                
                // 价格区间开关事件 - 使用事件委托绑定到弹窗内
                $(layero).on('click', '#enablePriceRange', function() {
                    // 延迟一点执行，确保layui开关状态已更新
                    setTimeout(function() {
                        var isEnabled = $('#enablePriceRange').prop('checked');
                        console.log('价格区间开关状态:', isEnabled);
                        
                        var $minInput = $('#priceRangeMin');
                        var $maxInput = $('#priceRangeMax');
                        
                        $minInput.prop('disabled', !isEnabled);
                        $maxInput.prop('disabled', !isEnabled);
                        
                        if (isEnabled) {
                            $minInput.val('-10');
                            $maxInput.val('50');
                            $minInput.focus();
                        } else {
                            $minInput.val('');
                            $maxInput.val('');
                        }
                    }, 100);
                });
                
                var groupMap = {}; // 分组ID -> 分组名
                var shopGroupMap = {}; // 店铺ID -> 分组ID
                
                // 加载数据
                function loadData() {
                    loadShopGroups().then(loadStores).catch(function(error) {
                        console.error('数据加载失败:', error);
                        $('#storesContainer').html('<div style="text-align: center; color: #ff5722; padding: 50px; grid-column: 1 / -1;"><i class="layui-icon layui-icon-close" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>数据加载失败，请刷新重试</div>');
                    });
                }
                
                // 加载店铺分组
                function loadShopGroups() {
                    return new Promise(function(resolve, reject) {
                        console.log('开始加载分组数据...');
                        
                        $.ajax({
                            url: 'ajax.php?act=getShopGroups',
                            type: 'GET',
                            dataType: 'json',
                            cache: false,
                            data: { '_t': Date.now() },
                            timeout: 10000,
                            success: function(res) {
                                console.log('分组数据返回:', res);
                                
                                if (res && res.code === 0 && res.data) {
                                    // 处理分组数据 - 修复数据结构问题
                                    var groups = Array.isArray(res.data) ? res.data : [];
                                    
                                    if (groups.length > 0) {
                                        groups.forEach(function(group) {
                                            if (group.id && group.name) {
                                                groupMap[group.id] = group.name;
                                                if (group.shopIds && Array.isArray(group.shopIds)) {
                                                    group.shopIds.forEach(function(shopId) {
                                                        shopGroupMap[shopId] = group.id;
                                                    });
                                                }
                                            }
                                        });
                                    }
                                    
                                    console.log('分组映射更新完成:', groupMap);
                                    console.log('店铺分组映射:', shopGroupMap);
                                    
                                    renderGroupFilter(groups);
                                    resolve(groups);
                                } else {
                                    console.warn('分组数据无效:', res);
                                    renderGroupFilter([]);
                                    resolve([]);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.warn('分组加载失败:', error);
                                renderGroupFilter([]);
                                resolve([]); // 不阻止店铺加载
                            }
                        });
                    });
                }
                
                // 加载店铺列表 - 参考plsc.php的实现
                function loadStores() {
                    return new Promise(function(resolve, reject) {
                        $('#storesContainer').html('<div style="text-align: center; color: #999; padding: 50px; grid-column: 1 / -1;"><i class="layui-icon layui-icon-loading loading-icon" style="font-size: 24px;"></i><p>正在加载店铺信息...</p></div>');
                        
                        console.log('开始加载店铺数据...');
                        
                        // 优先使用新接口 (与plsc.php保持一致)
                        $.ajax({
                            url: 'ajax.php?act=store_List',
                            type: 'GET', 
                            dataType: 'json',
                            cache: false,
                            data: { 
                                '_t': Date.now(),
                                'limit': 999,  // 获取所有店铺
                                'page': 1
                            },
                            timeout: 15000,
                            success: function(res) {
                                console.log('新接口 ajax.php?act=store_List 返回数据:', res);
                                
                                if (res && res.code === 0 && res.data && res.data.length > 0) {
                                    // 处理新接口数据结构 - 与plsc.php保持一致
                                    var stores = res.data.map(function(store) {
                                        // 解析warehouses JSON字符串为数组
                                        var warehouses = [];
                                        if (store.warehouses) {
                                            try {
                                                warehouses = JSON.parse(store.warehouses);
                                                if (!Array.isArray(warehouses)) {
                                                    warehouses = [];
                                                }
                                            } catch (e) {
                                                console.warn('解析店铺 ' + store.id + ' 的仓库数据失败:', e);
                                                warehouses = [];
                                            }
                                        }
                                        
                                        // 返回标准化的店铺数据结构
                                        return Object.assign({}, store, {
                                            warehouses: warehouses,
                                            // 确保有分组信息
                                            Groupname: store.Groupname || '未分组'
                                        });
                                    });
                                    
                                    console.log('使用新接口加载完成，共 ' + stores.length + ' 个店铺');
                                    renderStores(stores);
                                    resolve(stores);
                                } else {
                                    console.warn('新接口数据无效，尝试使用原接口');
                                    loadStoresFromOriginalAPI().then(resolve).catch(reject);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.warn('新接口请求失败，尝试使用原接口:', error);
                                loadStoresFromOriginalAPI().then(resolve).catch(reject);
                            }
                        });
                    });
                }
                
                // 从原接口加载店铺数据（备用方案）- 与plsc.php保持一致
                function loadStoresFromOriginalAPI() {
                    return new Promise(function(resolve, reject) {
                        console.log('使用原接口加载店铺数据...');
                        
                        $.ajax({
                            url: '/api/get',
                            type: 'GET',
                            dataType: 'json',
                            cache: false,
                            data: { '_t': Date.now() },
                            timeout: 15000,
                            success: function(res) {
                                console.log('原接口 /api/get 返回数据:', res);
                                
                                if(res && res.stores && res.stores.length > 0) {
                                    // 确保每个店铺都有warehouses数组
                                    res.stores.forEach(function(store) {
                                        if (!store.warehouses || !Array.isArray(store.warehouses)) {
                                            store.warehouses = []; // 确保warehouses是数组
                                        }
                                    });
                                    
                                    console.log('使用原接口加载完成，共 ' + res.stores.length + ' 个店铺');
                                    renderStores(res.stores);
                                    resolve(res.stores);
                                } else {
                                    var errorMsg = '店铺加载失败或没有可用店铺';
                                    console.error('店铺数据错误:', res);
                                    reject(new Error(errorMsg));
                                }
                            },
                            error: function(xhr, status, error) {
                                var errorMsg = '店铺数据加载失败';
                                console.error('原接口数据请求失败:', error);
                                reject(new Error(errorMsg + ': ' + error));
                            }
                        });
                    });
                }

                // 渲染分组筛选器
                function renderGroupFilter(groups) {
                    const $filter = $('#groupFilter');
                    $filter.find('option:gt(1)').remove(); // 保留默认选项
                    
                    if (groups && groups.length > 0) {
                        groups.forEach(function(group) {
                            if (group.id && group.name) {
                                $filter.append('<option value="' + group.id + '">' + group.name + '</option>');
                            }
                        });
                    }
                    
                    // 重新渲染表单
                    form.render('select');
                    
                    console.log('分组筛选器已渲染，共', groups ? groups.length : 0, '个分组');
                }

                // 渲染店铺列表 - 改进版本，参考plsc.php
                function renderStores(stores) {
                    const container = $('#storesContainer');
                    container.empty();
                    
                    if (!stores || stores.length === 0) {
                        container.html('<div style="text-align: center; color: #ff5722; padding: 50px; grid-column: 1 / -1;"><i class="layui-icon layui-icon-home" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>暂无可用店铺</div>');
                        return;
                    }
                    
                    stores.forEach(function(store) {
                        var storeId = store.id || store.ClientId;
                        var storeName = store.storename || store.name || '未命名店铺';
                        
                        // 获取分组信息，兼容两种数据结构
                        var groupId, groupName, isNewStore = false;
                        
                        if (store.Groupname !== undefined) {
                            // 新接口：直接使用分组名称
                            groupName = store.Groupname;
                            groupId = store.group_id;
                            
                            // 检查是否为新店铺（分组名为"未分组"或空）
                            isNewStore = (!groupName || groupName === '未分组' || groupName.trim() === '');
                        } else {
                            // 原接口：使用分组映射
                            groupId = shopGroupMap[storeId];
                            groupName = groupMap[groupId] || '未分组';
                            isNewStore = !shopGroupMap.hasOwnProperty(storeId);
                        }
                        
                        // 标准化未分组店铺的ID和名称
                        if (!groupName || groupName.trim() === '' || groupName === '未分组') {
                            groupName = '未分组';
                            groupId = 'ungrouped';
                            isNewStore = true;
                        }
                        
                        // 确保groupId有值
                        if (!groupId) {
                            groupId = 'ungrouped';
                        }
                        
                        // 输出调试信息
                        console.log('渲染店铺: ' + storeName + ', ID: ' + storeId + ', 分组ID: ' + groupId + ', 分组名: ' + groupName + ', 是否新店铺: ' + isNewStore);
                        
                        // 确保warehouses是数组并处理仓库数据
                        var warehouses = store.warehouses || [];
                        console.log('店铺 ' + storeName + ' 仓库数据:', warehouses);
                        
                        // 生成仓库选项 - 改进逻辑
                        var whOptions = '';
                        if (warehouses && warehouses.length > 0) {
                            warehouses.forEach(function(wh) {
                                if (wh && (wh.warehouse_id || wh.id)) {
                                    var whId = wh.warehouse_id || wh.id;
                                    var whName = wh.name || ('仓库-' + whId);
                                    var isDisabled = (wh.is_enabled === false || wh.status === 0) ? ' disabled' : '';
                                    whOptions += '<div class="warehouse-item' + isDisabled + '" data-value="' + whId + '">' + whName + '</div>';
                                }
                            });
                        }
                        
                        // 如果没有可用仓库，显示提示
                        if (!whOptions) {
                            whOptions = '<div class="warehouse-item disabled" style="color: #999; font-style: italic;">暂无可用仓库</div>';
                        }

                        var itemHtml = '<div class="store-item ' + (isNewStore ? 'new-store' : '') + '" data-store-id="' + storeId + '" data-group-id="' + groupId + '">' +
                            '<input type="checkbox" name="store" value="' + storeId + '">' +
                            '<div class="store-content">' +
                                '<div class="store-name" title="' + storeName + '">' + storeName + '</div>' +
                                '<span class="group-tag ' + (isNewStore ? 'new-tag' : '') + '">' + groupName + '</span>' +
                            '</div>' +
                            '<i class="layui-icon layui-icon-ok-circle selected-mark"></i>' +
                            '<div class="warehouse-selector">' + whOptions + '</div>' +
                        '</div>';
                        
                        container.append(itemHtml);
                    });
                    
                    console.log('店铺渲染完成，共', stores.length, '个店铺');
                    
                    // 统计各分组的店铺数量用于调试
                    var groupStats = {};
                    $('.store-item').each(function() {
                        var groupId = $(this).data('group-id') || 'ungrouped';
                        var groupName = $(this).find('.group-tag').text() || '未分组';
                        if (!groupStats[groupId]) {
                            groupStats[groupId] = { name: groupName, count: 0 };
                        }
                        groupStats[groupId].count++;
                    });
                    
                    console.log('分组统计:', groupStats);
                }
                
                // 刷新店铺和分组数据
                function refreshStoresData() {
                    var loadingIndex = layer.load(1, {
                        shade: [0.2, '#fff'],
                        content: '正在刷新店铺和分组数据...'
                    });
                    
                    // 清空现有映射，强制重新获取
                    groupMap = {};
                    shopGroupMap = {};
                    
                    // 先重新加载分组数据
                    loadShopGroups().then(function() {
                        console.log('分组数据已刷新，店铺分组映射：', shopGroupMap);
                        // 然后重新加载店铺数据
                        return loadStores();
                    }).then(function() {
                        layer.close(loadingIndex);
                        layer.msg('店铺和分组数据已刷新', {icon: 1, time: 2000});
                    }).catch(function(err) {
                        layer.close(loadingIndex);
                        console.error('刷新数据失败:', err);
                        // 如果分组加载失败，仍然刷新店铺数据
                        loadStores().then(function() {
                            layer.msg('店铺数据已刷新（分组数据可能未更新）', {icon: 2, time: 3000});
                        });
                    });
                }

                // --- 事件绑定 ---
                
                // 店铺点击选择仓库
                $(layero).on('click', '.store-item', function(e) {
                    if ($(e.target).hasClass('warehouse-item')) return;
                    e.preventDefault();
                    e.stopPropagation();
                    
                    var $storeItem = $(this);
                    var $currentSelector = $storeItem.find('.warehouse-selector');
                    var isCurrentlyVisible = $currentSelector.hasClass('show');
                    
                    // 移除所有店铺的选择状态
                    $('.store-item').removeClass('selecting-warehouse');
                    
                    // 隐藏所有仓库选择器
                    $('.warehouse-selector').removeClass('show').hide();
                    
                    // 如果当前选择器未显示，则立即显示它
                    if (!isCurrentlyVisible) {
                        // 标记当前店铺正在选择仓库
                        $storeItem.addClass('selecting-warehouse');
                        
                        // 计算店铺卡片的位置
                        var offset = $storeItem.offset();
                        var height = $storeItem.outerHeight();
                        
                        // 设置仓库选择器的位置
                        $currentSelector.css({
                            'top': offset.top + height + 2,
                            'left': offset.left,
                            'width': Math.max($storeItem.outerWidth(), 120),
                            'display': 'block'
                        });
                        
                        $currentSelector.addClass('show');
                    }
                });
                
                // 刷新店铺数据
                $(layero).on('click', '#refreshStores', function() {
                    console.log('用户点击刷新店铺数据');
                    refreshStoresData();
                });

                // 仓库选择
                $(layero).on('click', '.warehouse-item:not(.disabled)', function(e) {
                    e.stopPropagation();
                    var $this = $(this);
                    var storeItem = $this.closest('.store-item');
                    var warehouseId = $this.data('value');

                    // 选中当前仓库，取消其他仓库选择
                    $this.addClass('selected').siblings().removeClass('selected');
                    
                    // 标记店铺为已选择并设置仓库ID
                    storeItem.data('warehouse-id', warehouseId).addClass('selected has-warehouse').removeClass('selecting-warehouse');
                    
                    // 隐藏仓库选择器
                    $this.closest('.warehouse-selector').removeClass('show').hide();
                    
                    updateSelectedCount();
                });
                
                // 双击取消选择
                $(layero).on('dblclick', '.store-item', function(e) {
                    e.stopPropagation();
                    $(this).removeClass('selected has-warehouse selecting-warehouse').removeData('warehouse-id');
                    $(this).find('.warehouse-item').removeClass('selected');
                    $(this).find('.warehouse-selector').removeClass('show').hide();
                    updateSelectedCount();
                });

                // 分组筛选
                $(layero).on('change', '#groupFilter', function() {
                    var groupId = $(this).val();
                    console.log('分组筛选切换:', groupId);
                    
                    if (groupId === 'all') {
                        $('.store-item').show();
                        console.log('显示所有店铺');
                    } else if (groupId === 'ungrouped') {
                        $('.store-item').each(function() {
                            var $item = $(this);
                            var isUngrouped = $item.hasClass('new-store') || 
                                              $item.data('group-id') === 'ungrouped' ||
                                              $item.data('group-id') === null ||
                                              $item.data('group-id') === '';
                            if (isUngrouped) {
                                $item.show();
                                console.log('显示未分组店铺:', $item.find('.store-name').text());
                            } else {
                                $item.hide();
                            }
                        });
                    } else {
                        var visibleCount = 0;
                        $('.store-item').each(function() {
                            var $item = $(this);
                            if ($item.data('group-id') == groupId) {
                                $item.show();
                                visibleCount++;
                                console.log('显示分组店铺:', $item.find('.store-name').text(), '分组ID:', $item.data('group-id'));
                            } else {
                                $item.hide();
                            }
                        });
                        console.log('分组 ' + groupId + ' 显示了 ' + visibleCount + ' 个店铺');
                    }
                });

                // 商品输入变化时更新统计
                $(layero).on('input', 'textarea[name="product_input"]', function() {
                    updateProductCount();
                });

                function updateSelectedCount() {
                    var count = $('.store-item.selected.has-warehouse').length;
                    $('#selected-store-count').text(count);
                }

                function updateProductCount() {
                    var text = $(layero).find('textarea[name="product_input"]').val().trim();
                    var lines = text ? text.split('\n').filter(function(line) {
                        return line.trim().length > 0;
                    }) : [];
                    $('#product-count').text(lines.length);
                }

                // 点击其他地方隐藏仓库选择器
                $(document).on('click.warehouse-selector', function() {
                    $('.store-item').removeClass('selecting-warehouse');
                    $('.warehouse-selector').removeClass('show').hide();
                });
                
                // 提交任务
                $('#submit-task-btn').on('click', function() {
                    try {
                        var taskData = validateAndCollectData();
                        if (!taskData) return;
                        
                        var distributeSkus = $('#distributeSkus').is(':checked');
                        
                        if (distributeSkus) {
                            submitDistributedTask(taskData);
                        } else {
                            submitRegularTask(taskData);
                        }
                    } catch (error) {
                        layer.msg(error.message, {icon: 2});
                    }
                });
                
                // 验证和收集数据
                function validateAndCollectData() {
                    var productsText = $(layero).find('textarea[name="product_input"]').val().trim();
                    var price_preset = parseInt($(layero).find('select[name="price_preset"]').val());
                    var stock = parseInt($(layero).find('input[name="stock"]').val());
                    var scheduled_at = $(layero).find('input[name="scheduled_at"]').val().trim();
                    var store_config = [];

                    // 验证商品信息
                    if (!productsText) {
                        throw new Error('请填写商品信息');
                    }
                    
                    // 解析商品数据
                    var products = productsText.split('\n').map(function(line) {
                        line = line.trim();
                        if (!line) return null;
                        var parts = line.split(/\s+/);
                        if (!parts[0]) return null;
                        return {
                            sku: parts[0],
                            price: parts[1] || null,
                            purchase_price: parts[2] || null,
                            purchase_link: parts[3] || null
                        };
                    }).filter(function(p) {
                        return p && p.sku;
                    });

                    if (products.length === 0) {
                        throw new Error('未解析到有效的商品数据，请检查格式');
                    }
                    
                    // 验证库存
                    if (!stock || stock <= 0) {
                        throw new Error('库存必须是大于0的数字');
                    }
                    
                    // 收集店铺配置
                    $('.store-item.selected.has-warehouse').each(function() {
                        var storeId = $(this).data('store-id');
                        var warehouseId = $(this).data('warehouse-id');
                        if (storeId && warehouseId) {
                            store_config.push({
                                store_id: storeId,
                                warehouse_id: warehouseId
                            });
                        }
                    });

                    if (store_config.length === 0) {
                        throw new Error('请至少选择一个店铺并为其指定仓库');
                    }
                    
                    // 如果开启了均分，检查SKU数量是否足够
                    if ($('#distributeSkus').is(':checked') && products.length < store_config.length) {
                        throw new Error('SKU数量(' + products.length + ')少于店铺数量(' + store_config.length + ')，无法均分');
                    }
                    
                    // 收集价格区间设置
                    var priceRangeEnabled = $('#enablePriceRange').is(':checked');
                    var priceRange = null;
                    
                    if (priceRangeEnabled) {
                        var priceMin = parseFloat($('#priceRangeMin').val());
                        var priceMax = parseFloat($('#priceRangeMax').val());
                        
                        // 验证价格区间
                        if (isNaN(priceMin) || isNaN(priceMax)) {
                            throw new Error('价格区间的最小值和最大值必须是有效数字');
                        }
                        
                        if (priceMin > priceMax) {
                            throw new Error('价格区间的最小值不能大于最大值');
                        }
                        
                        priceRange = {
                            min: priceMin,
                            max: priceMax
                        };
                    }

                    return {
                        products: products,
                        store_config: store_config,
                        price_preset: price_preset,
                        stock: stock,
                        scheduled_at: scheduled_at || null,
                        price_range: priceRange
                    };
                }
                
                // 提交均分任务
                function submitDistributedTask(data) {
                    var totalProducts = data.products.length;
                    var totalStores = data.store_config.length;
                    var skusPerStore = Math.floor(totalProducts / totalStores);
                    var remainder = totalProducts % totalStores;

                    var confirmationMsg = '共 ' + totalProducts + ' 个SKU, ' + totalStores + ' 个店铺。<br>每个店铺将分配 ' + skusPerStore + ' 个SKU。';
                    if (remainder > 0) {
                        confirmationMsg += '<br>剩余 ' + remainder + ' 个SKU将依次分配给前面的店铺。';
                    }
                    confirmationMsg += '<br>后端将自动处理分配，是否继续？';

                    layer.confirm(confirmationMsg, {
                        icon: 3,
                        title: '均分SKU确认',
                        area: ['350px', 'auto']
                    }, function(index) {
                        layer.close(index);
                        
                        // 标记为均分模式，让后端处理分配逻辑
                        data.distributeSkus = true;
                        submitToBackend(data);
                    });
                }
                
                // 提交常规任务
                function submitRegularTask(data) {
                    var totalProducts = data.products.length;
                    
                    layer.confirm('即将创建定时上架任务：' + totalProducts + ' 个商品到 ' + data.store_config.length + ' 个店铺，是否继续？', {
                        icon: 3, 
                        title: '创建任务确认'
                    }, function(index) {
                        layer.close(index);
                        submitToBackend(data);
                    });
                }
                
                // 提交到后端
                function submitToBackend(data) {
                    console.log('提交数据:', data);
                    
                    var loading = layer.load(2, {shade: [0.3, '#000']});
                    $.ajax({
                        url: 'ajax.php?act=create_scheduled_task',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(data),
                        dataType: 'json',
                        timeout: 30000,
                        success: function(res) {
                            layer.close(loading);
                            if (res.code === 0 || res.code === 1) {
                                // 处理成功或部分成功的情况
                                var message = res.msg || '任务创建完成';
                                var icon = res.code === 0 ? 1 : 2; // 完全成功用绿色，部分成功用黄色
                                
                                // 如果有失败的记录，显示详细信息
                                if (res.data && res.data.failed_inserts && res.data.failed_inserts.length > 0) {
                                    message += '<br><br>失败记录：<br>' + res.data.failed_inserts.slice(0, 5).join('<br>');
                                    if (res.data.failed_inserts.length > 5) {
                                        message += '<br>... 还有 ' + (res.data.failed_inserts.length - 5) + ' 条失败记录';
                                    }
                                    
                                    layer.alert(message, {
                                        icon: icon,
                                        area: ['500px', 'auto'],
                                        title: '任务创建结果'
                                    });
                                } else {
                                    layer.msg(message, {icon: icon, time: 3000});
                                }
                                
                                layer.close(modalIndex);
                                table.reload('products-table');
                            } else {
                                layer.alert('任务创建失败: ' + (res.msg || '未知错误'), {icon: 2});
                            }
                        },
                        error: function(xhr, status, error) {
                            layer.close(loading);
                            console.error('提交请求失败:', xhr, status, error);
                            var errorMsg = '请求失败: ';
                            if (status === 'timeout') {
                                errorMsg += '请求超时，请重试';
                            } else if (xhr.status === 0) {
                                errorMsg += '网络连接失败';
                            } else {
                                errorMsg += '服务器错误 (' + xhr.status + ')';
                            }
                            layer.alert(errorMsg, {icon: 2});
                        }
                    });
                }
                
                // 取消按钮
                $('#cancel-task-btn').on('click', function() {
                    layer.close(modalIndex);
                });
                
                // 初始化加载数据
                loadData();
                updateProductCount();
            },
            end: function() {
                // 清理事件监听器
                $(document).off('click.warehouse-selector');
            }
        });
    });
});
</script>
</body>
</html>