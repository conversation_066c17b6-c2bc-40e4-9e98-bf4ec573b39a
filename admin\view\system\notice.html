<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统公告</title>
    <link rel="stylesheet" href="../../../assets/component/pear/css/pear.css" />
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>系统公告管理</span>
            <div style="float: right;">
                <button class="layui-btn layui-btn-sm" id="add-notice-btn">
                    <i class="layui-icon layui-icon-add-1"></i> 发布公告
                </button>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 当前公告 -->
            <div id="current-notice" style="margin-bottom: 20px;">
                <div class="layui-card">
                    <div class="layui-card-header">当前公告</div>
                    <div class="layui-card-body">
                        <div id="notice-content" style="min-height: 100px; padding: 15px; background: #f8f9fa; border-radius: 4px; color: #666;">
                            暂无系统公告
                        </div>
                        <div style="margin-top: 10px; text-align: right;">
                            <button class="layui-btn layui-btn-xs layui-btn-normal" id="edit-notice-btn">编辑</button>
                            <button class="layui-btn layui-btn-xs layui-btn-danger" id="clear-notice-btn">清空</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">系统信息</div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <tbody>
                                    <tr>
                                        <td width="30%">系统版本</td>
                                        <td>海豚ERP v1.0</td>
                                    </tr>
                                    <tr>
                                        <td>PHP版本</td>
                                        <td id="php-version">获取中...</td>
                                    </tr>
                                    <tr>
                                        <td>服务器时间</td>
                                        <td id="server-time">获取中...</td>
                                    </tr>
                                    <tr>
                                        <td>运行环境</td>
                                        <td id="server-env">获取中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">快速操作</div>
                        <div class="layui-card-body">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-fluid" onclick="window.open('../console/index.html')">
                                    <i class="layui-icon layui-icon-console"></i> 回到仪表盘
                                </button>
                                <br><br>
                                <button class="layui-btn layui-btn-normal layui-btn-fluid" onclick="window.open('../user/list.html')">
                                    <i class="layui-icon layui-icon-user"></i> 用户管理
                                </button>
                                <br><br>
                                <button class="layui-btn layui-btn-warm layui-btn-fluid" onclick="window.open('../monitor/logs.html')">
                                    <i class="layui-icon layui-icon-file"></i> 查看日志
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑公告弹窗 -->
    <div id="notice-form" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="noticeForm">
            <div class="layui-form-item">
                <label class="layui-form-label">公告标题</label>
                <div class="layui-input-block">
                    <input type="text" name="title" placeholder="请输入公告标题" class="layui-input" lay-verify="required">
                </div>
            </div>
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">公告内容</label>
                <div class="layui-input-block">
                    <textarea name="content" placeholder="请输入公告内容" class="layui-textarea" style="height: 200px;" lay-verify="required"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">优先级</label>
                <div class="layui-input-block">
                    <input type="radio" name="priority" value="info" title="普通" checked>
                    <input type="radio" name="priority" value="warning" title="重要">
                    <input type="radio" name="priority" value="danger" title="紧急">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="saveNotice">发布公告</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </div>

    <script src="../../../assets/component/layui/layui.js"></script>
    
    <script>
        layui.use(['form', 'layer', 'jquery'], function() {
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.jquery;
            
            var currentNotice = null;
            
            // 更新服务器时间
            function updateServerTime() {
                var now = new Date();
                var timeStr = now.getFullYear() + '-' + 
                             (now.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                             now.getDate().toString().padStart(2, '0') + ' ' + 
                             now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0') + ':' + 
                             now.getSeconds().toString().padStart(2, '0');
                $('#server-time').text(timeStr);
            }
            
            // 加载系统信息
            function loadSystemInfo() {
                $('#php-version').text('PHP ' + '<?php echo PHP_VERSION; ?>' || '未知');
                $('#server-env').text(navigator.userAgent.indexOf('Windows') > -1 ? 'Windows' : 'Linux');
                updateServerTime();
                setInterval(updateServerTime, 1000);
            }
            
            // 加载当前公告
            function loadCurrentNotice() {
                // 这里应该从服务器加载，暂时使用localStorage模拟
                var notice = localStorage.getItem('system_notice');
                if (notice) {
                    try {
                        currentNotice = JSON.parse(notice);
                        displayNotice(currentNotice);
                    } catch (e) {
                        console.error('解析公告数据失败:', e);
                    }
                }
            }
            
            // 显示公告
            function displayNotice(notice) {
                if (!notice) {
                    $('#notice-content').html('<div style="color: #999;">暂无系统公告</div>');
                    return;
                }
                
                var priorityClass = '';
                var priorityText = '';
                switch (notice.priority) {
                    case 'warning':
                        priorityClass = 'layui-bg-orange';
                        priorityText = '重要';
                        break;
                    case 'danger':
                        priorityClass = 'layui-bg-red';
                        priorityText = '紧急';
                        break;
                    default:
                        priorityClass = 'layui-bg-blue';
                        priorityText = '普通';
                        break;
                }
                
                var html = `
                    <div style="margin-bottom: 10px;">
                        <span style="font-size: 16px; font-weight: bold;">${notice.title}</span>
                        <span class="layui-badge ${priorityClass}" style="margin-left: 10px;">${priorityText}</span>
                    </div>
                    <div style="line-height: 1.6; color: #333;">${notice.content.replace(/\n/g, '<br>')}</div>
                    <div style="margin-top: 15px; color: #999; font-size: 12px;">发布时间: ${notice.created_at}</div>
                `;
                
                $('#notice-content').html(html);
            }
            
            // 发布公告按钮
            $('#add-notice-btn').on('click', function() {
                form.val('noticeForm', {
                    title: '',
                    content: '',
                    priority: 'info'
                });
                
                layer.open({
                    type: 1,
                    title: '发布系统公告',
                    content: $('#notice-form'),
                    area: ['600px', '450px'],
                    shadeClose: false
                });
            });
            
            // 编辑公告按钮
            $('#edit-notice-btn').on('click', function() {
                if (currentNotice) {
                    form.val('noticeForm', {
                        title: currentNotice.title,
                        content: currentNotice.content,
                        priority: currentNotice.priority
                    });
                } else {
                    form.val('noticeForm', {
                        title: '',
                        content: '',
                        priority: 'info'
                    });
                }
                
                layer.open({
                    type: 1,
                    title: currentNotice ? '编辑系统公告' : '发布系统公告',
                    content: $('#notice-form'),
                    area: ['600px', '450px'],
                    shadeClose: false
                });
            });
            
            // 清空公告按钮
            $('#clear-notice-btn').on('click', function() {
                layer.confirm('确定要清空当前公告吗？', {icon: 3, title: '提示'}, function(index) {
                    localStorage.removeItem('system_notice');
                    currentNotice = null;
                    displayNotice(null);
                    layer.msg('公告已清空', {icon: 1});
                    layer.close(index);
                });
            });
            
            // 表单提交
            form.on('submit(saveNotice)', function(data) {
                var notice = {
                    title: data.field.title,
                    content: data.field.content,
                    priority: data.field.priority,
                    created_at: new Date().toLocaleString()
                };
                
                // 这里应该提交到服务器，暂时使用localStorage
                localStorage.setItem('system_notice', JSON.stringify(notice));
                currentNotice = notice;
                displayNotice(notice);
                
                layer.msg('公告发布成功', {icon: 1});
                layer.closeAll();
                
                return false;
            });
            
            // 初始化
            loadSystemInfo();
            loadCurrentNotice();
        });
    </script>
</body>
</html> 