<?php
session_start();
$csrf_token = md5(mt_rand(0,999).time());
$_SESSION['csrf_token'] = $csrf_token;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>海豚ERP系统 - 登录注册</title>
    <link rel="stylesheet" href="../assets/component/pear/css/pear.css" />
    <style>
        :root {
            --primary-color: #1890ff;
            --bg-gradient: linear-gradient(145deg, #f0f9ff 0%, #e6f4ff 100%);
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: var(--bg-gradient);
            background-image: url(../assets/admin/images/background.svg);
        }

        .login-card {
            width: 1200px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            display: grid;
            grid-template-columns: 1fr 1fr;
        }

        .brand-section {
            padding: 50px;
            background: var(--primary-color);
            color: white;
            position: relative;
            border-radius: 12px 0 0 12px;
        }

        .brand-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 30px;
        }

        .brand-title {
            font-size: 28px;
            font-weight: 600;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 40px 0;
        }

        .feature-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }

        .login-section {
            padding: 50px 60px;
            position: relative;
        }

        /* 选项卡样式 */
        .form-tabs {
            display: flex;
            gap: 30px;
            margin-bottom: 40px;
        }

        .tab-item {
            cursor: pointer;
            font-size: 20px;
            color: #666;
            padding-bottom: 8px;
            transition: all 0.3s;
            position: relative;
        }

        .tab-item.active {
            color: var(--primary-color);
            font-weight: 500;
        }

        .tab-item.active::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--primary-color);
        }

        /* 注册表单调整 */
        .register-form .layui-form-item {
            margin-bottom: 22px;
        }

        .register-form .terms {
            margin-top: 30px;
            font-size: 12px;
            text-align: center;
            color: #666;
        }

        /* 公共样式 */
        .form-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .form-title-main {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }

        .form-title-sub {
            color: #666;
        }

        /* 表单面板切换 */
        .form-panel {
            display: none;
        }

        .form-panel.active {
            display: block;
        }

        /* 验证码样式 */
        .captcha-wrapper {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .captcha-wrapper .layui-input-wrap {
            flex: 1;
        }

        .verification-img {
            cursor: pointer;
        }
        
        /* 滑块验证码样式 */
        .slider-captcha {
            margin-bottom: 15px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .slider-captcha-container {
            position: relative;
            height: 40px;
            background: #f7f7f7;
        }
        
        .slider-captcha-track {
            height: 100%;
            background: #e8e8e8;
            position: relative;
        }
        
        .slider-captcha-thumb {
            position: absolute;
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            border-radius: 2px;
            top: 0;
            left: 0;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .slider-captcha-text {
            position: absolute;
            width: 100%;
            text-align: center;
            line-height: 40px;
            color: #666;
            user-select: none;
        }
        
        .sms-code-wrapper {
            display: flex;
            gap: 10px;
        }
        
        .sms-code-wrapper .layui-input-wrap {
            flex: 1;
        }
        
        .get-sms-btn {
            width: 120px;
        }
        
        .countdown {
            color: #999;
            text-align: center;
            line-height: 38px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- 左侧品牌信息 -->
            <div class="brand-section">
                <div class="brand-header">
                    <i class="layui-icon layui-icon-component" style="font-size:36px"></i>
                    <div>
                        <div class="brand-title">海豚ERP系统</div>
                        <div style="font-size:16px">好用不贵</div>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">精确选品</div>
                    <div class="feature-item">上货跟卖</div>
                    <div class="feature-item">竞品监控</div>
                    <div class="feature-item">数据分析</div>
                </div>
                
                <div class="protocol-links">
                    <span>© 2024 海豚ERP</span>
                    <span style="margin:0 10px">|</span>
                    <a href="#" style="color:rgba(255,255,255,0.8)">用户协议</a>
                    <span style="margin:0 10px">|</span>
                    <a href="#" style="color:rgba(255,255,255,0.8)">隐私政策</a>
                </div>
            </div>

            <!-- 右侧表单区域 -->
            <div class="login-section">
                <div class="form-tabs">
                    <div class="tab-item active" data-tab="login">登录</div>
                    <div class="tab-item" data-tab="register">注册</div>
                </div>

                <!-- 登录表单 -->
                <div class="layui-form login-form form-panel active" data-panel="login">
                    <div class="form-title">
                        <div class="form-title-main">欢迎登录</div>
                        <div class="form-title-sub">请使用您的平台账号登录</div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-username"></i>
                            </div>
                            <input name="username" type="text" 
                                   placeholder="手机号/邮箱/用户名" 
                                   class="layui-input" 
                                   lay-verify="required">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-password"></i>
                            </div>
                            <input name="password" type="password" 
                                   placeholder="请输入密码" 
                                   class="layui-input"
                                   lay-affix="eye">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="captcha-wrapper">
                            <div class="layui-input-wrap">
                                <div class="layui-input-prefix">
                                    <i class="layui-icon layui-icon-vercode"></i>
                                </div>
                                <input type="text" 
                                       placeholder="验证码" 
                                       class="layui-input" value="VBJFQ">
                            </div>
                            <img src="../assets/admin/images/captcha.gif" 
                                 class="verification-img"
                                 style="height:38px;border-radius:4px">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="remember-passsword">
                            <div class="remember-cehcked">
                                <input type="checkbox" name="auto_login" lay-skin="primary" title="自动登录">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <button class="layui-btn layui-btn-fluid" 
                                style="background:var(--primary-color)"
                                lay-submit lay-filter="login">立即登录</button>
                    </div>

                    <div class="layui-form-item" style="text-align:center;margin-top:20px">
                        <a href="javascript:;" class="forgot-password-link" style="color:#666">忘记密码？</a>
                    </div>

                    <div class="terms">
                        登录即表示同意用户协议与隐私条款
                    </div>
                </div>

                <!-- 注册表单 -->
                <div class="layui-form register-form form-panel" data-panel="register">
                    <div class="form-title">
                        <div class="form-title-main">新用户注册</div>
                        <div class="form-title-sub">创建您的系统账户</div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-username"></i>
                            </div>
                            <input name="username" lay-verify="required" 
                                   placeholder="用户名" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-cellphone"></i>
                            </div>
                            <input name="phone" lay-verify="required|phone" 
                                   placeholder="手机号码" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="sms-code-wrapper">
                            <div class="layui-input-wrap">
                                <div class="layui-input-prefix">
                                    <i class="layui-icon layui-icon-vercode"></i>
                                </div>
                                <input name="sms_code" lay-verify="required" 
                                       placeholder="短信验证码" class="layui-input">
                            </div>
                            <button type="button" class="layui-btn get-sms-btn" id="get-sms-btn">获取验证码</button>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-email"></i>
                            </div>
                            <input name="email" lay-verify="required|email" 
                                   placeholder="电子邮箱" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-password"></i>
                            </div>
                            <input type="password" name="password" 
                                   lay-verify="required" placeholder="密码" 
                                   class="layui-input" id="reg-password" lay-affix="eye">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-password"></i>
                            </div>
                            <input type="password" name="confirmPassword" 
                                   lay-verify="required|confirmPassword" 
                                   placeholder="确认密码" class="layui-input" lay-affix="eye">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="captcha-wrapper">
                            <div class="layui-input-wrap">
                                <div class="layui-input-prefix">
                                    <i class="layui-icon layui-icon-vercode"></i>
                                </div>
                                <input type="text" placeholder="图形验证码" 
                                       class="layui-input" name="captcha" lay-verify="required">
                            </div>
                            <img src="../assets/admin/images/captcha.gif" 
                                 class="verification-img" style="height:38px;border-radius:4px">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="remember-passsword">
                            <div class="remember-cehcked">
                                <input type="checkbox" name="agree" lay-skin="primary" 
                                       title="我已阅读并同意用户协议" checked>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <button class="layui-btn layui-btn-fluid" 
                                style="background:var(--primary-color)"
                                lay-submit lay-filter="register">立即注册</button>
                    </div>

                    <div class="terms">
                        注册即表示同意用户协议与隐私条款
                    </div>
                </div>
                
                <!-- 忘记密码表单 -->
                <div class="layui-form forgot-form form-panel" data-panel="forgot">
                    <div class="form-title">
                        <div class="form-title-main">重置密码</div>
                        <div class="form-title-sub">通过手机号验证重置您的密码</div>
                    </div>
                
                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix"><i class="layui-icon layui-icon-cellphone"></i></div>
                            <input name="phone" lay-verify="required|phone" placeholder="注册时使用的手机号码" class="layui-input">
                        </div>
                    </div>
                
                    <div class="layui-form-item">
                        <div class="sms-code-wrapper">
                            <div class="layui-input-wrap">
                                <div class="layui-input-prefix"><i class="layui-icon layui-icon-vercode"></i></div>
                                <input name="sms_code" lay-verify="required" placeholder="短信验证码" class="layui-input">
                            </div>
                            <button type="button" class="layui-btn get-sms-btn" id="get-reset-sms-btn">获取验证码</button>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix"><i class="layui-icon layui-icon-password"></i></div>
                            <input type="password" name="password" lay-verify="required" placeholder="新密码" class="layui-input" id="reset-password" lay-affix="eye">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix"><i class="layui-icon layui-icon-password"></i></div>
                            <input type="password" name="confirmPassword" lay-verify="required|confirmResetPassword" placeholder="确认新密码" class="layui-input" lay-affix="eye">
                        </div>
                    </div>
                
                    <div class="layui-form-item">
                        <button class="layui-btn layui-btn-fluid" style="background:var(--primary-color)" lay-submit lay-filter="forgot_password">立即重置</button>
                    </div>

                    <div class="layui-form-item" style="text-align:center;margin-top:20px">
                        <a href="javascript:;" class="back-to-login" style="color:#666">返回登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
<!-- 绑定手机号弹窗 -->
<div id="bind-phone-modal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="bind-phone-form" id="bind-phone-form">
        <div class="form-title" style="margin-bottom: 20px;">
            <div class="form-title-main">绑定手机号</div>
            <div class="form-title-sub">为了您的账户安全，请绑定手机号</div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-wrap">
                <div class="layui-input-prefix"><i class="layui-icon layui-icon-cellphone"></i></div>
                <input name="phone" lay-verify="required|phone" placeholder="请输入您的手机号码" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="sms-code-wrapper">
                <div class="layui-input-wrap">
                    <div class="layui-input-prefix"><i class="layui-icon layui-icon-vercode"></i></div>
                    <input name="sms_code" lay-verify="required" placeholder="短信验证码" class="layui-input">
                </div>
                <button type="button" class="layui-btn get-sms-btn" id="get-bind-sms-btn">获取验证码</button>
            </div>
        </div>

        <div class="layui-form-item">
            <button class="layui-btn layui-btn-fluid" style="background:var(--primary-color)" lay-submit lay-filter="bind_phone">立即绑定</button>
        </div>
    </form>
</div>
<script src="../assets/component/layui/layui.js"></script>
<script src="../assets/component/pear/pear.js"></script>
<script>
// 设置脚本安全策略
window.appendChildOrg = Element.prototype.appendChild;
Element.prototype.appendChild = function() {
    if(arguments[0].tagName == 'SCRIPT'){
        arguments[0].setAttribute('referrerpolicy', 'no-referrer');
    }
    return window.appendChildOrg.apply(this, arguments);
};
</script>
<script src="//static.geetest.com/static/tools/gt.js" onerror="console.error('Failed to load gt.js')"></script>
<script src="//static.geetest.com/v4/gt4.js" onerror="console.error('Failed to load gt4.js')"></script>
<script>
// 全局错误处理
window.onerror = function(msg, url, line, col, error) {
    console.error('Global error:', msg, 'at', url + ':' + line);
    if(msg.indexOf('errors is not defined') !== -1) {
        console.error('Geetest script loading error detected');
        layer.msg('验证码组件加载失败，请刷新页面重试', {icon: 5});
    }
    return false;
};

var captcha_open = 0;
var handlerEmbed = function (captchaObj) {
	captchaObj.appendTo('#captcha');
	captchaObj.onReady(function () {
		$("#captcha_wait").hide();
	}).onSuccess(function () {
		var result = captchaObj.getValidate();
		if (!result) {
			return alert('请完成验证');
		}
		$.captchaResult = result;
		$.captchaObj = captchaObj;
	});
};
layui.use(['form', 'jquery', 'popup', 'layer', 'util'], function(){
    var form = layui.form;
    var $ = layui.jquery;
    var popup = layui.popup;
    var layer = layui.layer;
    var util = layui.util;
    const VALID_CODE = "ht2025"; // 设置验证码常量
    var csrf_token = '<?=$csrf_token?>';
    
    // 滑块验证相关变量
    var isCaptchaVerified = false;
    var isDragging = false;
    var startX = 0;
    var thumbLeft = 0;
    var thumb = null;
    var trackWidth = 0;
    var captchaObjInstance; // 保存验证码实例
    var currentSmsAction = ''; // 用于区分当前是哪个操作在请求短信
    var currentSmsBtn = null;
    var handlerEmbed = function (captchaObj) {
        captchaObjInstance = captchaObj; // 保存实例
    	var phone;

    	// 检查验证码对象是否正确初始化
    	if (!captchaObj) {
    		console.error('Captcha object is null');
    		layer.msg('验证码初始化失败', {icon: 5});
    		return;
    	}

    	captchaObj.onReady(function () {
    		$("#wait").hide();
    		console.log('Captcha ready');
    	}).onSuccess(function () {
    		var result = captchaObj.getValidate();
    		console.log('Captcha result:', result);
    		if (!result) {
    			return alert('请完成验证');
    		}
            var $form = currentSmsBtn.closest('.layui-form, form');
            phone = $form.find('input[name="phone"]').val();

    		var ii = layer.load(2, {shade:[0.1,'#fff']});
            var ajaxUrl = '';
            var ajaxData = {
                phone: phone,
                csrf_token: csrf_token
            };

            // 添加验证码结果到数据中
            Object.assign(ajaxData, result);

            if (currentSmsAction === 'register') {
                ajaxUrl = 'ajax.php?act=sendcode';
            } else if (currentSmsAction === 'reset_password') {
                ajaxUrl = 'ajax.php?act=send_reset_code';
            } else if (currentSmsAction === 'bind_phone') {
                ajaxUrl = 'ajax.php?act=send_bind_code';
            }

            console.log('Sending SMS with data:', ajaxData);

    		$.ajax({
    			type : "POST",
    			url : ajaxUrl,
    			data : ajaxData,
    			success : function(data) {
    				layer.close(ii);
    				console.log('SMS response:', data); // 添加调试信息
    				if(data.code == 0 || data.code == 1){
                        startCountdown(currentSmsBtn);
                        layer.msg('发送成功，请注意查收！');
    				}else{
    				    layer.alert(data.msg);
    					if(captchaObj && typeof captchaObj.reset === 'function') {
    						captchaObj.reset();
    					}
    				}
    			} ,
                error: function(xhr, status, error) {
                    layer.close(ii);
                    console.log('SMS error:', xhr.responseText); // 添加调试信息
                    layer.msg('网络错误', {icon: 5});
                    if(captchaObj && typeof captchaObj.reset === 'function') {
                    	captchaObj.reset();
                    }
                }
    		});
    	}).onError(function(error){
    		console.error('Captcha error:', error);
    		layer.msg('验证码加载失败，请刷新页面重试', {icon: 5});
    	});

        // 统一处理获取验证码按钮
        $('.get-sms-btn').off('click').on('click', function() {
            currentSmsBtn = $(this);
            var $form = currentSmsBtn.closest('.layui-form, form');
            var phone = $form.find('input[name="phone"]').val();

            if(phone==''){layer.alert('手机号码不能为空！');return false;}
		    if(!/^1[3-9]\d{9}$/.test(phone)){layer.alert('手机号码不正确！');return false;}

            if(currentSmsBtn.attr('id') === 'get-sms-btn') currentSmsAction = 'register';
            if(currentSmsBtn.attr('id') === 'get-reset-sms-btn') currentSmsAction = 'reset_password';
            if(currentSmsBtn.attr('id') === 'get-bind-sms-btn') currentSmsAction = 'bind_phone';

            console.log('Current SMS action:', currentSmsAction); // 调试信息
            console.log('CSRF token:', csrf_token); // 调试信息

            // 检查验证码实例是否存在
            if(!captchaObjInstance) {
            	layer.msg('验证码未初始化，请刷新页面重试', {icon: 5});
            	return false;
            }

            try {
	            if(typeof captchaObjInstance.showCaptcha === 'function'){
	    			captchaObjInstance.showCaptcha();
	    		}else if(typeof captchaObjInstance.verify === 'function'){
	    			captchaObjInstance.verify();
	    		}else{
	    			console.error('Captcha object methods not available');
	    			layer.msg('验证码功能异常，请刷新页面重试', {icon: 5});
	    		}
    		} catch(e) {
    			console.error('Captcha error:', e);
    			layer.msg('验证码调用失败，请刷新页面重试', {icon: 5});
    		}
        });
    };
    // 检查极验证码库是否加载
    function checkGeetestLibrary() {
        if(typeof initGeetest4 === 'undefined' && typeof initGeetest === 'undefined') {
            console.error('Geetest library not loaded');
            layer.msg('验证码库加载失败，请检查网络连接', {icon: 5});
            return false;
        }
        return true;
    }

    // 延迟初始化验证码，确保库文件加载完成
    setTimeout(function() {
        if(!checkGeetestLibrary()) {
            return;
        }

        $.ajax({
    		url: "ajax.php?act=captcha",
    		type: "POST",
    		cache: false,
    		data: {csrf_token: csrf_token},
    		success: function (data) {
    			console.log('Captcha config:', data); // 调试信息
    			try {
    				if(data.version == 1){
    					if(typeof initGeetest4 === 'undefined') {
    						throw new Error('initGeetest4 is not defined');
    					}
    					initGeetest4({
    						captchaId: data.gt,
    						product: 'bind',
    						protocol: 'https://',
    						riskType: 'slide',
    						hideSuccess: true,
    					}, handlerEmbed);
    				}else{
    					if(typeof initGeetest === 'undefined') {
    						throw new Error('initGeetest is not defined');
    					}
    					initGeetest({
    						width: '100%',
    						gt: data.gt,
    						challenge: data.challenge,
    						new_captcha: data.new_captcha,
    						product: "bind",
    						offline: !data.success
    					}, handlerEmbed);
    				}
    			} catch(e) {
    				console.error('Captcha init error:', e);
    				layer.msg('验证码初始化失败: ' + e.message, {icon: 5});
    			}
    		},
    		error: function(xhr, status, error) {
    			console.error('Captcha config error:', xhr.responseText);
    			layer.msg('验证码配置加载失败', {icon: 5});
    		}
    	});
    }, 1000); // 延迟1秒确保库文件加载完成
    // 发送短信验证码
    function sendSmsCode() {
        
        if (!phone) {
            popup.warning("请输入手机号码");
            return;
        }
        
        // 显示加载中
        var btn = $('#get-sms-btn');
        btn.addClass('layui-btn-disabled');
        
        // 模拟发送请求
        $.ajax({
            url: 'ajax.php?act=send_sms',
            type: 'POST',
            data: {
                phone: phone,
                csrf_token: csrf_token
            },
            success: function(res) {
                if (res.code == 1) {
                    popup.success("验证码已发送");
                    startCountdown();
                } else {
                    popup.failure(res.msg || "发送失败");
                    btn.removeClass('layui-btn-disabled');
                }
            },
            error: function() {
                popup.failure("网络错误，请重试");
                btn.removeClass('layui-btn-disabled');
            }
        });
    }
    
    // 开始倒计时
    function startCountdown(btn) {
        var countdown = 60;
        
        btn.addClass('layui-btn-disabled').html('<span class="countdown">' + countdown + '秒</span>');
        
        var timer = setInterval(function() {
            countdown--;
            if (countdown <= 0) {
                clearInterval(timer);
                btn.removeClass('layui-btn-disabled').text('获取验证码');
                isCaptchaVerified = false;
            } else {
                btn.find('.countdown').text(countdown + '秒');
            }
        }, 1000);
    }

    // 选项卡切换
    $('.tab-item').on('click', function(){
        var tab = $(this).data('tab');
        $('.tab-item').removeClass('active');
        $(this).addClass('active');
        $('.form-panel').removeClass('active');
        $('[data-panel="'+tab+'"]').addClass('active');
        $('.form-tabs').show();
        $('[data-panel="forgot"]').removeClass('active');
    });

    // 忘记密码链接
    $('.forgot-password-link').on('click', function() {
        $('.form-panel').removeClass('active');
        $('[data-panel="forgot"]').addClass('active');
        $('.form-tabs').hide();
    });

    // 返回登录
    $('.back-to-login').on('click', function() {
        $('.form-panel').removeClass('active');
        $('.tab-item[data-tab="login"]').click();
    });

    // 密码一致性验证
    form.verify({
        confirmPassword: function(value){
            var password = $('#reg-password').val();
            if(value !== password){
                return '两次密码输入不一致';
            }
        },
        confirmResetPassword: function(value){
            var password = $('#reset-password').val();
            if(value !== password){
                return '两次密码输入不一致';
            }
        },
        phone: function(value) {
            if (!/^1[3-9]\d{9}$/.test(value)) {
                return '请输入正确的手机号码';
            }
        },
        email: function(value) {
            if (!/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(value)) {
                return '请输入正确的邮箱格式';
            }
        }
    });

    // 注册提交
    form.on('submit(register)', function(data){
        // 弹出注册码验证
        layer.prompt({
            title: '请输入注册邀请码，联系VX:TM5657TM',
            formType: 0,
            skin: 'layui-layer-admin', // 使用管理员皮肤
            area: ['400px', 'auto']     // 调整弹窗尺寸
        }, function(value, index){
            layer.close(index);
            
            if(value !== VALID_CODE){
                popup.warning("邀请码错误，请联系管理员获取");
                return false;
            }

            // 原有验证逻辑
            var field = data.field;
            if(!field.agree){
                popup.warning("请阅读并同意用户协议");
                return false;
            }
            
            if(!field.sms_code){
                popup.warning("请输入短信验证码");
                return false;
            }

            $.ajax({
                url: 'ajax.php?act=reg',
                type: 'POST',
                data: {
                    username: field.username,
                    phone: field.phone,
                    email: field.email,
                    password: field.password,
                    sms_code: field.sms_code,
                    csrf_token: csrf_token
                },
                success: function(res){
                    if(res.code == 1){
                        popup.success("注册成功", function(){
                            $('.tab-item[data-tab="login"]').click();
                        });
                    }else{
                        popup.failure(res.msg);
                    }
                }
            });
        });
        return false;
    });
    
    // 忘记密码提交
    form.on('submit(forgot_password)', function(data){
        var field = data.field;
        $.ajax({
            url: 'ajax.php?act=forgot_password',
            type: 'POST',
            data: {
                phone: field.phone,
                password: field.password,
                sms_code: field.sms_code,
                csrf_token: csrf_token
            },
            success: function(res){
                if(res.code == 1){
                    popup.success("密码重置成功", function(){
                        $('.back-to-login').click();
                    });
                }else{
                    popup.failure(res.msg);
                }
            }
        });
        return false;
    });

    // 登录提交
    form.on('submit(login)', function(data){
        var field = data.field; // 获取表单字段值
        
        $.ajax({
            url: 'ajax.php?act=login',
            type: 'POST',
            data: {
                username: field.username,
                password: field.password,
                csrf_token: csrf_token
            },
            success: function(res){
                if(res.code == 1){
                    popup.success("登录成功", function(){
                        location.href = "./";
                    });
                } else if (res.code == 2) {
                    // 强制绑定手机号
                    layer.open({
                        type: 1,
                        title: '绑定手机号',
                        area: ['450px', 'auto'],
                        content: $('#bind-phone-modal'),
                        closeBtn: 0, // 隐藏关闭按钮
                        end: function() {
                           // 清理表单，如果需要的话
                           $('#bind-phone-form')[0].reset();
                           form.render(null, 'bind-phone-form');
                        },
                        cancel: function() {
                            popup.warning("为了账户安全，请完成手机号绑定");
                            return false; // 阻止关闭
                        }
                    });
                } else {
                    popup.failure(res.msg);
                }
            },
            error: function(xhr, status, error){
                console.error('请求失败:', status, error);
                popup.failure('网络请求失败，请稍后重试');
            }
        });
        return false;
    });

    // 绑定手机号提交
    form.on('submit(bind_phone)', function(data){
        var field = data.field;
        $.ajax({
            url: 'ajax.php?act=bind_phone',
            type: 'POST',
            data: {
                phone: field.phone,
                sms_code: field.sms_code,
                csrf_token: csrf_token
            },
            success: function(res){
                if(res.code == 1){
                    popup.success("绑定成功", function(){
                        location.href = "./";
                    });
                }else{
                    popup.failure(res.msg);
                }
            }
        });
        return false;
    });
});
</script>
</body>
</html>