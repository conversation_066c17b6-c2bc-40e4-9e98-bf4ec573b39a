<?php
include("../includes/common.php");
$id = intval($_GET['id']);
$row=$DB->getRow("SELECT * FROM ozon_production WHERE id=:id AND uid=:uid limit 1", [':id'=>$id,':uid'=>$uid]);
//$row=$DB->getRow("SELECT * FROM production WHERE id=:id AND uid=:uid limit 1", [':id'=>$id,':uid'=>$uid]);
$categoryData = file_get_contents('./config/中文类目.json');
$row['category_ids'] = json_decode($row['category_chain'],true)['ids'];
if($row['category_ids']){
    list($category1, $category2, $category3) = explode(",", $row['category_ids']);
    $client = new \lib\OzonApiClient(2763302, '0a1f6874-fe81-4b30-97ce-4a3339eb077e');
    $Attributes = $client->attribute(['description_category_id'=>$category2,'type_id'=>$category3]);
    $Attributes = json_encode($Attributes);
}
$client = new \lib\OzonApiClient();
$offer_id = $row['offer_id']?$row['offer_id']:$client->generate_offer_id();
$row['price'] = $row['price']?$row['price']:"";
$row['old_price'] = $row['old_price']?$row['old_price']:"";

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商品编辑 - 采集系统</title>
    <link rel="stylesheet" href="/assets/component/layui/css/layui.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet" />
    <script src="/assets/js/jquery-3.6.0.min.js"></script>
    <script src="/assets/js/Sortable.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script src="/assets/component/layui/layui.js"></script>
    <!-- 美图设计室图片编辑器SDK -->
    <script src="https://public.static.meitudata.com/xiuxiu-pc/image-editor-sdk/3.0.0/dist/index.min.js"></script>
    
    <!-- 生产环境优化脚本 -->
    <script>
    // 修复jQuery passive event listener警告
    (function() {
        var supportsPassive = false;
        try {
            var opts = Object.defineProperty({}, 'passive', {
                get: function() {
                    supportsPassive = true;
                }
            });
            window.addEventListener("test", null, opts);
        } catch (e) {}

        // 覆盖jQuery的事件绑定方法
        if (supportsPassive && window.jQuery) {
            var _on = jQuery.fn.on;
            jQuery.fn.on = function() {
                var args = Array.prototype.slice.call(arguments);
                if (typeof args[1] === 'string' && (args[1].indexOf('scroll') > -1 || args[1].indexOf('wheel') > -1 || args[1].indexOf('touch') > -1)) {
                    if (typeof args[2] === 'function') {
                        args[2] = jQuery.proxy(args[2], this);
                        args[2].passive = true;
                    }
                }
                return _on.apply(this, args);
            };
        }
    })();

    // 过滤第三方警告信息
    if (typeof console !== 'undefined') {
        var originalError = console.error;
        console.error = function() {
            var args = Array.prototype.slice.call(arguments);
            var message = args.join(' ');
            
            // 过滤掉Chrome第三方cookie政策警告
            if (message.indexOf('third-party cookies') > -1 || 
                message.indexOf('Chrome is moving towards') > -1 ||
                message.indexOf('passive event listener') > -1) {
                return;
            }
            
            return originalError.apply(console, arguments);
        };
    }

    // 优化setTimeout性能
    window.optimizedSetTimeout = function(callback, delay) {
        if (delay < 100) {
            // 对于小于100ms的延迟，使用requestAnimationFrame优化
            return requestAnimationFrame(callback);
        }
        return setTimeout(callback, delay);
    };

    // 防抖函数 - 防止频繁执行
    window.debounce = function(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    };

    // 节流函数 - 限制执行频率
    window.throttle = function(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() { inThrottle = false; }, limit);
            }
        };
    };

    // 优化Ajax请求，避免重复请求
    window.ajaxCache = {};
    window.optimizedAjax = function(options) {
        var cacheKey = options.url + JSON.stringify(options.data || {});
        
        // 如果是GET请求且有缓存，返回缓存结果
        if (options.type === 'GET' && ajaxCache[cacheKey]) {
            var cached = ajaxCache[cacheKey];
            if (Date.now() - cached.timestamp < 30000) { // 30秒缓存
                if (options.success) {
                    setTimeout(function() {
                        options.success(cached.data);
                    }, 0);
                }
                return;
            }
        }
        
        var originalSuccess = options.success;
        options.success = function(data) {
            // 缓存GET请求结果
            if (options.type === 'GET') {
                ajaxCache[cacheKey] = {
                    data: data,
                    timestamp: Date.now()
                };
            }
            if (originalSuccess) originalSuccess(data);
        };
        
        return $.ajax(options);
    };
    </script>
    <style>
    /* 蓝色主题风格样式 */
    body {
        background: #f0f8ff;
        font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .optimized-form {
        padding-bottom: 100px;
        max-width: 1200px;
        margin: 0 auto;
        background: #fff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }
    .layui-form-pane .layui-form-label {
        background: #fafafa;
        color: #333;
        font-weight: 500;
        border: 1px solid #e8e8e8;
    }
    
    .form-section {
        margin-bottom: 24px;
        border: 1px solid #e8e8e8;
        background: #fff;
    }
    
    .form-section legend {
        background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
        color: #fff;
        font-size: 15px;
        font-weight: 500;
        padding: 12px 20px;
        margin: 0;
        border: none;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .layui-field-box {
        padding: 20px;
    }
    /* 图片预览区域现代商务风格 */
    .image-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        padding: 16px;
        background: #f8f9fa;
        border: 1px dashed #d0d7de;
        border-radius: 4px;
        min-height: 120px;
    }
    
    .image-item {
        position: relative;
        border: 1px solid #d0d7de;
        border-radius: 4px;
        padding: 4px;
        background: #fff;
        transition: all 0.2s ease;
    }
    
    .image-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-color: #0066cc;
    }
    .image-remove {
        position: absolute;
        right: -10px;
        top: -10px;
        cursor: pointer;
        color: #ff5722;
    }
    /* 商品属性布局 - 一行4个 */
    .attribute-group {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin: 20px 0;
    }
    
    @media (max-width: 1200px) {
        .attribute-group {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    @media (max-width: 768px) {
        .attribute-group {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 480px) {
        .attribute-group {
            grid-template-columns: 1fr;
        }
    }
    
    .attribute-input-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .attribute-label {
        font-size: 12px;
        color: #1e40af;
        font-weight: 600;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        padding: 4px 8px;
        border-radius: 4px 4px 0 0;
        border-bottom: 1px solid #93c5fd;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 12px;
        line-height: 1.2;
    }
    
    .attribute-label > span:first-child {
        font-weight: 600;
        color: #1e3a8a;
    }
    
    .attribute-input-group {
        border: 1px solid #bfdbfe;
        border-radius: 4px;
        background: #fff;
        overflow: hidden;
    }
    
    .attribute-input-group .layui-input,
    .attribute-input-group select {
        border: none;
        border-radius: 0 0 4px 4px;
        margin: 0;
        padding: 6px 8px;
        background: #fff;
        font-size: 13px;
        height: 20px;
        line-height: 14px;
    }
    
    .attribute-input-group .layui-input:focus,
    .attribute-input-group select:focus {
        outline: none;
        box-shadow: inset 0 0 0 2px #2563eb;
    }
    
    .attribute-input-group .select2-container {
        border: none;
    }
    
    .attribute-input-group .select2-container .select2-selection {
        border: none;
        border-radius: 0 0 4px 4px;
        background: #fff;
        min-height: 20px;
        padding: 3px 6px;
    }
    
    .attribute-input-group .select2-container .select2-selection__rendered {
        line-height: 14px;
        padding-left: 2px;
    }
    .dynamic-attribute {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 15px;
    }
    /* 类目选择器蓝色主题风格 */
    .category-selector {
        display: flex;
        gap: 16px;
        align-items: center;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        padding: 16px;
        border: 1px solid #bfdbfe;
        margin-bottom: 16px;
        border-radius: 6px;
    }
    
    .category-selector .layui-input-inline {
        flex: 1;
        min-width: 180px;
    }
    
    .category-selector select {
        height: 36px;
        border: 1px solid #93c5fd;
        background: #fff;
        color: #1e40af;
    }
    
    .category-selector .layui-icon-right {
        color: #666;
        font-size: 14px;
    }
    .upload-progress {
        width: 100px;
        margin-top: 5px;
    }
       /* 底部操作栏商务风格 */
    .form-actions {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        padding: 16px 30px;
        box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
        z-index: 999;
        text-align: center;
        border-top: 1px solid #e8e8e8;
        margin-top: 0 !important;
    }
    
    .form-actions button {
        margin: 0 6px;
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.2s ease;
        height: 36px;
        padding: 0 16px;
        font-size: 14px;
    }
    
    .form-actions .layui-btn-normal {
        background: #2563eb;
        border-color: #2563eb;
    }
    
    .form-actions .layui-btn-normal:hover {
        background: #1d4ed8;
        border-color: #1d4ed8;
    }
    
    /* 通用按钮样式 */
    .layui-btn {
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .layui-btn-normal {
        background: #2563eb;
        border-color: #2563eb;
    }
    
    .layui-btn-normal:hover {
        background: #1d4ed8;
        border-color: #1d4ed8;
    }
    
    .layui-btn-warm {
        background: #f0ad4e;
        border-color: #f0ad4e;
    }
    
    /* 页面头部样式 */
    .page-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: #fff;
        padding: 24px 32px;
        border-bottom: 3px solid #2563eb;
    }
    
    .page-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .page-title i {
        font-size: 26px;
        opacity: 0.9;
    }
    
    .page-subtitle {
        font-size: 14px;
        margin: 0;
        opacity: 0.8;
        color: #dbeafe;
    }
    /* 提示功能样式 */
    .attribute-tip {
        color: #666;
        margin-left: 6px;
        cursor: help;
        position: relative;
        display: inline-block;
    }
    
    .attribute-tip .layui-icon {
        font-size: 14px;
        transition: color 0.2s ease;
    }
    
    .attribute-tip:hover .layui-icon {
        color: #2563eb;
    }
    
    .custom-tooltip {
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 240px;
        max-width: 320px;
        background: #fff;
        border: 1px solid #d0d7de;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        padding: 12px;
        font-size: 12px;
        line-height: 1.5;
        color: #586069;
        border-radius: 4px;
        z-index: 999;
        display: none;
        word-wrap: break-word;
    }
    
    .attribute-tip:hover .custom-tooltip {
        display: block;
    }
    /* Select2商务风格 */
    .select2-container--default .select2-selection--single,
    .select2-container--default .select2-selection--multiple {
        border: 1px solid #93c5fd;
        border-radius: 4px;
        min-height: 36px;
        background: #fff;
    }
    
    .select2-container--default .select2-selection--multiple {
        padding: 4px 8px;
    }
    
    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background: #2563eb;
        color: #fff;
        border: none;
        border-radius: 3px;
        margin: 2px;
        padding: 2px 8px;
        font-size: 12px;
    }
    
    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        color: rgba(255,255,255,0.8);
        margin-right: 6px;
    }
    
    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
        color: #fff;
    }
    
    .select2-container {
        width: 100% !important;
    }
    
    .select2-dropdown {
        border: 1px solid #93c5fd;
        border-radius: 4px;
        box-shadow: 0 4px 16px rgba(37,99,235,0.1);
    }
    
    .select2-results__option--highlighted {
        background: #2563eb !important;
        color: #fff !important;
    }
    .image-item.selected {
        border-color: #1890ff;
        box-shadow: 0 0 8px rgba(24,144,255,0.3);
    }

    .image-item .select-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(24,144,255,0.3);
        display: none;
    }

    .image-item.selected .select-overlay {
        display: block;
    }

    .image-item .checkmark {
        position: absolute;
        top: 5px;
        left: 5px;  /* 已修改为左上角 */
        color: #fff;
        background: #1890ff;
        border-radius: 3px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        display: none;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        z-index: 2; /* 新增层级确保在最前 */
    }
    
    /* 新增复选框样式 */
    .image-item .checkmark::before {
        content: "\e605"; /* 使用layui的勾号图标 */
        font-family: "layui-icon" !important;
        font-size: 14px;
        position: relative;
        top: -1px;
    }
    
    /* 调整选中蒙层位置 */
    .image-item .select-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(24,144,255,0.3);
        display: none;
        border-radius: 4px; /* 添加圆角 */
    }
    
    /* 调整图片项选中状态 */
    .image-item.selected {
        border-color: #1890ff;
        box-shadow: 0 0 8px rgba(24,144,255,0.3);
        transform: scale(1.02); /* 添加轻微放大效果 */
        transition: all 0.3s ease;
    }
    
    /* 删除按钮位置调整 */
    .image-remove {
        position: absolute;
        right: 5px;  /* 原-10px改为5px */
        top: 5px;    /* 原-10px改为5px */
        cursor: pointer;
        color: #ff5722;
        background: rgba(255,255,255,0.8);
        border-radius: 50%;
        padding: 2px;
    }
    
    /* 美图编辑按钮样式 */
    .image-edit-btn {
        position: absolute;
        right: 5px;
        bottom: 5px;
        cursor: pointer;
        color: #1890ff;
        background: rgba(255,255,255,0.9);
        border-radius: 4px;
        padding: 4px;
        transition: all 0.3s;
        z-index: 4;
    }
    
    .image-edit-btn:hover {
        background: #1890ff;
        color: #fff;
        transform: scale(1.1);
    }
    
    .select-overlay {
        z-index: 2;
    }
    
    .image-remove {
        z-index: 3;
    }
    .checkmark::after {
        content: "✓";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        font-weight: bold;
    }
    
    .image-item {
        position: relative;
        z-index: 1 !important; /* 强制层级 */
    }
    
    
    .image-item.selected .checkmark {
        display: block !important;
        /* 新增动画效果 */
        animation: checkmarkAppear 0.3s ease;
    }
    
    @keyframes checkmarkAppear {
        from { opacity: 0; transform: scale(0.5); }
        to { opacity: 1; transform: scale(1); }
    }
    .checkmark {
        /* 新增基础状态样式 */
        display: none !important;
        /* 调整尺寸和定位 */
        top: 8px !important;
        left: 8px !important;
        width: 18px;
        height: 18px;
        /* 优化视觉表现 */
        background: #1890ff url('data:image/svg+xml;utf8,<svg viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg"><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg>') no-repeat center;
        background-size: 12px;
    }
    /* 在图片容器右上角添加操作提示 */
    .image-preview::after {
        content: "按住Ctrl点击可多选\A图片放大（双击触发）";
        white-space: pre; /* 关键样式：允许换行 */
        position: absolute;
        top: -25px;
        right: 10px;
        color: #666;
        font-size: 12px;
        background: rgba(255,255,255,0.9);
        padding: 2px 8px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    </style>
</head>
<body>
<div class="optimized-form">
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="layui-icon layui-icon-edit"></i>
            商品信息编辑
        </h1>
        <p class="page-subtitle">完善商品详情，优化销售效果</p>
    </div>
    
    <form class="layui-form layui-form-pane">
        <input type="hidden" name="id" value="<?=$id?>">
        <input type="hidden" name="shop_id">
        <input type="hidden" name="warehouse_id">
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-face-surprised"></i> Ozon同款参数快速数据补齐填写</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-col-md12">
                        <div class="layui-row layui-col-space10">
                            <label class="layui-form-label">商品链接</label>
                            <!-- 输入框列 -->
                            <div class="layui-col-md4">
                                <div class="layui-input-block" style="margin-left: 0;">
                                    <input type="text" id="skuurl" name="skuurl" 
                                           placeholder="请输入Ozon商品链接/SKU" 
                                           class="layui-input">
                                </div>
                            </div>
                            
                            <!-- 按钮列 -->
                            <div class="layui-col-md1">
                                <button type="button" class="layui-btn layui-btn-fluid" id="get_sku">
                                    <i class="layui-icon layui-icon-link"></i>获取类目
                                </button>
                            </div>
                            <div class="layui-col-md2">
                                <button type="button" class="layui-btn layui-btn-fluid" id="get_sku_attributes">
                                    <i class="layui-icon layui-icon-link"></i>获取商品属性特征数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <!-- 基本信息 -->
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-form"></i> 基本信息</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <label class="layui-form-label">商品名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" lay-verify="required" 
                               placeholder="请输入商品名称" class="layui-input" value="<?=$row['title']?>">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md4">
                            <label class="layui-form-label">销售价格</label>
                            <div class="layui-input-block">
                                <input type="number" name="price" lay-verify="required|number" 
                                       class="layui-input" placeholder="0.00" value="<?=$row['price']?>">
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">商品原价</label>
                            <div class="layui-input-block">
                                <input type="number" name="old_price" lay-verify="number" 
                                       class="layui-input" placeholder="折扣前的价格（将在产品卡上划掉）" value="<?=$row['old_price']?>">
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">商品货号 *</label>
                            <div class="layui-input-block">
                                <input type="text" name="offer_id" 
                                       class="layui-input" value="<?=$offer_id?>">
                                
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md4">
                            <label class="layui-form-label" style="width:115px;">型号名称 *</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="text" name="attributes[9048]" 
                                           class="layui-input">
                                    <span class="attribute-tip">
                                        <i class="layui-icon layui-icon-tips"></i>
                                        <div class="custom-tooltip">请以相同的方式为您要合并至一张卡片的商品填写</div>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">库存数量</label>
                            <div class="layui-input-block">
                                <input type="number" name="stock" lay-verify="number" 
                                       class="layui-input" value="10">
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label" style="width:115px;">含包装重量 *</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="weight" 
                                           class="layui-input">
                                    <span class="layui-input-suffix">克</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md4">
                            <label class="layui-form-label">包装长度 *</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="depth" 
                                           class="layui-input">
                                    <span class="layui-input-suffix">毫米</span>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">包装宽度 *</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="width" 
                                           class="layui-input">
                                    <span class="layui-input-suffix">毫米</span>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">包装高度 *</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="height" 
                                           class="layui-input">
                                    <span class="layui-input-suffix">毫米</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- 类目属性 -->
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-app"></i> 类目属性</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <label class="layui-form-label">商品类目</label>
                    <div class="layui-input-block category-selector">
                        <div class="layui-input-inline">
                            <select name="category1" lay-filter="category">
                                <option value="">一级类目</option>
                                
                            </select>
                        </div>
                        <i class="layui-icon layui-icon-right"></i>
                        <div class="layui-input-inline">
                            <select name="category2" lay-filter="category">
                                <option value="">二级类目</option>
                            </select>
                        </div>
                        <i class="layui-icon layui-icon-right"></i>
                        <div class="layui-input-inline">
                            <select name="category3" lay-filter="category">
                                <option value="">三级类目</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">商品属性</label>
                    <div class="layui-input-block">
                        <div id="specBox" class="attribute-group"></div>
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- 多媒体信息 -->
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-picture"></i> 多媒体信息</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <!-- 在表单中添加隐藏字段 -->
                    <input type="hidden" name="images" value="<?= $row['images'] ?? '' ?>">
                    <label class="layui-form-label">产品图片</label>
                    <div class="layui-input-block">
                        <div class="layui-upload">
                            <button type="button" class="layui-btn" id="uploadImg">
                                <i class="layui-icon layui-icon-upload"></i>上传图片
                            </button>
                            <button type="button" class="layui-btn layui-btn-normal" id="uploadImgUrl" style="margin-left: 10px;">
                                <i class="layui-icon layui-icon-link"></i>添加图片链接
                            </button>
                            <div class="layui-form-mid layui-word-aux" style="margin-left: 10px;">
                                支持本地上传或输入图片链接地址
                            </div>
                            <div class="layui-upload-list image-preview" id="previewBox">
                                <?php if($row['images']): ?>
                                    <?php foreach(explode(';', $row['images']) as $index => $img): ?>
                                        <div class="image-item" data-url="<?= $img ?>">
                                            <i class="layui-icon image-remove">&#x1006;</i>
                                            <div class="select-overlay"></div>
                                            <div class="checkmark">✓</div>
                                            <div class="image-edit-btn" onclick="openMTImageEditor('<?= $img ?>', '<?= $index ?>')">
                                                <i class="layui-icon layui-icon-picture"></i>
                                            </div>
                                            <img class="images" src="<?= $img ?>" 
                                                 style="width:120px;height:120px;object-fit:contain;">
                                            <div class="layui-progress" lay-filter="progress-<?= $index ?>">
                                                <div class="layui-progress-bar" lay-percent="100%"></div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">产品视频</label>
                    <div class="layui-input-block">
                        <div class="layui-input-group">
                            <input type="text" name="video" placeholder="请输入视频URL地址" 
                                   class="layui-input">
                            <span class="layui-input-suffix">
                                <i class="layui-icon layui-icon-link"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        
        <!-- 多媒体信息 -->
        <fieldset class="layui-elem-field form-section">
            <legend><i class="layui-icon layui-icon-picture"></i> 多媒体信息</legend>
            <div class="layui-field-box">
                <!-- 其他表单项... -->
                
                <div class="layui-form-item">
                    <label class="layui-form-label">
                        内容丰富
                        
                    </label>
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="fullscreenBtn" 
                                style="margin-left:10px;">
                        <i class="layui-icon layui-icon-screen-full"></i> 全屏
                    </button>
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-warm" id="adjustHeightBtn" 
                                style="margin-left:10px;">
                        <i class="layui-icon layui-icon-up-down"></i> 调整高度
                    </button>
                    <div class="iframe-container" style="position:relative; min-height:600px;">
                        <iframe id="iframe" src="jsonff.php?id=<?=$id?>" frameborder="0" 
                                style="width:100%; height:600px; border:1px solid #eee; min-height:500px; max-height:800px; overflow-y:auto;"></iframe>
                    </div>
                </div>
            </div>
        </fieldset>

        <!-- 表单操作 -->
        <div class="layui-form-item form-actions">
            <div style="display:inline-block;vertical-align: middle;">
                <label class="layui-form-label" style="width:120px;">图片翻译服务</label>
                <div class="layui-input-inline">
                    <select name="ImgTrans">
                        <option value="">请选择服务</option>
                        <option value="1">阿里云版</option>
                        <!--<option value="2">Google版</option>
                        <option value="4">DeepL版</option>
                        <option value="5">ChatGPT版</option>-->
                        <option value="6">百度版</option>
                    </select>
                </div>
            </div>
            <button class="layui-btn layui-btn-lg layui-btn-normal" lay-submit lay-filter="submitTranslate">
                <i class="layui-icon layui-icon-picture"></i>图片翻译 
            </button>
            <button class="layui-btn layui-btn-lg layui-btn-normal" lay-submit lay-filter="submitPublish">
                <i class="layui-icon layui-icon-upload-drag"></i>保存并发布
            </button>
            <button class="layui-btn layui-btn-lg layui-btn-normal" lay-submit lay-filter="submitSave">
                <i class="layui-icon layui-icon-ok"></i>保存商品
            </button>
            <button type="reset" class="layui-btn layui-btn-lg layui-btn-primary">
                <i class="layui-icon layui-icon-refresh"></i>重置表单
            </button>
        </div>
           </div>
        <div class="floating-action-bar">
            <button class="layui-btn layui-btn-danger" id="multiStorePublish">
                <i class="layui-icon layui-icon-release"></i>
                保存并发布
            </button>
            <button type="button" class="layui-btn" id="saveBtn">
                <i class="layui-icon layui-icon-ok"></i>
                保存
            </button>
            <button type="button" class="layui-btn layui-btn-primary" id="closeBtn">
                <i class="layui-icon layui-icon-close"></i>
                关闭
            </button>
        </div>
    </form>
</div>

<script>

let imageManager;
// 优化后的JS代码（部分核心交互）
layui.use(['form', 'upload', 'layer'], function(){
    var form = layui.form,
        upload = layui.upload,
        layer = layui.layer;
        //$ = layui.$;
    
    var previewBox = document.getElementById('previewBox');
    var sortable = new Sortable(previewBox, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        handle: '.images', // 指定可拖拽区域为图片本身
        onUpdate: function(evt) {
            updateImageFormData(); // 同步更新表单数据
        }
    });
    // 类目数据（替换为你的API返回数据）
    var categoryData = {};
    var currentSelecting = {}; // 记录当前选择状态
    let selectyu=[];
    let attributevalues= new Map();
    
    // 初始加载
    // 类目数据
    var categoryData = <?=$categoryData?>;
    var currentCategory = {1: null, 2: null, 3: null}; // 当前选择的各类目ID

    // 查找类目节点
    function findCategoryNode(categoryId) {
        function find(nodes) {
            for(const key in nodes){
                const node = nodes[key];
                if(node.descriptionCategoryId == categoryId) return node;
                if(node.nodes && Object.keys(node.nodes).length > 0){
                    const found = find(node.nodes);
                    if(found) return found;
                }
            }
            return null;
        }
        return find(categoryData.result);
    }

    // 修改加载子类目方法
    function loadSubCategories(level, parentId) {
        const $nextSelect = $(`select[name="category${level}"]`);
        $nextSelect.empty().append(`<option value="">请选择</option>`);
        
        if(parentId) {
            const parentNode = findCategoryNode(parentId);
            if(parentNode && parentNode.nodes) {
                Object.values(parentNode.nodes).forEach(item => {
                    if(!item.disabled && level<3) {
                        $nextSelect.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
                    }else{
                        $nextSelect.append(new Option(item.descriptionTypeName, item.descriptionTypeId));
                    }
                });
            }
        }
        
        // 修改渲染方式（统一使用全量渲染）
        layui.form.render('select');
    }
    
    // 修改初始化方法
    function initFirstCategory() {
        var $select = $('select[name="category1"]');
        $select.empty().append('<option value="">一级类目</option>');
        
        Object.values(categoryData.result).forEach(item => {
            if(!item.disabled) {
                $select.append(new Option(item.descriptionCategoryName, item.descriptionCategoryId));
            }
        });
        
        // 统一初始化渲染
        layui.form.render('select');
    }
    
    // 初始化
    initFirstCategory();
    
    // 如果有编辑数据，初始化已选类目
    
    <?php if($row['category_ids']):?>
    var savedCategories = "<?=$row['category_ids']?>".split(',');
    const Attributes = <?=$Attributes?>;
    renderAttributes(Attributes);
    
    savedCategories.forEach((id, index) => {
        const level = index + 1;
        if(level > 3) return;
        
        currentCategory[level] = id;
        $(`select[name="category${level}"]`).val(id);
        
        if(level <= 3) {
            loadSubCategories(level + 1, id);
        }
    });
    form.render('select');
    <?php endif;?>
    <?php if($row['attributesdata']):?>
    let savedAttributesData = <?=$row['attributesdata']?>;
    
    //form.render('select');
    <?php endif;?>


    
    function renderAttributes(attributes) {
        const container = $('#specBox');
        container.empty();
        let loadPromises = []; // 存储所有属性加载Promise
    
        attributes.forEach(attr => {
            if(attr.id != 4559 && attr.id != 21531 && attr.id != 4497 && attr.id != 11254 && attr.id != 8229 && attr.id != 9048){
                if (attr.dictionary_id > 0) {
                    selectyu.push(attr.id);
                    
                    // 创建属性加载Promise
                    let loadPromise = new Promise(resolve => {
                        attributes_select(attr.id, function(options) {
                            const selectHtml = `
                                <div class="attribute-input-group">
                                    <div class="attribute-label">
                                        <span>${attr.name}</span>
                                        ${attr.description ? `
                                            <span class="attribute-tip">
                                                <i class="layui-icon layui-icon-tips"></i>
                                                <div class="custom-tooltip">${attr.description}</div>
                                            </span>` : ''}
                                    </div>
                                    <select name="attributes[${attr.id}]" lay-ignore ${attr.is_collection ?  'multiple' : ''}>
                                        <option value="">-- 请选择 --</option>
                                        ${options.map(opt => `<option value="${opt.id}">${opt.name}</option>`).join('')}
                                    </select>
                                </div>
                            `;
                            container.append(selectHtml);
    
                            const $select = $(`select[name="attributes[${attr.id}]"]`);
                            $select.select2({
                                multiple: attr.is_collection,
                                placeholder: `请选择${attr.name}`,
                                width: '100%'
                            });
                            resolve(attr.id); // 解析属性ID表示加载完成
                        });
                    });
                    
                    loadPromises.push(loadPromise);
                } else {
                   // 普通输入框
                    const inputHtml = `
                        <div class="attribute-input-group">
                            <div class="attribute-label">
                                <span>${attr.name}</span>
                                ${attr.description ? `
                                    <span class="attribute-tip">
                                        <i class="layui-icon layui-icon-tips"></i>
                                        <div class="custom-tooltip">${attr.description}</div>
                                    </span>` : ''}
                            </div>
                            <input type="text" name="attributes[${attr.id}]" 
                                 placeholder="请输入${attr.name}" class="layui-input">
                        </div>
                    `;
                    container.append(inputHtml);
                }
            }
        });
    
        // 所有属性加载完成后设置值
        Promise.all(loadPromises).then(loadedIds => {
            if(typeof savedAttributesData !== 'undefined'){
                savedAttributesData.forEach(item => {
                    const [attributeKey, attributeValue] = Object.entries(item)[0];
                    const attributeId = attributeKey.match(/\[(\d+)\]/)[1];
                    
                    if(selectyu.includes(Number(attributeId))){
                        const $select = $(`select[name="attributes[${attributeId}]"]`);
                        if($select.length > 0){
                            // 处理多选情况
                            if($select.prop('multiple')){
                                $select.val(attributeValue).trigger('change');
                            } else {
                                $select.val(attributeValue).trigger('change');
                            }
                        }
                    }else{
                        $('input[name="attributes['+attributeId+']"]').val(attributeValue);
                        if(attributeId==9456){
                            $('input[name="height"]').val(attributeValue);
                        }
                        if(attributeId==9454){
                            $('input[name="depth"]').val(attributeValue);
                        }
                        if(attributeId==9455){
                            $('input[name="width"]').val(attributeValue);
                        }
                        if(attributeId==4497){
                            $('input[name="weight"]').val(attributeValue);
                        }
                        if(attributeId==9048){
                            $('input[name="attributes[9048]"]').val(attributeValue);
                        }
                    }
                });
            }
        });
    }
    

    // 类目选择事件
    form.on('select(category)', function(data){
        const level = parseInt(data.elem.name.replace('category',''));
        const value = data.value;
        
        currentCategory[level] = value;
        
        // 清空后续选择
        for(let l = level + 1; l <= 3; l++) {
            currentCategory[l] = null;
            $(`select[name="category${l}"]`).val('').trigger('change');
        }
        
        if(level < 3 && value) {
            loadSubCategories(level + 1, value);
        }
        
        if (level === 3 && value) {
            // 获取类目属性
            var category2 = $(`select[name="category2"]`).val();
            layer.load(2);
            $.ajax({
                url: './ajax.php?act=get_category_attributes',
                type: 'POST',
                data: { category2, category3: value },
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if(res.code === 0) {
                        renderAttributes(res.data); 
                    } else {
                        layer.msg(res.msg || '获取属性失败');
                    }
                }
            });
        }
    });
    // 图片数据管理（简化版）
    imageManager = {
        selected: new Set(), // 使用Set存储选中图片URL
        
        init: function() {
            $(document).on('click', '#previewBox .image-item', function(e) {
                const $item = $(this);
                const url = $item.data('url');
                
                // 判断是否多选模式（Mac用metaKey，Windows用ctrlKey）
                const isMultiSelect = e.ctrlKey || e.metaKey;
                
                if(isMultiSelect) {
                    // 多选模式：切换当前项
                    $item.toggleClass('selected');
                    imageManager.toggleSelect(url);
                } else {
                    // 单选模式：清空其他选中项
                    const wasSelected = $item.hasClass('selected');
                    $('.image-item.selected').removeClass('selected');
                    imageManager.selected.clear();
                    
                    // 切换当前项状态
                    if(!wasSelected) {
                        $item.addClass('selected');
                        imageManager.selected.add(url);
                    }
                }
                
                // 调试输出当前选中数量
    
            });
        },
    
        toggleSelect: function(url) {
            if(this.selected.has(url)) {
                this.selected.delete(url);
            } else {
                this.selected.add(url);
            }
        },
    
        getSelected: function() {
            return Array.from(this.selected);
        }
    };

    imageManager.init();
    $('#previewBox').hide().show(0);
    setTimeout(() => {
        $('#previewBox').find('.image-item').hide().show();
        form.render(); // 重要：触发layui重新渲染
    }, 100);
    setTimeout(() => {
        $('#previewBox').find('.image-item').each(function() {
            this.style.cssText += ';transform: translateZ(0);'; // 触发GPU渲染层
        });
        form.render();
    }, 500);
    
    // 修改上传处理
    layui.upload.render({
        elem: '#uploadImg',
        url: './ajax.php?act=upload_image', // 指定上传接口
        accept: 'images', // 只接受图片
        acceptMime: 'image/*',
        size: 10240, // 10MB限制
        before: function(obj) {
            // 上传前显示loading
            layer.load(2);
        },
        done: function(res) {
            layer.closeAll('loading');
            console.log('上传返回数据：', res); // 调试用
            
            if(res.code === 0) {
                const $item = $(`
                    <div class="image-item" data-url="${res.data.src}">
                        <i class="layui-icon image-remove">&#x1006;</i>
                        <div class="select-overlay"></div>
                        <i class="layui-icon checkmark">&#xe605;</i>
                        <img class="images" src="${res.data.src}" 
                             style="width:120px;height:120px;object-fit:contain;">
                        <div class="layui-progress">
                            <div class="layui-progress-bar" lay-percent="100%"></div>
                        </div>
                    </div>
                `);
                $('#previewBox').append($item);
                updateImageFormData(); // 更新表单数据
                layer.msg('图片上传成功', {icon: 1});
            } else {
                layer.msg(res.msg || '上传失败', {icon: 2});
            }
        },
        error: function() {
            layer.closeAll('loading');
            layer.msg('上传出错，请重试', {icon: 2});
        }
    });
    
    // 添加图片链接上传功能
    $('#uploadImgUrl').click(function() {
        layer.prompt({
            title: '请输入图片链接（支持多个链接，每行一个）',
            formType: 2, // 文本域
            value: '',
            area: ['600px', '200px'],
            btn: ['确定', '取消']
        }, function(value, index, elem) {
            if (!value || !value.trim()) {
                layer.msg('请输入有效的图片链接', {icon: 2});
                return;
            }
            
            // 支持多行输入，每行一个链接
            const imageUrls = value.trim().split('\n').map(url => url.trim()).filter(url => url);
            
            if (imageUrls.length === 0) {
                layer.msg('请输入有效的图片链接', {icon: 2});
                return;
            }
            
            layer.close(index);
            
            // 批量处理图片链接
            processBatchImageUrls(imageUrls);
        });
    });
    
    // 批量处理图片链接
    function processBatchImageUrls(imageUrls) {
        let successCount = 0;
        let failCount = 0;
        let processedCount = 0;
        const totalCount = imageUrls.length;
        
        layer.load(2);
        layer.msg(`正在处理 ${totalCount} 个图片链接...`, {time: 0});
        
        imageUrls.forEach((imageUrl, index) => {
            // 验证URL格式
            if (!isValidImageUrl(imageUrl)) {
                processedCount++;
                failCount++;
                checkComplete();
                return;
            }
            
            // 验证图片是否可访问
            const img = new Image();
            img.onload = function() {
                // 创建图片预览项
                const imageIndex = $('#previewBox .image-item').length;
                const $item = $(`
                    <div class="image-item" data-url="${imageUrl}">
                        <i class="layui-icon image-remove">&#x1006;</i>
                        <div class="select-overlay"></div>
                        <i class="layui-icon checkmark">&#xe605;</i>
                        <div class="image-edit-btn" onclick="openMTImageEditor('${imageUrl}', '${imageIndex}')">
                            <i class="layui-icon layui-icon-picture"></i>
                        </div>
                        <img class="images" src="${imageUrl}" 
                             style="width:120px;height:120px;object-fit:contain;">
                        <div class="layui-progress">
                            <div class="layui-progress-bar" lay-percent="100%"></div>
                        </div>
                    </div>
                `);
                
                $('#previewBox').append($item);
                successCount++;
                processedCount++;
                checkComplete();
            };
            
            img.onerror = function() {
                failCount++;
                processedCount++;
                checkComplete();
            };
            
            // 设置图片源，触发加载
            img.src = imageUrl;
        });
        
        function checkComplete() {
            if (processedCount === totalCount) {
                layer.closeAll('loading');
                updateImageFormData();
                
                if (successCount > 0 && failCount === 0) {
                    layer.msg(`成功添加 ${successCount} 张图片`, {icon: 1});
                } else if (successCount > 0 && failCount > 0) {
                    layer.msg(`成功添加 ${successCount} 张图片，${failCount} 张图片添加失败`, {icon: 3});
                } else {
                    layer.msg('所有图片链接都无法访问，请检查链接是否正确', {icon: 2});
                }
            }
        }
    }
    
    // 验证图片URL格式的函数
    function isValidImageUrl(url) {
        try {
            const urlObj = new URL(url);
            // 检查是否是http或https协议
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                return false;
            }
            // 检查文件扩展名是否为图片格式
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
            const pathname = urlObj.pathname.toLowerCase();
            return imageExtensions.some(ext => pathname.endsWith(ext)) || 
                   pathname.includes('/') || // 可能是动态生成的图片URL
                   url.includes('image') || // URL中包含image关键字
                   url.includes('img'); // URL中包含img关键字
        } catch (e) {
            return false;
        }
    }
    
    // 新增图片替换函数
    function replaceTranslatedImages(translatedImages) {
        translatedImages.forEach(pair => {
            const [originalUrl, translatedUrl] = pair;
            
            // 查找匹配的图片元素
            $(`#previewBox .image-item[data-url="${originalUrl}"]`).each(function() {
                // 替换图片地址
                $(this)
                    .data('url', translatedUrl)
                    .find('img.images')
                        .attr('src', translatedUrl)
                        .end()
                    .find('.image-remove') // 防止删除按钮失效
                        .off('click')
                        .on('click', function() {
                            $(this).closest('.image-item').remove();
                            updateImageFormData();
                        });
            });
        });
        
        // 更新隐藏字段
        updateImageFormData();
    }
    
    // 修改删除处理
    $('#previewBox').on('click', '.image-remove', function() {
        const url = $(this).closest('.image-item').data('url');
        imageManager.selected.delete(url);
        $(this).closest('.image-item').remove();
        updateImageFormData();
    });
    
    // 在layui.use回调中添加事件监听
    $('#specBox').on('mouseenter', '.attribute-tip', function() {
        $(this).find('.custom-tooltip').show();
    }).on('mouseleave', '.attribute-tip', function() {
        $(this).find('.custom-tooltip').hide();
    });
    
    
    // 更新表单中的图片数据（示例）
    function updateImageFormData() {
        var images = [];
        $('#previewBox .images').each(function() {
            images.push($(this).attr('src'));
        });
        $('input[name="images"]').val(images.join(';'));
    }
    
    // 图片放大（改为双击触发）
    $('#previewBox').on('dblclick', '.images', function(e){
        var src = $(this).attr('src');
        layer.photos({
            photos: {
                data: [{src: src}]
            },
            anim: 5
        });

        
        // 阻止事件冒泡（避免与单击事件冲突）
        e.stopPropagation();
        return false;
    });
    
    $("#get_sku").click(function () {
        var skuurl = $("#skuurl").val();
        layer.load(2);
        $.ajax({
            url: './ajax.php?act=get_sku',
            type: 'POST',
            dataType: 'json',
            data: { skuurl },
            success: function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    // 修正数据源（使用四级分类字段）
                    const savedCategories = [
                        res.description_category_id_level_2,
                        res.description_category_id_level_3,
                        res.description_type_id  // 修正四级分类字段
                    ].filter(Boolean);  // 过滤空值
    
                    // 清空已有分类选择
                    [1,2,3].forEach(level => {
                        $(`select[name="category${level}"]`).val('').trigger('change');
                    });
    
                    // 层级处理逻辑优化
                    savedCategories.forEach((id, index) => {
                        const level = index + 1;
                        //if(level > 3) return;
                        
                        currentCategory[level] = id;
                        $(`select[name="category${level}"]`).val(id);
                        
                        if(level <= 3) {
                
                            loadSubCategories(level + 1, id);
                        }
                        if (level === 3 && id) {
                            // 获取类目属性
                            var category2 = $(`select[name="category2"]`).val();
                            layer.load(2);
                            $.ajax({
                                url: './ajax.php?act=get_category_attributes',
                                type: 'POST',
                                data: { category2, category3: id },
                                dataType: 'json',
                                success: function(res) {
                                    layer.closeAll('loading');
                                    if(res.code === 0) {
                                        renderAttributes(res.data); 
                                    } else {
                                        layer.msg(res.msg || '获取属性失败');
                                    }
                                }
                            });
                        }
                    });
    
                    layer.msg('操作成功', { icon: 1 });
                } else {
                    layer.msg(res.msg || '操作失败', { icon: 2 });
                }
            },
            error: function () {
                layer.closeAll('loading');
                layer.msg('请求失败，请稍后重试', { icon: 2 });
            }
        });
    });

    $("#get_sku_attributes").click(function () {
        var skuurl = $("#skuurl").val();
        const numberSet = new Set(selectyu);
        
        layer.load(2);
        $.ajax({
            url: './ajax.php?act=get_sku_attributes',
            type: 'POST',
            dataType: 'json',
            data: {skuurl},
            success: function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    var attributes = res.attributes;
                    attributes.forEach(attr => {
                        if(numberSet.has(Number(attr.key))){

                            const isMulti = attr.is_collection;
                            const targetItem = attributevalues.get(Number(attr.key)).find(item => item.name === (attr.value?attr.value:attr?.collection[0]));
                            const targetId = targetItem?.id;
                            if(targetId){
    
                                //$(`select[name="attributes[${attr.key}]"]`).val(targetId);
                                const $select = $(`select[name="attributes[${attr.key}]"]`);
                                //$select.select2('destroy');
                                $select.next('.layui-form-select').remove();
                                
                                $select.val(targetId);
                                
                                // 步骤3：重新初始化 Select2
                                $select.select2();
                                //form.render('select');
                            }
                            
                        }else if(attr.value && attr.value != true){
                            $('input[name="attributes['+attr.key+']"]').val(attr.value);
                            /*
                            if(!attr.value && attr.collection){
                                $('input[name="attributes['+attr.key+']"]').val(attr.collection[0]);
                            }
                            */
                            if(attr.key==9456){
                                $('input[name="height"]').val(attr.value);
                            }
                            if(attr.key==9454){
                                $('input[name="depth"]').val(attr.value);
                            }
                            if(attr.key==9455){
                                $('input[name="width"]').val(attr.value);
                            }
                            if(attr.key==4497){
                                $('input[name="weight"]').val(attr.value);
                            }
                            if(attr.key==9048){
                                $('input[name="attributes[9048]"]').val(attr.value);
                            }
                        }
                        
                    });
                    layer.msg('操作成功',{icon: 1});
                } else {
                    layer.msg(res.msg || '操作失败',{icon: 2});
                }
            },
            error: function () {
                layer.closeAll('loading');
                layer.msg('请求失败，请稍后重试',{icon: 2});
            }
        });
    });
    
      
    // 全屏查看iframe
    $('#fullscreenBtn').click(function(){
        layer.open({
            type: 2,
            title: '详情内容（全屏模式）',
            shadeClose: true,
            shade: 0.8,
            maxmin: true,
            area: ['90%', '90%'],
            content: 'jsonff.php?id=<?=$id?>',
            success: function(layero, index){
                // 自适应高度
                
            }
        });
    });
    
    // 保留原有iframe点击功能（可选），用户反馈点击无法编辑，故注释掉
    // $('#iframe').click(function(){
    //     $('#fullscreenBtn').click();
    // });
    
    // 动态调整iframe高度
    function adjustIframeHeight() {
        const iframe = document.getElementById('iframe');
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const body = iframeDoc.body;
            const html = iframeDoc.documentElement;
            
            // 获取内容高度
            const height = Math.max(
                body.scrollHeight,
                body.offsetHeight,
                html.clientHeight,
                html.scrollHeight,
                html.offsetHeight
            );
            
            // 设置最小高度500px，最大高度1000px
            const finalHeight = Math.max(500, Math.min(1000, height + 50));
            iframe.style.height = finalHeight + 'px';
            
        } catch (e) {
            // 跨域限制，无法调整iframe高度
        }
    }
    
    // iframe加载完成后调整高度（优化性能）
    $('#iframe').on('load', function() {
        optimizedSetTimeout(adjustIframeHeight, 1000);
        // 使用更高效的定期检查，避免频繁操作
        var heightCheckInterval = setInterval(function() {
            if ($('#iframe').length === 0) {
                clearInterval(heightCheckInterval);
                return;
            }
            adjustIframeHeight();
        }, 5000); // 改为5秒检查一次，减少性能消耗
    });
    
    // 手动调整高度按钮
    $('#adjustHeightBtn').click(function(){
        const currentHeight = parseInt($('#iframe').css('height'));
        const heights = [400, 600, 800, 1000, 1200];
        const currentIndex = heights.findIndex(h => h >= currentHeight);
        const nextIndex = (currentIndex + 1) % heights.length;
        const newHeight = heights[nextIndex];
        
        $('#iframe').animate({height: newHeight + 'px'}, 300);
        $('.iframe-container').animate({minHeight: newHeight + 'px'}, 300);
        
        layer.msg(`高度调整为: ${newHeight}px`, {icon: 1, time: 1000});
    });
    
    // 双击翻译功能（使用防抖优化）
    $('input[type="text"], textarea').on('dblclick', debounce(function(e) {
        var $target = $(e.target);
        if ($target.is('input, textarea')) {
            var originalText = $target.val();
            if (!originalText) return;
            
            layer.confirm('确定要翻译此内容到俄语吗？', {
                title: '翻译确认',
                icon: 3,
                btn: ['确定', '取消']
            }, function(index) {
                layer.close(index);
                layer.msg('翻译中...', {icon: 16, time: 0});
                
                optimizedAjax({
                    url: './ajax.php?act=translate',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        text: originalText,
                        source: 'zh',
                        target: 'ru'
                    },
                    success: function(res) {
                        layer.closeAll();
                        if (res.code === 0) {
                            $target.val(res.data.translated_text);
                            layer.msg('翻译成功', {icon: 1});
                        } else {
                            layer.msg(res.msg || '翻译失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.closeAll();
                        layer.msg('翻译服务不可用', {icon: 2});
                    }
                });
            });
        }
    }, 300)); // 300ms防抖
    // 翻译提交处理
    form.on('submit(submitTranslate)', function(data){
        const selected = imageManager.getSelected();
        
        if(selected.length === 0) {
            layer.msg('请先点击选择要翻译的图片', {icon: 2});
            return false; // 阻止表单提交
        }
    
        // 获取选择的翻译服务
        const service = $('select[name="ImgTrans"]').val();
        if(!service) {
            layer.msg('请选择翻译服务', {icon: 2});
            return false;
        }
    

        layer.load(2);
        $.ajax({
            url: './ajax.php?act=submit_ImgTranslate',
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify({
                images: selected,
                service: service
            }),
            success: function(res) {
                layer.closeAll();
                if(res.code === 0) {
                    replaceTranslatedImages(res.images);
                    layer.msg(`成功替换 ${res.images.length} 张图片`, {icon:1});
                } else {
                    layer.msg(res.msg || '保存失败', {icon: 2});
                }
            },
            error: function(xhr) {
                layer.closeAll();
                layer.msg('请求失败: ' + xhr.statusText, {icon: 2});
            }
        });
        // 阻止表单默认提交
        return false; 
    });
    
    // 新增多店铺仓库选择逻辑
    let groupMap = {};
    let shopGroupMap = {};
    let allStores = [];
    
    function showMultiShopSelector(callback) {
        layer.open({
            type: 1,
            title: '选择发布店铺和仓库',
            area: ['90%', '80%'],
            maxmin: true,
            content: `
                <div class="multi-shop-selector">
                    <style>
                        .multi-shop-selector {
                            padding: 20px;
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                        }
                        
                        .selector-header {
                            margin-bottom: 15px;
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            flex-wrap: wrap;
                        }
                        
                        .selector-body {
                            flex: 1;
                            overflow-y: auto;
                            border: 1px solid #e6e6e6;
                            border-radius: 4px;
                            padding: 15px;
                            background: #f8f8f8;
                        }
                        
                        .stores-grid {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 12px;
                        }
                        
                        .shop-item {
                            position: relative;
                            border: 1px solid #e6e6e6;
                            border-radius: 6px;
                            padding: 12px;
                            background: #fff;
                            cursor: pointer;
                            transition: all 0.3s;
                            min-width: 220px;
                            max-width: 280px;
                            flex: 1 0 calc(25% - 12px);
                        }
                        
                        .shop-item:hover {
                            border-color: #1E9FFF;
                            box-shadow: 0 2px 8px rgba(30,159,255,0.2);
                        }
                        
                        .shop-item.selected {
                            border-color: #1E9FFF;
                            background: #f0f9ff;
                            box-shadow: 0 2px 8px rgba(30,159,255,0.3);
                        }
                        
                        .shop-header {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-bottom: 8px;
                        }
                        
                        .shop-name {
                            font-weight: 600;
                            color: #333;
                            font-size: 14px;
                        }
                        
                        .group-tag {
                            background: #e6f7ff;
                            color: #1890ff;
                            padding: 2px 6px;
                            border-radius: 10px;
                            font-size: 11px;
                        }
                        
                        .shop-checkbox {
                            margin-right: 8px;
                        }
                        
                        .warehouse-section {
                            margin-top: 8px;
                            padding-top: 8px;
                            border-top: 1px solid #f0f0f0;
                        }
                        
                        .warehouse-label {
                            font-size: 12px;
                            color: #666;
                            margin-bottom: 6px;
                        }
                        
                        .warehouse-select {
                            width: 100%;
                        }
                        
                        .warehouse-select select {
                            width: 100%;
                            height: 32px;
                            padding: 4px 8px;
                            border: 1px solid #d9d9d9;
                            border-radius: 4px;
                            font-size: 13px;
                        }
                        
                        .warehouse-select select:focus {
                            border-color: #1E9FFF;
                            outline: none;
                        }
                        
                        .selector-footer {
                            margin-top: 15px;
                            padding-top: 15px;
                            border-top: 1px solid #e6e6e6;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }
                        
                        .stats-info {
                            color: #666;
                            font-size: 14px;
                        }
                        
                        .stats-highlight {
                            color: #1E9FFF;
                            font-weight: 600;
                        }
                        
                        @media (max-width: 1200px) {
                            .shop-item {
                                flex: 1 0 calc(33.333% - 12px);
                            }
                        }
                        
                        @media (max-width: 768px) {
                            .shop-item {
                                flex: 1 0 calc(50% - 12px);
                            }
                        }
                        
                        @media (max-width: 480px) {
                            .shop-item {
                                flex: 1 0 100%;
                            }
                        }
                    </style>
                    
                    <div class="selector-header">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="layui-icon layui-icon-shop" style="font-size: 18px; color: #1E9FFF;"></i>
                            <span style="font-weight: 600; font-size: 16px;">店铺仓库选择</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <label style="font-size: 14px;">分组筛选：</label>
                            <select id="groupFilterSelect" style="width: 150px; height: 32px;">
                                <option value="all">所有分组</option>
                                <option value="ungrouped">未分组</option>
                            </select>
                        </div>
                        <button type="button" class="layui-btn layui-btn-sm" id="selectAllBtn">全选</button>
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="clearAllBtn">清空</button>
                    </div>
                    
                    <div class="selector-body">
                        <div class="stores-grid" id="storesGrid">
                            <div style="width: 100%; text-align: center; padding: 40px;">
                                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 24px;"></i>
                                <div style="margin-top: 10px;">正在加载店铺数据...</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="selector-footer">
                        <div class="stats-info">
                            已选择 <span class="stats-highlight" id="selectedCount">0</span> 个店铺仓库
                        </div>
                    </div>
                </div>
            `,
            success: function(layero, index) {
                // 初始化多店铺选择器
                initMultiShopSelector(layero, index);
            },
            btn: ['确定发布', '取消'],
            yes: function(index, layero) {
                const selectedStores = getSelectedStores();
                if (selectedStores.length === 0) {
                    layer.msg('请至少选择一个店铺和仓库', {icon: 2});
                    return false;
                }
                
                // 验证所有选中的店铺都已选择仓库
                const invalidStores = selectedStores.filter(store => !store.warehouseId);
                if (invalidStores.length > 0) {
                    layer.msg(`请为所有选中的店铺选择仓库`, {icon: 2});
                    return false;
                }
                
                callback(selectedStores);
                layer.close(index);
            }
        });
    }
    
    function initMultiShopSelector(layero, layerIndex) {
        // 加载分组数据
        loadShopGroups().then(() => {
            // 加载店铺数据
            loadAllStores();
        }).catch(err => {
            console.error('加载分组数据失败:', err);
            loadAllStores();
        });
        
        // 绑定分组筛选事件
        layero.find('#groupFilterSelect').on('change', function() {
            filterStoresByGroup($(this).val());
        });
        
        // 绑定全选/清空事件
        layero.find('#selectAllBtn').on('click', function() {
            selectAllVisibleStores();
        });
        
        layero.find('#clearAllBtn').on('click', function() {
            clearAllSelections();
        });
    }
    
    function loadShopGroups() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: 'ajax.php?act=manage_shop_groups&op=get',
                dataType: 'json',
                success: function(res) {
                    if (res && res.code === 1 && res.data && res.data.groups) {
                        groupMap = {};
                        shopGroupMap = {};
                        
                        res.data.groups.forEach(group => {
                            if (group.id && group.name) {
                                groupMap[group.id] = group.name;
                                if (group.shopIds && Array.isArray(group.shopIds)) {
                                    group.shopIds.forEach(shopId => {
                                        shopGroupMap[shopId] = group.id;
                                    });
                                }
                            }
                        });
                        
                        // 渲染分组筛选器
                        renderGroupFilter(res.data.groups);
                        resolve(res.data.groups);
                    } else {
                        reject(new Error('分组数据加载失败'));
                    }
                },
                error: function(xhr, status, error) {
                    reject(new Error('分组数据请求失败: ' + error));
                }
            });
        });
    }
    
    function renderGroupFilter(groups) {
        const $filter = $('#groupFilterSelect');
        $filter.find('option:gt(1)').remove();
        
        if (groups && groups.length > 0) {
            groups.forEach(group => {
                if (group.id && group.name) {
                    $filter.append(`<option value="${group.id}">${group.name}</option>`);
                }
            });
        }
    }
    
    function loadAllStores() {
        $.ajax({
            url: '/api/get',
            dataType: 'json',
            success: function(res) {
                if (res && res.stores && res.stores.length > 0) {
                    allStores = res.stores.map(store => ({
                        ...store,
                        warehouses: store.warehouses || []
                    }));
                    renderStoresGrid(allStores);
                } else {
                    $('#storesGrid').html('<div style="text-align: center; padding: 40px; color: #999;">没有可用的店铺</div>');
                }
            },
            error: function() {
                $('#storesGrid').html('<div style="text-align: center; padding: 40px; color: #ff5722;">店铺数据加载失败</div>');
            }
        });
    }
    
    function renderStoresGrid(stores) {
        const container = $('#storesGrid');
        container.empty();
        
        stores.forEach(store => {
            const groupId = shopGroupMap[store.id] || 'ungrouped';
            const groupName = groupMap[groupId] || '未分组';
            
            // 生成仓库选项
            let warehouseOptions = '<option value="">请选择仓库</option>';
            if (store.warehouses && store.warehouses.length > 0) {
                store.warehouses.forEach(wh => {
                    if (wh.status !== 0) {
                        warehouseOptions += `<option value="${wh.id}">${wh.name}</option>`;
                    }
                });
            }
            
            const shopItem = `
                <div class="shop-item" data-store-id="${store.id}" data-group-id="${groupId}">
                    <div class="shop-header">
                        <div style="display: flex; align-items: center;">
                            <input type="checkbox" class="shop-checkbox" value="${store.id}">
                            <span class="shop-name">${store.storename}</span>
                        </div>
                        <span class="group-tag">${groupName}</span>
                    </div>
                    <div class="warehouse-section">
                        <div class="warehouse-label">选择仓库：</div>
                        <div class="warehouse-select">
                            <select class="warehouse-dropdown" data-store-id="${store.id}" disabled>
                                ${warehouseOptions}
                            </select>
                        </div>
                    </div>
                </div>
            `;
            
            container.append(shopItem);
        });
        
        // 绑定事件
        bindStoreEvents();
    }
    
    function bindStoreEvents() {
        // 店铺复选框变化事件
        $(document).off('change', '.shop-checkbox').on('change', '.shop-checkbox', function() {
            const isChecked = $(this).is(':checked');
            const storeId = $(this).val();
            const shopItem = $(this).closest('.shop-item');
            const warehouseSelect = shopItem.find('.warehouse-dropdown');
            
            if (isChecked) {
                shopItem.addClass('selected');
                warehouseSelect.prop('disabled', false);
            } else {
                shopItem.removeClass('selected');
                warehouseSelect.prop('disabled', true).val('');
            }
            
            updateSelectedCount();
        });
        
        // 仓库选择变化事件
        $(document).off('change', '.warehouse-dropdown').on('change', '.warehouse-dropdown', function() {
            updateSelectedCount();
        });
        
        // 店铺项点击事件（点击非复选框区域也能选中）
        $(document).off('click', '.shop-item').on('click', '.shop-item', function(e) {
            if (!$(e.target).is('input, select, option')) {
                const checkbox = $(this).find('.shop-checkbox');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });
    }
    
    function filterStoresByGroup(groupId) {
        $('.shop-item').show();
        
        if (groupId === 'all') {
            return;
        }
        
        if (groupId === 'ungrouped') {
            $('.shop-item').each(function() {
                const itemGroupId = $(this).attr('data-group-id');
                if (itemGroupId && itemGroupId !== 'ungrouped') {
                    $(this).hide();
                }
            });
        } else {
            $('.shop-item').each(function() {
                const itemGroupId = $(this).attr('data-group-id');
                if (itemGroupId !== groupId) {
                    $(this).hide();
                }
            });
        }
    }
    
    function selectAllVisibleStores() {
        $('.shop-item:visible .shop-checkbox').prop('checked', true).trigger('change');
    }
    
    function clearAllSelections() {
        $('.shop-checkbox').prop('checked', false).trigger('change');
    }
    
    function updateSelectedCount() {
        const selectedCount = $('.shop-checkbox:checked').length;
        $('#selectedCount').text(selectedCount);
    }
    
    function getSelectedStores() {
        const selectedStores = [];
        $('.shop-checkbox:checked').each(function() {
            const storeId = $(this).val();
            const shopItem = $(this).closest('.shop-item');
            const storeName = shopItem.find('.shop-name').text();
            const warehouseSelect = shopItem.find('.warehouse-dropdown');
            const warehouseId = warehouseSelect.val();
            const warehouseName = warehouseSelect.find('option:selected').text();
            
            if (warehouseId) {
                selectedStores.push({
                    storeId: storeId,
                    storeName: storeName,
                    warehouseId: warehouseId,
                    warehouseName: warehouseName
                });
            }
        });
        return selectedStores;
    }
    
    // 修改表单提交处理 - 支持多店铺发布
    form.on('submit(submitPublish)', function(data){
        showMultiShopSelector((selectedStores) => {
            // 准备基础商品数据
            const categoryData = {
                ids: [],
                texts: []
            };
        
            [1,2,3].forEach(level => {
                const $select = $(`select[name="category${level}"]`);
                const selectedOption = $select.find('option:selected');
                
                if(selectedOption.val()) {
                    categoryData.ids.push(selectedOption.val());
                    categoryData.texts.push(selectedOption.text());
                }
            });
        
            // 验证类目选择
            if(categoryData.ids.length < 3) {
                layer.msg('请完整选择3级类目');
                return false;
            }
        
            // 添加类目数据到提交数据
            data.field.category_chain = {
                ids: categoryData.ids.join(','),
                texts: categoryData.texts.join(' > ')
            };
        
            data.field.category_full = JSON.stringify({
                level1: {id: categoryData.ids[0], name: categoryData.texts[0]},
                level2: {id: categoryData.ids[1], name: categoryData.texts[1]},
                level3: {id: categoryData.ids[2], name: categoryData.texts[2]}
            });
        
            // 添加选中的店铺仓库信息
            data.field.selectedStores = selectedStores;
            data.field.is_multi_publish = true;
        
            // 显示确认信息
            const storeList = selectedStores.map(store => 
                `${store.storeName} (${store.warehouseName})`
            ).join('<br>');
            
            layer.confirm(`即将发布商品到以下 ${selectedStores.length} 个店铺：<br><br>${storeList}<br><br>确定继续？`, {
                icon: 3,
                title: '多店铺发布确认',
                area: ['400px', 'auto']
            }, function(index) {
                layer.close(index);
                submitMultiStoreData(data.field, selectedStores);
            });
        });
        return false;
    });
    
    form.on('submit(submitSave)', function(data){
        const categoryData = {
            ids: [],
            texts: []
        };
    
        [1,2,3].forEach(level => {
            const $select = $(`select[name="category${level}"]`);
            const selectedOption = $select.find('option:selected');
            
            if(selectedOption.val()) {
                categoryData.ids.push(selectedOption.val());
                categoryData.texts.push(selectedOption.text());
            }
        });
    
        // 添加到提交数据（推荐使用更结构化的数据格式）
        data.field.category_chain = {
            ids: categoryData.ids.join(','),
            texts: categoryData.texts.join(' > ')
        };
    
        // 提交前的验证示例
        if(categoryData.ids.length < 3) {
            layer.msg('请完整选择3级类目');
            return false;
        }
    
        // 建议使用JSON格式保存更结构化的数据
        data.field.category_full = JSON.stringify({
            level1: {id: categoryData.ids[0], name: categoryData.texts[0]},
            level2: {id: categoryData.ids[1], name: categoryData.texts[1]},
            level3: {id: categoryData.ids[2], name: categoryData.texts[2]}
        });
        submitData(data.field, false);
        return false;
    });
    
    function submitData(formData, isPublish){
        layer.load(2);
        // 处理类目数据...
        formData.is_publish = isPublish ? 1 : 0;
        
        // 先保存基本商品信息
        $.ajax({
            url: './ajax.php?act=save_production',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(res) {
                if(res.code === 0) {
                    // 基本信息保存成功后，同时保存iframe中的详情内容
                    try {
                        const iframe = document.getElementById('iframe');
                        if (iframe && iframe.contentWindow && iframe.contentWindow.saveAllChanges) {
                            // 调用iframe中的保存函数
                            iframe.contentWindow.saveAllChanges().then(() => {
                                layer.closeAll();
                                layer.msg(isPublish ? '商品和详情内容保存成功' : '商品和详情内容保存成功', {icon: 1});
                            }).catch((error) => {
                                layer.closeAll();
                                layer.msg('商品信息保存成功，但详情内容保存失败: ' + error.message, {icon: 3});
                            });
                        } else {
                            // iframe未加载完成或不支持跨域调用，仅保存基本信息
                            layer.closeAll();
                            layer.msg((isPublish ? '发布成功' : '保存成功') + '（详情内容请手动保存）', {icon: 1});
                        }
                    } catch (error) {
                        // 跨域或其他错误，仅保存基本信息
                        layer.closeAll();
                        layer.msg((isPublish ? '发布成功' : '保存成功') + '（详情内容请手动保存）', {icon: 1});
                    }
                } else {
                    layer.closeAll();
                    layer.msg(res.msg || '操作失败', {icon: 2});
                }
            },
            error: function(xhr) {
                layer.closeAll();
                layer.msg('保存失败: ' + xhr.statusText, {icon: 2});
            }
        });
    }
    
    // 新增多店铺发布函数
    function submitMultiStoreData(formData, selectedStores) {
        const loadingIndex = layer.load(1, {
            shade: [0.3, '#fff'],
            content: `正在发布到 ${selectedStores.length} 个店铺...`
        });
        
        // 先保存商品基础信息
        formData.is_publish = 1;
        formData.is_multi_publish = true;
        
        // 保存基础商品信息
        $.ajax({
            url: './ajax.php?act=save_production',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(saveRes) {
                if (saveRes.code === 0) {
                    // 商品信息保存成功后，开始多店铺发布
                    const productId = formData.id;
                    
                    // 准备多店铺发布数据
                    const publishData = {
                        productId: productId,
                        product: formData,
                        stores: selectedStores.map(store => ({
                            storeId: store.storeId,
                            warehouse: store.warehouseId,
                            storeName: store.storeName,
                            warehouseName: store.warehouseName
                        }))
                    };
                    
                    // 调用多店铺发布接口（类似plsc.php的逻辑）
                    $.ajax({
                        url: '/api/multi_publish',  // 新的多店铺发布接口
                        type: 'POST',
                        dataType: 'json',
                        data: JSON.stringify(publishData),
                        timeout: 60000,
                        success: function(publishRes) {
                            layer.close(loadingIndex);
                            
                            if (publishRes.code === 0) {
                                // 成功发布
                                const successCount = publishRes.successCount || selectedStores.length;
                                const failCount = publishRes.failCount || 0;
                                
                                let message = `商品发布完成！成功发布到 ${successCount} 个店铺`;
                                if (failCount > 0) {
                                    message += `，${failCount} 个店铺发布失败`;
                                }
                                
                                layer.msg(message, {
                                    icon: successCount > 0 ? 1 : 2,
                                    time: 5000,
                                    area: ['400px', 'auto']
                                });
                                
                                // 如果有详细结果，显示
                                if (publishRes.details && publishRes.details.length > 0) {
                                    setTimeout(() => {
                                        showPublishResults(publishRes.details);
                                    }, 2000);
                                }
                                
                                // 保存iframe中的详情内容
                                saveIframeContent();
                                
                            } else {
                                layer.msg(publishRes.msg || '多店铺发布失败', {
                                    icon: 2,
                                    time: 4000,
                                    area: ['400px', 'auto']
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            layer.close(loadingIndex);
                            
                            let errorMsg = '多店铺发布请求失败';
                            if (status === 'timeout') {
                                errorMsg = '发布请求超时，请检查网络连接';
                            } else if (xhr.responseText) {
                                try {
                                    const res = JSON.parse(xhr.responseText);
                                    errorMsg = res.msg || errorMsg;
                                } catch (e) {
                                    errorMsg = '服务器响应异常';
                                }
                            }
                            
                            layer.msg(errorMsg, {
                                icon: 2,
                                time: 5000,
                                area: ['350px', 'auto']
                            });
                        }
                    });
                    
                } else {
                    layer.close(loadingIndex);
                    layer.msg(saveRes.msg || '商品信息保存失败', {icon: 2});
                }
            },
            error: function(xhr) {
                layer.close(loadingIndex);
                layer.msg('商品信息保存失败: ' + xhr.statusText, {icon: 2});
            }
        });
    }
    
    // 保存iframe详情内容的辅助函数
    function saveIframeContent() {
        try {
            const iframe = document.getElementById('iframe');
            if (iframe && iframe.contentWindow && iframe.contentWindow.saveAllChanges) {
                iframe.contentWindow.saveAllChanges().then(() => {
                    console.log('详情内容保存成功');
                }).catch((error) => {
                    console.error('详情内容保存失败:', error);
                });
            }
        } catch (error) {
            console.error('iframe跨域或其他错误:', error);
        }
    }
    
    // 显示发布结果详情
    function showPublishResults(details) {
        let resultHtml = '<div style="max-height: 400px; overflow-y: auto;">';
        resultHtml += '<table class="layui-table" style="margin: 0;">';
        resultHtml += '<thead><tr><th>店铺</th><th>仓库</th><th>状态</th><th>信息</th></tr></thead>';
        resultHtml += '<tbody>';
        
        details.forEach(result => {
            const statusClass = result.success ? 'layui-bg-green' : 'layui-bg-red';
            const statusText = result.success ? '成功' : '失败';
            resultHtml += `
                <tr>
                    <td>${result.storeName}</td>
                    <td>${result.warehouseName}</td>
                    <td class="${statusClass}" style="color: white; text-align: center;">${statusText}</td>
                    <td>${result.message || ''}</td>
                </tr>
            `;
        });
        
        resultHtml += '</tbody></table></div>';
        
        layer.open({
            type: 1,
            title: '发布结果详情',
            area: ['70%', '50%'],
            content: resultHtml,
            btn: ['确定'],
            yes: function(index) {
                layer.close(index);
            }
        });
    }

    // 表单提交优化
    form.on('submit(submitBtn)', function(data){
        // 获取类目链的改进方案
        const categoryData = {
            ids: [],
            texts: []
        };
    
        [1,2,3].forEach(level => {
            const $select = $(`select[name="category${level}"]`);
            const selectedOption = $select.find('option:selected');
            
            if(selectedOption.val()) {
                categoryData.ids.push(selectedOption.val());
                categoryData.texts.push(selectedOption.text());
            }
        });
    
        // 添加到提交数据（推荐使用更结构化的数据格式）
        data.field.category_chain = {
            ids: categoryData.ids.join(','),
            texts: categoryData.texts.join(' > ')
        };
    
        // 提交前的验证示例
        if(categoryData.ids.length < 3) {
            layer.msg('请完整选择3级类目');
            return false;
        }
    
        // 这里添加你的提交逻辑
        // 建议使用JSON格式保存更结构化的数据
        data.field.category_full = JSON.stringify({
            level1: {id: categoryData.ids[0], name: categoryData.texts[0]},
            level2: {id: categoryData.ids[1], name: categoryData.texts[1]},
            level3: {id: categoryData.ids[2], name: categoryData.texts[2]}
        });
        const attributes = {};
        $('[name^="attributes["]').each(function() {
            const name = $(this).attr('name');
            const id = name.match(/\[(.*?)\]/)[1];
            attributes[id] = $(this).val();
        });
        $('select[name^="attributes["]').each(function() {
            const name = $(this).attr('name');
            const id = name.match(/\[(.*?)\]/)[1];
            attributes[id] = $(this).val();
        });
    

        layer.load(2);
        $.ajax({
            url: './ajax.php?act=save_production',
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function(res) {
                layer.closeAll();
                if(res.code === 0) {
                    layer.msg('保存成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '保存失败', {icon: 2});
                }
            },
            error: function(xhr) {
                layer.closeAll();
                layer.msg('请求失败: ' + xhr.statusText, {icon: 2});
            }
        });
        return false;
    });
    
    function attributes_select(attribute_id,callback){
        <?php if($category2 and $category3){?>
            var category2 = <?=$category2?>;
            var category3 = <?=$category3?>;
        <? }else{ ?>
            var category2 = $(`select[name="category2"]`).val();
            var category3 = $(`select[name="category3"]`).val();
        <? }?>
        return new Promise((resolve, reject) => {
            $.ajax({
                url: './ajax.php?act=get_sku_attributevalues',
                type: 'POST',
                dataType: 'json',
                data: { 
                    attribute_id: attribute_id,
                    category2: category2,
                    category3: category3 
                },
                success: function(res) {
                    if (res.code === 0) {
                        //attributevalues.push({id:attribute_id,data:res.data})
                        attributevalues.set(attribute_id,res.data)
                        callback(res.data); // 成功时回调
                    } else {
                        layer.msg(res.msg || '获取属性失败');
                        callback([]);
                    }
                },
                error: function() {
                    layer.msg('请求失败');
                    callback([]);
                }
            });
        });
    }
    
    // 美图设计室SDK初始化
    function initMTImageEditor() {
        MTImageEditor.init({
            // 使用的SDK名称
            moduleName: 'image-editor-sdk',
            // 必填，到开放平台申请通过后才可用 (目前使用测试key)
            accessKey: 'cQwdx9FXKfEcwgu7txxxJkPrSXvrLMGk',
            title: '美图设计室Web版',
            // 默认以弹窗形式展现，指定el后嵌入到指定target
            el: '',
            // 是否带有全屏功能
            fullscreen: true,
            // 窗体宽高是否跟随window.onresize变更
            resizeAble: true
        });
        
        // 设置保存回调
        MTImageEditor.saveImage((base64, type, id) => {
            // base64：图片数据
            // type：图片类型,jpg|png
            // id：作图记录id //可用于二次编辑
            
            layer.load(2);
            // 将编辑后的图片保存到服务器
            $.ajax({
                url: './ajax.php?act=save_edited_image',
                type: 'POST',
                data: {
                    image_data: base64,
                    image_type: type,
                    record_id: id,
                    product_id: <?=$id?>,
                    original_index: window.currentEditingImageIndex
                },
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        // 替换页面中的图片
                        if (window.currentEditingImageIndex !== null) {
                            const $imageItem = $(`#previewBox .image-item:eq(${window.currentEditingImageIndex})`);
                            $imageItem.find('img.images').attr('src', res.data.new_image_url);
                            $imageItem.attr('data-url', res.data.new_image_url);
                            
                            // 更新表单数据
                            updateImageFormData();
                        }
                        
                        layer.msg('图片编辑完成！', {icon: 1});
                        // 关闭编辑器
                        MTImageEditor.close();
                    } else {
                        layer.msg(res.msg || '保存失败', {icon: 2});
                    }
                },
                error: function(xhr) {
                    layer.closeAll('loading');
                    layer.msg('保存失败: ' + xhr.statusText, {icon: 2});
                }
            });
        });
        
        // 监听弹窗关闭
        MTImageEditor.onClose(() => {
    
            window.currentEditingImageIndex = null;
        });
    }
    
    // 页面加载完成后初始化美图SDK
    // 页面性能优化
    $(document).ready(function() {
        // 清理旧的定时器，避免内存泄漏
        window.addEventListener('beforeunload', function() {
            // 清理所有定时器
            for (var i = 1; i < 99999; i++) {
                window.clearTimeout(i);
                window.clearInterval(i);
            }
            // 清理Ajax缓存
            window.ajaxCache = {};
        });

        // 优化图片懒加载
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            observer.unobserve(img);
                        }
                    }
                });
            });

            // 为所有图片添加懒加载
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }


    });
    
    $(document).ready(function() {
        initMTImageEditor();
    });
});

// 打开美图编辑器 - 全局函数
function openMTImageEditor(imageUrl, imageIndex) {
    if (!imageUrl) {
        layer.msg('图片地址无效', {icon: 2});
        return;
    }
    
    // 记录当前编辑的图片索引
    window.currentEditingImageIndex = imageIndex;
    
    
    
    // 打开指定图片进行编辑
    MTImageEditor.openImage(imageUrl);
}

// 二次编辑功能
function openMTImageEditorRecord(recordId) {
    MTImageEditor.openRecord(recordId);
}


// 获取图片数据
function getImagesData() {
    var images = [];
    $('#previewBox .image-item').each(function() {
        var imageUrl = $(this).attr('data-url') || $(this).find('img').attr('src');
        if (imageUrl) {
            images.push(imageUrl);
        }
    });
    return images;
}

// 获取主图
function getMainImage() {
    var firstImage = $('#previewBox .image-item:first');
    if (firstImage.length > 0) {
        return firstImage.attr('data-url') || firstImage.find('img').attr('src');
    }
    return '';
}

// 获取属性数据
function getAttributesData() {
    var attributes = {};
    $('[name^="attributes["]').each(function() {
        var name = $(this).attr('name');
        var value = $(this).val();
        if (value) {
            attributes[name] = value;
        }
    });
    return attributes;
}

// 保存新产品
function saveNewProduct() {
    layer.load(2); // 显示加载动画

    var productData = {
        id: 0, // 新增商品，id为0
        title: $('[name="title"]').val(),
        images: getImagesData().join(';'), // 使用分号分隔，与现有格式保持一致
        mainImage: getMainImage(),
        orderUrl: $('#orderUrl').val(),
        videos: $('#video').val(),
        detail_url: $('#detail_url').val(),
        jsonimgData: $('#jsonimgData').val(),
        weight: $('[name="weight"]').val(),
        height: $('[name="height"]').val(),
        depth: $('[name="depth"]').val(), 
        width: $('[name="width"]').val(),
        price: $('[name="price"]').val(),
        old_price: $('[name="old_price"]').val(),
        offer_id: $('[name="offer_id"]').val()
    };

    // 添加属性数据
    $('[name^="attributes["]').each(function() {
        var name = $(this).attr('name');
        var value = $(this).val();
        if (value) {
            productData[name] = value;
        }
    });

    $.ajax({
        url: './ajax.php?act=save_production',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(productData),
        dataType: 'json',
        success: function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg('保存成功', { icon: 1, time: 1500 }, function() {
                    // 关闭弹窗并刷新父窗口表格
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    parent.layui.table.reload('productionTable');
                });
            } else {
                layer.alert('保存失败: ' + (res.msg || '未知错误'), { icon: 2 });
            }
        },
        error: function(xhr) {
            layer.closeAll('loading');
            layer.alert('请求失败: ' + xhr.statusText, { icon: 2 });
        }
    });
}

// 为新保存按钮绑定事件
$(document).ready(function () {
    $('#saveBtn').on('click', function() {
        // 如果是新增商品（id为空），则调用新保存函数
        var productId = '<?php echo $id; ?>';
        if (!productId) {
            saveNewProduct();
        } else {
            // 如果是编辑，可以调用原来的保存逻辑
            layer.msg('编辑功能暂未实现，请使用"保存并发布"', {icon: 2});
        }
    });
    
    $('#closeBtn').on('click', function() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    });
});
</script>
</body>
</html>