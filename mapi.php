<?php
if (function_exists("set_time_limit")) {
    @set_time_limit(0);
}
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');
require './includes/common.php';
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
http_response_code(200);
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
$s = isset($_GET['s'])?$_GET['s']:exit('404 Not Found'.$clientip.'');
unset($_GET['s']);
if($s!='notice' and $s!='app'){
    if($islogin2==1){}else exit('{"code":-3,"msg":"No Login'.$clientip.'"}');
}

$json_data = file_get_contents('php://input');
// 解析 JSON
$data = json_decode(trim($json_data,':'), true);

$h = ['Content-Type: application/json'];
switch ($s) {
case 'upcookie':
    echo 'ok';
break;
}