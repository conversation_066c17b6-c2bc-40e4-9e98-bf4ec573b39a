<?php
namespace lib;

class OzonDataHandler {
    // 数据采集方法 - 更健壮的实现
    public function collectData($sku) {
        $baseDir = ROOT . 'assets/OzonDataHandler/';
        $targetDir = $baseDir . date("Ymd");
        try {
            // 确保目录存在
            if (!is_dir($targetDir) && !mkdir($targetDir, 0755, true)) {
                throw new \RuntimeException("无法创建文件夹: $targetDir");
            }
            
            // 采集两种类型的数据
            $urls = [
                "pdpPage2column" => "//www.ozon.ru/product/$sku/?layout_container=pdpPage2column&layout_page_index=2",
                "product" => "/product/$sku/"
            ];
            
            $success = true;
            foreach ($urls as $type => $url) {
                $res = $this->curl(['url' => $url]);
                if ($res === false) {
                    $success = false;
                    continue;
                }
                
                $filePath = "{$targetDir}/{$sku}_{$type}.json";
                if (!file_put_contents($filePath, $res) || !chmod($filePath, 0755)) {
                    $success = false;
                }
            }
            
            return $success;
            
        } catch (\Exception $e) {
            error_log("数据采集错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 处理Ozon商品数据
     * 
     * @param string $sku 商品SKU
     * @param int $type 处理类型：0-返回原始数据，1-返回特征数据
     * @return array|false 成功返回处理后的数据，失败返回false
     */
    public function processData($sku, $type = 0,$row) {
        // 设置执行环境
        
        ini_set('memory_limit', '512M');
        set_time_limit(60);
        
        try {
            // 1. 准备文件路径
            $baseDir = ROOT . 'assets/OzonDataHandler/' . date("Ymd") . '/';
            $files = [
                'pdp' => $baseDir . $sku . '_pdpPage2column.json',
                'product' => $baseDir . $sku . '_product.json'
            ];
            
            // 2. 读取并验证文件
            $contents = [];
            foreach ($files as $key => $filePath) {
                if (!file_exists($filePath)) {
                    throw new \RuntimeException("数据文件不存在: {$filePath}");
                }
                
                $content = file_get_contents($filePath);
                if ($content === false) {
                    throw new \RuntimeException("无法读取文件: {$filePath}");
                }
                
                $contents[$key] = $this->convertToUtf8($content);
            }
            
            // 3. 解析JSON数据
            $data = [];
            foreach ($contents as $key => $content) {
                $data[$key] = json_decode($content, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \RuntimeException("{$key}文件JSON解析错误: " . json_last_error_msg());
                }
            }
            
            // 4. 根据类型处理数据
            if ($type === 0) {
                // 返回两个文件的原始数据
                // 在processData中添加调试

                $webCharacteristics = $this->characteristics($data['pdp']['widgetStates']['webCharacteristics-3282540-pdpPage2column-2'],$row); #商品特性/规格
                $webDescription = $this->characteristics($data['pdp']['widgetStates']['webDescription-2983278-pdpPage2column-2'],$row); #配件/包装清单
                if($webDescription){
                    $webCharacteristics = array_merge($webCharacteristics, $webDescription);
                }
                $coverImageJson = json_decode($data['product']['widgetStates']['webGallery-3311626-default-1'],true); #商品主图
                
                if($coverImageJson['coverImage']){
                    $coverImage[] = ['id'=>4194,'complex_id'=>0,'values'=>['dictionary_value_id'=>0,'value'=>$coverImageJson['coverImage']]]; #商品主图
                }
                $webGallery[] = $this->images($coverImageJson); #商品详情页的图片/视频画廊配置
                if($coverImageJson['coverImage']){
                    $webGallery = array_merge($coverImage, $webGallery);
                }
                
                
                $result = array_merge($webCharacteristics, $webGallery);
                exit(json_encode($result,JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                return $result;
                return [
                    'pdpData' => $data['pdp'],
                    'productData' => $data['product']
                ];
                
            } else {
                // 提取特征数据
                if (!isset($data['pdp']['widgetStates'])) {
                    throw new \RuntimeException('pdp数据缺少widgetStates字段');
                }
                
                $characteristics = $this->extractCharacteristics($data['pdp']['widgetStates']);
                
                // 合并product数据中的有用信息
                if (isset($data['product']['productInfo'])) {
                    $characteristics['productInfo'] = $data['product']['productInfo'];
                }
                return $characteristics;
            }
            
        } catch (\Exception $e) {
            error_log("[OzonDataHandler] 数据处理错误(SKU:{$sku}): " . $e->getMessage());
            return false;
        }
    }

    /**
     * 从widgetStates中提取特征数据
     */
    private function extractCharacteristics($widgetStates) {
        $result = [];
        
        foreach ($widgetStates as $key => $value) {
            // 处理特征数据
            if (strpos($key, 'webCharacteristics') !== false) {
                $result['characteristics'] = is_string($value) ? json_decode($value, true) : $value;
            }
            
            // 处理价格数据
            if (strpos($key, 'webPrice') !== false) {
                $result['price'] = is_string($value) ? json_decode($value, true) : $value;
            }
            
            // 处理卖家数据
            if (strpos($key, 'webSeller') !== false) {
                $result['seller'] = is_string($value) ? json_decode($value, true) : $value;
            }
        }
        
        if (empty($result['characteristics'])) {
            throw new \RuntimeException('未找到特征数据');
        }
        
        return $result;
    }
    
    #特征数据处理方法
    private function characteristics($json,$row){
        #已经是解析后的数据
        if(!$json){
            return false;
        }
        if(is_array($json)){
            $data = $json;
        }else{
            $data = json_decode($json, true, JSON_THROW_ON_ERROR);
            // 检查是否解析成功
            exit($json);
            if (json_last_error() !== JSON_ERROR_NONE) {
                die('2JSON解析错误: ' . json_last_error_msg());
            }
        }
        
        
        $translations = [
            'Длина, см' => 10231,//长度(cm)
            'Ширина, см' => 10175,#宽度(cm)
            'Размеры, мм' => 4382,#'尺寸(mm)'
            'Сетчатый' => 10597,#'网状'
            'Количество мест' => 6882,#承载人数
            'Макс. нагрузка, кг' => 7915,#最大承重(kg)
            'Вес товара, г' => 4497,#商品重量(g)
            'Гамак' => 8229,#'吊床'
            'Целевая аудитория' => 9390,#适用人群
            'Взрослая' => 9390,#'成人'
            'Детская' => 9390,#'儿童'
            'В разобранном виде' => 10596,#组装形式
            'Цвет' => 10096,#颜色
            'Белый' => 10096,#白色
            'прозрачный' => 10096,#透明
            'Фабричное производство' => 22270,#工厂生产
            'Страна-изготовитель' => 4389,#
            'Китай' => 4389,#中国
            'Код продавца' => 9024, #商家编码
            'Комплектация'=>4384,
            '1*гамак' => 4384,
            'Количество в упаковке, шт' => 8513, // 包装数量(个)
            'Материал' => 6383, // 材料
            'Флаг России' => 10097, // 俄罗斯国旗(颜色值)
            'Искусственный шелк' => 6383, // 人造丝
            'Универсально' => 8449, // 通用
            'Для кого' => 8449, // 适用对象
            'Праздник' => 8448, // 节日
            '9 мая' => 8448, // 5月9日
            'День Победы' => 8448, // 胜利日
            '1 сентября' => 8448, // 9月1日
            'Флаг' => 8229, // 旗帜
            'OKZIRI' => 85, // 品牌
            'Датчик' => 6200, // 传感器
            'Измерения' => 6193, // 测量
            'Экран' => 4484, // 屏幕
            'Особенности метеоприборов' => 6205, // 气象仪器特点
            'Диапазон показателей температуры снаружи' => 6196, // 外部温度范围
            'Диапазон показателей температуры внутри' => 6197, // 内部温度范围
            'Диапазон показателей влажности' => 6198, // 湿度范围
            'Макс. темп. снаружи, +°С' => 6195, // 最高外部温度
            'Мин. темп. снаружи, -°С' => 6194, // 最低外部温度
            'Макс. число датчиков, шт.' => 6203, // 最大传感器数量
            'Вид питания' => 5283, // 电源类型
            'Вид, количество батарей' => 6206, // 电池类型和数量
            'Количество в комплекте, шт.' => 10119, // 套装数量
            'Гарантийный срок' => 4385, // 保修期
            'Термогигрометр' => 8229, // 温湿度计(已存在，不需重复添加)
            'LCD' => 4484, // LCD屏幕
            'Сменная батарея' => 5283, // 可更换电池
            '3V CR2032 - 1 шт' => 6206, // CR2032电池
            '1 год' => 4385, // 1年保修
            'Материал подкладки' => 5307, // 内衬材料
            'Количество внутренних отделений' => 22328, // 内部隔层数量
            'Вид принта' => 6391, // 印花类型
            'Материал фурнитуры' => 5311, // 配件材料
            'Особенности конструкции сумки' => 8567, // 包袋结构特点
            'Ручки' => 5297, // 手柄类型
            'Формат А4' => 11628, // A4格式
            'Вид сумки' => 20259, // 包袋类型
            'Высота ручек, см' => 22321, // 手柄高度(cm)
            'Пол' => 9163, // 性别
            'Коллекция' => 9725, // 系列
            'Тип застежки' => 5344, // 扣件类型
            'Полиэстер' => 5307, // 聚酯纤维
            'Металлизированный материал' => 5311, // 金属化材料
            'Однотонный' => 6391, // 纯色
            'Одна лямка' => 8567, // 单肩带
            'Защелка' => 5344, // 卡扣
            'На запястье' => 5297, // 手腕式
            'На локоть' => 5297, // 肘部式
            'На плечо' => 5297, // 肩背式
            'не вмещает' => 11628, // 不包含
            'багет' => 20259, // 法棍包
            'Весна-лето 2024' => 9725, // 2024春夏系列
            'Девочки' => 9163, // 女孩
            'Аутлет' => 10061, // 奥特莱斯
            'Унисекс' => 13216, // 男女通用
            'Искусственная/экокожа' => 21688, // 人造/生态皮革
            'Радиус приема сигнала датчика, м' => 6201, // 传感器信号接收半径(m)
            'Термометр' => 8229, // 温度计
            'Метеостанция' => 8229, // 气象站
            'Для кухонной мойки' => 6888, // 用于厨房水槽
            'На раковину' => 6890, // 安装在洗手盆上
            'На столешницу' => 6890, // 安装在台面上
            'Выдвижной' => 9715, // 可伸缩式
            'Гибкий' => 9715, // 柔性
            'С режимом "душ"' => 9715, // 带淋浴模式
            'Совмещенный излив для питьевой воды' => 9715, // 饮用水组合出水口
            'Аэратор' => 6892, // 起泡器
            'Гибкая подводка' => 6892, // 柔性连接管
            'С краном для питьевой воды' => 6892, // 带饮用水龙头
            'С душевым гарнитуром' => 6892, // 带淋浴套装
            'Керамический картридж' => 10393, // 陶瓷阀芯
            'Рычажное' => 9703, // 杠杆式
            'Современный' => 9707, // 现代风格
            'PVD' => 21385, // PVD镀层
            'Внешний проводной' => 6200, // 外接有线
            'температуры снаружи' => 6193, // 外部温度
            'Сменная батарея' => 5283, // 可更换电池
            'LR44-2шт' => 6206, // LR44电池2节
            '48х28х14' => 4382, // 48×28×14(mm)
            'Термометр - 1 шт. Батарейка - LR44 - 2 шт.' => 4384, // 温度计1个，LR44电池2节
            '1 год' => 4385, // 1年
            'SimpleShop/' => 9048, // SimpleShop品牌
            'Treversypro' => 9048, // Treversypro品牌
            'Корея' => 4389, // 韩国
            'Черный' => 10096, // 黑色
            'Безопасный' => 11794, // 安全
            '9015801900' => 23133, // HS编码
            'Погодная станция' => 23134, // 气象站
            'Прочие приборы и инструменты электронные' => 22232, // 其他电子仪器和工具
        ];
        
        $arrays = $this->attribute($row);
        
        if($arrays){
            $translations = array_merge($translations,$arrays);
        }

        foreach ($data['characteristics'] as $section) {
            $title = $section['title'];
            $translatedTitle = $translations[$title] ?? $title;
            if(empty($section['short']) and is_int($translatedTitle)){
                $values = ['dictionary_value_id'=>0,'value'=>$section['content']];
                $array[] =['id'=>$translatedTitle,'complex_id'=>0,'values'=>$values];
            }
            foreach ($section['short'] as $item) {
                $name = $item['name'];
                $translatedName = $translations[$name] ?? $name;
                $values = array_map(function($v) {
                    return ['dictionary_value_id'=>0,'value'=>$v['text']];
                }, $item['values']);
                if(is_int($translatedName)){
                    $array[] =['id'=>$translatedName,'complex_id'=>0,'values'=>$values];
                }
            }
        }
        return $array;
    }
    
    private function attribute($row){
        $client = new \lib\OzonApiClient();
        $data = $client->attribute($row);
        if($data){
            foreach ($data as $item){
                $items[$item['name']] = $item['id'];
            }
            return $items;
        }
        return false;
    }
    
    private function images($data){
        unset($data['images'][0]); #删除主图
        foreach ($data['images'] as $value){
            $values[] = ['dictionary_value_id'=>0,'value'=>$value['src']];
        }
        return ['id'=>4195,'complex_id'=>0,'values'=>$values];
    }
    
    // 改进的UTF-8转换方法
    private function convertToUtf8($string) {
        // 先尝试检测编码
        $encoding = mb_detect_encoding($string, mb_list_encodings(), true);
        
        // 如果检测失败，尝试常见编码
        if (!$encoding) {
            $encodingsToTry = ['Windows-1251', 'ISO-8859-1', 'UTF-8'];
            foreach ($encodingsToTry as $enc) {
                if (@mb_check_encoding($string, $enc)) {
                    $encoding = $enc;
                    break;
                }
            }
        }
        
        // 转换编码
        return $encoding !== 'UTF-8' 
            ? mb_convert_encoding($string, 'UTF-8', $encoding ?: 'auto') 
            : $string;
    }
    
    // 增强的cURL方法
    private function curl($data) {
        $url = "https://ozon.ssss00.me/api/v2";
        
        try {
            $ch = curl_init($url);
            $jsonData = json_encode($data);
            
            if ($jsonData === false) {
                throw new \RuntimeException('JSON编码失败: ' . json_last_error_msg());
            }
            
            $options = [
                CURLOPT_POST => true,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($jsonData)
                ],
                CURLOPT_POSTFIELDS => $jsonData,
                CURLOPT_SSL_VERIFYPEER => false, // 生产环境应该为true
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 5,
                CURLOPT_ENCODING => '', // 自动解压
            ];
            
            curl_setopt_array($ch, $options);
            
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new \RuntimeException('cURL错误: ' . curl_error($ch));
            }
            
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if ($httpCode >= 400) {
                throw new \RuntimeException("HTTP错误: $httpCode");
            }
            
            return $response;
            
        } catch (\Exception $e) {
            error_log("cURL请求错误: " . $e->getMessage());
            return false;
        } finally {
            if (isset($ch)) {
                curl_close($ch);
            }
        }
    }
    
    // 新增方法：提取商品信息
    public function extractProductInfo($sku) {
        $data = $this->processData($sku);
        if (!$data) {
            return false;
        }
        
        $result = [
            'title' => null,
            'price' => null,
            'rating' => null,
            'characteristics' => [],
            'seller' => null
        ];
        
        // 提取标题
        if (isset($data['title'])) {
            $result['title'] = $data['title'];
        }
        
        // 提取价格
        if (isset($data['price'])) {
            $result['price'] = is_string($data['price']) 
                ? $data['price'] 
                : json_encode($data['price']);
        }
        
        // 提取特征
        if (isset($data['characteristics'])) {
            $result['characteristics'] = $data['characteristics'];
        }
        
        return $result;
    }
}