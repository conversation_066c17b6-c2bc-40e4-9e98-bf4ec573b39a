<?php
session_start();
if (!isset($_SESSION['packer_id'])) {
    header("Location: packer_login.php");
    exit;
}
$packer_id = $_SESSION['packer_id'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>打包员订单列表</title>
    <link rel="stylesheet" href="../assets/component/layui/css/layui.css" />
    <link rel="stylesheet" href="../assets/css/order.css" />
    <style>
        body {
            padding: 20px;
            background: #f9f9f9;
        }
        .search-form {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
<div class="layui-card">
    <div class="layui-card-header">等待发货订单</div>
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane search-form" lay-filter="searchForm">
            <div class="layui-inline">
                <input type="text" name="sku" placeholder="SKU" class="layui-input" autocomplete="off">
            </div>
            <div class="layui-inline">
                <input type="text" name="posting_number" placeholder="货件编号" class="layui-input" autocomplete="off">
            </div>
            <div class="layui-inline">
                <input type="text" name="title" placeholder="标题" class="layui-input" autocomplete="off">
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 80px;">价格区间</label>
                <div class="layui-input-inline" style="width: 80px;">
                    <input type="text" name="money" placeholder="最低价" class="layui-input" autocomplete="off">
                </div>
                <div class="layui-input-inline" style="width: 80px;">
                    <input type="text" name="moneys" placeholder="最高价" class="layui-input" autocomplete="off">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 80px;">用户UID</label>
                <div class="layui-input-inline" style="width: 150px;">
                    <select name="user_uid" lay-search id="userUidSelect">
                        <option value="">加载中...</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                <button type="reset" class="layui-btn layui-btn-primary" lay-filter="reset">重置</button>
            </div>
        </form>

        <table id="orderTable" lay-filter="orderTable"></table>
    </div>
</div>

<script src="../assets/component/layui/layui.js"></script>
<script>
layui.use(['table', 'form', 'jquery'], function(){
    var table = layui.table;
    var form = layui.form;
    var $ = layui.$;
    var packerId = <?php echo json_encode($packer_id); ?>;

    function renderTable(filters) {
        filters = filters || {};
        filters.status = 'awaiting_deliver'; // Only show awaiting delivery
        filters.packer_id = packerId;

        table.render({
            elem: '#orderTable',
            url: 'ajax.php?act=get_packer_orders',
            where: filters,
            page: true,
            limit: 20,
            cols: [[
                {type: 'checkbox', fixed: 'left', width: 50},
                {field: 'posting_number', title: '货件编号', width: 180},
                {field: 'sku', title: 'SKU', width: 120},
                {field: 'title', title: '标题', width: 300},
                {field: 'price', title: '价格', width: 100, sort: true},
                {field: 'user_uid', title: '用户UID', width: 150},
                {field: 'quantity', title: '数量', width: 80},
                {field: 'order_date', title: '下单时间', width: 180, sort: true}
            ]]
        });
    }

    form.on('submit(search)', function(data){
        renderTable(data.field);
        return false;
    });

    form.on('reset(reset)', function(){
        renderTable();
    });

    // Load user UIDs for filter dropdown
    $.ajax({
        url: 'ajax.php?act=get_user_uids',
        dataType: 'json',
        success: function(res){
            if(res.code === 0){
                var options = '<option value="">全部用户UID</option>';
                res.data.forEach(function(user){
                    options += '<option value="'+user.uid+'">'+user.uid+'</option>';
                });
                $('#userUidSelect').html(options);
                form.render('select');
            }
        }
    });

    renderTable();
});
</script>
</body>
</html>
